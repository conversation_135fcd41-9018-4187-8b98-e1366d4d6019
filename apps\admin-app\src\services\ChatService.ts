import { supabase } from '../lib/supabase';

export interface ChatMessage {
  id: string;
  ride_id: string;
  sender_id: string;
  sender_type: 'passenger' | 'driver';
  message: string;
  message_type: 'text' | 'location' | 'image' | 'system';
  metadata?: {
    location?: [number, number];
    image_url?: string;
    system_type?: 'ride_started' | 'ride_completed' | 'driver_arrived';
  };
  created_at: string;
  read_at?: string;
}

export interface ChatParticipant {
  id: string;
  name: string;
  avatar?: string;
  user_type: 'passenger' | 'driver';
  is_online: boolean;
  last_seen?: string;
}

export class ChatService {
  private static instance: ChatService;

  static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  // Enviar mensagem de texto
  async sendTextMessage(rideId: string, message: string): Promise<ChatMessage | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      // Obter tipo do usuário
      const { data: profile } = await supabase
        .from('profiles')
        .select('user_type')
        .eq('id', user.id)
        .single();

      if (!profile) throw new Error('Perfil não encontrado');

      const { data, error } = await supabase
        .from('chat_messages')
        .insert({
          ride_id: rideId,
          sender_id: user.id,
          sender_type: profile.user_type,
          message: message.trim(),
          message_type: 'text'
        })
        .select()
        .single();

      if (error) {
        console.error('Erro ao enviar mensagem:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      return null;
    }
  }

  // Enviar localização
  async sendLocationMessage(rideId: string, location: [number, number]): Promise<ChatMessage | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      const { data: profile } = await supabase
        .from('profiles')
        .select('user_type')
        .eq('id', user.id)
        .single();

      if (!profile) throw new Error('Perfil não encontrado');

      const { data, error } = await supabase
        .from('chat_messages')
        .insert({
          ride_id: rideId,
          sender_id: user.id,
          sender_type: profile.user_type,
          message: 'Localização compartilhada',
          message_type: 'location',
          metadata: { location }
        })
        .select()
        .single();

      if (error) {
        console.error('Erro ao enviar localização:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Erro ao enviar localização:', error);
      return null;
    }
  }

  // Enviar mensagem do sistema
  async sendSystemMessage(
    rideId: string, 
    systemType: 'ride_started' | 'ride_completed' | 'driver_arrived',
    customMessage?: string
  ): Promise<ChatMessage | null> {
    try {
      const systemMessages = {
        ride_started: '🚗 Corrida iniciada! O motorista está a caminho.',
        ride_completed: '✅ Corrida finalizada! Obrigado por usar o MobiDrive.',
        driver_arrived: '📍 Motorista chegou ao local de partida!'
      };

      const message = customMessage || systemMessages[systemType];

      const { data, error } = await supabase
        .from('chat_messages')
        .insert({
          ride_id: rideId,
          sender_id: '00000000-0000-0000-0000-000000000000', // Sistema
          sender_type: 'driver', // Temporário
          message,
          message_type: 'system',
          metadata: { system_type: systemType }
        })
        .select()
        .single();

      if (error) {
        console.error('Erro ao enviar mensagem do sistema:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Erro ao enviar mensagem do sistema:', error);
      return null;
    }
  }

  // Obter mensagens do chat
  async getChatMessages(rideId: string, limit: number = 50): Promise<ChatMessage[]> {
    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select(`
          *,
          sender:profiles!chat_messages_sender_id_fkey(
            id,
            full_name
          )
        `)
        .eq('ride_id', rideId)
        .order('created_at', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('Erro ao obter mensagens:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Erro ao obter mensagens:', error);
      return [];
    }
  }

  // Marcar mensagens como lidas
  async markMessagesAsRead(rideId: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return false;

      const { error } = await supabase
        .from('chat_messages')
        .update({ read_at: new Date().toISOString() })
        .eq('ride_id', rideId)
        .neq('sender_id', user.id)
        .is('read_at', null);

      if (error) {
        console.error('Erro ao marcar mensagens como lidas:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao marcar mensagens como lidas:', error);
      return false;
    }
  }

  // Obter participantes do chat
  async getChatParticipants(rideId: string): Promise<ChatParticipant[]> {
    try {
      const { data, error } = await supabase
        .from('ride_requests')
        .select(`
          user_id,
          driver_id,
          user:profiles!ride_requests_user_id_fkey(
            id,
            full_name,
            user_type
          ),
          driver:profiles!ride_requests_driver_id_fkey(
            id,
            full_name,
            user_type
          )
        `)
        .eq('id', rideId)
        .single();

      if (error || !data) {
        console.error('Erro ao obter participantes:', error);
        return [];
      }

      const participants: ChatParticipant[] = [];

      if (data.user) {
        participants.push({
          id: data.user.id,
          name: data.user.full_name || 'Passageiro',
          user_type: 'passenger',
          is_online: true // Será implementado com presence
        });
      }

      if (data.driver) {
        participants.push({
          id: data.driver.id,
          name: data.driver.full_name || 'Motorista',
          user_type: 'driver',
          is_online: true // Será implementado com presence
        });
      }

      return participants;
    } catch (error) {
      console.error('Erro ao obter participantes:', error);
      return [];
    }
  }

  // Subscrever a mensagens em tempo real (desabilitado)
  subscribeToMessages(rideId: string, callback: (message: ChatMessage) => void) {
    console.log('⚠️ Realtime chat desabilitado para estabilidade');
    // Retornar um objeto mock para compatibilidade
    return {
      unsubscribe: () => console.log('Chat subscription mock unsubscribed')
    };
  }

  // Obter contagem de mensagens não lidas
  async getUnreadCount(rideId: string): Promise<number> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return 0;

      const { count, error } = await supabase
        .from('chat_messages')
        .select('*', { count: 'exact', head: true })
        .eq('ride_id', rideId)
        .neq('sender_id', user.id)
        .is('read_at', null);

      if (error) {
        console.error('Erro ao obter contagem não lidas:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Erro ao obter contagem não lidas:', error);
      return 0;
    }
  }

  // Deletar mensagem (apenas próprias mensagens)
  async deleteMessage(messageId: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return false;

      const { error } = await supabase
        .from('chat_messages')
        .delete()
        .eq('id', messageId)
        .eq('sender_id', user.id);

      if (error) {
        console.error('Erro ao deletar mensagem:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao deletar mensagem:', error);
      return false;
    }
  }

  // Buscar mensagens
  async searchMessages(rideId: string, query: string): Promise<ChatMessage[]> {
    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('ride_id', rideId)
        .ilike('message', `%${query}%`)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) {
        console.error('Erro ao buscar mensagens:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Erro ao buscar mensagens:', error);
      return [];
    }
  }
}

export const chatService = ChatService.getInstance();
