import { SearchResult, RouteResult, RideEstimate } from './MapboxService'

interface CacheItem<T> {
  data: T
  timestamp: number
  expiresAt: number
}

interface CacheConfig {
  searchTTL: number // Time to live for search results
  routeTTL: number // Time to live for route results
  rideEstimateTTL: number // Time to live for ride estimates
  maxItems: number // Maximum items in cache
}

class MapboxCacheService {
  private searchCache = new Map<string, CacheItem<SearchResult[]>>()
  private routeCache = new Map<string, CacheItem<RouteResult[]>>()
  private rideEstimateCache = new Map<string, CacheItem<RideEstimate>>()
  private config: CacheConfig = {
    searchTTL: 10 * 60 * 1000, // 10 minutes (increased)
    routeTTL: 15 * 60 * 1000, // 15 minutes (increased)
    rideEstimateTTL: 15 * 60 * 1000, // 15 minutes for ride estimates
    maxItems: 200 // Increased cache size
  }

  /**
   * Generate cache key for search
   */
  private generateSearchKey(query: string, options?: any): string {
    const optionsStr = options ? JSON.stringify(options) : ''
    return `search:${query.toLowerCase().trim()}:${optionsStr}`
  }

  /**
   * Generate cache key for route
   */
  private generateRouteKey(origin: [number, number], destination: [number, number]): string {
    return `route:${origin[0]},${origin[1]}:${destination[0]},${destination[1]}`
  }

  /**
   * Generate cache key for ride estimate
   */
  private generateRideEstimateKey(origin: [number, number], destination: [number, number]): string {
    return `estimate:${origin[0].toFixed(4)},${origin[1].toFixed(4)}:${destination[0].toFixed(4)},${destination[1].toFixed(4)}`
  }

  /**
   * Clean expired items from cache
   */
  private cleanExpired<T>(cache: Map<string, CacheItem<T>>): void {
    const now = Date.now()
    for (const [key, item] of cache.entries()) {
      if (now > item.expiresAt) {
        cache.delete(key)
      }
    }
  }

  /**
   * Ensure cache doesn't exceed max items
   */
  private enforceMaxItems<T>(cache: Map<string, CacheItem<T>>): void {
    if (cache.size > this.config.maxItems) {
      // Remove oldest items
      const entries = Array.from(cache.entries())
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp)

      const itemsToRemove = cache.size - this.config.maxItems
      for (let i = 0; i < itemsToRemove; i++) {
        cache.delete(entries[i][0])
      }
    }
  }

  /**
   * Cache search results
   */
  cacheSearchResults(query: string, results: SearchResult[], options?: any): void {
    const key = this.generateSearchKey(query, options)
    const now = Date.now()

    this.searchCache.set(key, {
      data: results,
      timestamp: now,
      expiresAt: now + this.config.searchTTL
    })

    this.cleanExpired(this.searchCache)
    this.enforceMaxItems(this.searchCache)
  }

  /**
   * Clear all search cache
   */
  clearSearchCache(): void {
    this.searchCache.clear()
    console.log('🗑️ Search cache cleared')
  }

  /**
   * Get cached search results
   */
  getCachedSearchResults(query: string, options?: any): SearchResult[] | null {
    const key = this.generateSearchKey(query, options)
    const item = this.searchCache.get(key)

    if (!item) return null

    const now = Date.now()
    if (now > item.expiresAt) {
      this.searchCache.delete(key)
      return null
    }

    return item.data
  }

  /**
   * Cache route results
   */
  cacheRouteResults(origin: [number, number], destination: [number, number], routes: RouteResult[]): void {
    const key = this.generateRouteKey(origin, destination)
    const now = Date.now()

    this.routeCache.set(key, {
      data: routes,
      timestamp: now,
      expiresAt: now + this.config.routeTTL
    })

    this.cleanExpired(this.routeCache)
    this.enforceMaxItems(this.routeCache)
  }

  /**
   * Get cached route results
   */
  getCachedRouteResults(origin: [number, number], destination: [number, number]): RouteResult[] | null {
    const key = this.generateRouteKey(origin, destination)
    const item = this.routeCache.get(key)

    if (!item) return null

    const now = Date.now()
    if (now > item.expiresAt) {
      this.routeCache.delete(key)
      return null
    }

    return item.data
  }

  /**
   * Cache ride estimate
   */
  cacheRideEstimate(origin: [number, number], destination: [number, number], estimate: RideEstimate): void {
    const key = this.generateRideEstimateKey(origin, destination)
    const now = Date.now()

    this.rideEstimateCache.set(key, {
      data: estimate,
      timestamp: now,
      expiresAt: now + this.config.rideEstimateTTL
    })

    this.cleanExpired(this.rideEstimateCache)
    this.enforceMaxItems(this.rideEstimateCache)
  }

  /**
   * Get cached ride estimate
   */
  getCachedRideEstimate(origin: [number, number], destination: [number, number]): RideEstimate | null {
    const key = this.generateRideEstimateKey(origin, destination)
    const item = this.rideEstimateCache.get(key)

    if (!item) return null

    const now = Date.now()
    if (now > item.expiresAt) {
      this.rideEstimateCache.delete(key)
      return null
    }

    return item.data
  }

  /**
   * Clear all caches
   */
  clearAll(): void {
    this.searchCache.clear()
    this.routeCache.clear()
    this.rideEstimateCache.clear()
  }

  /**
   * Clear expired items from all caches
   */
  cleanup(): void {
    this.cleanExpired(this.searchCache)
    this.cleanExpired(this.routeCache)
    this.cleanExpired(this.rideEstimateCache)
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      searchCache: {
        size: this.searchCache.size,
        maxItems: this.config.maxItems,
        ttl: this.config.searchTTL
      },
      routeCache: {
        size: this.routeCache.size,
        maxItems: this.config.maxItems,
        ttl: this.config.routeTTL
      },
      rideEstimateCache: {
        size: this.rideEstimateCache.size,
        maxItems: this.config.maxItems,
        ttl: this.config.rideEstimateTTL
      }
    }
  }

  /**
   * Update cache configuration
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
}

// Singleton instance
export const mapboxCacheService = new MapboxCacheService()

// Auto cleanup every 5 minutes
setInterval(() => {
  mapboxCacheService.cleanup()
}, 5 * 60 * 1000)

export default mapboxCacheService
