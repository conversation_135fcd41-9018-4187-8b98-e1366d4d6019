import React, { useRef, Suspense } from 'react'
import { <PERSON><PERSON>, useFrame, useLoader } from '@react-three/fiber'
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader'
import * as THREE from 'three'

// 🚗 CARRO 3D SIMPLES PARA LOGIN - APENAS O MODELO, SEM AMBIENTE 3D
// Renderização limpa e minimalista como elemento de UI

interface Car3DLoginProps {
  className?: string
  autoRotate?: boolean
  scale?: number
}

// Componente do carro 3D simples - apenas geometria
const SimpleCarModel: React.FC<{ autoRotate: boolean; scale: number }> = ({ autoRotate, scale }) => {
  const carRef = useRef<THREE.Group>(null)

  // Carregar apenas o modelo .obj
  const carModel = useLoader(OBJLoader, '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/base.obj')

  // Animação suave de rotação
  useFrame((state) => {
    if (carRef.current && autoRotate) {
      carRef.current.rotation.y = state.clock.elapsedTime * 0.2
    }
  })

  // Aplicar material simples ao modelo
  React.useEffect(() => {
    if (carModel) {
      carModel.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          // Material básico sem texturas complexas
          child.material = new THREE.MeshBasicMaterial({
            color: '#1a1a1a',
            transparent: true,
            opacity: 0.9
          })
        }
      })
    }
  }, [carModel])

  return (
    <group ref={carRef} scale={[scale, scale, scale]} position={[0, 0, 0]}>
      <primitive object={carModel} />
    </group>
  )
}

// Componente de fallback simples enquanto carrega
const SimpleCarLoader: React.FC<{ scale: number }> = ({ scale }) => {
  const loaderRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (loaderRef.current) {
      loaderRef.current.rotation.y = state.clock.elapsedTime * 0.3
    }
  })

  return (
    <group ref={loaderRef} scale={[scale, scale, scale]} position={[0, 0, 0]}>
      {/* Placeholder simples do carro */}
      <mesh position={[0, 0, 0]}>
        <boxGeometry args={[3, 1, 1.5]} />
        <meshBasicMaterial
          color="#333333"
          transparent
          opacity={0.6}
        />
      </mesh>

      {/* Teto simples */}
      <mesh position={[0, 0.6, 0]}>
        <boxGeometry args={[2, 0.6, 1.3]} />
        <meshBasicMaterial
          color="#222222"
          transparent
          opacity={0.6}
        />
      </mesh>
    </group>
  )
}

// Componente principal - renderização minimalista
export const Car3DLogin: React.FC<Car3DLoginProps> = ({
  className = "",
  autoRotate = true,
  scale = 1
}) => {
  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        camera={{
          position: [4, 2, 4],
          fov: 45,
          near: 0.1,
          far: 100
        }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "default"
        }}
        style={{ background: 'transparent' }}
      >
        {/* SEM iluminação complexa - apenas o modelo */}

        {/* Carro com Suspense - renderização simples */}
        <Suspense fallback={<SimpleCarLoader scale={scale} />}>
          <SimpleCarModel autoRotate={autoRotate} scale={scale} />
        </Suspense>
      </Canvas>
    </div>
  )
}

export default Car3DLogin
