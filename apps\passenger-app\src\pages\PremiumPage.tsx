import React from 'react'
import { motion } from 'framer-motion'
import { Crown, Star, Zap, Infinity, Gift, TrendingUp, ArrowLeft } from 'lucide-react'
import PremiumSubscriptionSystem from '../components/PremiumSubscriptionSystem'

export const PremiumPage: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  }

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Background GIF */}
      <div className="absolute inset-0 z-0">
        <img
          src="/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pb/Pb_00000.gif"
          alt="Background"
          className="w-full h-full object-cover opacity-30"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-yellow-900/30 to-black/70"></div>
      </div>

      {/* Header */}
      <div className="relative z-10 p-4">
        <div className="flex items-center justify-between mb-6">
          <motion.button
            onClick={() => window.history.back()}
            className="bg-black/20 backdrop-blur-md p-3 rounded-xl border border-white/10"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <ArrowLeft className="w-6 h-6 text-white" />
          </motion.button>

          <div className="text-center">
            <h1 className="text-2xl font-bold text-white">👑 Premium</h1>
            <p className="text-white/70 text-sm">Desbloqueie todo potencial</p>
          </div>

          <div className="w-12"></div>
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 px-4 pb-20">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-6"
        >
          {/* Hero Section */}
          <motion.div variants={itemVariants}>
            <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-yellow-400/20 shadow-2xl text-center">
              <motion.div
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}
                className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <Crown className="w-8 h-8 text-white" />
              </motion.div>

              <h1 className="text-2xl font-bold mb-3 text-white">
                👑 Seja Premium!
              </h1>
              <p className="text-white/70 mb-4 text-sm">
                Ganhe até 3x mais MobiCoins e desbloqueie benefícios exclusivos
              </p>

              <div className="grid grid-cols-2 gap-2">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <Infinity className="w-6 h-6 mx-auto mb-1 text-yellow-400" />
                  <div className="text-xs font-medium text-white">Até 1.000</div>
                  <div className="text-xs text-white/60">anúncios/dia</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <TrendingUp className="w-6 h-6 mx-auto mb-1 text-green-400" />
                  <div className="text-xs font-medium text-white">3x Ganhos</div>
                  <div className="text-xs text-white/60">multiplicador</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <Zap className="w-6 h-6 mx-auto mb-1 text-blue-400" />
                  <div className="text-xs font-medium text-white">Prioritário</div>
                  <div className="text-xs text-white/60">anúncios</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <Gift className="w-6 h-6 mx-auto mb-1 text-purple-400" />
                  <div className="text-xs font-medium text-white">Exclusivo</div>
                  <div className="text-xs text-white/60">ofertas</div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Comparação Gratuito vs Premium */}
          <motion.div variants={itemVariants}>
            <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
              <h2 className="text-xl font-bold text-white mb-4 text-center">
                Gratuito vs Premium
              </h2>
            
              <div className="space-y-3">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <div className="flex justify-between items-center">
                    <span className="text-white text-sm font-medium">Anúncios/dia</span>
                    <div className="flex gap-2 text-xs">
                      <span className="text-white/70">20</span>
                      <span className="text-yellow-400 font-bold">1.000</span>
                      <span className="text-orange-400 font-bold">2.000</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <div className="flex justify-between items-center">
                    <span className="text-white text-sm font-medium">Multiplicador</span>
                    <div className="flex gap-2 text-xs">
                      <span className="text-white/70">1x</span>
                      <span className="text-yellow-400 font-bold">2x</span>
                      <span className="text-orange-400 font-bold">3x</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <div className="flex justify-between items-center">
                    <span className="text-white text-sm font-medium">Anúncios prioritários</span>
                    <div className="flex gap-2 text-xs">
                      <span className="text-red-400">✗</span>
                      <span className="text-green-400">✓</span>
                      <span className="text-green-400">✓</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <div className="flex justify-between items-center">
                    <span className="text-white text-sm font-medium">Suporte prioritário</span>
                    <div className="flex gap-2 text-xs">
                      <span className="text-red-400">✗</span>
                      <span className="text-green-400">✓</span>
                      <span className="text-green-400">✓ VIP</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Calculadora de Ganhos */}
          <motion.div variants={itemVariants}>
            <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-green-400/20 shadow-2xl">
              <h2 className="text-xl font-bold mb-4 text-center text-white">
                💰 Potencial de Ganhos Mensais
              </h2>
            
              <div className="space-y-3">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                  <h3 className="text-sm font-bold mb-2 text-white">Gratuito</h3>
                  <div className="space-y-1">
                    <div className="text-xs text-white/70">20 anúncios/dia × 30 dias</div>
                    <div className="text-lg font-bold text-white">R$ 15,00</div>
                    <div className="text-xs text-white/60">por mês</div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-xl p-4 text-center border border-yellow-400/30">
                  <h3 className="text-sm font-bold mb-2 text-white flex items-center justify-center gap-1">
                    <Crown className="w-4 h-4 text-yellow-400" />
                    Premium
                  </h3>
                  <div className="space-y-1">
                    <div className="text-xs text-white/70">1.000 anúncios/dia × 30 dias</div>
                    <div className="text-xl font-bold text-yellow-400">R$ 1.500,00</div>
                    <div className="text-xs text-white/60">por mês</div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-orange-500/20 to-red-500/20 backdrop-blur-sm rounded-xl p-4 text-center border border-orange-400/30">
                  <h3 className="text-sm font-bold mb-2 text-white flex items-center justify-center gap-1">
                    <Star className="w-4 h-4 text-orange-400" />
                    Premium Plus
                  </h3>
                  <div className="space-y-1">
                    <div className="text-xs text-white/70">2.000 anúncios/dia × 30 dias</div>
                    <div className="text-xl font-bold text-orange-400">R$ 4.500,00</div>
                    <div className="text-xs text-white/60">por mês</div>
                  </div>
                </div>
              </div>

              <div className="text-center mt-4">
                <p className="text-white/60 text-xs">
                  * Valores estimados baseados na média de recompensas por anúncio
                </p>
              </div>
            </div>
          </motion.div>

          {/* Depoimentos */}
          <motion.div variants={itemVariants}>
            <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
              <h2 className="text-xl font-bold text-white mb-4 text-center">
                ⭐ O que nossos usuários dizem
              </h2>

              <div className="space-y-4">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                      M
                    </div>
                    <div className="ml-3">
                      <div className="font-bold text-white text-sm">Maria Silva</div>
                      <div className="text-xs text-white/60">Premium há 6 meses</div>
                    </div>
                  </div>
                  <p className="text-white/80 text-xs italic">
                    "Com o Premium, consegui ganhar mais de R$ 800 no primeiro mês!"
                  </p>
                  <div className="flex text-yellow-400 mt-2">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                  </div>
                </div>

                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                      J
                    </div>
                    <div className="ml-3">
                      <div className="font-bold text-white text-sm">João Santos</div>
                      <div className="text-xs text-white/60">Premium Plus há 3 meses</div>
                    </div>
                  </div>
                  <p className="text-white/80 text-xs italic">
                    "O Premium Plus vale cada centavo! Renda extra significativa."
                  </p>
                  <div className="flex text-yellow-400 mt-2">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 fill-current" />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Sistema de Assinatura */}
          <motion.div variants={itemVariants}>
            <PremiumSubscriptionSystem />
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}

export default PremiumPage
