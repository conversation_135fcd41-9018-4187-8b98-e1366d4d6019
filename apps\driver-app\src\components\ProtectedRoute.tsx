import React from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContextSimple'
import LoadingFallback from './LoadingFallback'

interface ProtectedRouteProps {
  children: React.ReactNode
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading } = useAuth()

  console.log('🛡️ ProtectedRoute: Estado atual', {
    loading,
    hasUser: !!user,
    userEmail: user?.email
  })

  // Mostrar loading enquanto verifica autenticação
  if (loading) {
    console.log('🛡️ ProtectedRoute: Mostrando loading...')
    return (
      <LoadingFallback
        message="Verificando autenticação..."
        timeout={20000}
        onTimeout={() => {
          console.log('🛡️ ProtectedRoute: Timeout na verificação de autenticação')
          window.location.reload()
        }}
      />
    )
  }

  // Se não há usuário, redirecionar para login
  if (!user) {
    console.log('🛡️ ProtectedRoute: Redirecionando para login (sem usuário)')
    return <Navigate to="/login" replace />
  }

  // Se há usuário, renderizar o conteúdo protegido
  console.log('🛡️ ProtectedRoute: Renderizando conteúdo protegido')
  return <>{children}</>
}

export default ProtectedRoute
