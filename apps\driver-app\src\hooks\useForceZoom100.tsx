import { useEffect } from 'react'

export const useForceZoom100 = (): void => {
  useEffect(() => {
    // Força zoom 100% no navegador
    const forceZoom = () => {
      if (document.body.style.zoom !== '1') {
        document.body.style.zoom = '1'
      }
    }

    forceZoom()
    
    // Monitora mudanças de zoom
    const interval = setInterval(forceZoom, 100)

    return () => clearInterval(interval)
  }, [])
}
