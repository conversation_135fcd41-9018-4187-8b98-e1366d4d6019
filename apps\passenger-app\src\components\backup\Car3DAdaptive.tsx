// 📦 BACKUP - COMPONENTE ADAPTATIVO MOBILE/DESKTOP
// Este arquivo foi movido para backup pois o projeto agora é mobile-only
// Para restaurar: copie de volta para src/components/

import React, { Suspense, useEffect } from 'react'
import { useDeviceDetection, getOptimalCarComponent, logDeviceInfo } from '../utils/deviceDetector'
import { Car3DMobile } from './mobile/Car3DMobile'
import { Car3DDesktop } from './desktop/Car3DDesktop'

// 🔄 COMPONENTE ADAPTATIVO (BACKUP)
// Detecta automaticamente o dispositivo e carrega a versão otimizada
// - Mobile: Componente leve e otimizado
// - Desktop: Componente de alta qualidade

interface Car3DAdaptiveProps {
  className?: string
  forceMode?: 'mobile' | 'desktop' | 'auto' // Permite forçar um modo específico
}

// Loader universal
const UniversalLoader: React.FC = () => {
  return (
    <div className="flex items-center justify-center w-full h-full">
      <div className="text-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        <div className="text-white text-sm">
          🚗 Carregando modelo 3D...
        </div>
      </div>
    </div>
  )
}

export const Car3DAdaptive: React.FC<Car3DAdaptiveProps> = ({ 
  className = "", 
  forceMode = 'auto' 
}) => {
  const deviceInfo = useDeviceDetection()

  // Log das informações do dispositivo
  useEffect(() => {
    logDeviceInfo(deviceInfo)
  }, [deviceInfo])

  // Determinar qual componente usar
  const getComponentToRender = () => {
    if (forceMode !== 'auto') {
      return forceMode
    }
    return getOptimalCarComponent(deviceInfo)
  }

  const componentMode = getComponentToRender()

  // Renderizar o componente apropriado
  const renderCarComponent = () => {
    switch (componentMode) {
      case 'mobile':
        console.log('📱 Carregando versão MOBILE otimizada')
        return <Car3DMobile className={className} />
      
      case 'desktop':
        console.log('🖥️ Carregando versão DESKTOP de alta qualidade')
        return <Car3DDesktop className={className} />
      
      default:
        console.log('🖥️ Fallback para versão DESKTOP')
        return <Car3DDesktop className={className} />
    }
  }

  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* Indicador de modo no canto superior esquerdo */}
      <div className="absolute top-4 left-4 bg-black/70 text-white px-3 py-2 rounded-lg text-xs z-10">
        <div className="flex items-center space-x-2">
          <span>
            {componentMode === 'mobile' ? '📱' : '🖥️'}
          </span>
          <span>
            {componentMode === 'mobile' ? 'MOBILE' : 'DESKTOP'}
          </span>
          {forceMode !== 'auto' && (
            <span className="text-yellow-400">(FORÇADO)</span>
          )}
        </div>
        <div className="text-xs text-gray-300 mt-1">
          {deviceInfo.screenWidth}x{deviceInfo.screenHeight}
        </div>
      </div>

      {/* Componente 3D com Suspense */}
      <Suspense fallback={<UniversalLoader />}>
        {renderCarComponent()}
      </Suspense>

      {/* Informações de debug (apenas em desenvolvimento) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-4 left-4 bg-black/70 text-white p-2 rounded text-xs max-w-xs">
          <div className="text-yellow-400 mb-1">DEBUG INFO:</div>
          <div>Modo: {componentMode}</div>
          <div>Touch: {deviceInfo.touchSupport ? 'Sim' : 'Não'}</div>
          <div>DPR: {deviceInfo.devicePixelRatio}</div>
          <div>Platform: {deviceInfo.platform}</div>
        </div>
      )}
    </div>
  )
}

// Componente com controles para testar diferentes modos
export const Car3DAdaptiveWithControls: React.FC<Car3DAdaptiveProps> = ({ 
  className = "" 
}) => {
  const [forceMode, setForceMode] = React.useState<'mobile' | 'desktop' | 'auto'>('auto')
  const deviceInfo = useDeviceDetection()

  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* Controles de teste */}
      <div className="absolute top-4 right-4 bg-black/70 text-white p-3 rounded-lg text-sm space-y-2 z-10">
        <div className="text-xs text-gray-300 mb-2">🔧 TESTE DE MODOS</div>
        
        <button
          onClick={() => setForceMode('auto')}
          className={`block w-full px-3 py-1 rounded transition-colors text-xs ${
            forceMode === 'auto' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-600 hover:bg-gray-500'
          }`}
        >
          🔄 Auto
        </button>
        
        <button
          onClick={() => setForceMode('mobile')}
          className={`block w-full px-3 py-1 rounded transition-colors text-xs ${
            forceMode === 'mobile' 
              ? 'bg-green-600 text-white' 
              : 'bg-gray-600 hover:bg-gray-500'
          }`}
        >
          📱 Mobile
        </button>
        
        <button
          onClick={() => setForceMode('desktop')}
          className={`block w-full px-3 py-1 rounded transition-colors text-xs ${
            forceMode === 'desktop' 
              ? 'bg-purple-600 text-white' 
              : 'bg-gray-600 hover:bg-gray-500'
          }`}
        >
          🖥️ Desktop
        </button>

        <div className="text-xs text-gray-300 mt-2 pt-2 border-t border-gray-600">
          Detectado: {deviceInfo.isMobile ? 'Mobile' : deviceInfo.isTablet ? 'Tablet' : 'Desktop'}
        </div>
      </div>

      {/* Componente adaptativo */}
      <Car3DAdaptive className={className} forceMode={forceMode} />
    </div>
  )
}

export default Car3DAdaptive
