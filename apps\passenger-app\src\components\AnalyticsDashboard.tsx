import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  BarChart3, 
  Clock, 
  Search, 
  Route, 
  TrendingUp, 
  Zap,
  Database,
  Activity,
  MapPin,
  RefreshCw
} from 'lucide-react'
import { mapboxAnalyticsService } from '../services/MapboxAnalyticsService'
import { mapboxCacheService } from '../services/MapboxCacheService'

interface MetricCardProps {
  title: string
  value: string | number
  subtitle?: string
  icon: React.ReactNode
  color: string
  trend?: {
    value: number
    isPositive: boolean
  }
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, subtitle, icon, color, trend }) => (
  <motion.div
    className="bg-white rounded-xl p-6 shadow-lg border border-gray-100"
    whileHover={{ scale: 1.02 }}
    transition={{ duration: 0.2 }}
  >
    <div className="flex items-center justify-between mb-4">
      <div className={`p-3 rounded-lg ${color}`}>
        {icon}
      </div>
      {trend && (
        <div className={`flex items-center space-x-1 text-sm ${
          trend.isPositive ? 'text-green-600' : 'text-red-600'
        }`}>
          <TrendingUp className={`w-4 h-4 ${trend.isPositive ? '' : 'rotate-180'}`} />
          <span>{Math.abs(trend.value)}%</span>
        </div>
      )}
    </div>
    <div>
      <h3 className="text-2xl font-bold text-gray-900 mb-1">{value}</h3>
      <p className="text-gray-600 text-sm font-medium">{title}</p>
      {subtitle && <p className="text-gray-500 text-xs mt-1">{subtitle}</p>}
    </div>
  </motion.div>
)

export const AnalyticsDashboard: React.FC = () => {
  const [analytics, setAnalytics] = useState<any>(null)
  const [cacheStats, setCacheStats] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  const loadAnalytics = () => {
    setIsLoading(true)
    
    try {
      const performanceSummary = mapboxAnalyticsService.getPerformanceSummary()
      const searchMetrics = mapboxAnalyticsService.getSearchMetrics()
      const routeMetrics = mapboxAnalyticsService.getRouteMetrics()
      const cacheData = mapboxCacheService.getStats()
      
      setAnalytics({
        performance: performanceSummary,
        search: searchMetrics,
        route: routeMetrics
      })
      
      setCacheStats(cacheData)
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadAnalytics()
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadAnalytics, 30000)
    return () => clearInterval(interval)
  }, [])

  if (isLoading || !analytics) {
    return (
      <div className="bg-white rounded-xl p-8 shadow-lg">
        <div className="flex items-center justify-center">
          <RefreshCw className="w-6 h-6 text-blue-500 animate-spin mr-2" />
          <span className="text-gray-600">Carregando analytics...</span>
        </div>
      </div>
    )
  }

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
          <p className="text-gray-600">Métricas de performance do Mapbox</p>
        </div>
        <motion.button
          onClick={loadAnalytics}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <RefreshCw className="w-4 h-4" />
          <span>Atualizar</span>
        </motion.button>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Latência Média de Busca"
          value={formatDuration(analytics.performance.searchPerformance.averageLatency)}
          subtitle={`${analytics.performance.searchPerformance.totalRequests} buscas`}
          icon={<Search className="w-6 h-6 text-white" />}
          color="bg-blue-500"
        />
        
        <MetricCard
          title="Latência Média de Rotas"
          value={formatDuration(analytics.performance.routePerformance.averageLatency)}
          subtitle={`${analytics.performance.routePerformance.totalRequests} rotas`}
          icon={<Route className="w-6 h-6 text-white" />}
          color="bg-green-500"
        />
        
        <MetricCard
          title="Taxa de Cache Hit"
          value={`${analytics.performance.cacheHitRate.toFixed(1)}%`}
          subtitle="Economia de API calls"
          icon={<Zap className="w-6 h-6 text-white" />}
          color="bg-yellow-500"
        />
        
        <MetricCard
          title="Taxa de Erro"
          value={`${analytics.performance.errorRate.toFixed(1)}%`}
          subtitle="Confiabilidade do sistema"
          icon={<Activity className="w-6 h-6 text-white" />}
          color="bg-red-500"
        />
      </div>

      {/* Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Search Statistics */}
        <motion.div
          className="bg-white rounded-xl p-6 shadow-lg border border-gray-100"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Search className="w-5 h-5 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Estatísticas de Busca</h3>
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total de Buscas</span>
              <span className="font-semibold">{analytics.search.totalSearches}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Buscas Bem-sucedidas</span>
              <span className="font-semibold text-green-600">{analytics.search.successfulSearches}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Buscas Falharam</span>
              <span className="font-semibold text-red-600">{analytics.search.failedSearches}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Média de Resultados</span>
              <span className="font-semibold">{analytics.search.averageResultsCount.toFixed(1)}</span>
            </div>
          </div>
        </motion.div>

        {/* Route Statistics */}
        <motion.div
          className="bg-white rounded-xl p-6 shadow-lg border border-gray-100"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-green-100 rounded-lg">
              <Route className="w-5 h-5 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Estatísticas de Rotas</h3>
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total de Rotas</span>
              <span className="font-semibold">{analytics.route.totalRoutes}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Rotas Bem-sucedidas</span>
              <span className="font-semibold text-green-600">{analytics.route.successfulRoutes}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Distância Média</span>
              <span className="font-semibold">{(analytics.route.averageDistance / 1000).toFixed(1)} km</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Duração Média</span>
              <span className="font-semibold">{Math.round(analytics.route.averageDuration / 60)} min</span>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Cache Statistics */}
      <motion.div
        className="bg-white rounded-xl p-6 shadow-lg border border-gray-100"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-2 bg-purple-100 rounded-lg">
            <Database className="w-5 h-5 text-purple-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">Estatísticas de Cache</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Cache de Busca</h4>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Itens em Cache</span>
                <span className="font-semibold">{cacheStats.searchCache.size}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Máximo de Itens</span>
                <span className="font-semibold">{cacheStats.searchCache.maxItems}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">TTL</span>
                <span className="font-semibold">{formatTime(cacheStats.searchCache.ttl)}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Cache de Rotas</h4>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Itens em Cache</span>
                <span className="font-semibold">{cacheStats.routeCache.size}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Máximo de Itens</span>
                <span className="font-semibold">{cacheStats.routeCache.maxItems}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">TTL</span>
                <span className="font-semibold">{formatTime(cacheStats.routeCache.ttl)}</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Session Info */}
      <motion.div
        className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-100"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <div className="flex items-center space-x-3 mb-3">
          <Clock className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Sessão Atual</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-sm text-gray-600">Duração da Sessão</p>
            <p className="font-semibold text-gray-900">{formatTime(analytics.performance.sessionDuration)}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Total de API Calls</p>
            <p className="font-semibold text-gray-900">{analytics.performance.totalApiCalls}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Economia de Cache</p>
            <p className="font-semibold text-green-600">
              {Math.round((analytics.performance.cacheHitRate / 100) * analytics.performance.totalApiCalls)} calls
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default AnalyticsDashboard
