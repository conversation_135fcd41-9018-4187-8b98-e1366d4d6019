import React, { useRef, useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import mapboxgl from 'mapbox-gl'
import { Navigation, MapPin, Target } from 'lucide-react'
import 'mapbox-gl/dist/mapbox-gl.css'

// Set Mapbox access token with fallback
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'
mapboxgl.accessToken = MAPBOX_TOKEN

interface RouteMapComponentProps {
  origin?: any
  destination?: any
  route?: any
  className?: string
  height?: string
}

export const RouteMapComponent: React.FC<RouteMapComponentProps> = ({
  origin,
  destination,
  route,
  className = "",
  height = "300px"
}) => {
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [isInitializing, setIsInitializing] = useState(true)

  useEffect(() => {
    if (!mapContainer.current || map.current) return

    // Initialize map
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/streets-v12',
      center: origin?.center || [-46.6333, -23.5505],
      zoom: 12,
      attributionControl: false,
      interactive: true
    })

    map.current.on('load', () => {
      setIsLoaded(true)
      setIsInitializing(false)

      // Add route source
      map.current!.addSource('route', {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: []
          }
        }
      })

      // Add route layer
      map.current!.addLayer({
        id: 'route',
        type: 'line',
        source: 'route',
        layout: {
          'line-join': 'round',
          'line-cap': 'round'
        },
        paint: {
          'line-color': '#3b82f6',
          'line-width': 4,
          'line-opacity': 0.8
        }
      })
    })

    return () => {
      if (map.current) {
        map.current.remove()
        map.current = null
      }
    }
  }, [origin])

  // Update markers and route when data changes
  useEffect(() => {
    if (!map.current || !isLoaded) return

    // Clear existing markers
    const existingMarkers = document.querySelectorAll('.custom-marker')
    existingMarkers.forEach(marker => marker.remove())

    // Add origin marker
    if (origin) {
      const originEl = document.createElement('div')
      originEl.className = 'custom-marker'
      originEl.innerHTML = `
        <div style="
          width: 32px;
          height: 32px;
          background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
          border: 3px solid white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 12px rgba(34, 197, 94, 0.4);
          font-size: 14px;
        ">🏠</div>
      `

      new mapboxgl.Marker(originEl)
        .setLngLat(origin.center)
        .addTo(map.current!)
    }

    // Add destination marker
    if (destination) {
      const destEl = document.createElement('div')
      destEl.className = 'custom-marker'
      destEl.innerHTML = `
        <div style="
          width: 32px;
          height: 32px;
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
          border: 3px solid white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
          font-size: 14px;
        ">🎯</div>
      `

      new mapboxgl.Marker(destEl)
        .setLngLat(destination.center)
        .addTo(map.current!)
    }

    // Update route
    if (route?.geometry) {
      const source = map.current!.getSource('route') as mapboxgl.GeoJSONSource
      if (source) {
        source.setData({
          type: 'Feature',
          properties: {},
          geometry: route.geometry
        })
      }

      // Fit bounds to show entire route
      if (origin && destination) {
        const bounds = new mapboxgl.LngLatBounds()
        bounds.extend(origin.center)
        bounds.extend(destination.center)

        map.current!.fitBounds(bounds, {
          padding: { top: 50, bottom: 50, left: 50, right: 50 },
          maxZoom: 15
        })
      }
    }
  }, [origin, destination, route, isLoaded])

  return (
    <div className={`relative ${className}`} style={{ height }}>
      {/* Map Container */}
      <div
        ref={mapContainer}
        className="w-full h-full rounded-2xl overflow-hidden shadow-lg"
      />

      {/* Loading Overlay */}
      {isInitializing && (
        <div className="absolute inset-0 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center z-10">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Navigation className="w-8 h-8 text-blue-500 mx-auto mb-3" />
            </motion.div>
            <p className="text-gray-700 font-medium">Carregando rota...</p>
          </motion.div>
        </div>
      )}

      {/* Route Info Overlay */}
      {origin && destination && !isInitializing && (
        <div className="absolute top-4 left-4 right-4 z-20">
          <motion.div
            className="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-white/30 p-3"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600 truncate max-w-[120px]">
                  {origin.place_name?.split(',')[0]}
                </span>
              </div>
              <Navigation className="w-4 h-4 text-gray-400" />
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-sm text-gray-600 truncate max-w-[120px]">
                  {destination.place_name?.split(',')[0]}
                </span>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default RouteMapComponent
