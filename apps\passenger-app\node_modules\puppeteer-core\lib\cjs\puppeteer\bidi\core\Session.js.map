{"version": 3, "file": "Session.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Session.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,kEAA0D;AAC1D,4DAIkC;AAClC,4DAAwE;AAExE,6CAAqC;AAGrC;;GAEG;IACU,OAAO;sBACV,8BAAY;;;;;;;;;;iBADT,OACX,SAAQ,WAAoD;;;YAqB5D,mLAAS,UAAU,6BAAV,UAAU,+FAAa;YA8ChC,wKAAQ,OAAO,6DAGd;YAaD,+JAAM,IAAI,6DAKT;YAMD,8KAAM,SAAS,6DAQd;YAMD,0LAAM,aAAa,6DAQlB;YAMD,4JAAM,GAAG,6DAMR;;;QA7HD,MAAM,CAAC,KAAK,CAAC,IAAI,CACf,UAAsB,EACtB,YAA8C;YAE9C,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE;gBACpD,YAAY;aACb,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAChD,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5B,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,GAjBI,mDAAO,CAiBU;QACnB,YAAY,GAAG,IAAI,+BAAe,EAAE,CAAC;QACrC,KAAK,CAAyB;QAC9B,OAAO,CAAW;QAE3B,yFAAgC;QAAhC,IAAS,UAAU,gDAAa;QAAhC,IAAS,UAAU,sDAAa;QAEhC,YAAoB,UAAsB,EAAE,IAA4B;YACtE,KAAK,EAAE,CAAC;;YAER,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;SAC9B;QAED,KAAK,CAAC,WAAW;YACf,qEAAqE;YACpE,IAAY,CAAC,OAAO,GAAG,MAAM,oBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3D,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE;gBACzC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;YAEH,4EAA4E;YAC5E,yEAAyE;YACzE,iEAAiE;YACjE,MAAM,IAAI,GAAG,IAAI,OAAO,EAAE,CAAC;YAC3B,IAAI,CAAC,EAAE,CAAC,mCAAmC,EAAE,IAAI,CAAC,EAAE;gBAClD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;oBACnB,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACf,IAAI,CAAC,IAAI,CAAC,mCAAmC,EAAE,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,IAAI,CAAC,mCAAmC,EAAE,IAAI,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;QACjC,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;QACD,IAAI,KAAK;YACP,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;QACpC,CAAC;QACD,IAAI,EAAE;YACJ,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;QAC9B,CAAC;QAGO,OAAO,CAAC,MAAe;YAC7B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,6BAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAED;;;;;;WAMG;QAKH,KAAK,CAAC,IAAI,CACR,MAAS,EACT,MAA6B;YAE7B,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACpD,CAAC;QAMD,KAAK,CAAC,SAAS,CACb,MAA6B,EAC7B,QAAgC;YAEhC,MAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACnC,MAAM;gBACN,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;QAMD,KAAK,CAAC,aAAa,CACjB,MAA6B,EAC7B,QAAgC;YAEhC,MAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACnC,MAAM;gBACN,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;QAMD,KAAK,CAAC,GAAG;YACP,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;YACrC,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACzC,CAAC;QACH,CAAC;QAEQ,4BA9GR,IAAA,sBAAM,GAAE,0BA8CR,+BAAe,uBAaf,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,4BAQD,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,gCAWD,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,sBAWD,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,GASQ,6BAAa,EAAC;YACtB,IAAI,CAAC,OAAO;gBACV,mEAAmE,CAAC;YACtE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAE3C,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,KAAK,CAAC,6BAAa,CAAC,EAAE,CAAC;QACzB,CAAC;;;AA1IU,0BAAO"}