// =====================================================
// SCRIPT PARA VERIFICAR ESTRUTURA DA TABELA PAYMENT_METHODS
// E ADAPTAR O CÓDIGO PARA FUNCIONAR COM ELA
// =====================================================

import { createClient } from '@supabase/supabase-js'

// Configuração do Supabase
const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function checkTableStructure() {
  console.log('🔍 Verificando estrutura da tabela payment_methods...')

  try {
    // 1. Tentar inserir um registro de teste para ver quais campos são aceitos
    console.log('\n🧪 Testando inserção com campos básicos...')
    
    const testRecord = {
      user_id: '00000000-0000-0000-0000-000000000000', // UUID de teste
      type: 'cash',
      display_name: 'Teste Dinheiro',
      is_default: true
    }

    const { data: insertData, error: insertError } = await supabase
      .from('payment_methods')
      .insert(testRecord)
      .select()

    if (insertError) {
      console.log('❌ Erro na inserção básica:', insertError.message)
      console.log('📋 Detalhes do erro:', insertError)
    } else {
      console.log('✅ Inserção básica funcionou!')
      console.log('📊 Dados inseridos:', insertData)
      
      // Deletar o registro de teste
      if (insertData && insertData[0]) {
        await supabase
          .from('payment_methods')
          .delete()
          .eq('id', insertData[0].id)
        console.log('🗑️ Registro de teste removido')
      }
    }

    // 2. Tentar com campos adicionais
    console.log('\n🧪 Testando inserção com campos adicionais...')
    
    const testRecordExtended = {
      user_id: '00000000-0000-0000-0000-000000000000',
      type: 'cash',
      display_name: 'Teste Dinheiro Extended',
      is_default: true,
      name: 'Dinheiro',
      is_active: true,
      cash_enabled: true,
      cash_change_limit: 50.00,
      cash_notes: 'Teste'
    }

    const { data: insertExtData, error: insertExtError } = await supabase
      .from('payment_methods')
      .insert(testRecordExtended)
      .select()

    if (insertExtError) {
      console.log('❌ Erro na inserção estendida:', insertExtError.message)
      console.log('📋 Campos que causaram erro:', insertExtError)
    } else {
      console.log('✅ Inserção estendida funcionou!')
      console.log('📊 Dados inseridos:', insertExtData)
      
      // Deletar o registro de teste
      if (insertExtData && insertExtData[0]) {
        await supabase
          .from('payment_methods')
          .delete()
          .eq('id', insertExtData[0].id)
        console.log('🗑️ Registro de teste estendido removido')
      }
    }

    // 3. Verificar registros existentes
    console.log('\n📊 Verificando registros existentes...')
    
    const { data: existingData, error: existingError } = await supabase
      .from('payment_methods')
      .select('*')
      .limit(3)

    if (existingError) {
      console.log('❌ Erro ao buscar registros:', existingError.message)
    } else {
      console.log('📋 Registros existentes:')
      if (existingData && existingData.length > 0) {
        console.table(existingData)
        
        // Mostrar estrutura baseada no primeiro registro
        console.log('\n🏗️ Estrutura detectada (campos disponíveis):')
        const fields = Object.keys(existingData[0])
        fields.forEach(field => {
          const value = existingData[0][field]
          const type = typeof value
          console.log(`  - ${field}: ${type} (exemplo: ${value})`)
        })
      } else {
        console.log('📭 Nenhum registro encontrado na tabela')
      }
    }

    // 4. Gerar código adaptado
    console.log('\n🔧 Gerando código adaptado...')
    generateAdaptedCode(existingData)

  } catch (error) {
    console.error('❌ Erro geral:', error)
  }
}

function generateAdaptedCode(sampleData) {
  if (!sampleData || sampleData.length === 0) {
    console.log('⚠️ Sem dados para gerar código adaptado')
    return
  }

  const availableFields = Object.keys(sampleData[0])
  console.log('\n📝 Código adaptado para PaymentMethodService:')
  
  console.log(`
// ✅ CÓDIGO ADAPTADO PARA CAMPOS DISPONÍVEIS
const cashMethod = {
  user_id: userId,
  type: 'cash',${availableFields.includes('display_name') ? '\n  display_name: \'Dinheiro\',' : ''}${availableFields.includes('name') ? '\n  name: \'Dinheiro\',' : ''}${availableFields.includes('is_default') ? '\n  is_default: true,' : ''}${availableFields.includes('is_active') ? '\n  is_active: true,' : ''}${availableFields.includes('cash_enabled') ? '\n  cash_enabled: true,' : ''}${availableFields.includes('cash_change_limit') ? '\n  cash_change_limit: 50.00,' : ''}${availableFields.includes('cash_notes') ? '\n  cash_notes: \'Tenha o valor exato ou próximo.\',' : ''}
}
`)

  console.log('\n🎯 Campos disponíveis na tabela:')
  availableFields.forEach(field => console.log(`  ✅ ${field}`))
  
  console.log('\n❌ Campos que NÃO existem na tabela:')
  const expectedFields = ['name', 'is_active', 'cash_enabled', 'cash_change_limit', 'cash_notes', 'card_holder_name', 'card_expiry_month', 'card_expiry_year', 'pix_key']
  expectedFields.forEach(field => {
    if (!availableFields.includes(field)) {
      console.log(`  ❌ ${field}`)
    }
  })
}

// Executar o script
checkTableStructure()
  .then(() => {
    console.log('\n🏁 Verificação concluída!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Erro fatal:', error)
    process.exit(1)
  })
