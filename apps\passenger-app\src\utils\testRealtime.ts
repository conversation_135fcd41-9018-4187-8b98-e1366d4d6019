import { supabase } from '../lib/supabase'

// 🧪 SCRIPT DE TESTE PARA REALTIME
// Simula um motorista aceitando uma corrida para testar o Realtime

export const testRealtimeRideAcceptance = async (rideId: string) => {
  try {
    console.log('🧪 Testando aceitação de corrida via Realtime...')
    console.log('📋 Ride ID:', rideId)

    // Simular dados de um motorista
    const mockDriver = {
      id: 'test-driver-123',
      name: '<PERSON> (<PERSON>e)',
      phone: '+55 11 99999-9999',
      location: [-46.6333, -23.5505], // São Paulo
      rating: 4.8,
      eta: 5
    }

    // 1. Simular atualização da corrida (como se um motorista aceitasse)
    console.log('🚗 Simulando aceitação do motorista...')
    
    const { error: updateError } = await supabase
      .from('ride_requests')
      .update({
        status: 'accepted',
        driver_id: mockDriver.id,
        driver_name: mockDriver.name,
        driver_phone: mockDriver.phone,
        driver_location: {
          lat: mockDriver.location[1],
          lng: mockDriver.location[0]
        },
        estimated_arrival: mockDriver.eta,
        accepted_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', rideId)

    if (updateError) {
      console.error('❌ Erro ao atualizar corrida:', updateError)
      return false
    }

    console.log('✅ Corrida atualizada com sucesso')

    // 2. Inserir resposta do motorista
    console.log('📝 Inserindo resposta do motorista...')
    
    const { error: responseError } = await supabase
      .from('driver_responses')
      .insert({
        ride_id: rideId,
        driver_id: mockDriver.id,
        driver_name: mockDriver.name,
        driver_phone: mockDriver.phone,
        driver_location: {
          lat: mockDriver.location[1],
          lng: mockDriver.location[0]
        },
        estimated_arrival: mockDriver.eta,
        accepted: true,
        created_at: new Date().toISOString()
      })

    if (responseError) {
      console.error('❌ Erro ao inserir resposta:', responseError)
      return false
    }

    console.log('✅ Resposta do motorista inserida com sucesso')
    console.log('🎉 Teste de Realtime concluído!')
    
    return true

  } catch (error) {
    console.error('❌ Erro no teste de Realtime:', error)
    return false
  }
}

// Função para testar conexão Realtime
export const testRealtimeConnection = async () => {
  try {
    console.log('🔄 Testando conexão Realtime...')
    
    // Verificar se está conectado
    const isConnected = supabase.realtime.isConnected()
    console.log('📡 Status da conexão:', isConnected ? 'Conectado' : 'Desconectado')
    
    if (!isConnected) {
      console.log('🔌 Tentando conectar...')
      await supabase.realtime.connect()
      
      // Aguardar um pouco para a conexão estabelecer
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const newStatus = supabase.realtime.isConnected()
      console.log('📡 Novo status:', newStatus ? 'Conectado' : 'Desconectado')
    }
    
    // Testar canal simples
    console.log('📺 Testando canal de teste...')
    
    const testChannel = supabase
      .channel('test-channel')
      .on('broadcast', { event: 'test' }, (payload) => {
        console.log('📦 Mensagem de teste recebida:', payload)
      })
      .subscribe((status) => {
        console.log('📡 Status da inscrição:', status)
      })
    
    // Enviar mensagem de teste
    setTimeout(() => {
      testChannel.send({
        type: 'broadcast',
        event: 'test',
        payload: { message: 'Teste de Realtime funcionando!' }
      })
    }, 1000)
    
    // Limpar após 5 segundos
    setTimeout(() => {
      supabase.removeChannel(testChannel)
      console.log('🧹 Canal de teste removido')
    }, 5000)
    
    return true
    
  } catch (error) {
    console.error('❌ Erro no teste de conexão:', error)
    return false
  }
}

// Função para simular múltiplas respostas de motoristas
export const simulateMultipleDriverResponses = async (rideId: string) => {
  try {
    console.log('🚗🚗🚗 Simulando múltiplas respostas de motoristas...')
    
    const mockDrivers = [
      {
        id: 'driver-001',
        name: 'Carlos Santos',
        phone: '+55 11 98888-8888',
        location: [-46.6300, -23.5500],
        eta: 3,
        accepted: false
      },
      {
        id: 'driver-002',
        name: 'Maria Oliveira',
        phone: '+55 11 97777-7777',
        location: [-46.6350, -23.5520],
        eta: 4,
        accepted: false
      },
      {
        id: 'driver-003',
        name: 'Pedro Costa',
        phone: '+55 11 96666-6666',
        location: [-46.6280, -23.5480],
        eta: 2,
        accepted: true // Este vai aceitar
      }
    ]
    
    // Inserir respostas com delay
    for (let i = 0; i < mockDrivers.length; i++) {
      const driver = mockDrivers[i]
      
      setTimeout(async () => {
        console.log(`🚗 Inserindo resposta do motorista ${i + 1}:`, driver.name)
        
        const { error } = await supabase
          .from('driver_responses')
          .insert({
            ride_id: rideId,
            driver_id: driver.id,
            driver_name: driver.name,
            driver_phone: driver.phone,
            driver_location: {
              lat: driver.location[1],
              lng: driver.location[0]
            },
            estimated_arrival: driver.eta,
            accepted: driver.accepted,
            created_at: new Date().toISOString()
          })
        
        if (error) {
          console.error(`❌ Erro ao inserir resposta do motorista ${i + 1}:`, error)
        } else {
          console.log(`✅ Resposta do motorista ${i + 1} inserida`)
          
          // Se aceitou, atualizar a corrida
          if (driver.accepted) {
            await supabase
              .from('ride_requests')
              .update({
                status: 'accepted',
                driver_id: driver.id,
                driver_name: driver.name,
                driver_phone: driver.phone,
                driver_location: {
                  lat: driver.location[1],
                  lng: driver.location[0]
                },
                estimated_arrival: driver.eta,
                accepted_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .eq('id', rideId)
            
            console.log(`🎉 Corrida aceita pelo motorista: ${driver.name}`)
          }
        }
      }, i * 2000) // 2 segundos entre cada resposta
    }
    
    return true
    
  } catch (error) {
    console.error('❌ Erro na simulação:', error)
    return false
  }
}

// Expor funções globalmente para teste no console
if (typeof window !== 'undefined') {
  (window as any).testRealtime = {
    testConnection: testRealtimeConnection,
    acceptRide: testRealtimeRideAcceptance,
    simulateMultiple: simulateMultipleDriverResponses
  }
  
  console.log('🧪 Funções de teste disponíveis:')
  console.log('- window.testRealtime.testConnection()')
  console.log('- window.testRealtime.acceptRide(rideId)')
  console.log('- window.testRealtime.simulateMultiple(rideId)')
}
