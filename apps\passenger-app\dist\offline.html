<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sem conexão - MacedoConnect</title>
  <link rel="manifest" href="/manifest.json">
  <meta name="theme-color" content="#0f172a">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background-color: #f8fafc;
      color: #0f172a;
      text-align: center;
      padding: 0 20px;
    }
    
    .container {
      max-width: 500px;
      padding: 40px 20px;
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
      font-size: 24px;
      margin-bottom: 16px;
      color: #0f172a;
    }
    
    p {
      font-size: 16px;
      line-height: 1.5;
      color: #64748b;
      margin-bottom: 24px;
    }
    
    .icon {
      width: 80px;
      height: 80px;
      margin-bottom: 24px;
    }
    
    .button {
      display: inline-block;
      background-color: #0f172a;
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.2s;
    }
    
    .button:hover {
      background-color: #1e293b;
    }
    
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #0f172a;
        color: #f8fafc;
      }
      
      .container {
        background-color: #1e293b;
      }
      
      h1 {
        color: #f8fafc;
      }
      
      p {
        color: #cbd5e1;
      }
      
      .button {
        background-color: #3b82f6;
      }
      
      .button:hover {
        background-color: #2563eb;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <h1>Você está offline</h1>
    <p>Parece que você está sem conexão com a internet. Verifique sua conexão e tente novamente.</p>
    <a href="/" class="button">Tentar novamente</a>
  </div>
  
  <script>
    // Verificar periodicamente se a conexão foi restaurada
    window.addEventListener('online', () => {
      window.location.reload();
    });
    
    // Tentar novamente quando o botão for clicado
    document.querySelector('.button').addEventListener('click', (e) => {
      e.preventDefault();
      window.location.reload();
    });
  </script>
</body>
</html>
