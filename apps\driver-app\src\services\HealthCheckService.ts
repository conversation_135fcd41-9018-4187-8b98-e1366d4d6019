/**
 * Sistema de Health Check Automático para MobiDrive
 * Monitora a saúde do sistema e reporta problemas automaticamente
 */

import { supabase } from '../lib/supabase'
import { analyticsService } from './AnalyticsService'
import { notificationService } from './NotificationService'

interface HealthStatus {
  service: string
  status: 'healthy' | 'degraded' | 'unhealthy'
  latency: number
  lastCheck: Date
  error?: string
}

interface SystemHealth {
  overall: 'healthy' | 'degraded' | 'unhealthy'
  services: HealthStatus[]
  uptime: number
  lastUpdate: Date
}

class HealthCheckService {
  private healthStatus: SystemHealth = {
    overall: 'healthy',
    services: [],
    uptime: Date.now(),
    lastUpdate: new Date()
  }
  
  private checkInterval: NodeJS.Timeout | null = null
  private isRunning = false

  constructor() {
    this.startHealthChecks()
  }

  // Iniciar verificações automáticas
  startHealthChecks(): void {
    if (this.isRunning) return

    this.isRunning = true
    console.log('🏥 Health Check Service iniciado')

    // Verificação inicial
    this.runHealthCheck()

    // Verificações periódicas (a cada 2 minutos)
    this.checkInterval = setInterval(() => {
      this.runHealthCheck()
    }, 2 * 60 * 1000)
  }

  // Parar verificações
  stopHealthChecks(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
    this.isRunning = false
    console.log('🏥 Health Check Service parado')
  }

  // Executar verificação completa
  private async runHealthCheck(): Promise<void> {
    console.log('🔍 Executando health check...')
    
    const services: HealthStatus[] = []

    // Verificar Supabase
    services.push(await this.checkSupabase())

    // Verificar Mapbox
    services.push(await this.checkMapbox())

    // Verificar Geolocalização
    services.push(await this.checkGeolocation())

    // Verificar Local Storage
    services.push(await this.checkLocalStorage())

    // Verificar Network
    services.push(await this.checkNetwork())

    // Verificar Performance
    services.push(await this.checkPerformance())

    // Calcular status geral
    const overall = this.calculateOverallHealth(services)

    // Atualizar status
    this.healthStatus = {
      overall,
      services,
      uptime: Date.now() - this.healthStatus.uptime,
      lastUpdate: new Date()
    }

    // Reportar problemas críticos
    await this.reportCriticalIssues()

    // Log do resultado
    console.log(`🏥 Health Check concluído - Status: ${overall}`)
  }

  // Verificar Supabase
  private async checkSupabase(): Promise<HealthStatus> {
    const startTime = Date.now()
    
    try {
      // Teste de conectividade básica
      const { error } = await supabase
        .from('ride_requests')
        .select('id')
        .limit(1)

      const latency = Date.now() - startTime

      if (error) {
        return {
          service: 'supabase',
          status: 'unhealthy',
          latency,
          lastCheck: new Date(),
          error: error.message
        }
      }

      // Teste de Realtime
      const realtimeStatus = supabase.realtime.isConnected() ? 'healthy' : 'degraded'

      return {
        service: 'supabase',
        status: latency > 3000 ? 'degraded' : realtimeStatus,
        latency,
        lastCheck: new Date()
      }
    } catch (error) {
      return {
        service: 'supabase',
        status: 'unhealthy',
        latency: Date.now() - startTime,
        lastCheck: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Verificar Mapbox
  private async checkMapbox(): Promise<HealthStatus> {
    const startTime = Date.now()
    
    try {
      const token = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN
      if (!token) {
        return {
          service: 'mapbox',
          status: 'unhealthy',
          latency: 0,
          lastCheck: new Date(),
          error: 'Token não configurado'
        }
      }

      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/test.json?access_token=${token}`,
        { signal: AbortSignal.timeout(5000) }
      )

      const latency = Date.now() - startTime

      return {
        service: 'mapbox',
        status: response.ok ? (latency > 2000 ? 'degraded' : 'healthy') : 'unhealthy',
        latency,
        lastCheck: new Date(),
        error: response.ok ? undefined : `HTTP ${response.status}`
      }
    } catch (error) {
      return {
        service: 'mapbox',
        status: 'unhealthy',
        latency: Date.now() - startTime,
        lastCheck: new Date(),
        error: error instanceof Error ? error.message : 'Network error'
      }
    }
  }

  // Verificar Geolocalização
  private async checkGeolocation(): Promise<HealthStatus> {
    const startTime = Date.now()

    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve({
          service: 'geolocation',
          status: 'unhealthy',
          latency: 0,
          lastCheck: new Date(),
          error: 'Geolocation não suportada'
        })
        return
      }

      const timeout = setTimeout(() => {
        resolve({
          service: 'geolocation',
          status: 'degraded',
          latency: Date.now() - startTime,
          lastCheck: new Date(),
          error: 'Timeout'
        })
      }, 10000)

      navigator.geolocation.getCurrentPosition(
        () => {
          clearTimeout(timeout)
          const latency = Date.now() - startTime
          resolve({
            service: 'geolocation',
            status: latency > 5000 ? 'degraded' : 'healthy',
            latency,
            lastCheck: new Date()
          })
        },
        (error) => {
          clearTimeout(timeout)
          resolve({
            service: 'geolocation',
            status: 'unhealthy',
            latency: Date.now() - startTime,
            lastCheck: new Date(),
            error: error.message
          })
        },
        { timeout: 10000, enableHighAccuracy: false }
      )
    })
  }

  // Verificar Local Storage
  private async checkLocalStorage(): Promise<HealthStatus> {
    const startTime = Date.now()

    try {
      const testKey = 'health_check_test'
      const testValue = Date.now().toString()

      localStorage.setItem(testKey, testValue)
      const retrieved = localStorage.getItem(testKey)
      localStorage.removeItem(testKey)

      const latency = Date.now() - startTime

      return {
        service: 'localStorage',
        status: retrieved === testValue ? 'healthy' : 'unhealthy',
        latency,
        lastCheck: new Date(),
        error: retrieved !== testValue ? 'Data integrity error' : undefined
      }
    } catch (error) {
      return {
        service: 'localStorage',
        status: 'unhealthy',
        latency: Date.now() - startTime,
        lastCheck: new Date(),
        error: error instanceof Error ? error.message : 'Storage error'
      }
    }
  }

  // Verificar Network
  private async checkNetwork(): Promise<HealthStatus> {
    const startTime = Date.now()

    try {
      const connection = (navigator as any).connection
      const isOnline = navigator.onLine

      if (!isOnline) {
        return {
          service: 'network',
          status: 'unhealthy',
          latency: 0,
          lastCheck: new Date(),
          error: 'Offline'
        }
      }

      // Teste de conectividade real
      const response = await fetch('https://www.google.com/favicon.ico', {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      })

      const latency = Date.now() - startTime

      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
      
      if (connection) {
        const effectiveType = connection.effectiveType
        if (effectiveType === 'slow-2g' || effectiveType === '2g') {
          status = 'degraded'
        }
      }

      if (latency > 3000) {
        status = 'degraded'
      }

      return {
        service: 'network',
        status: response.ok ? status : 'unhealthy',
        latency,
        lastCheck: new Date(),
        error: response.ok ? undefined : `HTTP ${response.status}`
      }
    } catch (error) {
      return {
        service: 'network',
        status: 'unhealthy',
        latency: Date.now() - startTime,
        lastCheck: new Date(),
        error: error instanceof Error ? error.message : 'Network error'
      }
    }
  }

  // Verificar Performance
  private async checkPerformance(): Promise<HealthStatus> {
    const startTime = Date.now()

    try {
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
      const issues: string[] = []

      // Verificar uso de memória
      if ('memory' in performance) {
        const memory = (performance as any).memory
        const memoryUsage = (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
        
        if (memoryUsage > 90) {
          status = 'unhealthy'
          issues.push('High memory usage')
        } else if (memoryUsage > 70) {
          status = 'degraded'
          issues.push('Elevated memory usage')
        }
      }

      // Verificar FPS (se disponível)
      if ('requestIdleCallback' in window) {
        const fpsStart = performance.now()
        await new Promise(resolve => requestAnimationFrame(resolve))
        const fpsTime = performance.now() - fpsStart
        
        if (fpsTime > 50) { // < 20 FPS
          status = 'degraded'
          issues.push('Low FPS')
        }
      }

      const latency = Date.now() - startTime

      return {
        service: 'performance',
        status,
        latency,
        lastCheck: new Date(),
        error: issues.length > 0 ? issues.join(', ') : undefined
      }
    } catch (error) {
      return {
        service: 'performance',
        status: 'unhealthy',
        latency: Date.now() - startTime,
        lastCheck: new Date(),
        error: error instanceof Error ? error.message : 'Performance check error'
      }
    }
  }

  // Calcular status geral
  private calculateOverallHealth(services: HealthStatus[]): 'healthy' | 'degraded' | 'unhealthy' {
    const unhealthyCount = services.filter(s => s.status === 'unhealthy').length
    const degradedCount = services.filter(s => s.status === 'degraded').length

    if (unhealthyCount > 0) return 'unhealthy'
    if (degradedCount > 1) return 'degraded'
    if (degradedCount > 0) return 'degraded'
    
    return 'healthy'
  }

  // Reportar problemas críticos
  private async reportCriticalIssues(): Promise<void> {
    const criticalServices = this.healthStatus.services.filter(s => s.status === 'unhealthy')
    
    if (criticalServices.length > 0) {
      // Log para analytics
      analyticsService.track('system_health_critical', {
        overall_status: this.healthStatus.overall,
        critical_services: criticalServices.map(s => s.service),
        errors: criticalServices.map(s => s.error).filter(Boolean)
      })

      // Notificar usuário se necessário
      if (criticalServices.some(s => ['supabase', 'network'].includes(s.service))) {
        notificationService.showNotification({
          title: '⚠️ Problema de Conectividade',
          message: 'Alguns recursos podem estar indisponíveis. Verificando...',
          type: 'warning',
          priority: 'normal'
        })
      }
    }
  }

  // Obter status atual
  getHealthStatus(): SystemHealth {
    return { ...this.healthStatus }
  }

  // Verificar se um serviço específico está saudável
  isServiceHealthy(serviceName: string): boolean {
    const service = this.healthStatus.services.find(s => s.service === serviceName)
    return service?.status === 'healthy'
  }

  // Obter latência de um serviço
  getServiceLatency(serviceName: string): number {
    const service = this.healthStatus.services.find(s => s.service === serviceName)
    return service?.latency || 0
  }

  // Forçar verificação manual
  async forceHealthCheck(): Promise<SystemHealth> {
    await this.runHealthCheck()
    return this.getHealthStatus()
  }
}

// Singleton instance
export const healthCheckService = new HealthCheckService()

export default healthCheckService
