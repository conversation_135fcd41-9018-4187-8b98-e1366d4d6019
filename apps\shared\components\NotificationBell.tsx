import React, { useState } from 'react';
import { <PERSON>, <PERSON>Off, Check, Trash2, X } from 'lucide-react';
import { useNotifications } from '../contexts/NotificationContext';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface NotificationBellProps {
  className?: string;
  iconClassName?: string;
  badgeClassName?: string;
  dropdownClassName?: string;
  notificationClassName?: string;
  emptyStateClassName?: string;
  maxHeight?: string;
  showToggleSound?: boolean;
}

/**
 * Componente de Sino de Notificação
 * 
 * Este componente exibe um sino com um contador de notificações não lidas
 * e um dropdown com a lista de notificações.
 */
export const NotificationBell: React.FC<NotificationBellProps> = ({
  className = "relative",
  iconClassName = "h-6 w-6 text-gray-600 dark:text-gray-300 cursor-pointer hover:text-primary",
  badgeClassName = "absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",
  dropdownClassName = "absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 overflow-hidden",
  notificationClassName = "p-3 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",
  emptyStateClassName = "p-4 text-center text-gray-500 dark:text-gray-400",
  maxHeight = "max-h-96",
  showToggleSound = true
}) => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllNotifications,
    soundsEnabled,
    toggleSounds
  } = useNotifications();
  
  const [isOpen, setIsOpen] = useState(false);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleNotificationClick = (id: string) => {
    markAsRead(id);
  };

  const handleDeleteNotification = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    deleteNotification(id);
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  const handleDeleteAll = () => {
    deleteAllNotifications();
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true,
        locale: ptBR
      });
    } catch (error) {
      return dateString;
    }
  };

  // Determinar ícone e cor com base no tipo de notificação
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <div className="h-2 w-2 rounded-full bg-green-500"></div>;
      case 'warning':
        return <div className="h-2 w-2 rounded-full bg-yellow-500"></div>;
      case 'error':
        return <div className="h-2 w-2 rounded-full bg-red-500"></div>;
      case 'ride':
        return <div className="h-2 w-2 rounded-full bg-blue-500"></div>;
      case 'payment':
        return <div className="h-2 w-2 rounded-full bg-purple-500"></div>;
      case 'chat':
        return <div className="h-2 w-2 rounded-full bg-indigo-500"></div>;
      default:
        return <div className="h-2 w-2 rounded-full bg-gray-500"></div>;
    }
  };

  return (
    <div className={className}>
      <div className="relative" onClick={toggleDropdown}>
        <Bell className={iconClassName} />
        {unreadCount > 0 && (
          <span className={badgeClassName}>
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </div>

      {isOpen && (
        <div className={`${dropdownClassName} ${maxHeight} overflow-y-auto`}>
          <div className="flex justify-between items-center p-3 bg-gray-100 dark:bg-gray-700">
            <h3 className="font-medium">Notificações</h3>
            <div className="flex space-x-2">
              {showToggleSound && (
                <button 
                  onClick={toggleSounds}
                  className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                  title={soundsEnabled ? "Desativar sons" : "Ativar sons"}
                >
                  {soundsEnabled ? (
                    <Bell className="h-4 w-4" />
                  ) : (
                    <BellOff className="h-4 w-4" />
                  )}
                </button>
              )}
              {unreadCount > 0 && (
                <button 
                  onClick={handleMarkAllAsRead}
                  className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                  title="Marcar todas como lidas"
                >
                  <Check className="h-4 w-4" />
                </button>
              )}
              {notifications.length > 0 && (
                <button 
                  onClick={handleDeleteAll}
                  className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                  title="Limpar todas"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              )}
              <button 
                onClick={() => setIsOpen(false)}
                className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                title="Fechar"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>

          {notifications.length === 0 ? (
            <div className={emptyStateClassName}>
              Nenhuma notificação
            </div>
          ) : (
            <div>
              {notifications.map((notification) => (
                <div 
                  key={notification.id}
                  className={`${notificationClassName} ${!notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
                  onClick={() => handleNotificationClick(notification.id)}
                >
                  <div className="flex justify-between">
                    <div className="flex items-center space-x-2">
                      {getNotificationIcon(notification.type)}
                      <h4 className="font-medium">{notification.title}</h4>
                    </div>
                    <button 
                      onClick={(e) => handleDeleteNotification(e, notification.id)}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {notification.message}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {formatDate(notification.created_at)}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default NotificationBell;
