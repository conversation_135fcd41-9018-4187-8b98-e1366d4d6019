/**
 * Sistema de cache para consultas do Supabase
 * 
 * Este módulo fornece um sistema de cache para melhorar o desempenho
 * das consultas ao Supabase, reduzindo o número de requisições ao servidor.
 */

// Detectar se estamos no navegador ou no servidor
const isBrowser = typeof window !== 'undefined';

// Configurações padrão
const DEFAULT_CONFIG = {
  enabled: true,
  ttl: 5 * 60 * 1000, // 5 minutos em milissegundos
  maxSize: 100, // Número máximo de itens no cache
  prefix: 'supabase-cache',
  debug: false
};

/**
 * Classe para gerenciar o cache de consultas do Supabase
 */
class SupabaseCache {
  constructor(config = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.cache = new Map();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      evictions: 0
    };
    
    // Inicializar cache do localStorage se estiver no navegador
    if (isBrowser && this.config.enabled) {
      this._initFromStorage();
    }
  }
  
  /**
   * Inicializa o cache a partir do localStorage
   * @private
   */
  _initFromStorage() {
    try {
      const storedCache = localStorage.getItem(`${this.config.prefix}-data`);
      if (storedCache) {
        const parsedCache = JSON.parse(storedCache);
        
        // Verificar se o cache é válido
        if (parsedCache && typeof parsedCache === 'object') {
          // Converter o objeto em Map e filtrar entradas expiradas
          Object.entries(parsedCache).forEach(([key, entry]) => {
            if (entry.expires > Date.now()) {
              this.cache.set(key, entry);
            }
          });
          
          this._log('Cache inicializado a partir do localStorage');
        }
      }
      
      // Carregar estatísticas
      const storedStats = localStorage.getItem(`${this.config.prefix}-stats`);
      if (storedStats) {
        this.stats = { ...this.stats, ...JSON.parse(storedStats) };
      }
    } catch (error) {
      console.error('Erro ao inicializar cache do localStorage:', error);
      // Limpar cache em caso de erro
      this.clear();
    }
  }
  
  /**
   * Salva o cache no localStorage
   * @private
   */
  _saveToStorage() {
    if (!isBrowser || !this.config.enabled) return;
    
    try {
      // Converter Map para objeto para armazenar no localStorage
      const cacheObject = {};
      this.cache.forEach((value, key) => {
        cacheObject[key] = value;
      });
      
      localStorage.setItem(`${this.config.prefix}-data`, JSON.stringify(cacheObject));
      localStorage.setItem(`${this.config.prefix}-stats`, JSON.stringify(this.stats));
      
      this._log('Cache salvo no localStorage');
    } catch (error) {
      console.error('Erro ao salvar cache no localStorage:', error);
    }
  }
  
  /**
   * Registra mensagens de debug se o modo debug estiver ativado
   * @private
   * @param {string} message Mensagem de debug
   * @param {any} data Dados adicionais (opcional)
   */
  _log(message, data) {
    if (this.config.debug) {
      console.log(`[SupabaseCache] ${message}`, data || '');
    }
  }
  
  /**
   * Gera uma chave de cache para uma consulta
   * @param {string} table Nome da tabela
   * @param {Object} query Parâmetros da consulta
   * @returns {string} Chave de cache
   */
  generateKey(table, query = {}) {
    // Criar uma string ordenada dos parâmetros da consulta
    const queryString = Object.entries(query)
      .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
      .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
      .join('&');
      
    return `${table}:${queryString}`;
  }
  
  /**
   * Obtém um item do cache
   * @param {string} key Chave do item
   * @returns {any|null} Valor do item ou null se não encontrado ou expirado
   */
  get(key) {
    const entry = this.cache.get(key);
    
    // Verificar se o item existe e não expirou
    if (entry && entry.expires > Date.now()) {
      this.stats.hits++;
      this._log(`Cache hit: ${key}`, entry.value);
      return entry.value;
    }
    
    // Se o item expirou, removê-lo do cache
    if (entry) {
      this.cache.delete(key);
    }
    
    this.stats.misses++;
    this._log(`Cache miss: ${key}`);
    return null;
  }
  
  /**
   * Adiciona ou atualiza um item no cache
   * @param {string} key Chave do item
   * @param {any} value Valor do item
   * @param {number} ttl Tempo de vida em milissegundos (opcional)
   */
  set(key, value, ttl = this.config.ttl) {
    // Verificar se o cache está ativado
    if (!this.config.enabled) return;
    
    // Verificar se o cache está cheio
    if (this.cache.size >= this.config.maxSize) {
      this._evictOldest();
    }
    
    // Calcular tempo de expiração
    const expires = Date.now() + ttl;
    
    // Adicionar item ao cache
    this.cache.set(key, { value, expires });
    this.stats.sets++;
    
    this._log(`Cache set: ${key}`, { value, expires: new Date(expires) });
    
    // Salvar cache no localStorage
    this._saveToStorage();
    
    return value;
  }
  
  /**
   * Remove o item mais antigo do cache
   * @private
   */
  _evictOldest() {
    // Encontrar o item mais antigo (com menor tempo de expiração)
    let oldestKey = null;
    let oldestExpires = Infinity;
    
    this.cache.forEach((entry, key) => {
      if (entry.expires < oldestExpires) {
        oldestKey = key;
        oldestExpires = entry.expires;
      }
    });
    
    // Remover o item mais antigo
    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.stats.evictions++;
      this._log(`Cache eviction: ${oldestKey}`);
    }
  }
  
  /**
   * Remove um item do cache
   * @param {string} key Chave do item
   */
  remove(key) {
    const removed = this.cache.delete(key);
    
    if (removed) {
      this._log(`Cache remove: ${key}`);
      this._saveToStorage();
    }
    
    return removed;
  }
  
  /**
   * Limpa todo o cache
   */
  clear() {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      evictions: 0
    };
    
    if (isBrowser) {
      localStorage.removeItem(`${this.config.prefix}-data`);
      localStorage.removeItem(`${this.config.prefix}-stats`);
    }
    
    this._log('Cache limpo');
  }
  
  /**
   * Obtém estatísticas do cache
   * @returns {Object} Estatísticas do cache
   */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0
      ? (this.stats.hits / (this.stats.hits + this.stats.misses)) * 100
      : 0;
      
    return {
      ...this.stats,
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate: hitRate.toFixed(2) + '%'
    };
  }
}

// Exportar uma instância única
export default new SupabaseCache();
