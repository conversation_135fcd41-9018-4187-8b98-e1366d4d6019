// =====================================================
// SCRIPT PARA DESCOBRIR CAMPOS DA TABELA PAYMENT_METHODS
// Testa campos um por um para descobrir a estrutura real
// =====================================================

import { createClient } from '@supabase/supabase-js'

// Configuração do Supabase
const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function discoverFields() {
  console.log('🔍 Descobrindo campos da tabela payment_methods...')

  // Lista de campos possíveis para testar
  const possibleFields = [
    'id',
    'user_id', 
    'type',
    'name',
    'display_name',
    'is_default',
    'is_active',
    'created_at',
    'updated_at',
    'card_last_four',
    'card_brand',
    'card_holder_name',
    'card_expiry_month',
    'card_expiry_year',
    'pix_key',
    'cash_enabled',
    'cash_change_limit',
    'cash_notes'
  ]

  const existingFields = []
  const nonExistingFields = []

  console.log('\n🧪 Testando campos um por um...')

  for (const field of possibleFields) {
    try {
      const { data, error } = await supabase
        .from('payment_methods')
        .select(field)
        .limit(1)

      if (error) {
        console.log(`❌ ${field}: ${error.message}`)
        nonExistingFields.push(field)
      } else {
        console.log(`✅ ${field}: OK`)
        existingFields.push(field)
      }
    } catch (error) {
      console.log(`❌ ${field}: ${error.message}`)
      nonExistingFields.push(field)
    }
  }

  console.log('\n📊 RESULTADO DA DESCOBERTA:')
  console.log('\n✅ CAMPOS QUE EXISTEM:')
  existingFields.forEach(field => console.log(`  - ${field}`))
  
  console.log('\n❌ CAMPOS QUE NÃO EXISTEM:')
  nonExistingFields.forEach(field => console.log(`  - ${field}`))

  // Gerar código adaptado
  console.log('\n🔧 CÓDIGO ADAPTADO PARA PaymentMethodService:')
  
  const cashMethodCode = generateCashMethodCode(existingFields)
  console.log(cashMethodCode)

  const addMethodCode = generateAddMethodCode(existingFields)
  console.log(addMethodCode)

  return { existingFields, nonExistingFields }
}

function generateCashMethodCode(fields) {
  let code = `
// ✅ MÉTODO PARA CRIAR CASH METHOD (APENAS CAMPOS EXISTENTES)
const cashMethod = {
  user_id: userId,
  type: 'cash',`

  if (fields.includes('name')) {
    code += `\n  name: 'Dinheiro',`
  }
  if (fields.includes('display_name')) {
    code += `\n  display_name: 'Dinheiro',`
  }
  if (fields.includes('is_default')) {
    code += `\n  is_default: true,`
  }
  if (fields.includes('is_active')) {
    code += `\n  is_active: true,`
  }
  if (fields.includes('cash_enabled')) {
    code += `\n  cash_enabled: true,`
  }
  if (fields.includes('cash_change_limit')) {
    code += `\n  cash_change_limit: 50.00,`
  }
  if (fields.includes('cash_notes')) {
    code += `\n  cash_notes: 'Tenha o valor exato ou próximo.',`
  }

  code += `\n}`

  return code
}

function generateAddMethodCode(fields) {
  let code = `
// ✅ MÉTODO PARA ADICIONAR PAYMENT METHOD (APENAS CAMPOS EXISTENTES)
const newMethod = {
  user_id: userId,
  type: method.type || 'credit_card',`

  if (fields.includes('name')) {
    code += `\n  name: method.name || 'Novo Método',`
  }
  if (fields.includes('display_name')) {
    code += `\n  display_name: method.name || 'Novo Método',`
  }
  if (fields.includes('is_default')) {
    code += `\n  is_default: method.is_default || false,`
  }
  if (fields.includes('is_active')) {
    code += `\n  is_active: true,`
  }
  if (fields.includes('card_last_four')) {
    code += `\n  card_last_four: method.card_last_four,`
  }
  if (fields.includes('card_brand')) {
    code += `\n  card_brand: method.card_brand,`
  }
  if (fields.includes('card_holder_name')) {
    code += `\n  card_holder_name: method.card_holder_name,`
  }
  if (fields.includes('pix_key')) {
    code += `\n  pix_key: method.pix_key,`
  }

  code += `\n}`

  return code
}

// Executar o script
discoverFields()
  .then((result) => {
    console.log('\n🎯 RESUMO:')
    console.log(`✅ Campos existentes: ${result.existingFields.length}`)
    console.log(`❌ Campos não existentes: ${result.nonExistingFields.length}`)
    console.log('\n🏁 Descoberta concluída!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Erro fatal:', error)
    process.exit(1)
  })
