import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  Play,
  DollarSign,
  Clock,
  Eye,
  TrendingUp,
  Target
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContextSimple';
import { GradientBackground } from '../components/GradientBackground';
import { useNoZoom } from '../hooks/useNoZoom';
import { appodealService, AppodealRewardedAd, AppodealUserStats } from '../services/AppodealService';
import { analyticsService } from '../services/AnalyticsService';
import '../styles/no-zoom.css';

export const FreeAdsMobile: React.FC = () => {
  const { user } = useAuth();
  const [userStats, setUserStats] = useState<AppodealUserStats | null>(null);
  const [ads, setAds] = useState<AppodealRewardedAd[]>([]);
  const [loading, setLoading] = useState(true);
  const [watchingAd, setWatchingAd] = useState<AppodealRewardedAd | null>(null);
  const [currentSession, setCurrentSession] = useState<string | null>(null);
  const [watchProgress, setWatchProgress] = useState(0);
  const [appodealReady, setAppodealReady] = useState(false);

  useNoZoom();

  useEffect(() => {
    if (user) {
      loadData();
      analyticsService.track('appodeal_page_view', {
        user_id: user.id,
        platform: 'appodeal'
      });
    }
  }, [user]);

  const loadData = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Inicializar Appodeal se necessário
      if (!appodealReady) {
        const initialized = await appodealService.initialize(user.id);
        setAppodealReady(initialized);
      }

      const [statsData, adsData] = await Promise.all([
        appodealService.getUserStats(user.id),
        appodealService.getAvailableAds(user.id, 20)
      ]);

      setUserStats(statsData);
      setAds(adsData);
    } catch (error) {
      console.error('Erro ao carregar dados do Appodeal:', error);
    } finally {
      setLoading(false);
    }
  };

  const startWatchingAd = async (ad: AppodealRewardedAd) => {
    if (!user || !userStats?.canWatchMore) return;

    try {
      const sessionId = await appodealService.startAdSession(user.id, ad.id);
      setCurrentSession(sessionId);
      setWatchingAd(ad);
      setWatchProgress(0);

      analyticsService.track('appodeal_ad_started', {
        ad_id: ad.id,
        ad_title: ad.title,
        reward_amount: ad.rewardAmount,
        platform: 'appodeal'
      });

      // Simular progresso do anúncio
      simulateAdProgress(ad, sessionId);
    } catch (error) {
      console.error('Erro ao iniciar anúncio Appodeal:', error);
      alert('Erro ao iniciar anúncio. Tente novamente.');
    }
  };

  const simulateAdProgress = (ad: AppodealRewardedAd, sessionId: string) => {
    const duration = ad.duration * 1000; // converter para ms
    const interval = 100; // atualizar a cada 100ms
    const increment = (interval / duration) * 100;

    const progressInterval = setInterval(async () => {
      setWatchProgress(prev => {
        const newProgress = prev + increment;

        if (newProgress >= 100) {
          clearInterval(progressInterval);
          completeAd(sessionId, ad.duration);
          return 100;
        }

        return newProgress;
      });
    }, interval);
  };

  const completeAd = async (sessionId: string, watchedDuration: number) => {
    try {
      const result = await appodealService.completeAdSession(sessionId, watchedDuration);

      if (result.success) {
        alert(`🎉 ${result.message}\nPowered by Appodeal`);

        analyticsService.track('appodeal_ad_completed', {
          session_id: sessionId,
          reward_earned: result.rewardEarned,
          platform: 'appodeal'
        });
      } else {
        alert(`⚠️ ${result.message}`);
      }

      // Recarregar dados
      await loadData();

    } catch (error) {
      console.error('Erro ao completar anúncio Appodeal:', error);
      alert('Erro ao processar recompensa. Tente novamente.');
    } finally {
      setWatchingAd(null);
      setCurrentSession(null);
      setWatchProgress(0);
    }
  };

  const skipAd = () => {
    if (currentSession) {
      // Registrar que o anúncio foi pulado
      analyticsService.track('appodeal_ad_skipped', {
        session_id: currentSession,
        progress: watchProgress,
        platform: 'appodeal'
      });
    }

    setWatchingAd(null);
    setCurrentSession(null);
    setWatchProgress(0);
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'high_revenue':
        return 'from-green-500 to-green-600';
      case 'medium_revenue':
        return 'from-yellow-500 to-yellow-600';
      case 'low_revenue':
        return 'from-blue-500 to-blue-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'high_revenue':
        return '💰 Alta Receita';
      case 'medium_revenue':
        return '💵 Média Receita';
      case 'low_revenue':
        return '💴 Baixa Receita';
      default:
        return '💰 Padrão';
    }
  };

  if (loading) {
    return (
      <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">
        <GradientBackground variant="static" opacity={0.7} />
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 flex flex-col h-full min-h-screen items-center justify-center px-4">
          <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center">
            <motion.div
              className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <DollarSign className="w-8 h-8 text-white" />
            </motion.div>
            <h2 className="text-xl font-bold text-white mb-2">Inicializando Appodeal...</h2>
            <p className="text-white/70">Carregando anúncios premium para você ganhar mais</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">
        <GradientBackground variant="static" opacity={0.7} />
        <div className="absolute inset-0 bg-black/20"></div>

        <div className="relative z-10 flex flex-col h-full min-h-screen">
          {/* Header */}
          <motion.div
            className="pt-8 pb-6"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="flex items-center justify-between px-4 mb-4">
              <motion.button
                onClick={() => window.location.href = '/dashboard'}
                className="p-2 text-white/80 hover:text-white transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ArrowLeft className="w-6 h-6" />
              </motion.button>
              
              <div className="text-center">
                <h1 className="text-2xl font-bold text-white">
                  💰 Anúncios Appodeal
                </h1>
                <p className="text-xs text-white/70">Ganhe até R$ 10 por dia • Powered by Appodeal</p>
              </div>

              <div className="w-10 h-10"></div>
            </div>
          </motion.div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto px-4 space-y-6">
            {/* Progresso Diário */}
            {userStats && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
              >
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <Target className="w-5 h-5" />
                  <span>Progresso de Hoje</span>
                </h3>
                
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-3 bg-green-500/20 rounded-xl border border-green-500/30">
                    <div className="text-2xl font-bold text-green-400">
                      {appodealService.formatCurrency(userStats.dailyEarnings)}
                    </div>
                    <div className="text-xs text-green-200">Ganho Hoje</div>
                  </div>
                  <div className="text-center p-3 bg-blue-500/20 rounded-xl border border-blue-500/30">
                    <div className="text-2xl font-bold text-blue-400">{userStats.totalAdsWatched}</div>
                    <div className="text-xs text-blue-200">Anúncios Assistidos</div>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex justify-between text-sm text-white/70 mb-2">
                    <span>Meta Diária</span>
                    <span>R$ 10,00</span>
                  </div>
                  <div className="bg-white/10 rounded-full h-2">
                    <motion.div
                      className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${(userStats.dailyEarnings / 1000) * 100}%` }}
                      transition={{ duration: 1 }}
                    />
                  </div>
                </div>

                {!userStats.canWatchMore && (
                  <div className="text-center p-3 bg-yellow-500/20 rounded-xl border border-yellow-500/30">
                    <p className="text-yellow-200 text-sm">
                      🎉 Meta diária atingida! Volte amanhã para ganhar mais.
                    </p>
                    <p className="text-yellow-300 text-xs mt-1">
                      Powered by Appodeal
                    </p>
                  </div>
                )}
              </motion.div>
            )}

            {/* Estatísticas do Usuário */}
            {userStats && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
              >
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5" />
                  <span>Suas Estatísticas</span>
                </h3>
                
                <div className="grid grid-cols-2 gap-3">
                  <div className="text-center p-3 bg-purple-500/20 rounded-xl border border-purple-500/30">
                    <div className="text-lg font-bold text-purple-400">
                      {appodealService.formatCurrency(userStats.totalEarned)}
                    </div>
                    <div className="text-xs text-purple-200">Total Ganho</div>
                  </div>
                  <div className="text-center p-3 bg-orange-500/20 rounded-xl border border-orange-500/30">
                    <div className="text-lg font-bold text-orange-400">{userStats.totalAdsWatched}</div>
                    <div className="text-xs text-orange-200">Total Anúncios</div>
                  </div>
                  <div className="text-center p-3 bg-pink-500/20 rounded-xl border border-pink-500/30">
                    <div className="text-lg font-bold text-pink-400">{appodealService.formatCurrency(userStats.averageCPM)}</div>
                    <div className="text-xs text-pink-200">CPM Médio</div>
                  </div>
                  <div className="text-center p-3 bg-cyan-500/20 rounded-xl border border-cyan-500/30">
                    <div className="text-lg font-bold text-cyan-400">Appodeal</div>
                    <div className="text-xs text-cyan-200">Plataforma</div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Lista de Anúncios Appodeal */}
            {ads.length > 0 && userStats?.canWatchMore && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
              >
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <Play className="w-5 h-5" />
                  <span>Anúncios Appodeal Disponíveis</span>
                </h3>

                <div className="space-y-3">
                  {ads.map((ad) => (
                    <motion.div
                      key={ad.id}
                      className="bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-colors cursor-pointer"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => startWatchingAd(ad)}
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                          <Play className="w-6 h-6 text-white" />
                        </div>

                        <div className="flex-1">
                          <h4 className="font-semibold text-white text-sm">{ad.title}</h4>
                          <p className="text-white/60 text-xs mt-1">{ad.description}</p>

                          <div className="flex items-center space-x-3 mt-2">
                            <div className={`px-2 py-1 rounded-full bg-gradient-to-r ${getCategoryColor(ad.category)} text-xs text-white`}>
                              {getCategoryLabel(ad.category)}
                            </div>

                            <div className="flex items-center space-x-1 text-green-400 text-xs">
                              <DollarSign className="w-3 h-3" />
                              <span>{appodealService.formatCurrency(ad.rewardAmount)}</span>
                            </div>

                            <div className="flex items-center space-x-1 text-white/60 text-xs">
                              <Clock className="w-3 h-3" />
                              <span>{Math.ceil(ad.duration / 60)}min</span>
                            </div>

                            <div className="flex items-center space-x-1 text-blue-400 text-xs">
                              <span>📱 {ad.advertiser}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {ads.length === 0 && userStats?.canWatchMore && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center"
              >
                <Eye className="w-16 h-16 text-white/50 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Carregando anúncios Appodeal...</h3>
                <p className="text-white/70 text-sm">
                  Aguarde enquanto carregamos os melhores anúncios para você!
                </p>
                <div className="mt-4 text-blue-400 text-xs">
                  Powered by Appodeal
                </div>
              </motion.div>
            )}
          </div>

          {/* Footer */}
          <motion.div
            className="pb-4 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <p className="text-white/50 text-xs">
              © 2024 MobiDrive. Ganhe dinheiro assistindo anúncios.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Modal de Anúncio Appodeal */}
      <AnimatePresence>
        {watchingAd && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-black/80 backdrop-blur-md rounded-2xl p-6 border border-white/10 max-w-sm w-full"
            >
              <div className="text-center mb-4">
                <h3 className="text-lg font-semibold text-white mb-2">{watchingAd.title}</h3>
                <p className="text-white/70 text-sm">{watchingAd.description}</p>
                <div className="text-blue-400 text-xs mt-1">Powered by Appodeal</div>
              </div>

              <div className="mb-4">
                <div className="bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg h-32 flex items-center justify-center mb-4">
                  <Play className="w-12 h-12 text-white" />
                </div>

                <div className="mb-2">
                  <div className="flex justify-between text-sm text-white/70 mb-1">
                    <span>Progresso</span>
                    <span>{Math.round(watchProgress)}%</span>
                  </div>
                  <div className="bg-white/10 rounded-full h-2">
                    <motion.div
                      className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full"
                      style={{ width: `${watchProgress}%` }}
                    />
                  </div>
                </div>

                <div className="text-center text-green-400 text-sm">
                  Recompensa: {appodealService.formatCurrency(watchingAd.rewardAmount)}
                </div>
              </div>

              <motion.button
                onClick={skipAd}
                className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-xl font-semibold"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Pular Anúncio
              </motion.button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default FreeAdsMobile;
