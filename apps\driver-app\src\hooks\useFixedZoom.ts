import { useEffect, useRef } from 'react'

// 🔒 HOOK PARA GARANTIR ZOOM FIXO 100%
// Força zoom 1 em qualquer elemento e seus filhos

export const useFixedZoom = () => {
  const elementRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    // Função para forçar zoom 1 no elemento e filhos
    const forceZoomFixed = () => {
      // Força zoom no elemento principal
      element.style.zoom = '1'
      element.style.transform = 'scale(1)'
      element.style.webkitTransform = 'scale(1)'
      element.style.MozTransform = 'scale(1)'
      element.style.msTransform = 'scale(1)'
      element.style.OTransform = 'scale(1)'

      // Força zoom em todos os filhos
      const allChildren = element.querySelectorAll('*')
      allChildren.forEach((child: Element) => {
        const htmlChild = child as HTMLElement
        htmlChild.style.zoom = '1'
        htmlChild.style.transform = 'scale(1)'
        htmlChild.style.webkitTransform = 'scale(1)'
        htmlChild.style.MozTransform = 'scale(1)'
        htmlChild.style.msTransform = 'scale(1)'
        htmlChild.style.OTransform = 'scale(1)'
      })
    }

    // Força zoom inicial
    forceZoomFixed()

    // Observer para novos elementos
    const mutationObserver = new MutationObserver(() => {
      forceZoomFixed()
    })

    // Observar mudanças
    mutationObserver.observe(element, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    })

    // Força zoom periodicamente com frequência moderada
    const interval = setInterval(forceZoomFixed, 1000) // A cada 1 segundo

    // Cleanup
    return () => {
      mutationObserver.disconnect()
      clearInterval(interval)
    }
  }, [])

  return elementRef
}

// Hook para aplicar zoom fixo APENAS em elementos estruturais do iPhone
export const useFixedZoomOnMockupOnly = () => {
  useEffect(() => {
    const forceZoomOnMockupElements = () => {
      // Seletores específicos para elementos do iPhone, NÃO do conteúdo do app
      const mockupSelectors = [
        '.mobile-mockup-container > div',  // Container do iPhone
        '.mobile-mockup-screen',           // Tela do iPhone
        '.mobile-mockup-container .absolute:not(.mobile-mockup-content):not(.mobile-mockup-content *)', // Elementos do iPhone
        '.mobile-mockup-container .absolute[class*="top-0"]',    // Notch e Status bar
        '.mobile-mockup-container .absolute[class*="bottom-"]',  // Home indicator e badges
        '.mobile-mockup-container .absolute[class*="left-0"]',   // Botões volume
        '.mobile-mockup-container .absolute[class*="right-0"]',  // Botão power
      ]

      mockupSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector)
        elements.forEach((element: Element) => {
          const htmlElement = element as HTMLElement

          // Só aplica se NÃO for conteúdo do app
          if (!htmlElement.closest('.mobile-mockup-content')) {
            htmlElement.style.zoom = '1'

            // Preserva transforms existentes mas força zoom
            const currentTransform = htmlElement.style.transform
            if (currentTransform && currentTransform.includes('translate')) {
              // Mantém translate mas força scale(1)
              htmlElement.style.transform = currentTransform.replace(/scale\([^)]*\)/g, '') + ' scale(1)'
            } else if (!currentTransform || !currentTransform.includes('scale')) {
              htmlElement.style.transform = (currentTransform || '') + ' scale(1)'
            }
          }
        })
      })
    }

    // Força zoom inicial
    forceZoomOnMockupElements()

    // Força zoom periodicamente com frequência moderada
    const interval = setInterval(forceZoomOnMockupElements, 1000) // A cada 1 segundo

    return () => clearInterval(interval)
  }, [])
}

// Hook para prevenir zoom por gestos/teclado
export const usePreventZoom = () => {
  useEffect(() => {
    // Previne zoom por Ctrl + scroll
    const preventWheelZoom = (e: WheelEvent) => {
      if (e.ctrlKey) {
        e.preventDefault()
        return false
      }
    }

    // Previne zoom por Ctrl + +/-/0
    const preventKeyboardZoom = (e: KeyboardEvent) => {
      if (e.ctrlKey && (e.key === '+' || e.key === '-' || e.key === '0' || e.key === '=' || e.key === 'Equal')) {
        e.preventDefault()
        return false
      }
    }

    // Previne zoom por touch (pinch)
    const preventTouchZoom = (e: TouchEvent) => {
      if (e.touches.length > 1) {
        e.preventDefault()
        return false
      }
    }

    // Adicionar listeners
    document.addEventListener('wheel', preventWheelZoom, { passive: false })
    document.addEventListener('keydown', preventKeyboardZoom)
    document.addEventListener('touchstart', preventTouchZoom, { passive: false })
    document.addEventListener('touchmove', preventTouchZoom, { passive: false })

    return () => {
      document.removeEventListener('wheel', preventWheelZoom)
      document.removeEventListener('keydown', preventKeyboardZoom)
      document.removeEventListener('touchstart', preventTouchZoom)
      document.removeEventListener('touchmove', preventTouchZoom)
    }
  }, [])
}

// Hook para forçar zoom em elementos com data-zoom-fixed
export const useFixedZoomOnDataAttribute = () => {
  useEffect(() => {
    const forceZoomOnDataElements = () => {
      const elements = document.querySelectorAll('[data-zoom-fixed="true"]')
      elements.forEach((element: Element) => {
        const htmlElement = element as HTMLElement

        // Força zoom 1
        htmlElement.style.zoom = '1'
        htmlElement.style.transform = htmlElement.style.transform?.includes('translate')
          ? htmlElement.style.transform.replace(/scale\([^)]*\)/g, 'scale(1)')
          : 'scale(1)'
        htmlElement.style.webkitTransform = htmlElement.style.webkitTransform?.includes('translate')
          ? htmlElement.style.webkitTransform.replace(/scale\([^)]*\)/g, 'scale(1)')
          : 'scale(1)'
        htmlElement.style.MozTransform = 'scale(1)'
        htmlElement.style.msTransform = 'scale(1)'
        htmlElement.style.OTransform = 'scale(1)'

        // Força dimensões se especificadas no style
        const computedStyle = window.getComputedStyle(htmlElement)
        if (htmlElement.style.width) {
          htmlElement.style.minWidth = htmlElement.style.width
          htmlElement.style.maxWidth = htmlElement.style.width
        }
        if (htmlElement.style.height) {
          htmlElement.style.minHeight = htmlElement.style.height
          htmlElement.style.maxHeight = htmlElement.style.height
        }
        if (htmlElement.style.fontSize) {
          htmlElement.style.fontSize = htmlElement.style.fontSize
        }
      })
    }

    // Força zoom inicial
    forceZoomOnDataElements()

    // Força zoom com frequência moderada (evita loop infinito)
    const interval = setInterval(forceZoomOnDataElements, 2000) // A cada 2 segundos

    return () => clearInterval(interval)
  }, [])
}

// Hook completo que combina todas as proteções - APENAS PARA MOCKUP
export const useZoomLock = () => {
  const elementRef = useFixedZoom()
  usePreventZoom()
  useFixedZoomOnMockupOnly()  // Apenas elementos do iPhone, não do app

  return elementRef
}

export default useFixedZoom
