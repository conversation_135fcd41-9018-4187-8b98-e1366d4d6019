/**
 * 🎯 APPODEAL SERVICE - PLATAFORMA ÚNICA DE MONETIZAÇÃO
 * Integração com Appodeal para web/PWA e futuras versões mobile
 */

import { supabase } from '../lib/supabase';
import { analyticsService } from './AnalyticsService';

export interface AppodealAdConfig {
  appKey: string;
  testMode: boolean;
  userId?: string;
  environment: 'web' | 'ios' | 'android';
  // Configurações específicas para produção
  productionConfig?: {
    iosAppKey?: string;
    androidAppKey?: string;
    webFallbackEnabled: boolean;
  };
}

export interface AppodealRewardedAd {
  id: string;
  title: string;
  description: string;
  duration: number; // em segundos
  rewardAmount: number; // em centavos
  category: 'high_revenue' | 'medium_revenue' | 'low_revenue';
  advertiser: string;
  isReady: boolean;
  estimatedCPM: number; // CPM estimado em centavos
}

export interface AppodealAdSession {
  sessionId: string;
  adId: string;
  userId: string;
  startTime: string;
  endTime?: string;
  completed: boolean;
  rewardEarned: number;
  actualCPM?: number;
}

export interface AppodealUserStats {
  totalEarned: number;
  totalAdsWatched: number;
  averageCPM: number;
  dailyEarnings: number;
  canWatchMore: boolean;
  nextResetTime: string;
}

export class AppodealService {
  private static instance: AppodealService;
  private isInitialized = false;
  private config: AppodealAdConfig;
  private dailyLimit = 1000; // R$ 10 em centavos
  private maxAdsPerDay = 50;
  
  // Configurações específicas por ambiente
  private webConfig = {
    // Para ambiente web, usaremos simulação até integração real
    simulateAds: true,
    baseRewardAmount: 25, // 25 centavos base
    cpmRange: { min: 15, max: 75 } // CPM entre R$ 0,15 e R$ 0,75
  };

  static getInstance(): AppodealService {
    if (!AppodealService.instance) {
      AppodealService.instance = new AppodealService();
    }
    return AppodealService.instance;
  }

  constructor() {
    this.config = {
      appKey: process.env.REACT_APP_APPODEAL_APP_KEY || 'demo-app-key',
      testMode: process.env.NODE_ENV !== 'production',
      environment: 'web',
      productionConfig: {
        iosAppKey: process.env.REACT_APP_APPODEAL_IOS_KEY,
        androidAppKey: process.env.REACT_APP_APPODEAL_ANDROID_KEY,
        webFallbackEnabled: true
      }
    };
  }

  // Inicializar Appodeal
  async initialize(userId?: string): Promise<boolean> {
    try {
      console.log('🎯 Inicializando Appodeal Service...');
      
      this.config.userId = userId;
      
      if (this.config.environment === 'web') {
        // Para web, inicializar simulação
        await this.initializeWebSimulation();
      } else {
        // Para mobile, usar SDK nativo (futuro)
        await this.initializeNativeSDK();
      }
      
      this.isInitialized = true;
      
      analyticsService.track('appodeal_initialized', {
        user_id: userId,
        environment: this.config.environment,
        test_mode: this.config.testMode
      });
      
      console.log('✅ Appodeal inicializado com sucesso');
      return true;
      
    } catch (error) {
      console.error('❌ Erro ao inicializar Appodeal:', error);
      return false;
    }
  }

  // Inicialização para ambiente web/PWA
  private async initializeWebSimulation(): Promise<void> {
    console.log('🌐 Inicializando simulação web do Appodeal...');
    
    // Simular carregamento de configurações
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Registrar métricas de inicialização
    analyticsService.track('appodeal_web_simulation_started', {
      config: this.webConfig
    });
  }

  // Inicialização para SDK nativo (futuro)
  private async initializeNativeSDK(): Promise<void> {
    console.log('📱 Inicializando Appodeal SDK nativo...');
    
    // TODO: Implementar quando app for publicado nas lojas
    // if (window.Appodeal) {
    //   window.Appodeal.initialize(this.config.appKey, Appodeal.REWARDED_VIDEO);
    // }
  }

  // Obter anúncios disponíveis
  async getAvailableAds(userId: string, limit: number = 10): Promise<AppodealRewardedAd[]> {
    if (!this.isInitialized) {
      await this.initialize(userId);
    }

    try {
      // Verificar se usuário pode assistir mais anúncios
      const userStats = await this.getUserStats(userId);
      
      if (!userStats.canWatchMore) {
        return [];
      }

      if (this.config.environment === 'web') {
        return this.generateWebSimulationAds(limit);
      } else {
        return this.getNativeAds(limit);
      }
      
    } catch (error) {
      console.error('❌ Erro ao obter anúncios:', error);
      return [];
    }
  }

  // Gerar anúncios simulados para web
  private generateWebSimulationAds(limit: number): AppodealRewardedAd[] {
    const adTemplates = [
      {
        title: 'Novo iPhone 15 Pro',
        description: 'Descubra as inovações do iPhone 15 Pro',
        advertiser: 'Apple',
        category: 'high_revenue' as const,
        duration: 30,
        baseCPM: 60
      },
      {
        title: 'Netflix - Séries Exclusivas',
        description: 'Assista às melhores séries originais',
        advertiser: 'Netflix',
        category: 'medium_revenue' as const,
        duration: 20,
        baseCPM: 40
      },
      {
        title: 'Coca-Cola Zero',
        description: 'O sabor que você ama, zero açúcar',
        advertiser: 'Coca-Cola',
        category: 'medium_revenue' as const,
        duration: 15,
        baseCPM: 35
      },
      {
        title: 'Banco Inter Digital',
        description: 'Conta digital gratuita com cartão',
        advertiser: 'Banco Inter',
        category: 'high_revenue' as const,
        duration: 45,
        baseCPM: 70
      },
      {
        title: 'Shopee - Ofertas',
        description: 'Milhões de produtos com frete grátis',
        advertiser: 'Shopee',
        category: 'low_revenue' as const,
        duration: 10,
        baseCPM: 20
      }
    ];

    const ads: AppodealRewardedAd[] = [];
    
    for (let i = 0; i < Math.min(limit, adTemplates.length); i++) {
      const template = adTemplates[i];
      const cpmVariation = (Math.random() - 0.5) * 0.3; // ±30% variação
      const actualCPM = Math.round(template.baseCPM * (1 + cpmVariation));
      
      ads.push({
        id: `appodeal_ad_${Date.now()}_${i}`,
        title: template.title,
        description: template.description,
        duration: template.duration,
        rewardAmount: actualCPM,
        category: template.category,
        advertiser: template.advertiser,
        isReady: true,
        estimatedCPM: actualCPM
      });
    }

    // Ordenar por CPM (maior primeiro) para maximizar receita
    return ads.sort((a, b) => b.estimatedCPM - a.estimatedCPM);
  }

  // Obter anúncios nativos (futuro)
  private async getNativeAds(limit: number): Promise<AppodealRewardedAd[]> {
    // TODO: Implementar quando SDK nativo estiver disponível
    return [];
  }

  // Iniciar sessão de anúncio
  async startAdSession(userId: string, adId: string): Promise<string> {
    try {
      const sessionId = `appodeal_session_${Date.now()}_${Math.random()}`;
      
      // Registrar início da sessão no banco
      const { error } = await supabase
        .from('ad_watch_sessions')
        .insert([{
          id: sessionId,
          user_id: userId,
          ad_id: adId,
          start_time: new Date().toISOString(),
          watched_duration: 0,
          completed: false,
          reward_earned: 0,
          ip_address: await this.getUserIP(),
          user_agent: navigator.userAgent
        }]);

      if (error) throw error;

      analyticsService.track('appodeal_ad_started', {
        user_id: userId,
        ad_id: adId,
        session_id: sessionId,
        platform: 'appodeal'
      });

      return sessionId;
      
    } catch (error) {
      console.error('❌ Erro ao iniciar sessão de anúncio:', error);
      throw error;
    }
  }

  // Completar sessão de anúncio
  async completeAdSession(sessionId: string, watchedDuration: number): Promise<{
    success: boolean;
    rewardEarned: number;
    message: string;
  }> {
    try {
      // Obter dados da sessão
      const { data: session, error: sessionError } = await supabase
        .from('ad_watch_sessions')
        .select(`
          *,
          ad_videos (
            duration,
            reward_value
          )
        `)
        .eq('id', sessionId)
        .single();

      if (sessionError) throw sessionError;

      // Calcular recompensa baseada no tempo assistido
      const minWatchPercentage = 0.8; // 80% mínimo
      const watchPercentage = watchedDuration / (session.ad_videos?.duration || 30);
      
      if (watchPercentage < minWatchPercentage) {
        return {
          success: false,
          rewardEarned: 0,
          message: `Assista pelo menos ${Math.round(minWatchPercentage * 100)}% do anúncio para ganhar a recompensa`
        };
      }

      // Verificar limite diário
      const userStats = await this.getUserStats(session.user_id);
      
      if (userStats.dailyEarnings >= this.dailyLimit) {
        return {
          success: false,
          rewardEarned: 0,
          message: 'Limite diário de R$ 10 atingido'
        };
      }

      // Calcular recompensa final
      let rewardEarned = session.ad_videos?.reward_value || this.webConfig.baseRewardAmount;
      
      // Aplicar bônus por assistir completamente
      if (watchPercentage >= 0.95) {
        rewardEarned = Math.round(rewardEarned * 1.1); // 10% bônus
      }

      // Verificar se não excede limite diário
      if (userStats.dailyEarnings + rewardEarned > this.dailyLimit) {
        rewardEarned = this.dailyLimit - userStats.dailyEarnings;
      }

      // Atualizar sessão
      const { error: updateError } = await supabase
        .from('ad_watch_sessions')
        .update({
          end_time: new Date().toISOString(),
          completed: true,
          reward_earned: rewardEarned,
          watched_duration: watchedDuration
        })
        .eq('id', sessionId);

      if (updateError) throw updateError;

      // Atualizar progresso diário
      await this.updateDailyProgress(session.user_id, rewardEarned);

      analyticsService.track('appodeal_ad_completed', {
        user_id: session.user_id,
        session_id: sessionId,
        reward_earned: rewardEarned,
        watch_percentage: watchPercentage,
        platform: 'appodeal'
      });

      return {
        success: true,
        rewardEarned,
        message: `Parabéns! Você ganhou R$ ${(rewardEarned / 100).toFixed(2)} com Appodeal!`
      };

    } catch (error) {
      console.error('❌ Erro ao completar sessão:', error);
      return {
        success: false,
        rewardEarned: 0,
        message: 'Erro ao processar recompensa'
      };
    }
  }

  // Obter estatísticas do usuário
  async getUserStats(userId: string): Promise<AppodealUserStats> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const { data: progress, error } = await supabase
        .from('user_ad_progress')
        .select('*')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      const dailyEarnings = progress?.total_earned || 0;
      const totalAdsWatched = progress?.videos_watched || 0;
      
      // Calcular estatísticas históricas
      const { data: history } = await supabase
        .from('user_ad_progress')
        .select('total_earned, videos_watched')
        .eq('user_id', userId);

      const totalEarned = history?.reduce((sum, day) => sum + day.total_earned, 0) || 0;
      const totalAds = history?.reduce((sum, day) => sum + day.videos_watched, 0) || 0;
      const averageCPM = totalAds > 0 ? totalEarned / totalAds : 0;

      const canWatchMore = dailyEarnings < this.dailyLimit && totalAdsWatched < this.maxAdsPerDay;
      
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      return {
        totalEarned,
        totalAdsWatched: totalAds,
        averageCPM,
        dailyEarnings,
        canWatchMore,
        nextResetTime: tomorrow.toISOString()
      };

    } catch (error) {
      console.error('❌ Erro ao obter estatísticas:', error);
      return {
        totalEarned: 0,
        totalAdsWatched: 0,
        averageCPM: 0,
        dailyEarnings: 0,
        canWatchMore: true,
        nextResetTime: new Date().toISOString()
      };
    }
  }

  // Atualizar progresso diário
  private async updateDailyProgress(userId: string, rewardEarned: number): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    
    const { data: existing } = await supabase
      .from('user_ad_progress')
      .select('*')
      .eq('user_id', userId)
      .eq('date', today)
      .single();

    if (existing) {
      const newTotal = existing.total_earned + rewardEarned;
      const newCount = existing.videos_watched + 1;
      
      await supabase
        .from('user_ad_progress')
        .update({
          total_earned: newTotal,
          videos_watched: newCount,
          can_watch_more: newTotal < this.dailyLimit && newCount < this.maxAdsPerDay
        })
        .eq('user_id', userId)
        .eq('date', today);
    } else {
      await supabase
        .from('user_ad_progress')
        .insert([{
          user_id: userId,
          date: today,
          total_earned: rewardEarned,
          videos_watched: 1,
          daily_limit: this.dailyLimit,
          can_watch_more: rewardEarned < this.dailyLimit
        }]);
    }
  }

  // Utilitários
  private async getUserIP(): Promise<string> {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return 'unknown';
    }
  }

  // Verificar se Appodeal está pronto
  isReady(): boolean {
    return this.isInitialized;
  }

  // Obter configuração atual
  getConfig(): AppodealAdConfig {
    return { ...this.config };
  }

  // Formatar moeda
  formatCurrency(centavos: number): string {
    return `R$ ${(centavos / 100).toFixed(2)}`;
  }
}

export const appodealService = AppodealService.getInstance();
