import React, { useEffect } from 'react'
import { RegisterMobile } from '../pages/RegisterMobile'
import { Register } from '../pages/Register'
import { DeviceWrapper } from './DeviceWrapper'
import { useDeviceDetection, shouldUseMobileNativeUI, shouldUseDesktopMockup, logDeviceInfo } from '../utils/deviceDetector'

// 🔄 WRAPPER ADAPTATIVO DE REGISTRO
// Escolhe automaticamente entre UI mobile nativa e mockup desktop
// Baseado na detecção real do dispositivo
// MANTÉM DESIGN ORIGINAL + CONVERSÃO ANDROID NATIVA

interface AdaptiveRegisterWrapperProps {
  onRegister?: (data: any) => void
}

export const AdaptiveRegisterWrapper: React.FC<AdaptiveRegisterWrapperProps> = ({ onRegister }) => {
  const deviceInfo = useDeviceDetection()

  // Log informações do dispositivo para debug
  useEffect(() => {
    logDeviceInfo()
  }, [])

  // Decide qual UI usar baseado no dispositivo
  const useMobileNativeUI = shouldUseMobileNativeUI()
  const useDesktopMockup = shouldUseDesktopMockup()

  // Debug removido para console limpo

  // MOBILE NATIVO: Usa React Native Elements diretamente
  if (useMobileNativeUI) {
    return (
      <div style={{ 
        width: '100vw', 
        height: '100vh', 
        overflow: 'hidden',
        backgroundColor: '#f5f5f5'
      }}>
        <RegisterMobile onRegister={onRegister} />
      </div>
    )
  }

  // DESKTOP MOCKUP: Usa o sistema de mockup iPhone
  if (useDesktopMockup) {
    return (
      <DeviceWrapper>
        <Register />
      </DeviceWrapper>
    )
  }

  // FALLBACK: Usa mobile nativo como padrão
  return (
    <div style={{ 
      width: '100vw', 
      height: '100vh', 
      overflow: 'hidden',
      backgroundColor: '#f5f5f5'
    }}>
      <RegisterMobile onRegister={onRegister} />
    </div>
  )
}

export default AdaptiveRegisterWrapper
