import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { useNavigate } from 'react-router-dom';

// Define types
export interface Profile {
  id: string;
  full_name?: string;
  avatar_url?: string;
  phone_number?: string;
  email?: string;
  user_type?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AuthState {
  user: User | null;
  session: Session | null;
  profile: Profile | null;
  isLoading: boolean;
  isOnline: boolean;
  isConnected: boolean;
  error: string | null;
}

export interface AuthContextType extends AuthState {
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signUp: (email: string, password: string, userData?: Partial<Profile>) => Promise<{ data: any; error: any }>;
  signOut: () => Promise<void>;
  updateProfile: (data: Partial<Profile>) => Promise<{ data: any; error: any }>;
  resetPassword: (email: string) => Promise<{ data: any; error: any }>;
  updatePassword: (password: string) => Promise<{ data: any; error: any }>;
  refreshSession: () => Promise<void>;
  checkConnection: () => Promise<{ connected: boolean; error?: any }>;
  syncOfflineOperations: () => Promise<any>;
  isAdmin: () => boolean;
  isDriver: () => boolean;
  isPassenger: () => boolean;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export interface AuthProviderProps {
  children: React.ReactNode;
  supabase: any;
  appType: 'admin' | 'driver' | 'passenger';
  appPort?: string;
  redirects?: {
    afterSignIn?: string;
    afterSignOut?: string;
    accessDenied?: string;
  };
}

/**
 * Shared Authentication Provider
 * 
 * This component provides authentication context for all MobiDrive apps.
 * It handles user authentication, profile management, and session state.
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({
  children,
  supabase,
  appType,
  appPort,
  redirects = {
    afterSignIn: '/dashboard',
    afterSignOut: '/login',
    accessDenied: '/access-denied',
  },
}) => {
  const navigate = useNavigate();
  const [state, setState] = useState<AuthState>({
    user: null,
    session: null,
    profile: null,
    isLoading: true,
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    isConnected: false,
    error: null,
  });

  // Check current session
  useEffect(() => {
    const checkSession = async () => {
      try {
        // Check if browser is online
        if (!navigator.onLine) {
          setState(prev => ({ ...prev, isLoading: false, isOnline: false }));
          return;
        }

        // Get current session
        const { session, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) throw sessionError;

        if (session) {
          // Store information about the app type
          localStorage.setItem('mobidrive-app-type', appType);
          if (appPort) {
            localStorage.setItem('mobidrive-current-port', appPort);
          }

          // Get user if session exists
          const { user, error: userError } = await supabase.auth.getUser();
          if (userError) throw userError;

          // Fetch user profile
          await fetchUserProfile(user);
          
          // Check connection
          if (supabase.checkConnection) {
            const { connected } = await supabase.checkConnection();
            setState(prev => ({ ...prev, isConnected: connected }));
          }
        } else {
          setState({
            user: null,
            session: null,
            profile: null,
            isLoading: false,
            isOnline: navigator.onLine,
            isConnected: false,
            error: null,
          });
        }
      } catch (error: any) {
        setState({
          user: null,
          session: null,
          profile: null,
          isLoading: false,
          isOnline: navigator.onLine,
          isConnected: false,
          error: error.message,
        });
      }
    };

    checkSession();

    // Set up auth state change listener
    const { data: authListener } = supabase.client.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          // Store information about the app type
          localStorage.setItem('mobidrive-app-type', appType);
          if (appPort) {
            localStorage.setItem('mobidrive-current-port', appPort);
          }

          const { user } = await supabase.auth.getUser();
          await fetchUserProfile(user);
          
          // Check connection
          if (supabase.checkConnection) {
            const { connected } = await supabase.checkConnection();
            setState(prev => ({ ...prev, isConnected: connected }));
          }
        } else if (event === 'SIGNED_OUT') {
          setState({
            user: null,
            session: null,
            profile: null,
            isLoading: false,
            isOnline: navigator.onLine,
            isConnected: false,
            error: null,
          });
        }
      }
    );
    
    // Set up online/offline listeners
    const handleOnline = () => {
      setState(prev => ({ ...prev, isOnline: true }));
      if (supabase.checkConnection) {
        supabase.checkConnection().then(({ connected }: { connected: boolean }) => {
          setState(prev => ({ ...prev, isConnected: connected }));
          if (connected && supabase.offline) {
            supabase.syncOfflineOperations();
          }
        });
      }
    };
    
    const handleOffline = () => {
      setState(prev => ({ ...prev, isOnline: false, isConnected: false }));
    };
    
    if (typeof window !== 'undefined') {
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
    }

    return () => {
      authListener.subscription.unsubscribe();
      
      if (typeof window !== 'undefined') {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      }
    };
  }, [navigate, supabase, appType, appPort]);

  // Fetch user profile
  const fetchUserProfile = async (user: User) => {
    try {
      // Check if browser is online
      if (!navigator.onLine) {
        setState(prev => ({
          ...prev,
          user,
          profile: null,
          isLoading: false,
          isOnline: false,
          error: 'No internet connection',
        }));
        return;
      }

      // Fetch user profile
      const { profile, error } = await supabase.auth.getProfile(user.id);

      if (error) throw error;

      // Check if user has the correct user type for this app
      if (profile && profile.user_type !== appType) {
        // Logout if user type doesn't match app type
        await supabase.auth.logout();

        setState({
          user: null,
          session: null,
          profile: null,
          isLoading: false,
          isOnline: navigator.onLine,
          isConnected: false,
          error: `This account is not registered as a ${appType}.`,
        });

        // Redirect to access denied page
        navigate(redirects.accessDenied || '/access-denied');

        // Check if we're on the correct port
        if (appPort && window.location.port !== appPort) {
          const protocol = window.location.protocol;
          const hostname = window.location.hostname;
          window.location.href = `${protocol}//${hostname}:${appPort}${redirects.accessDenied || '/access-denied'}`;
        }

        return;
      }

      setState(prev => ({
        ...prev,
        user,
        session: null, // Will be set by the auth state change listener
        profile,
        isLoading: false,
        error: null,
      }));
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        user,
        profile: null,
        isLoading: false,
        error: error.message,
      }));
    }
  };

  // Login with email and password
  const signIn = async (email: string, password: string) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Clear any existing sessions to avoid conflicts
      try {
        Object.keys(localStorage).forEach(key => {
          if ((key.includes('supabase') || key.includes('sb-')) &&
              !key.includes(appType)) {
            localStorage.removeItem(key);
          }
        });
      } catch (e) {
        console.error('Error clearing existing sessions:', e);
      }

      const { error } = await supabase.auth.login({ email, password });

      if (error) {
        setState(prev => ({ ...prev, isLoading: false, error: error.message }));
        return { data: null, error };
      }

      // Store information about the app type
      localStorage.setItem('mobidrive-app-type', appType);
      if (appPort) {
        localStorage.setItem('mobidrive-current-port', appPort);
      }

      // Get session and user
      const { session } = await supabase.auth.getSession();
      const { user } = await supabase.auth.getUser();

      return { data: { session, user }, error: null };
    } catch (error: any) {
      setState(prev => ({ ...prev, isLoading: false, error: error.message }));
      return { data: null, error };
    }
  };

  // Register a new user
  const signUp = async (email: string, password: string, userData?: Partial<Profile>) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const { error, data } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            ...userData,
            user_type: appType,
          },
        },
      });

      if (error) {
        setState(prev => ({ ...prev, isLoading: false, error: error.message }));
        return { data: null, error };
      }

      // Store information about the app type
      localStorage.setItem('mobidrive-app-type', appType);
      if (appPort) {
        localStorage.setItem('mobidrive-current-port', appPort);
      }

      setState(prev => ({ ...prev, isLoading: false }));
      return { data, error: null };
    } catch (error: any) {
      setState(prev => ({ ...prev, isLoading: false, error: error.message }));
      return { data: null, error };
    }
  };

  // Logout
  const signOut = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));
      
      const { error } = await supabase.auth.logout();
      if (error) throw error;
      
      // Clear app information
      localStorage.removeItem('mobidrive-app-type');
      localStorage.removeItem('mobidrive-current-port');
      
      // Clear all Supabase items to ensure there are no conflicts
      Object.keys(localStorage).forEach(key => {
        if (key.includes('supabase') ||
            key.includes('mobidrive') ||
            key.includes('sb-')) {
          localStorage.removeItem(key);
        }
      });
      
      setState({
        user: null,
        session: null,
        profile: null,
        isLoading: false,
        isOnline: navigator.onLine,
        isConnected: false,
        error: null,
      });

      // Check if we're on the correct port
      if (appPort && window.location.port !== appPort) {
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;
        window.location.href = `${protocol}//${hostname}:${appPort}${redirects.afterSignOut || '/login'}`;
      }
    } catch (error: any) {
      setState(prev => ({ ...prev, isLoading: false, error: error.message }));
    }
  };

  // Update user profile
  const updateProfile = async (data: Partial<Profile>) => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));

      if (!state.user) {
        throw new Error('User not authenticated');
      }

      const { profile, error } = await supabase.auth.updateProfile(state.user.id, data);

      if (error) {
        setState(prev => ({ ...prev, isLoading: false, error: error.message }));
        return { data: null, error };
      }

      setState(prev => ({
        ...prev,
        profile,
        isLoading: false,
        error: null,
      }));

      return { data: profile, error: null };
    } catch (error: any) {
      setState(prev => ({ ...prev, isLoading: false, error: error.message }));
      return { data: null, error };
    }
  };
  
  // Reset password
  const resetPassword = async (email: string) => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));
      
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: window.location.origin + (redirects.afterSignIn || '/dashboard'),
      });
      
      setState(prev => ({ ...prev, isLoading: false, error: error?.message || null }));
      return { data, error };
    } catch (error: any) {
      setState(prev => ({ ...prev, isLoading: false, error: error.message }));
      return { data: null, error };
    }
  };
  
  // Update password
  const updatePassword = async (password: string) => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));
      
      const { data, error } = await supabase.auth.updateUser({
        password,
      });
      
      setState(prev => ({ ...prev, isLoading: false, error: error?.message || null }));
      return { data, error };
    } catch (error: any) {
      setState(prev => ({ ...prev, isLoading: false, error: error.message }));
      return { data: null, error };
    }
  };
  
  // Refresh session
  const refreshSession = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));
      
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) throw error;
      
      if (data.session && data.user) {
        await fetchUserProfile(data.user);
      }
      
      setState(prev => ({ ...prev, isLoading: false }));
    } catch (error: any) {
      setState(prev => ({ ...prev, isLoading: false, error: error.message }));
    }
  };
  
  // Check connection status
  const checkConnection = async () => {
    try {
      if (!supabase.checkConnection) {
        return { connected: navigator.onLine };
      }
      
      const result = await supabase.checkConnection();
      setState(prev => ({ ...prev, isConnected: result.connected }));
      return result;
    } catch (error) {
      setState(prev => ({ ...prev, isConnected: false }));
      return { connected: false, error };
    }
  };
  
  // Sync offline operations
  const syncOfflineOperations = async () => {
    if (!supabase.offline) {
      return { total: 0, succeeded: 0, failed: 0, errors: [] };
    }
    
    return supabase.syncOfflineOperations();
  };
  
  // Check user roles
  const isAdmin = () => state.profile?.user_type === 'admin';
  const isDriver = () => state.profile?.user_type === 'driver';
  const isPassenger = () => state.profile?.user_type === 'passenger';

  return (
    <AuthContext.Provider
      value={{
        ...state,
        signIn,
        signUp,
        signOut,
        updateProfile,
        resetPassword,
        updatePassword,
        refreshSession,
        checkConnection,
        syncOfflineOperations,
        isAdmin,
        isDriver,
        isPassenger,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Hook to use the auth context
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthProvider;
