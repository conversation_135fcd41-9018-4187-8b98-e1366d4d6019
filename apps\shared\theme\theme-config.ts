/**
 * MobiDrive Shared Theme Configuration
 * 
 * This file contains the standardized theme configuration for all MobiDrive apps.
 * It ensures consistent colors, typography, and other design elements across
 * the admin, driver, and passenger applications.
 */

export type ThemeMode = 'light' | 'dark' | 'system';

export interface ThemeColors {
  primary: string;
  primaryLight: string;
  primaryDark: string;
  secondary: string;
  secondaryLight: string;
  secondaryDark: string;
  success: string;
  warning: string;
  danger: string;
  info: string;
  background: string;
  backgroundAlt: string;
  surface: string;
  surfaceAlt: string;
  text: string;
  textLight: string;
  textDark: string;
  border: string;
  borderLight: string;
  borderDark: string;
}

export interface ThemeConfig {
  colors: {
    light: ThemeColors;
    dark: ThemeColors;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    pill: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
  };
  typography: {
    fontFamily: string;
    fontFamilyMono: string;
    fontWeights: {
      light: number;
      normal: number;
      medium: number;
      semibold: number;
      bold: number;
    };
    fontSize: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      xxl: string;
    };
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  transitions: {
    fast: string;
    normal: string;
    slow: string;
  };
  zIndex: {
    base: number;
    dropdown: number;
    sticky: number;
    fixed: number;
    modal: number;
    popover: number;
    tooltip: number;
  };
}

/**
 * MobiDrive Theme Configuration
 * 
 * This is the main theme configuration used across all MobiDrive apps.
 */
export const mobidriveTheme: ThemeConfig = {
  colors: {
    light: {
      primary: '#2563eb', // Blue 600
      primaryLight: '#60a5fa', // Blue 400
      primaryDark: '#1d4ed8', // Blue 700
      secondary: '#4f46e5', // Indigo 600
      secondaryLight: '#818cf8', // Indigo 400
      secondaryDark: '#4338ca', // Indigo 700
      success: '#22c55e', // Green 500
      warning: '#f59e0b', // Amber 500
      danger: '#ef4444', // Red 500
      info: '#3b82f6', // Blue 500
      background: '#f8fafc', // Slate 50
      backgroundAlt: '#f1f5f9', // Slate 100
      surface: '#ffffff', // White
      surfaceAlt: '#f8fafc', // Slate 50
      text: '#0f172a', // Slate 900
      textLight: '#64748b', // Slate 500
      textDark: '#0f172a', // Slate 900
      border: '#e2e8f0', // Slate 200
      borderLight: '#f1f5f9', // Slate 100
      borderDark: '#cbd5e1', // Slate 300
    },
    dark: {
      primary: '#3b82f6', // Blue 500
      primaryLight: '#60a5fa', // Blue 400
      primaryDark: '#2563eb', // Blue 600
      secondary: '#6366f1', // Indigo 500
      secondaryLight: '#818cf8', // Indigo 400
      secondaryDark: '#4f46e5', // Indigo 600
      success: '#22c55e', // Green 500
      warning: '#f59e0b', // Amber 500
      danger: '#ef4444', // Red 500
      info: '#3b82f6', // Blue 500
      background: '#0f172a', // Slate 900
      backgroundAlt: '#1e293b', // Slate 800
      surface: '#1e293b', // Slate 800
      surfaceAlt: '#334155', // Slate 700
      text: '#f8fafc', // Slate 50
      textLight: '#cbd5e1', // Slate 300
      textDark: '#f8fafc', // Slate 50
      border: '#334155', // Slate 700
      borderLight: '#475569', // Slate 600
      borderDark: '#1e293b', // Slate 800
    },
  },
  borderRadius: {
    sm: '0.25rem', // 4px
    md: '0.5rem', // 8px
    lg: '1rem', // 16px
    xl: '1.5rem', // 24px
    pill: '9999px',
  },
  spacing: {
    xs: '0.25rem', // 4px
    sm: '0.5rem', // 8px
    md: '1rem', // 16px
    lg: '1.5rem', // 24px
    xl: '2rem', // 32px
    xxl: '3rem', // 48px
  },
  typography: {
    fontFamily: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontFamilyMono: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
    fontWeights: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    fontSize: {
      xs: '0.75rem', // 12px
      sm: '0.875rem', // 14px
      md: '1rem', // 16px
      lg: '1.125rem', // 18px
      xl: '1.25rem', // 20px
      xxl: '1.5rem', // 24px
    },
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  },
  transitions: {
    fast: '150ms cubic-bezier(0.4, 0, 0.2, 1)',
    normal: '300ms cubic-bezier(0.4, 0, 0.2, 1)',
    slow: '500ms cubic-bezier(0.4, 0, 0.2, 1)',
  },
  zIndex: {
    base: 0,
    dropdown: 1000,
    sticky: 1100,
    fixed: 1200,
    modal: 1300,
    popover: 1400,
    tooltip: 1500,
  },
};

export default mobidriveTheme;
