#!/bin/bash

# =====================================================
# SCRIPT DE DEPLOY PARA MUNDODAINOVACAO.COM
# =====================================================

set -e  # Exit on any error

echo "🚀 Iniciando deploy para mundodainovacao.com..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="mundodainovacao.com"
BUILD_DIR="dist"
REMOTE_USER="root"  # Ajuste conforme necessário
REMOTE_HOST="mundodainovacao.com"
REMOTE_PATH="/var/www/mundodainovacao.com"

echo -e "${BLUE}📋 Configuração do Deploy:${NC}"
echo -e "   Domínio: ${GREEN}$DOMAIN${NC}"
echo -e "   Diretório: ${GREEN}$BUILD_DIR${NC}"
echo -e "   Servidor: ${GREEN}$REMOTE_HOST${NC}"
echo ""

# Step 1: Clean previous build
echo -e "${YELLOW}🧹 Limpando build anterior...${NC}"
if [ -d "$BUILD_DIR" ]; then
    rm -rf "$BUILD_DIR"
    echo -e "${GREEN}✅ Build anterior removido${NC}"
fi

# Step 2: Install dependencies
echo -e "${YELLOW}📦 Instalando dependências...${NC}"
npm install
echo -e "${GREEN}✅ Dependências instaladas${NC}"

# Step 3: Build for production
echo -e "${YELLOW}🔨 Construindo para produção...${NC}"
npm run build:prod
echo -e "${GREEN}✅ Build de produção concluído${NC}"

# Step 4: Verify build
echo -e "${YELLOW}🔍 Verificando build...${NC}"
if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}❌ Erro: Diretório de build não encontrado!${NC}"
    exit 1
fi

if [ ! -f "$BUILD_DIR/index.html" ]; then
    echo -e "${RED}❌ Erro: index.html não encontrado no build!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Build verificado com sucesso${NC}"

# Step 5: Show build info
echo -e "${BLUE}📊 Informações do Build:${NC}"
echo -e "   Tamanho total: ${GREEN}$(du -sh $BUILD_DIR | cut -f1)${NC}"
echo -e "   Arquivos: ${GREEN}$(find $BUILD_DIR -type f | wc -l)${NC}"
echo ""

# Step 6: Create deployment package
echo -e "${YELLOW}📦 Criando pacote de deploy...${NC}"
tar -czf deploy-$(date +%Y%m%d-%H%M%S).tar.gz -C $BUILD_DIR .
echo -e "${GREEN}✅ Pacote criado${NC}"

echo ""
echo -e "${GREEN}🎉 Build concluído com sucesso!${NC}"
echo ""
echo -e "${BLUE}📋 Próximos passos para deploy:${NC}"
echo -e "   1. Faça upload dos arquivos da pasta '${GREEN}$BUILD_DIR${NC}' para o servidor"
echo -e "   2. Configure o Nginx com o arquivo '${GREEN}nginx.conf${NC}'"
echo -e "   3. Configure SSL/HTTPS"
echo -e "   4. Teste o domínio: ${GREEN}https://$DOMAIN${NC}"
echo ""
echo -e "${YELLOW}💡 Comandos úteis:${NC}"
echo -e "   Preview local: ${GREEN}npm run preview${NC}"
echo -e "   Testar build: ${GREEN}cd $BUILD_DIR && python -m http.server 8080${NC}"
echo ""
