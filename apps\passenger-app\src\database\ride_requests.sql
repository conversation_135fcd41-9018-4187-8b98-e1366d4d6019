-- =====================================================
-- SCRIPT PARA CRIAR SISTEMA COMPLETO DE RIDE REQUESTS
-- =====================================================
--
-- Execute este script no SQL Editor do Supabase Dashboard
-- URL: https://udquhavmgqtpkubrfzdm.supabase.co/project/default/sql
--
-- =====================================================

-- Habilitar extensão PostGIS para cálculos geográficos
CREATE EXTENSION IF NOT EXISTS postgis;

-- Tabela para ride requests
CREATE TABLE IF NOT EXISTS ride_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  driver_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  origin_address TEXT NOT NULL,
  origin_coords POINT NOT NULL,
  destination_address TEXT NOT NULL,
  destination_coords POINT NOT NULL,
  distance DECIMAL(10,2), -- em km
  duration INTEGER, -- em minutos
  estimated_price DECIMAL(10,2),
  final_price DECIMAL(10,2),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'in_progress', 'completed', 'cancelled')),
  vehicle_type TEXT DEFAULT 'economy' CHECK (vehicle_type IN ('economy', 'comfort', 'moto')),
  passenger_notes TEXT,
  driver_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  accepted_at TIMESTAMPTZ,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  cancelled_at TIMESTAMPTZ
);

-- Tabela para driver locations (tracking em tempo real)
CREATE TABLE IF NOT EXISTS driver_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  location POINT NOT NULL,
  heading DECIMAL(5,2), -- direção em graus
  speed DECIMAL(5,2), -- velocidade em km/h
  is_active BOOLEAN DEFAULT true,
  is_available BOOLEAN DEFAULT true,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_ride_requests_status ON ride_requests(status);
CREATE INDEX IF NOT EXISTS idx_ride_requests_user_id ON ride_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_ride_requests_driver_id ON ride_requests(driver_id);
CREATE INDEX IF NOT EXISTS idx_ride_requests_created_at ON ride_requests(created_at);

-- Índice espacial para driver locations
CREATE INDEX IF NOT EXISTS idx_driver_locations_location ON driver_locations USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_driver_locations_user_id ON driver_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_driver_locations_is_active ON driver_locations(is_active);
CREATE INDEX IF NOT EXISTS idx_driver_locations_is_available ON driver_locations(is_available);

-- RLS (Row Level Security)
ALTER TABLE ride_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;

-- Políticas para ride_requests
CREATE POLICY "Users can view own ride requests" ON ride_requests
  FOR SELECT USING (auth.uid() = user_id OR auth.uid() = driver_id);

CREATE POLICY "Users can insert own ride requests" ON ride_requests
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Drivers can update assigned ride requests" ON ride_requests
  FOR UPDATE USING (auth.uid() = driver_id OR auth.uid() = user_id);

-- Políticas para driver_locations
CREATE POLICY "Drivers can manage own location" ON driver_locations
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Anyone can view active driver locations" ON driver_locations
  FOR SELECT USING (is_active = true);

-- Função para buscar motoristas próximos e ativos
CREATE OR REPLACE FUNCTION get_nearby_active_drivers(
  passenger_lat DECIMAL,
  passenger_lng DECIMAL,
  radius_km DECIMAL DEFAULT 5
)
RETURNS TABLE (
  user_id UUID,
  location POINT,
  distance DECIMAL,
  heading DECIMAL,
  speed DECIMAL,
  updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    dl.user_id,
    dl.location,
    ST_Distance(
      ST_GeogFromText('POINT(' || passenger_lng || ' ' || passenger_lat || ')'),
      ST_GeogFromText('POINT(' || ST_X(dl.location) || ' ' || ST_Y(dl.location) || ')')
    ) / 1000 AS distance, -- converter para km
    dl.heading,
    dl.speed,
    dl.updated_at
  FROM driver_locations dl
  WHERE
    dl.is_active = true
    AND dl.is_available = true
    AND ST_DWithin(
      ST_GeogFromText('POINT(' || passenger_lng || ' ' || passenger_lat || ')'),
      ST_GeogFromText('POINT(' || ST_X(dl.location) || ' ' || ST_Y(dl.location) || ')'),
      radius_km * 1000 -- converter km para metros
    )
  ORDER BY distance ASC
  LIMIT 10;
END;
$$;

-- Função para atualizar localização do motorista
CREATE OR REPLACE FUNCTION update_driver_location(
  driver_user_id UUID,
  lat DECIMAL,
  lng DECIMAL,
  driver_heading DECIMAL DEFAULT NULL,
  driver_speed DECIMAL DEFAULT NULL,
  active BOOLEAN DEFAULT true,
  available BOOLEAN DEFAULT true
)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO driver_locations (user_id, location, heading, speed, is_active, is_available, updated_at)
  VALUES (driver_user_id, POINT(lng, lat), driver_heading, driver_speed, active, available, NOW())
  ON CONFLICT (user_id)
  DO UPDATE SET
    location = POINT(lng, lat),
    heading = COALESCE(driver_heading, driver_locations.heading),
    speed = COALESCE(driver_speed, driver_locations.speed),
    is_active = active,
    is_available = available,
    updated_at = NOW();
END;
$$;

-- Trigger para atualizar timestamp automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_driver_locations_updated_at
  BEFORE UPDATE ON driver_locations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Tabela para eventos de emergência
CREATE TABLE IF NOT EXISTS emergency_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  ride_id UUID REFERENCES ride_requests(id) ON DELETE SET NULL,
  driver_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  location POINT,
  address TEXT,
  emergency_type TEXT NOT NULL CHECK (emergency_type IN ('sos_button', 'route_deviation', 'panic_button', 'automatic')),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'false_alarm')),
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  resolved_at TIMESTAMPTZ
);

-- Tabela para localizações de emergência (tracking contínuo)
CREATE TABLE IF NOT EXISTS emergency_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  emergency_id UUID NOT NULL REFERENCES emergency_events(id) ON DELETE CASCADE,
  location POINT NOT NULL,
  accuracy DECIMAL(8,2),
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Tabela para contatos de emergência
CREATE TABLE IF NOT EXISTS emergency_contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  phone TEXT NOT NULL,
  email TEXT,
  relationship TEXT NOT NULL,
  is_emergency_contact BOOLEAN DEFAULT true,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabela para compartilhamento de viagens
CREATE TABLE IF NOT EXISTS trip_shares (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ride_id UUID NOT NULL REFERENCES ride_requests(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  origin TEXT NOT NULL,
  destination TEXT NOT NULL,
  driver_info JSONB,
  estimated_arrival TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ
);

-- Tabela para histórico de ETA
CREATE TABLE IF NOT EXISTS eta_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ride_id UUID NOT NULL REFERENCES ride_requests(id) ON DELETE CASCADE,
  driver_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  pickup_eta INTEGER NOT NULL, -- minutos
  trip_duration INTEGER NOT NULL, -- minutos
  total_time INTEGER NOT NULL, -- minutos
  distance DECIMAL(10,2), -- km
  driver_distance DECIMAL(10,2), -- km
  confidence INTEGER, -- 0-100%
  traffic_condition TEXT CHECK (traffic_condition IN ('light', 'moderate', 'heavy')),
  driver_location POINT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_emergency_events_user_id ON emergency_events(user_id);
CREATE INDEX IF NOT EXISTS idx_emergency_events_status ON emergency_events(status);
CREATE INDEX IF NOT EXISTS idx_emergency_events_created_at ON emergency_events(created_at);

CREATE INDEX IF NOT EXISTS idx_emergency_locations_emergency_id ON emergency_locations(emergency_id);
CREATE INDEX IF NOT EXISTS idx_emergency_locations_timestamp ON emergency_locations(timestamp);

CREATE INDEX IF NOT EXISTS idx_emergency_contacts_user_id ON emergency_contacts(user_id);
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_is_active ON emergency_contacts(is_active);

CREATE INDEX IF NOT EXISTS idx_trip_shares_ride_id ON trip_shares(ride_id);
CREATE INDEX IF NOT EXISTS idx_trip_shares_user_id ON trip_shares(user_id);
CREATE INDEX IF NOT EXISTS idx_trip_shares_is_active ON trip_shares(is_active);

CREATE INDEX IF NOT EXISTS idx_eta_history_ride_id ON eta_history(ride_id);
CREATE INDEX IF NOT EXISTS idx_eta_history_driver_id ON eta_history(driver_id);
CREATE INDEX IF NOT EXISTS idx_eta_history_created_at ON eta_history(created_at);

-- RLS para novas tabelas
ALTER TABLE emergency_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE emergency_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE emergency_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE eta_history ENABLE ROW LEVEL SECURITY;

-- Políticas para emergency_events
CREATE POLICY "Users can manage own emergency events" ON emergency_events
  FOR ALL USING (auth.uid() = user_id OR auth.uid() = driver_id);

CREATE POLICY "Emergency center can view all events" ON emergency_events
  FOR SELECT USING (true); -- TODO: Restringir para usuários admin/emergência

-- Políticas para emergency_contacts
CREATE POLICY "Users can manage own emergency contacts" ON emergency_contacts
  FOR ALL USING (auth.uid() = user_id);

-- Políticas para trip_shares
CREATE POLICY "Users can manage own trip shares" ON trip_shares
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Anyone can view active trip shares" ON trip_shares
  FOR SELECT USING (is_active = true);

-- Políticas para eta_history
CREATE POLICY "Users can view related ETA history" ON eta_history
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM ride_requests WHERE id = eta_history.ride_id
      UNION
      SELECT driver_id FROM ride_requests WHERE id = eta_history.ride_id
    )
  );

-- Inserir dados de exemplo para teste
INSERT INTO driver_locations (user_id, location, heading, speed, is_active, is_available) VALUES
  ('00000000-0000-0000-0000-000000000001', POINT(-46.6333, -23.5505), 45.0, 0.0, true, true),
  ('00000000-0000-0000-0000-000000000002', POINT(-46.6400, -23.5600), 90.0, 0.0, true, true),
  ('00000000-0000-0000-0000-000000000003', POINT(-46.6200, -23.5400), 180.0, 0.0, true, true)
ON CONFLICT (user_id) DO NOTHING;
