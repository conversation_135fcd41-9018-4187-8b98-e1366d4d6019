import React, { useRef, useState, useEffect, Suspense } from 'react'
import { Canvas, useFrame, useLoader } from '@react-three/fiber'
import { Environment, useTexture } from '@react-three/drei'
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader'
import { TextureLoader } from 'three'
import * as THREE from 'three'

// 🖥️ VERSÃO DESKTOP DE ALTA QUALIDADE
// - Materiais físicos realistas
// - Texturas de alta resolução
// - Iluminação avançada
// - Controles completos

// Componente otimizado que carrega apenas as texturas necessárias
const SimpleOBJCar: React.FC<{
  manualRotation: [number, number, number]
  textureMode: 'shaded' | 'pbr'
  textureConfig: number
  realisticMaterial: boolean
}> = ({ manualRotation, textureMode, textureConfig, realisticMaterial }) => {
  const carRef = useRef<THREE.Group>(null)

  // Carregar o modelo .obj
  const carModel = useLoader(OBJLoader, '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Shaded/base.obj')

  // Carregar texturas condicionalmente para otimizar performance
  const shadedTexture = textureMode === 'shaded'
    ? useLoader(TextureLoader, '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Shaded/shaded.png')
    : null

  const pbrTextures = textureMode === 'pbr'
    ? useTexture({
        diffuse: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_diffuse.png',
        metallic: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_metallic.png',
        normal: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_normal.png',
        roughness: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_roughness.png'
      })
    : null

  useFrame((state) => {
    if (carRef.current) {
      const scrollY = window.scrollY || 0
      const maxScroll = Math.max(document.body.scrollHeight - window.innerHeight, 1)
      const scrollProgress = Math.min(scrollY / maxScroll, 1)

      carRef.current.rotation.x = manualRotation[0] + Math.sin(scrollProgress * Math.PI) * 0.1
      carRef.current.rotation.y = manualRotation[1] + scrollProgress * Math.PI * 2 + state.clock.elapsedTime * 0.02
      carRef.current.rotation.z = manualRotation[2]
      carRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  // Aplicar texturas ao modelo
  useEffect(() => {
    if (carModel && (shadedTexture || pbrTextures)) {
      console.log('🎨 Aplicando texturas ao modelo...')
      console.log('📸 Modo de textura:', textureMode)
      if (shadedTexture) console.log('📸 Textura shaded carregada:', shadedTexture)
      if (pbrTextures) console.log('📸 Texturas PBR carregadas:', pbrTextures)

      // Configurar a textura shaded - múltiplas tentativas de correção
      console.log('🔧 Testando configurações de textura...')

      // Configurações diferentes baseadas no textureConfig
      if (textureMode === 'shaded' && shadedTexture) {
        shadedTexture.wrapS = THREE.RepeatWrapping
        shadedTexture.wrapT = THREE.RepeatWrapping

        switch (textureConfig) {
          case 0: // Configuração original
            shadedTexture.flipY = false
            shadedTexture.repeat.set(1, 1)
            shadedTexture.offset.set(0, 0)
            console.log('🎨 Config 0: flipY=false, repeat=(1,1), offset=(0,0)')
            break
          case 1: // Espelhar horizontalmente
            shadedTexture.flipY = false
            shadedTexture.repeat.set(-1, 1)
            shadedTexture.offset.set(1, 0)
            console.log('🎨 Config 1: flipY=false, repeat=(-1,1), offset=(1,0)')
            break
          case 2: // Espelhar verticalmente
            shadedTexture.flipY = false
            shadedTexture.repeat.set(1, -1)
            shadedTexture.offset.set(0, 1)
            console.log('🎨 Config 2: flipY=false, repeat=(1,-1), offset=(0,1)')
            break
          case 3: // Espelhar ambos
            shadedTexture.flipY = false
            shadedTexture.repeat.set(-1, -1)
            shadedTexture.offset.set(1, 1)
            console.log('🎨 Config 3: flipY=false, repeat=(-1,-1), offset=(1,1)')
            break
        }
      }

      carModel.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh) {
          console.log(`🎨 Aplicando material em: ${child.name}`)
          console.log(`📐 UV attributes:`, child.geometry.attributes.uv ? 'Presente' : 'Ausente')

          // Aplicar material realista baseado no modo selecionado
          const meshName = child.name.toLowerCase()

          if (textureMode === 'shaded' && shadedTexture) {
            // Método 1: Textura shaded com material configurável
            if (realisticMaterial) {
              // Material físico realista
              if (meshName.includes('glass') || meshName.includes('window')) {
                // Vidros com material físico avançado
                child.material = new THREE.MeshPhysicalMaterial({
                  map: shadedTexture,
                  transparent: true,
                  opacity: 0.1,
                  transmission: 0.95,
                  thickness: 0.5,
                  roughness: 0.0,
                  metalness: 0.0,
                  clearcoat: 1.0,
                  clearcoatRoughness: 0.0,
                  ior: 1.5,
                })
              } else {
                // Carroceria com efeito de tinta automotiva
                child.material = new THREE.MeshPhysicalMaterial({
                  map: shadedTexture,
                  metalness: 0.9,
                  roughness: 0.1,
                  clearcoat: 1.0,           // Verniz automotivo
                  clearcoatRoughness: 0.05, // Verniz bem polido
                  envMapIntensity: 1.5,     // Reflexões intensas
                  iridescence: 0.1,         // Efeito metálico sutil
                  iridescenceIOR: 1.3,
                })
              }
              console.log(`✅ Material físico realista aplicado em: ${child.name}`)
            } else {
              // Material padrão mais simples
              child.material = new THREE.MeshStandardMaterial({
                map: shadedTexture,
                metalness: 0.2,
                roughness: 0.8,
              })
              console.log(`✅ Material padrão aplicado em: ${child.name}`)
            }
          } else if (textureMode === 'pbr' && pbrTextures) {
            // Método 2: Texturas PBR com material físico
            child.material = new THREE.MeshPhysicalMaterial({
              map: pbrTextures.diffuse,
              metalnessMap: pbrTextures.metallic,
              normalMap: pbrTextures.normal,
              roughnessMap: pbrTextures.roughness,
              metalness: 1.0,
              roughness: 0.1,
              clearcoat: 0.8,
              clearcoatRoughness: 0.1,
              envMapIntensity: 2.0,
            })
            console.log(`✅ Material PBR físico aplicado em: ${child.name}`)
          } else {
            // Fallback: Material físico básico
            child.material = new THREE.MeshPhysicalMaterial({
              color: '#1a1a1a',
              metalness: 0.9,
              roughness: 0.1,
              clearcoat: 0.8,
              clearcoatRoughness: 0.1,
              envMapIntensity: 1.5,
            })
            console.log(`⚠️ Material físico básico aplicado em: ${child.name}`)
          }

          child.castShadow = true
          child.receiveShadow = true
        }
      })
    }
  }, [carModel, shadedTexture, pbrTextures, textureMode, textureConfig, realisticMaterial])

  return (
    <group ref={carRef} position={[0, 0, 0]} scale={[2, 2, 2]}>
      {/* Tentar rotação inicial para corrigir orientação da textura */}
      <group rotation={[0, Math.PI, 0]}>
        <primitive object={carModel} />
      </group>
    </group>
  )
}

// Componente de fallback otimizado (sem texturas para carregamento rápido)
const FallbackCar: React.FC<{ manualRotation: [number, number, number] }> = ({ manualRotation }) => {
  const carRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (carRef.current) {
      const scrollY = window.scrollY || 0
      const maxScroll = Math.max(document.body.scrollHeight - window.innerHeight, 1)
      const scrollProgress = Math.min(scrollY / maxScroll, 1)

      carRef.current.rotation.x = manualRotation[0] + Math.sin(scrollProgress * Math.PI) * 0.1
      carRef.current.rotation.y = manualRotation[1] + scrollProgress * Math.PI * 2 + state.clock.elapsedTime * 0.02
      carRef.current.rotation.z = manualRotation[2]
      carRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={carRef} position={[0, 0, 0]} scale={[1.2, 1.2, 1.2]}>
      {/* Corpo principal do carro */}
      <mesh position={[0, 0.5, 0]} castShadow receiveShadow>
        <boxGeometry args={[4.5, 1.2, 2.2]} />
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.95}
          roughness={0.05}
          envMapIntensity={1.5}
        />
      </mesh>

      {/* Teto do carro */}
      <mesh position={[0.2, 1.4, 0]} castShadow>
        <boxGeometry args={[3.2, 0.9, 2]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* Para-brisa frontal com efeito de vidro */}
      <mesh position={[1.5, 1.4, 0]} rotation={[0, 0, -0.3]} castShadow>
        <boxGeometry args={[0.9, 0.8, 1.8]} />
        <meshPhysicalMaterial
          color="#87CEEB"
          transparent={true}
          opacity={0.15}
          roughness={0.05}
          metalness={0.1}
          transmission={0.9}
          thickness={0.5}
          ior={1.5}
          clearcoat={1.0}
          clearcoatRoughness={0.1}
        />
      </mesh>

      {/* Para-brisa traseiro com efeito de vidro */}
      <mesh position={[-1.3, 1.4, 0]} rotation={[0, 0, 0.2]} castShadow>
        <boxGeometry args={[0.7, 0.7, 1.8]} />
        <meshPhysicalMaterial
          color="#87CEEB"
          transparent={true}
          opacity={0.15}
          roughness={0.05}
          metalness={0.1}
          transmission={0.9}
          thickness={0.5}
          ior={1.5}
          clearcoat={1.0}
          clearcoatRoughness={0.1}
        />
      </mesh>

      {/* Vidros laterais */}
      <mesh position={[0.5, 1.4, 1.15]} rotation={[0, 0, 0]} castShadow>
        <boxGeometry args={[2, 0.7, 0.05]} />
        <meshPhysicalMaterial
          color="#87CEEB"
          transparent={true}
          opacity={0.15}
          roughness={0.05}
          metalness={0.1}
          transmission={0.9}
          thickness={0.5}
          ior={1.5}
          clearcoat={1.0}
          clearcoatRoughness={0.1}
        />
      </mesh>
      <mesh position={[0.5, 1.4, -1.15]} rotation={[0, 0, 0]} castShadow>
        <boxGeometry args={[2, 0.7, 0.05]} />
        <meshPhysicalMaterial
          color="#87CEEB"
          transparent={true}
          opacity={0.15}
          roughness={0.05}
          metalness={0.1}
          transmission={0.9}
          thickness={0.5}
          ior={1.5}
          clearcoat={1.0}
          clearcoatRoughness={0.1}
        />
      </mesh>

      {/* Rodas */}
      {[
        [1.6, -0.3, 1.3],
        [1.6, -0.3, -1.3],
        [-1.6, -0.3, 1.3],
        [-1.6, -0.3, -1.3]
      ].map((position, index) => (
        <group key={index} position={position as [number, number, number]}>
          <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
            <cylinderGeometry args={[0.5, 0.5, 0.4]} />
            <meshStandardMaterial color="#1a1a1a" metalness={0.1} roughness={0.9} />
          </mesh>
          <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0.15]} castShadow>
            <cylinderGeometry args={[0.35, 0.35, 0.1]} />
            <meshStandardMaterial color="#c0c0c0" metalness={0.9} roughness={0.1} />
          </mesh>
        </group>
      ))}

      {/* Faróis */}
      <mesh position={[2.3, 0.4, 0.8]} castShadow>
        <sphereGeometry args={[0.2]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.8}
        />
      </mesh>
      <mesh position={[2.3, 0.4, -0.8]} castShadow>
        <sphereGeometry args={[0.2]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.8}
        />
      </mesh>

      {/* Luzes dos faróis */}
      <spotLight
        position={[2.3, 0.4, 0.8]}
        angle={0.4}
        penumbra={0.5}
        intensity={3}
        color="#ffffff"
        distance={25}
        castShadow
        target-position={[8, 0, 0.8]}
      />
      <spotLight
        position={[2.3, 0.4, -0.8]}
        angle={0.4}
        penumbra={0.5}
        intensity={3}
        color="#ffffff"
        distance={25}
        castShadow
        target-position={[8, 0, -0.8]}
      />

      {/* Lanternas traseiras */}
      <mesh position={[-2.3, 0.4, 0.8]} castShadow>
        <sphereGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#ff0000"
          emissive="#ff0000"
          emissiveIntensity={0.4}
        />
      </mesh>
      <mesh position={[-2.3, 0.4, -0.8]} castShadow>
        <sphereGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#ff0000"
          emissive="#ff0000"
          emissiveIntensity={0.4}
        />
      </mesh>

      {/* Logo MobiDrive nas portas */}
      <mesh position={[0, 0.8, 1.15]} rotation={[0, 0, 0]}>
        <circleGeometry args={[0.2]} />
        <meshStandardMaterial
          color="#3B82F6"
          emissive="#3B82F6"
          emissiveIntensity={0.3}
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>
      <mesh position={[0, 0.8, -1.15]} rotation={[0, Math.PI, 0]}>
        <circleGeometry args={[0.2]} />
        <meshStandardMaterial
          color="#3B82F6"
          emissive="#3B82F6"
          emissiveIntensity={0.3}
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>

      {/* Texto "M" no logo */}
      <mesh position={[0, 0.8, 1.16]} rotation={[0, 0, 0]}>
        <boxGeometry args={[0.1, 0.15, 0.02]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.5}
        />
      </mesh>
      <mesh position={[0, 0.8, -1.16]} rotation={[0, Math.PI, 0]}>
        <boxGeometry args={[0.1, 0.15, 0.02]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.5}
        />
      </mesh>
    </group>
  )
}

// Componente de loading
const SimpleLoader: React.FC = () => {
  const loaderRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (loaderRef.current) {
      loaderRef.current.rotation.y = state.clock.elapsedTime * 1
    }
  })

  return (
    <group ref={loaderRef}>
      <mesh position={[0, 0, 0]}>
        <torusGeometry args={[1, 0.2, 8, 16]} />
        <meshStandardMaterial
          color="#3B82F6"
          emissive="#3B82F6"
          emissiveIntensity={0.5}
        />
      </mesh>
    </group>
  )
}

// Componente principal
interface Car3DSimpleOBJProps {
  className?: string
}

export const Car3DSimpleOBJ: React.FC<Car3DSimpleOBJProps> = ({ className = "" }) => {
  const [rotation, setRotation] = useState<[number, number, number]>([0, 0, 0])
  const [isDragging, setIsDragging] = useState(false)
  const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 })
  const [useOBJ, setUseOBJ] = useState(true)
  const [textureMode, setTextureMode] = useState<'shaded' | 'pbr'>('shaded')
  const [textureConfig, setTextureConfig] = useState(2) // Config 2 (Flip V) é a correta
  const [realisticMaterial, setRealisticMaterial] = useState(true) // Material físico realista por padrão

  // Handlers para mouse
  const handleMouseDown = (event: React.MouseEvent) => {
    event.preventDefault()
    setIsDragging(true)
    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isDragging) return
    event.preventDefault()

    const deltaX = event.clientX - lastPosition.x
    const deltaY = event.clientY - lastPosition.y

    setRotation(prev => [
      prev[0] + deltaY * 0.005,
      prev[1] + deltaX * 0.005,
      prev[2]
    ])

    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Event listeners globais
  useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false)
    const handleGlobalMouseMove = (event: MouseEvent) => {
      if (!isDragging) return

      const deltaX = event.clientX - lastPosition.x
      const deltaY = event.clientY - lastPosition.y

      setRotation(prev => [
        prev[0] + deltaY * 0.005,
        prev[1] + deltaX * 0.005,
        prev[2]
      ])

      setLastPosition({ x: event.clientX, y: event.clientY })
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }
  }, [isDragging, lastPosition])

  return (
    <div
      className={`w-full h-full ${className} select-none relative`}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
        pointerEvents: 'auto',
        cursor: isDragging ? 'grabbing' : 'grab',
        touchAction: 'none'
      }}
    >
      <Canvas
        shadows
        camera={{ position: [6, 4, 6], fov: 50 }}
        style={{
          background: 'transparent',
          width: '100%',
          height: '100%'
        }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]}
      >
        {/* Iluminação otimizada para materiais físicos */}
        <ambientLight intensity={0.3} />

        {/* Luz principal mais intensa para clearcoat */}
        <directionalLight
          position={[10, 10, 5]}
          intensity={2.0}
          castShadow
          shadow-mapSize-width={4096}
          shadow-mapSize-height={4096}
          shadow-camera-near={0.1}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />

        {/* Luzes de preenchimento para reflexões */}
        <pointLight position={[-8, 6, -8]} intensity={0.8} color="#ffffff" />
        <pointLight position={[8, 6, 8]} intensity={0.8} color="#ffffff" />
        <pointLight position={[0, 10, 0]} intensity={0.6} color="#f0f8ff" />

        {/* Ambiente HDR para reflexões realistas */}
        <Environment preset="studio" background={false} />

        {/* Carro com Suspense */}
        <Suspense fallback={<SimpleLoader />}>
          {useOBJ ? (
            <SimpleOBJCar manualRotation={rotation} textureMode={textureMode} textureConfig={textureConfig} realisticMaterial={realisticMaterial} />
          ) : (
            <FallbackCar manualRotation={rotation} />
          )}
        </Suspense>

        {/* Chão */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1, 0]} receiveShadow>
          <planeGeometry args={[30, 30]} />
          <meshStandardMaterial
            color="#1a1a1a"
            transparent
            opacity={0.2}
            metalness={0.8}
            roughness={0.2}
          />
        </mesh>
      </Canvas>

      {/* Controles */}
      <div className="absolute bottom-4 right-4 bg-black/50 text-white p-2 rounded text-xs space-y-2">
        <button
          onClick={() => setUseOBJ(!useOBJ)}
          className="block w-full px-3 py-1 bg-blue-500 hover:bg-blue-600 rounded transition-colors"
        >
          {useOBJ ? 'Usar Fallback' : 'Usar Modelo OBJ'}
        </button>

        {useOBJ && (
          <>
            <button
              onClick={() => setTextureMode(textureMode === 'shaded' ? 'pbr' : 'shaded')}
              className="block w-full px-3 py-1 bg-green-500 hover:bg-green-600 rounded transition-colors"
            >
              Textura: {textureMode === 'shaded' ? 'Shaded' : 'PBR'}
            </button>

            {textureMode === 'shaded' && (
              <button
                onClick={() => setTextureConfig((textureConfig + 1) % 4)}
                className="block w-full px-3 py-1 bg-purple-500 hover:bg-purple-600 rounded transition-colors"
              >
                Config: {textureConfig} ({['Original', 'Flip H', 'Flip V', 'Flip HV'][textureConfig]})
              </button>
            )}

            <button
              onClick={() => setRealisticMaterial(!realisticMaterial)}
              className="block w-full px-3 py-1 bg-orange-500 hover:bg-orange-600 rounded transition-colors"
            >
              Material: {realisticMaterial ? 'Realista ✨' : 'Padrão'}
            </button>
          </>
        )}
      </div>

      {/* Status */}
      <div className="absolute top-4 right-4 bg-black/50 text-white p-2 rounded text-xs space-y-1">
        <div>Modelo: {useOBJ ? 'OBJ Real' : 'Fallback Otimizado'}</div>
        {useOBJ && (
          <>
            <div>Textura: {textureMode === 'shaded' ? 'Shaded Original' : 'PBR Avançada'}</div>
            <div>Material: {realisticMaterial ? 'Físico Realista' : 'Standard'}</div>
            {textureMode === 'shaded' && (
              <div>Config: {textureConfig} (Flip V ✓)</div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default Car3DSimpleOBJ
