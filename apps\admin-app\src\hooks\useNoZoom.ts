import { useEffect } from 'react'

// 🚫 HOOK PARA DESABILITAR ZOOM COMPLETAMENTE NAS PÁGINAS DO APP
// Bloqueia todos os tipos de zoom exceto em mapas (.mapbox, .map-container)

export const useNoZoom = () => {
  useEffect(() => {
    // 1. CONFIGURAR META VIEWPORT PARA DESABILITAR ZOOM
    const setupViewport = () => {
      let metaViewport = document.querySelector('meta[name="viewport"]')
      
      if (!metaViewport) {
        metaViewport = document.createElement('meta')
        metaViewport.setAttribute('name', 'viewport')
        document.head.appendChild(metaViewport)
      }
      
      // Configuração que desabilita zoom completamente
      metaViewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
      )
    }

    // 2. PREVENIR ZOOM POR WHEEL (Ctrl + Scroll)
    const preventWheelZoom = (e: WheelEvent) => {
      // Verifica se é tentativa de zoom
      if (e.ctrlKey || e.metaKey) {
        // Verifica se NÃO está em um mapa
        const target = e.target as HTMLElement
        const isInMap = target.closest('.mapbox-gl-canvas, .mapbox, .map-container, .leaflet-container, [data-allow-zoom="true"]')
        
        if (!isInMap) {
          e.preventDefault()
          e.stopPropagation()
          return false
        }
      }
    }

    // 3. PREVENIR ZOOM POR TECLADO (Ctrl + +/-)
    const preventKeyboardZoom = (e: KeyboardEvent) => {
      // Teclas de zoom
      const zoomKeys = ['+', '-', '=', '0', 'Equal', 'Minus', 'Digit0']
      
      if ((e.ctrlKey || e.metaKey) && (
        zoomKeys.includes(e.key) || 
        zoomKeys.includes(e.code) ||
        e.key === '+' || e.key === '-' || e.key === '=' || e.key === '0'
      )) {
        // Verifica se NÃO está em um mapa
        const target = e.target as HTMLElement
        const isInMap = target.closest('.mapbox-gl-canvas, .mapbox, .map-container, .leaflet-container, [data-allow-zoom="true"]')
        
        if (!isInMap) {
          e.preventDefault()
          e.stopPropagation()
          return false
        }
      }
    }

    // 4. PREVENIR ZOOM POR TOUCH (Pinch)
    const preventTouchZoom = (e: TouchEvent) => {
      // Detecta pinch (2 dedos)
      if (e.touches.length > 1) {
        // Verifica se NÃO está em um mapa
        const target = e.target as HTMLElement
        const isInMap = target.closest('.mapbox-gl-canvas, .mapbox, .map-container, .leaflet-container, [data-allow-zoom="true"]')
        
        if (!isInMap) {
          e.preventDefault()
          e.stopPropagation()
          return false
        }
      }
    }

    // 5. PREVENIR ZOOM POR GESTURE EVENTS (Safari)
    const preventGestureZoom = (e: Event) => {
      const target = e.target as HTMLElement
      const isInMap = target.closest('.mapbox-gl-canvas, .mapbox, .map-container, .leaflet-container, [data-allow-zoom="true"]')
      
      if (!isInMap) {
        e.preventDefault()
        e.stopPropagation()
        return false
      }
    }

    // 6. PREVENIR DOUBLE TAP ZOOM (Mobile)
    let lastTouchEnd = 0
    const preventDoubleTapZoom = (e: TouchEvent) => {
      const now = Date.now()
      if (now - lastTouchEnd <= 300) {
        // Verifica se NÃO está em um mapa
        const target = e.target as HTMLElement
        const isInMap = target.closest('.mapbox-gl-canvas, .mapbox, .map-container, .leaflet-container, [data-allow-zoom="true"]')
        
        if (!isInMap) {
          e.preventDefault()
          e.stopPropagation()
          return false
        }
      }
      lastTouchEnd = now
    }

    // 7. APLICAR CONFIGURAÇÕES
    setupViewport()

    // Adicionar event listeners
    document.addEventListener('wheel', preventWheelZoom, { passive: false })
    document.addEventListener('keydown', preventKeyboardZoom, { passive: false })
    document.addEventListener('touchstart', preventTouchZoom, { passive: false })
    document.addEventListener('touchmove', preventTouchZoom, { passive: false })
    document.addEventListener('touchend', preventDoubleTapZoom, { passive: false })
    
    // Safari gesture events
    document.addEventListener('gesturestart', preventGestureZoom, { passive: false })
    document.addEventListener('gesturechange', preventGestureZoom, { passive: false })
    document.addEventListener('gestureend', preventGestureZoom, { passive: false })

    // 8. APLICAR CSS PARA DESABILITAR ZOOM
    const style = document.createElement('style')
    style.textContent = `
      /* DESABILITA ZOOM EM TODA A APLICAÇÃO */
      html, body {
        touch-action: manipulation !important;
        -webkit-touch-callout: none !important;
        -webkit-tap-highlight-color: transparent !important;
        overscroll-behavior: none !important;
      }
      
      /* PERMITE SCROLL VERTICAL */
      html, body {
        overflow-x: hidden !important;
        overflow-y: auto !important;
      }
      
      /* EXCEÇÃO PARA MAPAS - PERMITE ZOOM */
      .mapbox-gl-canvas,
      .mapbox,
      .map-container,
      .leaflet-container,
      [data-allow-zoom="true"] {
        touch-action: auto !important;
        -webkit-user-select: auto !important;
        user-select: auto !important;
      }
      
      /* PERMITE SELEÇÃO DE TEXTO EM INPUTS E BOTÕES */
      input, textarea, [contenteditable], button, [role="button"], .btn {
        -webkit-user-select: text !important;
        user-select: text !important;
        touch-action: manipulation !important;
        pointer-events: auto !important;
      }

      /* GARANTE QUE BOTÕES FUNCIONEM CORRETAMENTE */
      button, [role="button"], .btn, [type="button"], [type="submit"] {
        -webkit-user-select: none !important;
        user-select: none !important;
        touch-action: manipulation !important;
        pointer-events: auto !important;
        cursor: pointer !important;
      }

      /* DESABILITA ZOOM EM IMAGENS */
      img {
        -webkit-user-select: none !important;
        user-select: none !important;
        touch-action: manipulation !important;
      }
    `
    document.head.appendChild(style)

    // Cleanup
    return () => {
      document.removeEventListener('wheel', preventWheelZoom)
      document.removeEventListener('keydown', preventKeyboardZoom)
      document.removeEventListener('touchstart', preventTouchZoom)
      document.removeEventListener('touchmove', preventTouchZoom)
      document.removeEventListener('touchend', preventDoubleTapZoom)
      document.removeEventListener('gesturestart', preventGestureZoom)
      document.removeEventListener('gesturechange', preventGestureZoom)
      document.removeEventListener('gestureend', preventGestureZoom)
      
      if (style.parentNode) {
        style.parentNode.removeChild(style)
      }
    }
  }, [])
}

// Hook específico para componentes de mapa
export const useMapZoom = () => {
  useEffect(() => {
    // Adiciona atributo para permitir zoom em mapas
    const mapContainers = document.querySelectorAll('.mapbox-gl-canvas, .mapbox, .map-container, .leaflet-container')
    
    mapContainers.forEach(container => {
      container.setAttribute('data-allow-zoom', 'true')
    })
  }, [])
}

export default useNoZoom
