// =====================================================
// MONITOR COM FLUXO REAL DO USUÁRIO
// Faz login, navega pelas páginas e testa funcionalidades reais
// =====================================================

import puppeteer from 'puppeteer'

class RealUserFlowMonitor {
  constructor() {
    this.browser = null
    this.page = null
    this.logs = []
    this.errors = []
    this.warnings = []
    this.isLoggedIn = false
  }

  async init() {
    console.log('🎯 INICIANDO MONITOR COM FLUXO REAL DO USUÁRIO')
    console.log('=' .repeat(60))
    
    this.browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--start-maximized'
      ]
    })
    
    this.page = await this.browser.newPage()
    await this.page.setViewport({ width: 1280, height: 720 })
    
    this.setupConsoleMonitoring()
    console.log('✅ Navegador iniciado e monitoramento configurado')
  }

  setupConsoleMonitoring() {
    this.page.on('console', (msg) => {
      const logEntry = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toLocaleTimeString()
      }
      
      this.logs.push(logEntry)
      
      const time = logEntry.timestamp
      
      switch (msg.type()) {
        case 'error':
          this.errors.push(logEntry)
          console.log(`[${time}] ❌ ERROR: ${msg.text()}`)
          break
        case 'warning':
          this.warnings.push(logEntry)
          console.log(`[${time}] ⚠️ WARNING: ${msg.text()}`)
          break
        default:
          // Filtrar logs importantes
          if (msg.text().includes('❌') || msg.text().includes('ERROR')) {
            this.errors.push(logEntry)
            console.log(`[${time}] 🔴 ${msg.text()}`)
          } else if (msg.text().includes('✅') || msg.text().includes('SUCCESS')) {
            console.log(`[${time}] 🟢 ${msg.text()}`)
          } else if (msg.text().includes('🚗') || msg.text().includes('💰') || msg.text().includes('📍')) {
            console.log(`[${time}] 🎯 ${msg.text()}`)
          }
          break
      }
    })

    this.page.on('pageerror', (error) => {
      this.errors.push({
        type: 'pageerror',
        message: error.message,
        timestamp: new Date().toLocaleTimeString()
      })
      console.log(`[${new Date().toLocaleTimeString()}] 💥 PAGE ERROR: ${error.message}`)
    })
  }

  async performLogin() {
    console.log('\n🔐 ETAPA 1: FAZENDO LOGIN')
    console.log('-' .repeat(40))
    
    try {
      // Navegar para a página de login
      console.log('🌐 Navegando para página de login...')
      await this.page.goto('http://localhost:3000/login', { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })
      
      console.log('⏱️ Aguardando página carregar...')
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Verificar se há campos de login
      const emailField = await this.page.$('input[type="email"], input[name="email"], input[placeholder*="email" i]')
      const passwordField = await this.page.$('input[type="password"], input[name="password"]')
      
      if (emailField && passwordField) {
        console.log('📝 Preenchendo credenciais...')
        
        // Preencher email
        await emailField.click()
        await emailField.type('<EMAIL>', { delay: 100 })

        // Preencher senha
        await passwordField.click()
        await passwordField.type('test123456', { delay: 100 })
        
        console.log('🔘 Procurando botão de login...')
        
        // Procurar botão de login usando XPath e CSS
        let loginButton = await this.page.$('button[type="submit"]')

        if (!loginButton) {
          // Tentar encontrar por texto usando XPath
          const buttons = await this.page.$x('//button[contains(text(), "Login") or contains(text(), "Entrar") or contains(text(), "Sign in")]')
          loginButton = buttons[0] || null
        }

        if (!loginButton) {
          // Tentar encontrar qualquer botão na página
          const allButtons = await this.page.$$('button')
          loginButton = allButtons[0] || null
          console.log(`🔍 Encontrados ${allButtons.length} botões na página`)
        }
        
        if (loginButton) {
          console.log('🖱️ Clicando no botão de login...')
          await loginButton.click()
          
          // Aguardar redirecionamento
          console.log('⏱️ Aguardando login...')
          await new Promise(resolve => setTimeout(resolve, 5000))
          
          // Verificar se foi redirecionado
          const currentUrl = this.page.url()
          if (!currentUrl.includes('/login')) {
            console.log('✅ Login realizado com sucesso!')
            this.isLoggedIn = true
          } else {
            console.log('⚠️ Ainda na página de login, tentando método alternativo...')
            await this.tryAlternativeLogin()
          }
        } else {
          console.log('⚠️ Botão de login não encontrado, tentando Enter...')
          await passwordField.press('Enter')
          await new Promise(resolve => setTimeout(resolve, 3000))
          this.isLoggedIn = !this.page.url().includes('/login')
        }
      } else {
        console.log('⚠️ Campos de login não encontrados, verificando se já está logado...')
        await this.checkIfAlreadyLoggedIn()
      }
      
    } catch (error) {
      console.error('❌ Erro durante login:', error.message)
    }
    
    return this.isLoggedIn
  }

  async tryAlternativeLogin() {
    try {
      // Tentar login via Supabase diretamente
      console.log('🔄 Tentando login alternativo via JavaScript...')
      
      await this.page.evaluate(() => {
        // Tentar usar o Supabase client diretamente
        if (window.supabase) {
          return window.supabase.auth.signInWithPassword({
            email: '<EMAIL>',
            password: 'test123456'
          })
        }
      })
      
      await new Promise(resolve => setTimeout(resolve, 3000))
      this.isLoggedIn = !this.page.url().includes('/login')
      
      if (this.isLoggedIn) {
        console.log('✅ Login alternativo funcionou!')
      }
    } catch (error) {
      console.log('⚠️ Login alternativo falhou:', error.message)
    }
  }

  async checkIfAlreadyLoggedIn() {
    try {
      // Navegar para uma página protegida para verificar
      await this.page.goto('http://localhost:3000/ride-request/map', { 
        waitUntil: 'networkidle2',
        timeout: 15000 
      })
      
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Se não foi redirecionado para login, está logado
      this.isLoggedIn = !this.page.url().includes('/login')
      
      if (this.isLoggedIn) {
        console.log('✅ Usuário já estava logado!')
      } else {
        console.log('❌ Usuário não está logado')
      }
    } catch (error) {
      console.log('⚠️ Erro ao verificar login:', error.message)
    }
  }

  async testMapSelection() {
    console.log('\n🗺️ ETAPA 2: TESTANDO SELEÇÃO DE MAPA')
    console.log('-' .repeat(40))
    
    try {
      console.log('🌐 Navegando para seleção de mapa...')
      await this.page.goto('http://localhost:3000/ride-request/map', { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })
      
      console.log('⏱️ Aguardando mapa carregar...')
      await new Promise(resolve => setTimeout(resolve, 8000))
      
      // Verificar se o mapa carregou
      const mapContainer = await this.page.$('.mapboxgl-map, #map, [class*="map"]')
      if (mapContainer) {
        console.log('✅ Mapa encontrado na página')
      } else {
        console.log('⚠️ Container do mapa não encontrado')
      }
      
      // Tentar clicar em um ponto do mapa para selecionar destino
      console.log('🖱️ Tentando selecionar destino no mapa...')
      await this.page.click('body', { button: 'left' })
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Procurar botão de confirmação
      let confirmButton = await this.page.$('button[type="submit"]')

      if (!confirmButton) {
        const buttons = await this.page.$x('//button[contains(text(), "Confirmar") or contains(text(), "Continuar") or contains(text(), "Confirm")]')
        confirmButton = buttons[0] || null
      }
      if (confirmButton) {
        console.log('🔘 Botão de confirmação encontrado')
        await confirmButton.click()
        await new Promise(resolve => setTimeout(resolve, 3000))
        console.log('✅ Destino confirmado')
      }
      
    } catch (error) {
      console.error('❌ Erro durante teste do mapa:', error.message)
    }
  }

  async testTripDetails() {
    console.log('\n🚗 ETAPA 3: TESTANDO DETALHES DA VIAGEM')
    console.log('-' .repeat(40))
    
    try {
      console.log('🌐 Navegando para detalhes da viagem...')
      await this.page.goto('http://localhost:3000/ride-request/details', { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })
      
      console.log('⏱️ Aguardando página carregar...')
      await new Promise(resolve => setTimeout(resolve, 10000))
      
      // Verificar se os preços carregaram
      const priceElements = await this.page.$$('[class*="price"], [class*="valor"], .currency')
      console.log(`💰 Encontrados ${priceElements.length} elementos de preço`)
      
      // Verificar métodos de pagamento
      const paymentMethods = await this.page.$$('[class*="payment"], [class*="pagamento"]')
      console.log(`💳 Encontrados ${paymentMethods.length} métodos de pagamento`)
      
      // Tentar selecionar um veículo
      const vehicleOptions = await this.page.$$('[class*="vehicle"], [class*="veiculo"], button')
      if (vehicleOptions.length > 0) {
        console.log(`🚗 Encontradas ${vehicleOptions.length} opções de veículo`)
        console.log('🖱️ Selecionando primeiro veículo...')
        await vehicleOptions[0].click()
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
      
      // Procurar botão de solicitar viagem
      let requestButton = await this.page.$('button[type="submit"]')

      if (!requestButton) {
        const buttons = await this.page.$x('//button[contains(text(), "Solicitar") or contains(text(), "Confirmar") or contains(text(), "Request")]')
        requestButton = buttons[0] || null
      }
      if (requestButton) {
        console.log('🔘 Botão de solicitação encontrado')
        console.log('🖱️ Clicando para solicitar viagem...')
        await requestButton.click()
        await new Promise(resolve => setTimeout(resolve, 5000))
        console.log('✅ Viagem solicitada')
      }
      
    } catch (error) {
      console.error('❌ Erro durante teste de detalhes:', error.message)
    }
  }

  async generateReport() {
    console.log('\n' + '=' .repeat(60))
    console.log('📊 RELATÓRIO DO FLUXO REAL DO USUÁRIO')
    console.log('=' .repeat(60))
    
    console.log(`🔐 Login realizado: ${this.isLoggedIn ? '✅ SIM' : '❌ NÃO'}`)
    console.log(`📝 Total de logs capturados: ${this.logs.length}`)
    console.log(`❌ Total de erros: ${this.errors.length}`)
    console.log(`⚠️ Total de warnings: ${this.warnings.length}`)
    
    if (this.errors.length > 0) {
      console.log('\n❌ ERROS ENCONTRADOS:')
      this.errors.slice(0, 5).forEach((error, i) => {
        console.log(`  ${i + 1}. ${error.text || error.message}`)
      })
    }
    
    // Análise final
    if (this.isLoggedIn && this.errors.length === 0) {
      console.log('\n🎉 EXCELENTE! Fluxo completo funcionando sem erros!')
    } else if (this.isLoggedIn && this.errors.length < 3) {
      console.log('\n✅ BOM! Fluxo funcionando com pequenos problemas.')
    } else if (!this.isLoggedIn) {
      console.log('\n⚠️ PROBLEMA: Não foi possível fazer login.')
    } else {
      console.log('\n❌ PROBLEMÁTICO: Muitos erros encontrados.')
    }
    
    return {
      isLoggedIn: this.isLoggedIn,
      totalLogs: this.logs.length,
      totalErrors: this.errors.length,
      totalWarnings: this.warnings.length,
      logs: this.logs,
      errors: this.errors
    }
  }

  async runCompleteFlow() {
    try {
      await this.init()
      
      // Etapa 1: Login
      const loginSuccess = await this.performLogin()
      
      if (loginSuccess) {
        // Etapa 2: Teste do mapa
        await this.testMapSelection()
        
        // Etapa 3: Teste dos detalhes
        await this.testTripDetails()
      } else {
        console.log('⚠️ Pulando testes pois login falhou')
      }
      
      // Gerar relatório
      const report = await this.generateReport()
      
      return report
      
    } finally {
      if (this.browser) {
        console.log('\n🔒 Fechando navegador...')
        await this.browser.close()
      }
    }
  }
}

// Executar fluxo completo
const monitor = new RealUserFlowMonitor()

monitor.runCompleteFlow()
  .then((report) => {
    console.log('\n🏁 TESTE DE FLUXO REAL CONCLUÍDO!')
    process.exit(report.totalErrors > 0 ? 1 : 0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
