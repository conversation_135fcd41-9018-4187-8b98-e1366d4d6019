# 🚀 DEPLOY NO VERCEL - MUNDODAINOVACAO.COM

## 📋 **PASSO A PASSO COMPLETO:**

### **PASSO 1: ACESSAR VERCEL**
1. Acesse: https://vercel.com
2. Faça login com GitHub, <PERSON><PERSON><PERSON><PERSON> ou email
3. Clique em "New Project"

### **PASSO 2: CONECTAR REPOSITÓRIO**
1. **Se o código estiver no GitHub:**
   - Conecte sua conta GitHub
   - Selecione o repositório MobiDrive
   - Escolha a pasta `apps/passenger-app`

2. **Se não estiver no GitHub:**
   - Clique em "Browse" ou "Upload"
   - <PERSON><PERSON><PERSON> upload da pasta `apps/passenger-app`

### **PASSO 3: CONFIGURAR O PROJETO**

#### **Build Settings:**
- **Framework Preset:** Vite
- **Root Directory:** `apps/passenger-app` (se necessário)
- **Build Command:** `npm run build:prod`
- **Output Directory:** `dist`
- **Install Command:** `npm install`

#### **Environment Variables:**
Adicione estas variáveis na seção "Environment Variables":

```
VITE_SUPABASE_URL=https://udquhavmgqtpkubrfzdm.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzMzQ4NzQsImV4cCI6MjA1MDkxMDg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
VITE_MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoibW9iaWRyaXZlIiwiYSI6ImNtNGxqZGZhZjBhZGsyanM4ZGZhZjBhZGsifQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
VITE_APP_DOMAIN=mundodainovacao.com
VITE_APP_URL=https://mundodainovacao.com
VITE_APP_ENVIRONMENT=production
```

### **PASSO 4: FAZER DEPLOY**
1. Clique em "Deploy"
2. Aguarde o build (2-5 minutos)
3. Vercel gerará uma URL temporária (ex: mobidrive-xxx.vercel.app)

### **PASSO 5: CONFIGURAR DOMÍNIO CUSTOMIZADO**
1. Vá para "Settings" > "Domains"
2. Adicione: `mundodainovacao.com`
3. Adicione: `www.mundodainovacao.com`
4. Configure DNS conforme instruções do Vercel

### **PASSO 6: CONFIGURAR DNS**
No seu provedor de domínio (onde comprou mundodainovacao.com):

#### **Registros DNS necessários:**
```
Tipo: A
Nome: @
Valor: ***********

Tipo: CNAME
Nome: www
Valor: cname.vercel-dns.com
```

**OU usar CNAME (recomendado):**
```
Tipo: CNAME
Nome: @
Valor: cname.vercel-dns.com

Tipo: CNAME
Nome: www
Valor: cname.vercel-dns.com
```

### **PASSO 7: VERIFICAR SSL**
- Vercel configura SSL automaticamente
- Aguarde 5-10 minutos para propagação
- Teste: https://mundodainovacao.com

## 🔧 **CONFIGURAÇÕES AVANÇADAS:**

### **Redirects (Opcional):**
Se quiser redirecionar www para não-www:
```json
{
  "redirects": [
    {
      "source": "https://www.mundodainovacao.com/(.*)",
      "destination": "https://mundodainovacao.com/$1",
      "permanent": true
    }
  ]
}
```

### **Headers de Segurança:**
Já configurados no `vercel.json`:
- X-Frame-Options: SAMEORIGIN
- X-XSS-Protection: 1; mode=block
- X-Content-Type-Options: nosniff
- Referrer-Policy: no-referrer-when-downgrade

## 📊 **MONITORAMENTO:**

### **Analytics:**
- Vercel Analytics automático
- Acesse: Dashboard > Analytics
- Monitore performance e visitantes

### **Logs:**
- Dashboard > Functions > View Logs
- Monitore erros e performance

## 🆘 **TROUBLESHOOTING:**

### **Build Falha:**
1. Verifique se `package.json` tem `build:prod` script
2. Confirme variáveis de ambiente
3. Veja logs de build no dashboard

### **Domínio não funciona:**
1. Verifique configuração DNS
2. Aguarde propagação (até 24h)
3. Use ferramenta: https://dnschecker.org

### **SSL não funciona:**
1. Aguarde 10-15 minutos
2. Verifique se DNS está correto
3. Force refresh: Ctrl+F5

## ✅ **CHECKLIST FINAL:**

- [ ] Projeto criado no Vercel
- [ ] Build bem-sucedido
- [ ] Variáveis de ambiente configuradas
- [ ] Domínio customizado adicionado
- [ ] DNS configurado
- [ ] SSL ativo
- [ ] Site funcionando em https://mundodainovacao.com
- [ ] Todas as páginas carregando
- [ ] Login/cadastro funcionando
- [ ] Mapbox carregando
- [ ] Responsivo em mobile

## 🎯 **LINKS ÚTEIS:**

- **Vercel Dashboard:** https://vercel.com/dashboard
- **Documentação:** https://vercel.com/docs
- **DNS Checker:** https://dnschecker.org
- **SSL Checker:** https://www.ssllabs.com/ssltest/

---

**🎉 Após seguir estes passos, seu MobiDrive estará no ar em mundodainovacao.com!**
