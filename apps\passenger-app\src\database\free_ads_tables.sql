-- 💰 SISTEMA DE ANÚNCIOS GRATUITOS - MOBIDRIVE
-- Tabelas para sistema que permite ganhar R$ 10/dia assistindo vídeos

-- =====================================================
-- 1. TABELA DE VÍDEOS DE ANÚNCIOS
-- =====================================================

CREATE TABLE IF NOT EXISTS ad_videos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT,
    video_url TEXT NOT NULL,
    thumbnail_url TEXT,
    duration INTEGER NOT NULL, -- duração em segundos
    reward_value INTEGER NOT NULL, -- valor em centavos
    category TEXT NOT NULL CHECK (category IN ('high_revenue', 'medium_revenue', 'low_revenue')),
    advertiser TEXT NOT NULL,
    target_audience TEXT[], -- array de strings
    min_watch_time INTEGER, -- tempo mínimo para ganhar recompensa
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_ad_videos_active ON ad_videos(is_active);
CREATE INDEX IF NOT EXISTS idx_ad_videos_category ON ad_videos(category);
CREATE INDEX IF NOT EXISTS idx_ad_videos_reward ON ad_videos(reward_value DESC);

-- =====================================================
-- 2. TABELA DE PROGRESSO DIÁRIO DOS USUÁRIOS
-- =====================================================

CREATE TABLE IF NOT EXISTS user_ad_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    total_earned INTEGER DEFAULT 0, -- total ganho no dia em centavos
    videos_watched INTEGER DEFAULT 0,
    daily_limit INTEGER DEFAULT 1000, -- limite diário em centavos (R$ 10)
    can_watch_more BOOLEAN DEFAULT true,
    next_reset_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraint para garantir um registro por usuário por dia
    UNIQUE(user_id, date)
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_user_ad_progress_user_date ON user_ad_progress(user_id, date);
CREATE INDEX IF NOT EXISTS idx_user_ad_progress_date ON user_ad_progress(date);

-- =====================================================
-- 3. TABELA DE SESSÕES DE VISUALIZAÇÃO
-- =====================================================

CREATE TABLE IF NOT EXISTS ad_watch_sessions (
    id TEXT PRIMARY KEY, -- ID customizado para facilitar tracking
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    ad_id UUID NOT NULL REFERENCES ad_videos(id) ON DELETE CASCADE,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    watched_duration INTEGER DEFAULT 0, -- duração assistida em segundos
    completed BOOLEAN DEFAULT false,
    reward_earned INTEGER DEFAULT 0, -- recompensa ganha em centavos
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_ad_watch_sessions_user ON ad_watch_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_watch_sessions_ad ON ad_watch_sessions(ad_id);
CREATE INDEX IF NOT EXISTS idx_ad_watch_sessions_completed ON ad_watch_sessions(completed);
CREATE INDEX IF NOT EXISTS idx_ad_watch_sessions_date ON ad_watch_sessions(start_time);

-- =====================================================
-- 4. TABELA DE HISTÓRICO DE GANHOS
-- =====================================================

CREATE TABLE IF NOT EXISTS ad_earnings_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL REFERENCES ad_watch_sessions(id) ON DELETE CASCADE,
    ad_id UUID NOT NULL REFERENCES ad_videos(id) ON DELETE CASCADE,
    amount_earned INTEGER NOT NULL, -- valor ganho em centavos
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadados para analytics
    watch_percentage DECIMAL(5,2), -- porcentagem do vídeo assistida
    device_type TEXT,
    browser_info TEXT
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_ad_earnings_user ON ad_earnings_history(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_earnings_date ON ad_earnings_history(earned_at);
CREATE INDEX IF NOT EXISTS idx_ad_earnings_amount ON ad_earnings_history(amount_earned);

-- =====================================================
-- 5. TRIGGERS PARA ATUALIZAÇÃO AUTOMÁTICA
-- =====================================================

-- Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Aplicar trigger nas tabelas
CREATE TRIGGER update_ad_videos_updated_at 
    BEFORE UPDATE ON ad_videos 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_ad_progress_updated_at 
    BEFORE UPDATE ON user_ad_progress 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ad_watch_sessions_updated_at 
    BEFORE UPDATE ON ad_watch_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 6. POLÍTICAS RLS (ROW LEVEL SECURITY)
-- =====================================================

-- Habilitar RLS nas tabelas
ALTER TABLE ad_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_ad_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_watch_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_earnings_history ENABLE ROW LEVEL SECURITY;

-- Políticas para ad_videos (todos podem ver vídeos ativos)
CREATE POLICY "Anyone can view active ad videos" ON ad_videos
    FOR SELECT USING (is_active = true);

-- Políticas para user_ad_progress (usuários só veem seus próprios dados)
CREATE POLICY "Users can view own ad progress" ON user_ad_progress
    FOR ALL USING (auth.uid() = user_id);

-- Políticas para ad_watch_sessions (usuários só veem suas próprias sessões)
CREATE POLICY "Users can view own watch sessions" ON ad_watch_sessions
    FOR ALL USING (auth.uid() = user_id);

-- Políticas para ad_earnings_history (usuários só veem seus próprios ganhos)
CREATE POLICY "Users can view own earnings history" ON ad_earnings_history
    FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- 7. FUNÇÕES UTILITÁRIAS
-- =====================================================

-- Função para obter estatísticas de um usuário
CREATE OR REPLACE FUNCTION get_user_ad_stats(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_earned', COALESCE(SUM(total_earned), 0),
        'total_videos_watched', COALESCE(SUM(videos_watched), 0),
        'days_active', COUNT(*) FILTER (WHERE videos_watched > 0),
        'average_daily_earnings', COALESCE(AVG(total_earned) FILTER (WHERE videos_watched > 0), 0),
        'last_activity', MAX(date)
    ) INTO result
    FROM user_ad_progress
    WHERE user_id = user_uuid;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para verificar se usuário pode assistir mais vídeos
CREATE OR REPLACE FUNCTION can_user_watch_more_ads(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    today_progress RECORD;
BEGIN
    SELECT * INTO today_progress
    FROM user_ad_progress
    WHERE user_id = user_uuid 
    AND date = CURRENT_DATE;
    
    -- Se não há registro para hoje, pode assistir
    IF NOT FOUND THEN
        RETURN true;
    END IF;
    
    -- Verificar se ainda pode assistir mais
    RETURN today_progress.can_watch_more;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para resetar progresso diário (executar via cron)
CREATE OR REPLACE FUNCTION reset_daily_ad_progress()
RETURNS INTEGER AS $$
DECLARE
    reset_count INTEGER;
BEGIN
    -- Resetar progresso para usuários que passaram do limite
    UPDATE user_ad_progress 
    SET can_watch_more = true
    WHERE date < CURRENT_DATE
    AND can_watch_more = false;
    
    GET DIAGNOSTICS reset_count = ROW_COUNT;
    
    RETURN reset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 8. DADOS DE EXEMPLO (VÍDEOS DE TESTE)
-- =====================================================

-- Inserir alguns vídeos de exemplo para teste
INSERT INTO ad_videos (title, description, video_url, thumbnail_url, duration, reward_value, category, advertiser, target_audience, min_watch_time) VALUES
('Novo iPhone 15 - Descubra as novidades', 'Conheça todas as funcionalidades do novo iPhone 15 da Apple', 'https://example.com/video1.mp4', 'https://example.com/thumb1.jpg', 30, 50, 'high_revenue', 'Apple', ARRAY['tecnologia', 'smartphones'], 24),

('Coca-Cola - Momentos Especiais', 'Compartilhe momentos especiais com Coca-Cola', 'https://example.com/video2.mp4', 'https://example.com/thumb2.jpg', 15, 25, 'medium_revenue', 'Coca-Cola', ARRAY['bebidas', 'lifestyle'], 12),

('Netflix - Novos Lançamentos', 'Descubra os novos filmes e séries da Netflix', 'https://example.com/video3.mp4', 'https://example.com/thumb3.jpg', 20, 30, 'medium_revenue', 'Netflix', ARRAY['entretenimento', 'streaming'], 16),

('McDonald\'s - Novo Big Mac', 'Experimente o novo Big Mac com ingredientes especiais', 'https://example.com/video4.mp4', 'https://example.com/thumb4.jpg', 25, 40, 'high_revenue', 'McDonald\'s', ARRAY['comida', 'fast-food'], 20),

('Banco Inter - Conta Digital', 'Abra sua conta digital gratuita no Banco Inter', 'https://example.com/video5.mp4', 'https://example.com/thumb5.jpg', 45, 75, 'high_revenue', 'Banco Inter', ARRAY['financeiro', 'banco'], 36),

('Shopee - Ofertas Imperdíveis', 'Encontre as melhores ofertas na Shopee', 'https://example.com/video6.mp4', 'https://example.com/thumb6.jpg', 10, 15, 'low_revenue', 'Shopee', ARRAY['e-commerce', 'compras'], 8),

('Uber - Viaje com Segurança', 'Conheça as medidas de segurança do Uber', 'https://example.com/video7.mp4', 'https://example.com/thumb7.jpg', 35, 45, 'medium_revenue', 'Uber', ARRAY['transporte', 'mobilidade'], 28),

('Nubank - Cartão sem Anuidade', 'Solicite seu cartão Nubank sem anuidade', 'https://example.com/video8.mp4', 'https://example.com/thumb8.jpg', 40, 60, 'high_revenue', 'Nubank', ARRAY['financeiro', 'cartão'], 32)

ON CONFLICT DO NOTHING;

-- =====================================================
-- 9. COMENTÁRIOS E DOCUMENTAÇÃO
-- =====================================================

COMMENT ON TABLE ad_videos IS 'Tabela de vídeos de anúncios disponíveis para visualização';
COMMENT ON TABLE user_ad_progress IS 'Progresso diário dos usuários no sistema de anúncios';
COMMENT ON TABLE ad_watch_sessions IS 'Sessões de visualização de vídeos pelos usuários';
COMMENT ON TABLE ad_earnings_history IS 'Histórico detalhado de ganhos dos usuários';

COMMENT ON COLUMN ad_videos.reward_value IS 'Valor da recompensa em centavos (100 = R$ 1,00)';
COMMENT ON COLUMN ad_videos.category IS 'Categoria do anúncio: high_revenue (alta receita), medium_revenue (média receita), low_revenue (baixa receita)';
COMMENT ON COLUMN user_ad_progress.daily_limit IS 'Limite diário de ganhos em centavos (1000 = R$ 10,00)';
COMMENT ON COLUMN ad_watch_sessions.watched_duration IS 'Duração assistida em segundos';

-- =====================================================
-- 10. GRANTS E PERMISSÕES
-- =====================================================

-- Garantir que usuários autenticados possam acessar as tabelas
GRANT SELECT ON ad_videos TO authenticated;
GRANT ALL ON user_ad_progress TO authenticated;
GRANT ALL ON ad_watch_sessions TO authenticated;
GRANT ALL ON ad_earnings_history TO authenticated;

-- Permitir execução das funções
GRANT EXECUTE ON FUNCTION get_user_ad_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION can_user_watch_more_ads(UUID) TO authenticated;
