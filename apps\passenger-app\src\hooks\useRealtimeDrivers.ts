import { useState, useEffect, useCallback, useRef } from 'react'
import { realtimeLocationService, DriverLocation, LocationUpdate } from '../services/RealtimeLocationService'

interface UseRealtimeDriversOptions {
  userLocation?: [number, number]
  radiusKm?: number
  autoConnect?: boolean
}

interface UseRealtimeDriversReturn {
  drivers: DriverLocation[]
  nearbyDrivers: DriverLocation[]
  isConnected: boolean
  isLoading: boolean
  error: string | null
  activeDriversCount: number
  connect: () => Promise<void>
  disconnect: () => Promise<void>
  getDriversNear: (lat: number, lng: number, radius?: number) => DriverLocation[]
  refreshDrivers: () => Promise<void>
}

export function useRealtimeDrivers(options: UseRealtimeDriversOptions = {}): UseRealtimeDriversReturn {
  const {
    userLocation,
    radiusKm = 5,
    autoConnect = true
  } = options

  const [drivers, setDrivers] = useState<DriverLocation[]>([])
  const [nearbyDrivers, setNearbyDrivers] = useState<DriverLocation[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeDriversCount, setActiveDriversCount] = useState(0)

  const hookId = useRef(`drivers-hook-${Date.now()}-${Math.random()}`)
  const locationHookId = useRef(`location-hook-${Date.now()}-${Math.random()}`)

  /**
   * Handle driver updates from the service
   */
  const handleDriversUpdate = useCallback((updatedDrivers: DriverLocation[]) => {
    console.log(`📱 Received ${updatedDrivers.length} driver updates`)
    setDrivers(updatedDrivers)
    setActiveDriversCount(updatedDrivers.length)

    // Filter nearby drivers if user location is available
    if (userLocation) {
      const nearby = updatedDrivers.filter(driver => {
        const distance = calculateDistance(
          userLocation[1], userLocation[0],
          driver.latitude, driver.longitude
        )
        return distance <= radiusKm && driver.is_available && driver.is_active
      })
      setNearbyDrivers(nearby)
      console.log(`📍 Found ${nearby.length} nearby drivers within ${radiusKm}km`)
    } else {
      setNearbyDrivers(updatedDrivers.filter(d => d.is_available && d.is_active))
    }

    setError(null)
  }, [userLocation, radiusKm])

  /**
   * Handle individual location updates
   */
  const handleLocationUpdate = useCallback((update: LocationUpdate) => {
    console.log(`📡 Location update for driver: ${update.driver_id}`)
    // The service already handles updating the drivers list
    // This is just for additional real-time feedback if needed
  }, [])

  /**
   * Connect to realtime service
   */
  const connect = useCallback(async () => {
    if (isConnected) return

    setIsLoading(true)
    setError(null)

    try {
      console.log('🔄 Connecting to realtime drivers...')
      
      // Initialize the service if not already done
      await realtimeLocationService.initialize()

      // Subscribe to driver updates
      realtimeLocationService.subscribeToDrivers(hookId.current, handleDriversUpdate)
      realtimeLocationService.subscribeToLocationUpdates(locationHookId.current, handleLocationUpdate)

      setIsConnected(true)
      console.log('✅ Connected to realtime drivers')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect to realtime drivers'
      console.error('❌ Error connecting to realtime drivers:', err)
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [isConnected, handleDriversUpdate, handleLocationUpdate])

  /**
   * Disconnect from realtime service
   */
  const disconnect = useCallback(async () => {
    if (!isConnected) return

    try {
      console.log('🔌 Disconnecting from realtime drivers...')
      
      realtimeLocationService.unsubscribe(hookId.current)
      realtimeLocationService.unsubscribe(locationHookId.current)

      setIsConnected(false)
      setDrivers([])
      setNearbyDrivers([])
      setActiveDriversCount(0)
      
      console.log('✅ Disconnected from realtime drivers')
    } catch (err) {
      console.error('❌ Error disconnecting from realtime drivers:', err)
    }
  }, [isConnected])

  /**
   * Get drivers near a specific location
   */
  const getDriversNear = useCallback((lat: number, lng: number, radius: number = radiusKm): DriverLocation[] => {
    return realtimeLocationService.getDriversNearLocation(lat, lng, radius)
  }, [radiusKm])

  /**
   * Refresh drivers manually
   */
  const refreshDrivers = useCallback(async () => {
    if (!isConnected) {
      await connect()
    } else {
      // Force a refresh by reconnecting
      await disconnect()
      await connect()
    }
  }, [isConnected, connect, disconnect])

  /**
   * Calculate distance between two points
   */
  const calculateDistance = useCallback((lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371 // Earth's radius in kilometers
    const dLat = toRadians(lat2 - lat1)
    const dLon = toRadians(lon2 - lon1)
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }, [])

  const toRadians = useCallback((degrees: number): number => {
    return degrees * (Math.PI / 180)
  }, [])

  /**
   * Auto-connect on mount if enabled
   */
  useEffect(() => {
    if (autoConnect) {
      connect()
    }

    // Cleanup on unmount
    return () => {
      if (isConnected) {
        realtimeLocationService.unsubscribe(hookId.current)
        realtimeLocationService.unsubscribe(locationHookId.current)
      }
    }
  }, [autoConnect]) // Only run on mount

  /**
   * Update nearby drivers when user location changes
   */
  useEffect(() => {
    if (userLocation && drivers.length > 0) {
      const nearby = drivers.filter(driver => {
        const distance = calculateDistance(
          userLocation[1], userLocation[0],
          driver.latitude, driver.longitude
        )
        return distance <= radiusKm && driver.is_available && driver.is_active
      })
      setNearbyDrivers(nearby)
      console.log(`📍 Updated nearby drivers: ${nearby.length} within ${radiusKm}km`)
    }
  }, [userLocation, drivers, radiusKm, calculateDistance])

  /**
   * Monitor connection status
   */
  useEffect(() => {
    const checkConnection = () => {
      const serviceConnected = realtimeLocationService.isConnectedToRealtime()
      if (serviceConnected !== isConnected) {
        setIsConnected(serviceConnected)
      }
    }

    const interval = setInterval(checkConnection, 5000) // Check every 5 seconds
    return () => clearInterval(interval)
  }, [isConnected])

  return {
    drivers,
    nearbyDrivers,
    isConnected,
    isLoading,
    error,
    activeDriversCount,
    connect,
    disconnect,
    getDriversNear,
    refreshDrivers
  }
}

export default useRealtimeDrivers
