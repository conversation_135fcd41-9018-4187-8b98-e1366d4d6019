/**
 * Sistema de Expansão Global para MobiDrive
 * Gerencia lançamento em novas cidades e países
 */

import { supabase } from '../lib/supabase'
import { analyticsService } from './AnalyticsService'
import { notificationService } from './NotificationService'

interface City {
  id: string
  name: string
  country: string
  population: number
  coordinates: { lat: number; lng: number }
  status: 'planned' | 'launching' | 'active' | 'paused'
  launch_date?: string
  market_potential: number
  competition_level: 'low' | 'medium' | 'high'
  regulatory_status: 'approved' | 'pending' | 'restricted'
}

interface ExpansionMetrics {
  total_cities: number
  active_cities: number
  planned_cities: number
  total_population_covered: number
  market_penetration: number
  revenue_by_city: Record<string, number>
  growth_rate_by_city: Record<string, number>
}

interface LaunchPlan {
  city_id: string
  phases: Array<{
    name: string
    duration_weeks: number
    objectives: string[]
    success_metrics: Record<string, number>
    budget: number
  }>
  total_budget: number
  expected_roi: number
  risk_factors: string[]
  mitigation_strategies: string[]
}

class ExpansionService {
  private cities: City[] = []
  private expansionMetrics: ExpansionMetrics | null = null

  constructor() {
    this.loadCities()
    this.loadExpansionMetrics()
  }

  // Carregar cidades
  async loadCities(): Promise<void> {
    try {
      const { data: cities, error } = await supabase
        .from('cities')
        .select('*')
        .order('population', { ascending: false })

      if (error) throw error

      this.cities = cities || []
      console.log('🌍 Cidades carregadas:', this.cities.length)
    } catch (error) {
      console.error('Erro ao carregar cidades:', error)
      // Fallback para dados mock
      this.cities = this.getMockCities()
    }
  }

  // Carregar métricas de expansão
  async loadExpansionMetrics(): Promise<void> {
    try {
      // Em produção, viria de queries complexas
      this.expansionMetrics = {
        total_cities: 47,
        active_cities: 23,
        planned_cities: 24,
        total_population_covered: 89456000,
        market_penetration: 12.7,
        revenue_by_city: {
          'sao-paulo': 45678900,
          'rio-janeiro': 23456700,
          'belo-horizonte': 12345600
        },
        growth_rate_by_city: {
          'sao-paulo': 23.4,
          'rio-janeiro': 18.7,
          'belo-horizonte': 31.2
        }
      }
    } catch (error) {
      console.error('Erro ao carregar métricas de expansão:', error)
    }
  }

  // Analisar potencial de mercado
  async analyzeMarketPotential(cityId: string): Promise<{
    score: number
    factors: Record<string, number>
    recommendation: 'high' | 'medium' | 'low'
    estimated_revenue: number
    launch_timeline: string
  }> {
    const city = this.cities.find(c => c.id === cityId)
    if (!city) throw new Error('Cidade não encontrada')

    // Algoritmo de análise de mercado
    const factors = {
      population_score: Math.min(city.population / 1000000 * 20, 30), // Max 30 pontos
      competition_score: city.competition_level === 'low' ? 25 : 
                        city.competition_level === 'medium' ? 15 : 5,
      regulatory_score: city.regulatory_status === 'approved' ? 20 :
                       city.regulatory_status === 'pending' ? 10 : 0,
      economic_score: Math.random() * 15 + 10, // Simulado
      infrastructure_score: Math.random() * 10 + 5 // Simulado
    }

    const totalScore = Object.values(factors).reduce((sum, score) => sum + score, 0)
    
    const recommendation = totalScore >= 70 ? 'high' : 
                          totalScore >= 50 ? 'medium' : 'low'

    const estimatedRevenue = city.population * 0.15 * 50 * 12 // Estimativa baseada em população

    const launchTimeline = recommendation === 'high' ? '3-6 meses' :
                          recommendation === 'medium' ? '6-12 meses' : '12+ meses'

    // Analytics
    analyticsService.track('market_analysis_performed', {
      city_id: cityId,
      city_name: city.name,
      total_score: totalScore,
      recommendation,
      estimated_revenue: estimatedRevenue
    })

    return {
      score: totalScore,
      factors,
      recommendation,
      estimated_revenue: estimatedRevenue,
      launch_timeline: launchTimeline
    }
  }

  // Criar plano de lançamento
  async createLaunchPlan(cityId: string): Promise<LaunchPlan> {
    const city = this.cities.find(c => c.id === cityId)
    if (!city) throw new Error('Cidade não encontrada')

    const marketAnalysis = await this.analyzeMarketPotential(cityId)

    const phases = [
      {
        name: 'Preparação e Regulamentação',
        duration_weeks: 8,
        objectives: [
          'Obter licenças necessárias',
          'Estabelecer parcerias locais',
          'Configurar infraestrutura técnica',
          'Recrutar equipe local'
        ],
        success_metrics: {
          licenses_obtained: 100,
          partnerships_signed: 5,
          team_hired: 15
        },
        budget: 500000
      },
      {
        name: 'Recrutamento de Motoristas',
        duration_weeks: 6,
        objectives: [
          'Recrutar 500 motoristas',
          'Treinamento e certificação',
          'Distribuir equipamentos',
          'Testes beta'
        ],
        success_metrics: {
          drivers_recruited: 500,
          drivers_certified: 450,
          beta_rides_completed: 1000
        },
        budget: 300000
      },
      {
        name: 'Lançamento Soft',
        duration_weeks: 4,
        objectives: [
          'Lançar para 10% da população',
          'Campanhas de marketing locais',
          'Monitorar métricas',
          'Ajustar operações'
        ],
        success_metrics: {
          users_acquired: city.population * 0.1 * 0.05, // 5% de 10% da população
          rides_completed: 5000,
          avg_rating: 4.5
        },
        budget: 800000
      },
      {
        name: 'Lançamento Completo',
        duration_weeks: 8,
        objectives: [
          'Expansão para toda a cidade',
          'Campanhas de marketing massivas',
          'Otimizar operações',
          'Estabelecer market share'
        ],
        success_metrics: {
          users_acquired: city.population * 0.15, // 15% da população
          market_share: 25,
          monthly_revenue: marketAnalysis.estimated_revenue / 12
        },
        budget: 1500000
      }
    ]

    const totalBudget = phases.reduce((sum, phase) => sum + phase.budget, 0)
    const expectedRoi = (marketAnalysis.estimated_revenue * 2) / totalBudget // ROI em 2 anos

    const riskFactors = [
      'Resistência regulatória',
      'Competição agressiva',
      'Baixa adoção inicial',
      'Problemas operacionais',
      'Questões de segurança'
    ]

    const mitigationStrategies = [
      'Engajamento proativo com reguladores',
      'Diferenciação por qualidade de serviço',
      'Campanhas de educação do mercado',
      'Equipe local experiente',
      'Investimento em tecnologia de segurança'
    ]

    const launchPlan: LaunchPlan = {
      city_id: cityId,
      phases,
      total_budget: totalBudget,
      expected_roi: expectedRoi,
      risk_factors: riskFactors,
      mitigation_strategies: mitigationStrategies
    }

    // Salvar plano no banco
    await this.saveLaunchPlan(launchPlan)

    return launchPlan
  }

  // Salvar plano de lançamento
  private async saveLaunchPlan(plan: LaunchPlan): Promise<void> {
    try {
      const { error } = await supabase
        .from('launch_plans')
        .upsert({
          city_id: plan.city_id,
          phases: plan.phases,
          total_budget: plan.total_budget,
          expected_roi: plan.expected_roi,
          risk_factors: plan.risk_factors,
          mitigation_strategies: plan.mitigation_strategies,
          created_at: new Date().toISOString()
        })

      if (error) throw error

      analyticsService.track('launch_plan_created', {
        city_id: plan.city_id,
        total_budget: plan.total_budget,
        expected_roi: plan.expected_roi,
        phases_count: plan.phases.length
      })
    } catch (error) {
      console.error('Erro ao salvar plano de lançamento:', error)
    }
  }

  // Executar lançamento
  async executeLaunch(cityId: string): Promise<void> {
    const city = this.cities.find(c => c.id === cityId)
    if (!city) throw new Error('Cidade não encontrada')

    try {
      // Atualizar status da cidade
      await supabase
        .from('cities')
        .update({
          status: 'launching',
          launch_date: new Date().toISOString()
        })
        .eq('id', cityId)

      // Notificar equipe
      notificationService.showNotification({
        title: '🚀 Lançamento Iniciado!',
        message: `MobiDrive está sendo lançado em ${city.name}`,
        type: 'success',
        priority: 'high'
      })

      // Analytics
      analyticsService.track('city_launch_started', {
        city_id: cityId,
        city_name: city.name,
        population: city.population
      })

      console.log(`🚀 Lançamento iniciado em ${city.name}`)
    } catch (error) {
      console.error('Erro ao executar lançamento:', error)
      throw error
    }
  }

  // Monitorar progresso do lançamento
  async monitorLaunchProgress(cityId: string): Promise<{
    current_phase: number
    completion_percentage: number
    metrics: Record<string, number>
    issues: string[]
    next_actions: string[]
  }> {
    // Simular dados de progresso
    const progress = {
      current_phase: 2,
      completion_percentage: 67,
      metrics: {
        drivers_recruited: 342,
        users_acquired: 1247,
        rides_completed: 3456,
        avg_rating: 4.6,
        revenue_generated: 45678
      },
      issues: [
        'Atraso no recrutamento de motoristas',
        'Baixa adoção em algumas regiões'
      ],
      next_actions: [
        'Intensificar campanhas de recrutamento',
        'Focar marketing em regiões específicas',
        'Ajustar preços para aumentar demanda'
      ]
    }

    analyticsService.track('launch_progress_monitored', {
      city_id: cityId,
      current_phase: progress.current_phase,
      completion_percentage: progress.completion_percentage
    })

    return progress
  }

  // Obter cidades prioritárias para expansão
  getPriorityCities(): City[] {
    return this.cities
      .filter(city => city.status === 'planned')
      .sort((a, b) => b.market_potential - a.market_potential)
      .slice(0, 10)
  }

  // Obter métricas de expansão
  getExpansionMetrics(): ExpansionMetrics | null {
    return this.expansionMetrics
  }

  // Dados mock para desenvolvimento
  private getMockCities(): City[] {
    return [
      {
        id: 'sao-paulo',
        name: 'São Paulo',
        country: 'Brasil',
        population: 12300000,
        coordinates: { lat: -23.5505, lng: -46.6333 },
        status: 'active',
        market_potential: 95,
        competition_level: 'high',
        regulatory_status: 'approved'
      },
      {
        id: 'rio-janeiro',
        name: 'Rio de Janeiro',
        country: 'Brasil',
        population: 6700000,
        coordinates: { lat: -22.9068, lng: -43.1729 },
        status: 'active',
        market_potential: 88,
        competition_level: 'high',
        regulatory_status: 'approved'
      },
      {
        id: 'belo-horizonte',
        name: 'Belo Horizonte',
        country: 'Brasil',
        population: 2500000,
        coordinates: { lat: -19.9167, lng: -43.9345 },
        status: 'launching',
        market_potential: 82,
        competition_level: 'medium',
        regulatory_status: 'approved'
      },
      {
        id: 'brasilia',
        name: 'Brasília',
        country: 'Brasil',
        population: 3000000,
        coordinates: { lat: -15.7942, lng: -47.8822 },
        status: 'planned',
        market_potential: 79,
        competition_level: 'medium',
        regulatory_status: 'pending'
      },
      {
        id: 'salvador',
        name: 'Salvador',
        country: 'Brasil',
        population: 2900000,
        coordinates: { lat: -12.9714, lng: -38.5014 },
        status: 'planned',
        market_potential: 76,
        competition_level: 'low',
        regulatory_status: 'approved'
      }
    ]
  }
}

// Singleton instance
export const expansionService = new ExpansionService()

export default expansionService
