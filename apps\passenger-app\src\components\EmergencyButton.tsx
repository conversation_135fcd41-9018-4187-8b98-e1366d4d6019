import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { AlertTriangle, Phone, Shield, MapPin, Clock, X } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContextSimple'

interface EmergencyButtonProps {
  rideId?: string
  driverInfo?: {
    id: string
    name: string
    phone: string
    vehicle: string
  }
  currentLocation?: {
    lat: number
    lng: number
    address: string
  }
}

export const EmergencyButton: React.FC<EmergencyButtonProps> = ({
  rideId,
  driverInfo,
  currentLocation
}) => {
  const { user } = useAuth()
  const [isEmergencyActive, setIsEmergencyActive] = useState(false)
  const [emergencyStep, setEmergencyStep] = useState<'button' | 'confirm' | 'active' | 'help'>('button')
  const [countdown, setCountdown] = useState(5)
  const [emergencyId, setEmergencyId] = useState<string | null>(null)

  // Countdown para cancelar emergência
  useEffect(() => {
    if (emergencyStep === 'confirm' && countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    } else if (emergencyStep === 'confirm' && countdown === 0) {
      activateEmergency()
    }
  }, [emergencyStep, countdown])

  const handleEmergencyPress = () => {
    setEmergencyStep('confirm')
    setCountdown(5)
  }

  const cancelEmergency = () => {
    setEmergencyStep('button')
    setCountdown(5)
  }

  const activateEmergency = async () => {
    try {
      setEmergencyStep('active')
      setIsEmergencyActive(true)

      // Criar evento de emergência no banco
      const emergencyData = {
        user_id: user?.id,
        ride_id: rideId,
        driver_id: driverInfo?.id,
        location: currentLocation ? `POINT(${currentLocation.lng} ${currentLocation.lat})` : null,
        address: currentLocation?.address,
        emergency_type: 'sos_button',
        status: 'active',
        metadata: {
          driver_info: driverInfo,
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent
        },
        created_at: new Date().toISOString()
      }

      const { data: emergency, error } = await supabase
        .from('emergency_events')
        .insert(emergencyData)
        .select()
        .single()

      if (error) {
        console.error('Erro ao criar evento de emergência:', error)
        return
      }

      setEmergencyId(emergency.id)

      // Notificar autoridades e contatos de emergência
      await notifyEmergencyContacts(emergency.id)

      // Iniciar gravação de áudio se disponível
      startEmergencyRecording()

      // Compartilhar localização em tempo real
      startLocationSharing()

      console.log('🚨 Emergência ativada:', emergency.id)
    } catch (error) {
      console.error('Erro ao ativar emergência:', error)
    }
  }

  const notifyEmergencyContacts = async (emergencyId: string) => {
    try {
      // Notificar central de emergência
      await supabase
        .from('notifications')
        .insert({
          user_id: 'emergency_center',
          title: '🚨 EMERGÊNCIA ATIVADA',
          message: `Usuário ${user?.email} ativou botão de emergência`,
          type: 'emergency',
          metadata: {
            emergency_id: emergencyId,
            user_id: user?.id,
            ride_id: rideId,
            location: currentLocation,
            driver_info: driverInfo
          }
        })

      // Notificar motorista se houver
      if (driverInfo?.id) {
        await supabase
          .from('notifications')
          .insert({
            user_id: driverInfo.id,
            title: '🚨 Emergência do Passageiro',
            message: 'O passageiro ativou o botão de emergência',
            type: 'emergency',
            metadata: {
              emergency_id: emergencyId,
              passenger_id: user?.id
            }
          })
      }

      console.log('✅ Contatos de emergência notificados')
    } catch (error) {
      console.error('Erro ao notificar contatos:', error)
    }
  }

  const startEmergencyRecording = () => {
    try {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ audio: true })
          .then(stream => {
            console.log('🎤 Gravação de emergência iniciada')
            // TODO: Implementar gravação e upload para Supabase Storage
          })
          .catch(error => {
            console.log('Erro ao iniciar gravação:', error)
          })
      }
    } catch (error) {
      console.log('Gravação não disponível:', error)
    }
  }

  const startLocationSharing = () => {
    if (navigator.geolocation) {
      const watchId = navigator.geolocation.watchPosition(
        async (position) => {
          if (emergencyId) {
            await supabase
              .from('emergency_locations')
              .insert({
                emergency_id: emergencyId,
                location: `POINT(${position.coords.longitude} ${position.coords.latitude})`,
                accuracy: position.coords.accuracy,
                timestamp: new Date().toISOString()
              })
          }
        },
        (error) => console.error('Erro ao obter localização:', error),
        { enableHighAccuracy: true, maximumAge: 0, timeout: 5000 }
      )

      // Salvar watchId para parar depois
      return () => navigator.geolocation.clearWatch(watchId)
    }
  }

  const resolveEmergency = async () => {
    if (emergencyId) {
      await supabase
        .from('emergency_events')
        .update({
          status: 'resolved',
          resolved_at: new Date().toISOString()
        })
        .eq('id', emergencyId)
    }

    setIsEmergencyActive(false)
    setEmergencyStep('button')
    setEmergencyId(null)
  }

  return (
    <AnimatePresence>
      {emergencyStep === 'button' && (
        <motion.button
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={handleEmergencyPress}
          className="fixed bottom-6 right-6 z-50 w-16 h-16 bg-red-500 hover:bg-red-600 rounded-full shadow-2xl flex items-center justify-center"
        >
          <Shield className="w-8 h-8 text-white" />
        </motion.button>
      )}

      {emergencyStep === 'confirm' && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4"
        >
          <div className="bg-white rounded-2xl p-6 max-w-sm w-full text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>

            <h3 className="text-xl font-bold text-gray-900 mb-2">
              Ativar Emergência?
            </h3>

            <p className="text-gray-600 mb-6">
              Isso irá notificar as autoridades e seus contatos de emergência
            </p>

            <div className="text-3xl font-bold text-red-500 mb-6">
              {countdown}
            </div>

            <div className="flex space-x-3">
              <button
                onClick={cancelEmergency}
                className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-colors"
              >
                Cancelar
              </button>

              <button
                onClick={activateEmergency}
                className="flex-1 bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-xl transition-colors"
              >
                Confirmar
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {emergencyStep === 'active' && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          className="fixed bottom-6 left-6 right-6 z-50 bg-red-500 text-white rounded-2xl p-4 shadow-2xl"
        >
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
              <span className="font-semibold">EMERGÊNCIA ATIVA</span>
            </div>
            <button
              onClick={resolveEmergency}
              className="bg-white/20 hover:bg-white/30 rounded-lg p-2"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          <p className="text-sm opacity-90 mb-3">
            Autoridades foram notificadas. Sua localização está sendo compartilhada.
          </p>

          <div className="flex space-x-2">
            <button className="flex-1 bg-white/20 hover:bg-white/30 rounded-lg py-2 px-3 text-sm font-medium flex items-center justify-center space-x-1">
              <Phone className="w-4 h-4" />
              <span>Ligar 190</span>
            </button>

            <button className="flex-1 bg-white/20 hover:bg-white/30 rounded-lg py-2 px-3 text-sm font-medium flex items-center justify-center space-x-1">
              <MapPin className="w-4 h-4" />
              <span>Compartilhar</span>
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default EmergencyButton
