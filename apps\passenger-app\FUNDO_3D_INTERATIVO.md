# Fundo 3D Interativo - MobiDrive

## 🚗 Descrição

Implementação de um fundo animado interativo com um carro 3D moderno preto que pode ser rotacionado através de drag-and-drop na tela de login do MobiDrive.

## ✨ Características

### Carro 3D Moderno
- **Design**: Carro preto moderno com detalhes realistas
- **Materiais**: Superfícies metálicas com reflexos
- **Componentes**: Carroceria, rodas, faróis, lanternas, para-brisas
- **Animação**: Flutuação sutil automática

### Interatividade
- **Drag & Drop**: Arraste o mouse para rotacionar o carro
- **Controles Intuitivos**: 
  - Cursor muda para "grab" quando disponível
  - Cursor muda para "grabbing" durante o arraste
- **Rotação Suave**: Movimento fluido e responsivo

### Iluminação Avançada
- **Ambiente HDR**: Reflexões realistas do ambiente urbano
- **Múltiplas Luzes**: 
  - Luz ambiente para iluminação geral
  - Luz direcional com sombras
  - Luz de preenchimento para detalhes
- **Sombras**: Sombras projetadas no chão

## 🛠️ Tecnologias Utilizadas

- **Three.js**: Engine 3D para renderização
- **React Three Fiber**: Integração React + Three.js
- **React Three Drei**: Utilitários e componentes prontos
- **Framer Motion**: Animações e transições
- **TypeScript**: Tipagem estática

## 📁 Estrutura de Arquivos

```
apps/passenger-app/src/components/
├── Car3D.tsx              # Componente principal do carro 3D
├── Car3DFallback.tsx      # Componente de fallback/loading
└── ...
```

## 🎮 Como Usar

### Interação Básica
1. **Visualizar**: O carro aparece automaticamente no fundo da tela de login
2. **Rotacionar**: Clique e arraste para rotacionar o carro em qualquer direção
3. **Soltar**: Solte o mouse para parar a rotação

### Controles
- **Mouse Down**: Inicia o modo de rotação
- **Mouse Move**: Rotaciona o carro baseado no movimento
- **Mouse Up**: Para a rotação

## 🔧 Configurações

### Performance
- **Otimização Vite**: Chunks separados para Three.js
- **Pre-bundling**: Dependências 3D otimizadas
- **Lazy Loading**: Carregamento sob demanda

### Fallbacks
- **ErrorBoundary**: Captura erros do Three.js
- **Suspense**: Loading durante carregamento
- **Fallback Component**: Animação 2D como backup

## 🎨 Personalização

### Cores do Carro
```typescript
// Em Car3D.tsx, linha ~20
<meshStandardMaterial 
  color="#1a1a1a"  // Cor principal (preto)
  metalness={0.9}   // Nível metálico
  roughness={0.1}   // Rugosidade
/>
```

### Iluminação
```typescript
// Luz ambiente
<ambientLight intensity={0.4} />

// Luz direcional
<directionalLight
  position={[10, 10, 5]}
  intensity={1}
  castShadow
/>
```

### Posição da Câmera
```typescript
// No Canvas
camera={{ position: [5, 3, 5], fov: 50 }}
```

## 🚀 Melhorias Futuras

### Funcionalidades Planejadas
- [ ] Múltiplos modelos de carro
- [ ] Cores personalizáveis
- [ ] Animações de entrada/saída
- [ ] Efeitos de partículas
- [ ] Som ambiente
- [ ] Modo noturno/diurno

### Otimizações
- [ ] LOD (Level of Detail)
- [ ] Instancing para múltiplos objetos
- [ ] Texture streaming
- [ ] WebGL2 features

## 🐛 Troubleshooting

### Problemas Comuns

**Carro não aparece:**
- Verifique se WebGL está habilitado no navegador
- Confirme se as dependências Three.js foram instaladas
- Verifique o console para erros

**Performance baixa:**
- Reduza a qualidade das sombras
- Diminua o `shadow-mapSize`
- Desabilite o Environment HDR

**Erro de importação:**
- Execute `npm install` para instalar dependências
- Verifique se o Vite está configurado corretamente

### Logs de Debug
```typescript
// Adicione no componente Car3D
console.log('Car3D mounted')
console.log('Rotation:', rotation)
```

## 📱 Compatibilidade

### Navegadores Suportados
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### Dispositivos
- ✅ Desktop (recomendado)
- ✅ Tablet
- ⚠️ Mobile (performance limitada)

## 📄 Licença

Este componente faz parte do projeto MobiDrive e segue a mesma licença do projeto principal.
