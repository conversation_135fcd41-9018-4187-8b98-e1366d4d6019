-- 🗄️ ESTRUTURA COMPLETA DO BANCO MOBIDRIVE
-- Criado automaticamente pelo sistema de melhorias

-- Extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- <PERSON><PERSON><PERSON> de perfis (j<PERSON> <PERSON>e, mas garantir estrutura)
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    full_name TEXT,
    phone TEXT,
    user_type TEXT CHECK (user_type IN ('passenger', 'driver', 'admin')) DEFAULT 'passenger',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ta<PERSON><PERSON> de solicitações de corrida
CREATE TABLE IF NOT EXISTS ride_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) NOT NULL,
    driver_id UUID REFERENCES profiles(id),
    origin_address TEXT NOT NULL,
    origin_coords POINT NOT NULL,
    destination_address TEXT NOT NULL,
    destination_coords POINT NOT NULL,
    distance DECIMAL(10,2),
    duration INTEGER,
    estimated_price DECIMAL(10,2) NOT NULL,
    final_price DECIMAL(10,2),
    status TEXT CHECK (status IN ('pending', 'accepted', 'in_progress', 'completed', 'cancelled')) DEFAULT 'pending',
    vehicle_type TEXT DEFAULT 'economy',
    payment_method TEXT DEFAULT 'cash',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE
);

-- Tabela de localizações de motoristas (melhorada)
CREATE TABLE IF NOT EXISTS driver_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) NOT NULL UNIQUE,
    location POINT NOT NULL,
    heading DECIMAL(5,2),
    speed DECIMAL(5,2),
    is_active BOOLEAN DEFAULT true,
    is_available BOOLEAN DEFAULT true,
    last_ping TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de motoristas (informações específicas)
CREATE TABLE IF NOT EXISTS drivers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) NOT NULL UNIQUE,
    vehicle_type TEXT DEFAULT 'economy',
    vehicle_make TEXT,
    vehicle_model TEXT,
    vehicle_year INTEGER,
    vehicle_color TEXT,
    license_plate TEXT,
    rating DECIMAL(3,2) DEFAULT 4.5,
    total_rides INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de localizações de usuários
CREATE TABLE IF NOT EXISTS user_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) NOT NULL,
    location POINT NOT NULL,
    accuracy DECIMAL(10,2),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de pagamentos
CREATE TABLE IF NOT EXISTS payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    ride_id UUID REFERENCES ride_requests(id) NOT NULL,
    user_id UUID REFERENCES profiles(id) NOT NULL,
    payment_method_id UUID,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'BRL',
    status TEXT CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'refunded')) DEFAULT 'pending',
    external_payment_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Tabela de métodos de pagamento
CREATE TABLE IF NOT EXISTS payment_methods (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) NOT NULL,
    type TEXT CHECK (type IN ('cash', 'credit_card', 'debit_card', 'pix')) NOT NULL,
    display_name TEXT NOT NULL,
    is_default BOOLEAN DEFAULT false,
    card_last_four TEXT,
    card_brand TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de notificações
CREATE TABLE IF NOT EXISTS notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT DEFAULT 'info',
    data JSONB,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de avaliações
CREATE TABLE IF NOT EXISTS ride_ratings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    ride_id UUID REFERENCES ride_requests(id) NOT NULL,
    rater_id UUID REFERENCES profiles(id) NOT NULL,
    rated_id UUID REFERENCES profiles(id) NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5) NOT NULL,
    comment TEXT,
    categories JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices geoespaciais para performance
CREATE INDEX IF NOT EXISTS idx_driver_locations_location ON driver_locations USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_user_locations_location ON user_locations USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_ride_requests_origin ON ride_requests USING GIST(origin_coords);
CREATE INDEX IF NOT EXISTS idx_ride_requests_destination ON ride_requests USING GIST(destination_coords);

-- Índices para queries frequentes
CREATE INDEX IF NOT EXISTS idx_ride_requests_user_status ON ride_requests(user_id, status);
CREATE INDEX IF NOT EXISTS idx_ride_requests_driver_status ON ride_requests(driver_id, status);
CREATE INDEX IF NOT EXISTS idx_driver_locations_active ON driver_locations(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_notifications_user_unread ON notifications(user_id, read_at);
CREATE INDEX IF NOT EXISTS idx_payments_ride ON payments(ride_id);

-- RLS (Row Level Security)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE ride_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE ride_ratings ENABLE ROW LEVEL SECURITY;

-- Políticas RLS básicas
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can view own rides" ON ride_requests;
CREATE POLICY "Users can view own rides" ON ride_requests FOR SELECT USING (auth.uid() = user_id OR auth.uid() = driver_id);

DROP POLICY IF EXISTS "Users can create rides" ON ride_requests;
CREATE POLICY "Users can create rides" ON ride_requests FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Drivers can update assigned rides" ON ride_requests;
CREATE POLICY "Drivers can update assigned rides" ON ride_requests FOR UPDATE USING (auth.uid() = driver_id OR auth.uid() = user_id);

-- Função para buscar motoristas próximos (melhorada)
CREATE OR REPLACE FUNCTION find_nearby_drivers(
    user_lat DECIMAL,
    user_lng DECIMAL,
    radius_km DECIMAL DEFAULT 5
)
RETURNS TABLE (
    driver_id UUID,
    driver_name TEXT,
    driver_phone TEXT,
    distance_km DECIMAL,
    location_lat DECIMAL,
    location_lng DECIMAL,
    heading DECIMAL,
    speed DECIMAL,
    is_available BOOLEAN,
    vehicle_type TEXT,
    rating DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        dl.user_id,
        p.full_name,
        p.phone,
        ST_Distance(
            ST_GeogFromText('POINT(' || user_lng || ' ' || user_lat || ')'),
            ST_GeogFromText('POINT(' || ST_X(dl.location) || ' ' || ST_Y(dl.location) || ')')
        ) / 1000 AS distance_km,
        ST_Y(dl.location) AS lat,
        ST_X(dl.location) AS lng,
        dl.heading,
        dl.speed,
        dl.is_active AND COALESCE(dl.is_available, true) AS is_available,
        COALESCE(d.vehicle_type, 'economy') AS vehicle_type,
        COALESCE(d.rating, 4.5) AS rating
    FROM driver_locations dl
    JOIN profiles p ON p.id = dl.user_id
    LEFT JOIN drivers d ON d.user_id = dl.user_id
    WHERE
        dl.is_active = true
        AND COALESCE(dl.is_available, true) = true
        AND p.user_type = 'driver'
        AND ST_DWithin(
            ST_GeogFromText('POINT(' || user_lng || ' ' || user_lat || ')'),
            ST_GeogFromText('POINT(' || ST_X(dl.location) || ' ' || ST_Y(dl.location) || ')'),
            radius_km * 1000
        )
        AND dl.updated_at > NOW() - INTERVAL '5 minutes' -- Only active drivers
    ORDER BY distance_km, d.rating DESC NULLS LAST
    LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- Função para criar uma nova corrida
CREATE OR REPLACE FUNCTION create_ride_request(
    p_user_id UUID,
    p_origin_address TEXT,
    p_origin_lat DECIMAL,
    p_origin_lng DECIMAL,
    p_destination_address TEXT,
    p_destination_lat DECIMAL,
    p_destination_lng DECIMAL,
    p_distance DECIMAL,
    p_duration INTEGER,
    p_estimated_price DECIMAL,
    p_vehicle_type TEXT DEFAULT 'economy',
    p_payment_method TEXT DEFAULT 'cash'
)
RETURNS UUID AS $$
DECLARE
    ride_id UUID;
BEGIN
    INSERT INTO ride_requests (
        user_id,
        origin_address,
        origin_coords,
        destination_address,
        destination_coords,
        distance,
        duration,
        estimated_price,
        vehicle_type,
        payment_method
    ) VALUES (
        p_user_id,
        p_origin_address,
        POINT(p_origin_lng, p_origin_lat),
        p_destination_address,
        POINT(p_destination_lng, p_destination_lat),
        p_distance,
        p_duration,
        p_estimated_price,
        p_vehicle_type,
        p_payment_method
    ) RETURNING id INTO ride_id;

    RETURN ride_id;
END;
$$ LANGUAGE plpgsql;

-- Função para atribuir motorista automaticamente
CREATE OR REPLACE FUNCTION assign_driver_to_ride(
    p_ride_id UUID,
    p_max_distance_km DECIMAL DEFAULT 10
)
RETURNS BOOLEAN AS $$
DECLARE
    ride_origin POINT;
    best_driver_id UUID;
    driver_distance DECIMAL;
    estimated_arrival INTEGER;
BEGIN
    -- Buscar origem da corrida
    SELECT origin_coords INTO ride_origin
    FROM ride_requests
    WHERE id = p_ride_id AND status = 'pending';

    IF ride_origin IS NULL THEN
        RETURN FALSE;
    END IF;

    -- Encontrar o melhor motorista disponível
    SELECT driver_id, distance_km INTO best_driver_id, driver_distance
    FROM find_nearby_drivers(
        ST_Y(ride_origin),
        ST_X(ride_origin),
        p_max_distance_km
    )
    WHERE is_available = true
    ORDER BY distance_km, rating DESC
    LIMIT 1;

    IF best_driver_id IS NULL THEN
        RETURN FALSE;
    END IF;

    -- Calcular ETA estimado (distância em km * 2 minutos por km + 2 minutos base)
    estimated_arrival := (driver_distance * 2 + 2)::INTEGER;

    -- Atribuir motorista à corrida
    UPDATE ride_requests SET
        driver_id = best_driver_id,
        status = 'accepted',
        accepted_at = NOW(),
        updated_at = NOW()
    WHERE id = p_ride_id;

    -- Marcar motorista como indisponível
    UPDATE driver_locations SET
        is_available = false,
        updated_at = NOW()
    WHERE user_id = best_driver_id;

    -- Inserir notificação para o motorista
    INSERT INTO notifications (user_id, title, message, type, data)
    VALUES (
        best_driver_id,
        'Nova Corrida Disponível',
        'Você foi selecionado para uma nova corrida',
        'ride_request',
        json_build_object(
            'ride_id', p_ride_id,
            'estimated_arrival', estimated_arrival,
            'distance_km', driver_distance
        )
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Tabela de mensagens de chat
CREATE TABLE IF NOT EXISTS chat_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    ride_id UUID REFERENCES ride_requests(id) NOT NULL,
    sender_id UUID REFERENCES profiles(id) NOT NULL,
    sender_type TEXT CHECK (sender_type IN ('passenger', 'driver')) NOT NULL,
    message TEXT NOT NULL,
    message_type TEXT CHECK (message_type IN ('text', 'location', 'image', 'system')) DEFAULT 'text',
    metadata JSONB,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de configurações de notificação
CREATE TABLE IF NOT EXISTS notification_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) NOT NULL UNIQUE,
    ride_updates BOOLEAN DEFAULT true,
    promotions BOOLEAN DEFAULT true,
    chat_messages BOOLEAN DEFAULT true,
    payment_updates BOOLEAN DEFAULT true,
    system_updates BOOLEAN DEFAULT true,
    push_enabled BOOLEAN DEFAULT true,
    email_enabled BOOLEAN DEFAULT false,
    sms_enabled BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de subscriptions push
CREATE TABLE IF NOT EXISTS push_subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) NOT NULL,
    subscription JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de sessões de analytics
CREATE TABLE IF NOT EXISTS analytics_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id TEXT NOT NULL UNIQUE,
    user_id UUID REFERENCES profiles(id),
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    page_views INTEGER DEFAULT 0,
    events_count INTEGER DEFAULT 0,
    duration INTEGER,
    referrer TEXT,
    utm_source TEXT,
    utm_medium TEXT,
    utm_campaign TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de eventos de analytics
CREATE TABLE IF NOT EXISTS analytics_events (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id),
    session_id TEXT REFERENCES analytics_sessions(session_id),
    event_name TEXT NOT NULL,
    event_category TEXT NOT NULL,
    properties JSONB,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    page_url TEXT,
    user_agent TEXT,
    device_info JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de métricas de performance
CREATE TABLE IF NOT EXISTS analytics_performance (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id),
    session_id TEXT REFERENCES analytics_sessions(session_id),
    metric_name TEXT NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    unit TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    page_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para chat
CREATE INDEX IF NOT EXISTS idx_chat_messages_ride ON chat_messages(ride_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sender ON chat_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created ON chat_messages(created_at);

-- Índices para analytics
CREATE INDEX IF NOT EXISTS idx_analytics_events_user ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_session ON analytics_events(session_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON analytics_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_performance_metric ON analytics_performance(metric_name);

-- RLS para novas tabelas
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_performance ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para chat
CREATE POLICY "Users can view chat messages from their rides" ON chat_messages FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM ride_requests
        WHERE id = chat_messages.ride_id
        AND (user_id = auth.uid() OR driver_id = auth.uid())
    )
);

CREATE POLICY "Users can send chat messages to their rides" ON chat_messages FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM ride_requests
        WHERE id = chat_messages.ride_id
        AND (user_id = auth.uid() OR driver_id = auth.uid())
    )
    AND sender_id = auth.uid()
);

-- Políticas RLS para configurações
CREATE POLICY "Users can view own notification settings" ON notification_settings FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own notification settings" ON notification_settings FOR ALL USING (auth.uid() = user_id);

-- Políticas RLS para push subscriptions
CREATE POLICY "Users can manage own push subscriptions" ON push_subscriptions FOR ALL USING (auth.uid() = user_id);

-- Políticas RLS para analytics (apenas próprios dados)
CREATE POLICY "Users can view own analytics sessions" ON analytics_sessions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create analytics sessions" ON analytics_sessions FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can view own analytics events" ON analytics_events FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create analytics events" ON analytics_events FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can view own performance metrics" ON analytics_performance FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create performance metrics" ON analytics_performance FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

-- Triggers para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_driver_locations_updated_at BEFORE UPDATE ON driver_locations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_settings_updated_at BEFORE UPDATE ON notification_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_push_subscriptions_updated_at BEFORE UPDATE ON push_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
