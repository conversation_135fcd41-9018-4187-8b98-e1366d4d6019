// =====================================================
// SCRIPT PARA TESTAR URLs ESPECÍFICAS DO MAPBOX
// Testa as URLs exatas que estão falhando nos logs
// =====================================================

const MAPBOX_TOKEN = 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'

async function testSpecificMapboxUrls() {
  console.log('🔍 Testando URLs específicas que estão falhando...')

  const urlsToTest = [
    // 1. Styles API (dark-v11 que está falhando)
    {
      name: 'Styles API - dark-v11',
      url: `https://api.mapbox.com/styles/v1/mapbox/dark-v11?sdk=js-3.12.0&access_token=${MAPBOX_TOKEN}`
    },
    // 2. Styles API - streets-v11 (alternativa)
    {
      name: 'Styles API - streets-v11',
      url: `https://api.mapbox.com/styles/v1/mapbox/streets-v11?access_token=${MAPBOX_TOKEN}`
    },
    // 3. Directions Matrix API
    {
      name: 'Directions Matrix API',
      url: `https://api.mapbox.com/directions-matrix/v1/mapbox/driving/-43.2087896,-12.1829372;-43.226789600000004,-12.1909372?access_token=${MAPBOX_TOKEN}&sources=0&destinations=0`
    },
    // 4. Geocoding API (São Paulo)
    {
      name: 'Geocoding API - São Paulo',
      url: `https://api.mapbox.com/geocoding/v5/mapbox.places/São Paulo.json?access_token=${MAPBOX_TOKEN}`
    },
    // 5. Directions API
    {
      name: 'Directions API',
      url: `https://api.mapbox.com/directions/v5/mapbox/driving/-46.6333,-23.5505;-46.6433,-23.5605?steps=true&geometries=geojson&access_token=${MAPBOX_TOKEN}`
    }
  ]

  for (const test of urlsToTest) {
    try {
      console.log(`\n🧪 Testando: ${test.name}`)
      console.log(`🔗 URL: ${test.url.substring(0, 100)}...`)
      
      const response = await fetch(test.url)
      
      console.log(`📊 Status: ${response.status} ${response.statusText}`)
      console.log(`📋 Headers:`)
      console.log(`  - Content-Type: ${response.headers.get('content-type')}`)
      console.log(`  - Content-Length: ${response.headers.get('content-length')}`)
      
      if (response.ok) {
        console.log('✅ Sucesso!')
        
        // Tentar ler o conteúdo
        try {
          const contentType = response.headers.get('content-type')
          if (contentType?.includes('application/json')) {
            const data = await response.json()
            console.log(`📄 Tipo de resposta: JSON`)
            console.log(`📊 Dados: ${JSON.stringify(data).substring(0, 200)}...`)
          } else {
            const text = await response.text()
            console.log(`📄 Tipo de resposta: ${contentType}`)
            console.log(`📊 Tamanho: ${text.length} caracteres`)
          }
        } catch (parseError) {
          console.log(`⚠️ Erro ao ler resposta: ${parseError.message}`)
        }
      } else {
        console.log('❌ Falhou!')
        
        try {
          const errorText = await response.text()
          console.log(`📄 Erro: ${errorText.substring(0, 500)}`)
        } catch (readError) {
          console.log(`💥 Erro ao ler erro: ${readError.message}`)
        }
      }
      
    } catch (error) {
      console.log(`💥 Erro de rede: ${error.message}`)
    }
  }

  // Teste adicional: verificar se o token está válido
  console.log('\n🔑 Verificando validade do token...')
  try {
    const tokenUrl = `https://api.mapbox.com/tokens/v2?access_token=${MAPBOX_TOKEN}`
    const tokenResponse = await fetch(tokenUrl)
    
    console.log(`📊 Token Status: ${tokenResponse.status}`)
    
    if (tokenResponse.ok) {
      const tokenData = await tokenResponse.json()
      console.log('✅ Token válido!')
      console.log(`📋 Scopes: ${tokenData.scopes?.join(', ') || 'N/A'}`)
      console.log(`📊 Usage: ${JSON.stringify(tokenData.usage || {})}`)
    } else {
      const errorText = await tokenResponse.text()
      console.log('❌ Token inválido!')
      console.log(`📄 Erro: ${errorText}`)
    }
  } catch (error) {
    console.log(`💥 Erro ao verificar token: ${error.message}`)
  }
}

// Executar teste
testSpecificMapboxUrls()
  .then(() => {
    console.log('\n🏁 Teste de URLs específicas concluído!')
  })
  .catch((error) => {
    console.error('\n💥 Erro fatal:', error)
  })
