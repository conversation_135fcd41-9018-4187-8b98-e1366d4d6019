-- =====================================================
-- DADOS DE TESTE PARA MOBIDRIVE
-- =====================================================
--
-- Execute este script APÓS o ride_requests.sql
-- para inserir dados de teste realistas
--
-- =====================================================

-- Inserir perfis de usuários de teste (motoristas)
INSERT INTO auth.users (id, email, created_at, updated_at) VALUES
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>', NOW(), NOW()),
  ('22222222-2222-2222-2222-222222222222', '<EMAIL>', NOW(), NOW()),
  ('33333333-3333-3333-3333-333333333333', '<EMAIL>', NOW(), NOW()),
  ('*************-4444-4444-************', '<EMAIL>', NOW(), NOW()),
  ('*************-5555-5555-************', '<EMAIL>', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Inserir localizações de motoristas em São Paulo
INSERT INTO driver_locations (user_id, location, heading, speed, is_active, is_available, updated_at) VALUES
  -- Centro de São Paulo
  ('11111111-1111-1111-1111-111111111111', POINT(-46.6333, -23.5505), 45.0, 0.0, true, true, NOW()),

  -- Vila Madalena
  ('22222222-2222-2222-2222-222222222222', POINT(-46.6875, -23.5447), 90.0, 15.0, true, true, NOW()),

  -- Moema
  ('33333333-3333-3333-3333-333333333333', POINT(-46.6634, -23.5928), 180.0, 0.0, true, true, NOW()),

  -- Pinheiros
  ('*************-4444-4444-************', POINT(-46.7019, -23.5629), 270.0, 25.0, true, true, NOW()),

  -- Itaim Bibi
  ('*************-5555-5555-************', POINT(-46.6784, -23.5751), 135.0, 10.0, true, true, NOW())
ON CONFLICT (user_id) DO UPDATE SET
  location = EXCLUDED.location,
  heading = EXCLUDED.heading,
  speed = EXCLUDED.speed,
  updated_at = NOW();

-- Inserir tabela de notificações se não existir
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT NOT NULL,
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'critical')),
  metadata JSONB,
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabela para analytics events
CREATE TABLE IF NOT EXISTS analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT NOT NULL,
  user_id UUID,
  session_id TEXT NOT NULL,
  properties JSONB,
  page_url TEXT,
  user_agent TEXT,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabela para métricas de performance
CREATE TABLE IF NOT EXISTS performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name TEXT NOT NULL,
  value DECIMAL(10,4) NOT NULL,
  unit TEXT NOT NULL,
  context JSONB,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabela para push subscriptions
CREATE TABLE IF NOT EXISTS push_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  endpoint TEXT NOT NULL,
  p256dh TEXT NOT NULL,
  auth TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, endpoint)
);

-- Índices para todas as novas tabelas
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority);

CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_session_id ON analytics_events(session_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON analytics_events(timestamp);

CREATE INDEX IF NOT EXISTS idx_performance_metrics_metric_name ON performance_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp);

CREATE INDEX IF NOT EXISTS idx_push_subscriptions_user_id ON push_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_is_active ON push_subscriptions(is_active);

-- RLS para todas as novas tabelas
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_subscriptions ENABLE ROW LEVEL SECURITY;

-- Políticas para notificações
CREATE POLICY "Users can view own notifications" ON notifications
  FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "System can insert notifications" ON notifications
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update own notifications" ON notifications
  FOR UPDATE USING (auth.uid()::text = user_id::text);

-- Políticas para analytics events
CREATE POLICY "Users can view own analytics" ON analytics_events
  FOR SELECT USING (auth.uid()::text = user_id::text OR user_id IS NULL);

CREATE POLICY "Anyone can insert analytics" ON analytics_events
  FOR INSERT WITH CHECK (true);

-- Políticas para performance metrics
CREATE POLICY "Anyone can insert performance metrics" ON performance_metrics
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view performance metrics" ON performance_metrics
  FOR SELECT USING (true); -- TODO: Restringir para admins

-- Políticas para push subscriptions
CREATE POLICY "Users can manage own push subscriptions" ON push_subscriptions
  FOR ALL USING (auth.uid() = user_id);

-- Tabela para ratings de corridas
CREATE TABLE IF NOT EXISTS ride_ratings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ride_id UUID NOT NULL REFERENCES ride_requests(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  driver_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  overall_rating INTEGER NOT NULL CHECK (overall_rating >= 1 AND overall_rating <= 5),
  driver_rating INTEGER NOT NULL CHECK (driver_rating >= 1 AND driver_rating <= 5),
  vehicle_rating INTEGER NOT NULL CHECK (vehicle_rating >= 1 AND vehicle_rating <= 5),
  route_rating INTEGER NOT NULL CHECK (route_rating >= 1 AND route_rating <= 5),
  safety_rating INTEGER NOT NULL CHECK (safety_rating >= 1 AND safety_rating <= 5),
  punctuality_rating INTEGER NOT NULL CHECK (punctuality_rating >= 1 AND punctuality_rating <= 5),
  communication_rating INTEGER NOT NULL CHECK (communication_rating >= 1 AND communication_rating <= 5),
  feedback_text TEXT,
  feedback_categories TEXT[],
  would_recommend BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(ride_id, user_id)
);

-- Tabela para perfis de motoristas (ratings agregados)
CREATE TABLE IF NOT EXISTS driver_profiles (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  average_rating DECIMAL(3,2) DEFAULT 0.00,
  total_ratings INTEGER DEFAULT 0,
  total_rides INTEGER DEFAULT 0,
  vehicle_info JSONB,
  license_info JSONB,
  is_verified BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabela para métodos de pagamento
CREATE TABLE IF NOT EXISTS payment_methods (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('credit_card', 'debit_card', 'pix', 'digital_wallet')),
  name TEXT NOT NULL,
  last_four TEXT,
  brand TEXT,
  expires_at DATE,
  is_default BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabela para pagamentos
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ride_id UUID NOT NULL REFERENCES ride_requests(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  payment_method_id UUID REFERENCES payment_methods(id) ON DELETE SET NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'BRL',
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'refunded')),
  transaction_id TEXT,
  gateway_response JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  processed_at TIMESTAMPTZ,
  failed_at TIMESTAMPTZ,
  refunded_at TIMESTAMPTZ
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_ride_ratings_ride_id ON ride_ratings(ride_id);
CREATE INDEX IF NOT EXISTS idx_ride_ratings_user_id ON ride_ratings(user_id);
CREATE INDEX IF NOT EXISTS idx_ride_ratings_driver_id ON ride_ratings(driver_id);
CREATE INDEX IF NOT EXISTS idx_ride_ratings_overall_rating ON ride_ratings(overall_rating);
CREATE INDEX IF NOT EXISTS idx_ride_ratings_created_at ON ride_ratings(created_at);

CREATE INDEX IF NOT EXISTS idx_driver_profiles_average_rating ON driver_profiles(average_rating);
CREATE INDEX IF NOT EXISTS idx_driver_profiles_total_ratings ON driver_profiles(total_ratings);
CREATE INDEX IF NOT EXISTS idx_driver_profiles_is_active ON driver_profiles(is_active);

CREATE INDEX IF NOT EXISTS idx_payment_methods_user_id ON payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_is_default ON payment_methods(is_default);
CREATE INDEX IF NOT EXISTS idx_payment_methods_is_active ON payment_methods(is_active);

CREATE INDEX IF NOT EXISTS idx_payments_ride_id ON payments(ride_id);
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- RLS para novas tabelas
ALTER TABLE ride_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- Políticas para ride_ratings
CREATE POLICY "Users can view ratings for their rides" ON ride_ratings
  FOR SELECT USING (
    auth.uid() = user_id OR
    auth.uid() = driver_id OR
    auth.uid() IN (SELECT user_id FROM ride_requests WHERE id = ride_ratings.ride_id) OR
    auth.uid() IN (SELECT driver_id FROM ride_requests WHERE id = ride_ratings.ride_id)
  );

CREATE POLICY "Users can create ratings for their rides" ON ride_ratings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Políticas para driver_profiles
CREATE POLICY "Anyone can view active driver profiles" ON driver_profiles
  FOR SELECT USING (is_active = true);

CREATE POLICY "Drivers can update own profile" ON driver_profiles
  FOR ALL USING (auth.uid() = user_id);

-- Políticas para payment_methods
CREATE POLICY "Users can manage own payment methods" ON payment_methods
  FOR ALL USING (auth.uid() = user_id);

-- Políticas para payments
CREATE POLICY "Users can view own payments" ON payments
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create payments for own rides" ON payments
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    auth.uid() IN (SELECT user_id FROM ride_requests WHERE id = payments.ride_id)
  );

-- Inserir dados de exemplo para contatos de emergência
-- (Será usado quando um usuário real se cadastrar)

-- Função para criar dados de teste para um usuário
CREATE OR REPLACE FUNCTION create_test_data_for_user(user_uuid UUID)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  -- Inserir contatos de emergência de exemplo
  INSERT INTO emergency_contacts (user_id, name, phone, relationship, is_emergency_contact, is_active) VALUES
    (user_uuid, 'Maria Silva', '+55 11 99999-1111', 'Mãe', true, true),
    (user_uuid, 'João Santos', '+55 11 99999-2222', 'Pai', true, true),
    (user_uuid, 'Ana Costa', '+55 11 99999-3333', 'Irmã', true, true)
  ON CONFLICT DO NOTHING;

  -- Inserir uma corrida de exemplo (histórico)
  INSERT INTO ride_requests (
    user_id,
    origin_address,
    origin_coords,
    destination_address,
    destination_coords,
    distance,
    duration,
    estimated_price,
    status,
    vehicle_type,
    created_at
  ) VALUES (
    user_uuid,
    'Av. Paulista, 1000 - Bela Vista, São Paulo - SP',
    POINT(-46.6520, -23.5618),
    'Shopping Eldorado - Pinheiros, São Paulo - SP',
    POINT(-46.6927, -23.5629),
    8.5,
    25,
    18.90,
    'completed',
    'economy',
    NOW() - INTERVAL '2 hours'
  ) ON CONFLICT DO NOTHING;
END;
$$;

-- Função para simular motoristas em movimento
CREATE OR REPLACE FUNCTION simulate_driver_movement()
RETURNS VOID
LANGUAGE plpgsql
AS $$
DECLARE
  driver_record RECORD;
  new_lat DECIMAL;
  new_lng DECIMAL;
  new_heading DECIMAL;
  new_speed DECIMAL;
BEGIN
  -- Atualizar localização de cada motorista ativo
  FOR driver_record IN
    SELECT user_id, ST_X(location) as lng, ST_Y(location) as lat, heading, speed
    FROM driver_locations
    WHERE is_active = true
  LOOP
    -- Simular movimento pequeno (máximo 0.001 graus = ~100m)
    new_lat := driver_record.lat + (random() - 0.5) * 0.001;
    new_lng := driver_record.lng + (random() - 0.5) * 0.001;
    new_heading := (driver_record.heading + (random() - 0.5) * 30) % 360;
    new_speed := GREATEST(0, driver_record.speed + (random() - 0.5) * 10);

    -- Atualizar localização
    UPDATE driver_locations
    SET
      location = POINT(new_lng, new_lat),
      heading = new_heading,
      speed = new_speed,
      updated_at = NOW()
    WHERE user_id = driver_record.user_id;
  END LOOP;
END;
$$;

-- Criar função para gerar ride request de teste
CREATE OR REPLACE FUNCTION create_test_ride_request(
  passenger_id UUID,
  origin_addr TEXT DEFAULT 'Av. Paulista, 1000 - São Paulo, SP',
  dest_addr TEXT DEFAULT 'Shopping Ibirapuera - São Paulo, SP'
)
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
  ride_id UUID;
  origin_point POINT := POINT(-46.6520, -23.5618); -- Paulista
  dest_point POINT := POINT(-46.6575, -23.5755);   -- Ibirapuera
BEGIN
  INSERT INTO ride_requests (
    user_id,
    origin_address,
    origin_coords,
    destination_address,
    destination_coords,
    distance,
    duration,
    estimated_price,
    status,
    vehicle_type
  ) VALUES (
    passenger_id,
    origin_addr,
    origin_point,
    dest_addr,
    dest_point,
    5.2,
    18,
    12.50,
    'pending',
    'economy'
  ) RETURNING id INTO ride_id;

  RETURN ride_id;
END;
$$;

-- Inserir alguns dados de ETA de exemplo
INSERT INTO eta_history (
  ride_id,
  driver_id,
  pickup_eta,
  trip_duration,
  total_time,
  distance,
  driver_distance,
  confidence,
  traffic_condition,
  driver_location
)
SELECT
  gen_random_uuid(), -- ride_id fictício
  '11111111-1111-1111-1111-111111111111',
  generate_series(3, 8), -- ETA de 3 a 8 minutos
  generate_series(15, 25), -- Duração de 15 a 25 minutos
  generate_series(18, 33), -- Tempo total
  5.2,
  1.5,
  generate_series(85, 95), -- Confiança de 85% a 95%
  'moderate',
  POINT(-46.6333, -23.5505)
FROM generate_series(1, 10);

-- Inserir dados de exemplo para trip shares
INSERT INTO trip_shares (
  ride_id,
  user_id,
  origin,
  destination,
  driver_info,
  estimated_arrival,
  is_active,
  expires_at
) VALUES (
  gen_random_uuid(),
  '11111111-1111-1111-1111-111111111111',
  'Av. Paulista, 1000 - São Paulo, SP',
  'Shopping Ibirapuera - São Paulo, SP',
  '{"name": "João Silva", "phone": "+55 11 99999-9999", "vehicle": "Honda Civic Prata", "plate": "ABC-1234"}',
  '15 minutos',
  true,
  NOW() + INTERVAL '24 hours'
);

-- Comentários para execução
-- =====================================================
-- INSTRUÇÕES DE USO:
-- =====================================================
--
-- 1. Execute o script ride_requests.sql primeiro
-- 2. Execute este script (test_data.sql)
-- 3. Para criar dados para um usuário específico:
--    SELECT create_test_data_for_user('seu-user-id-aqui');
--
-- 4. Para simular movimento dos motoristas:
--    SELECT simulate_driver_movement();
--
-- 5. Para criar uma corrida de teste:
--    SELECT create_test_ride_request('seu-user-id-aqui');
--
-- =====================================================
