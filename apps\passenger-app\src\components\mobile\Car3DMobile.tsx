import React, { useRef, useState, useEffect, Suspense } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Environment } from '@react-three/drei'
import * as THREE from 'three'

// 📱 VERSÃO MOBILE OTIMIZADA
// - Performance otimizada para dispositivos móveis
// - Fallback rápido sem texturas pesadas
// - Interface touch-friendly
// - Menor uso de recursos

// Componente 3D simplificado para mobile
const SimpleMobileCar: React.FC<{ manualRotation: [number, number, number] }> = ({ manualRotation }) => {
  const carRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (carRef.current) {
      const scrollY = window.scrollY || 0
      const maxScroll = Math.max(document.body.scrollHeight - window.innerHeight, 1)
      const scrollProgress = Math.min(scrollY / maxScroll, 1)

      carRef.current.rotation.x = manualRotation[0] + Math.sin(scrollProgress * Math.PI) * 0.1
      carRef.current.rotation.y = manualRotation[1] + scrollProgress * Math.PI * 2 + state.clock.elapsedTime * 0.02
      carRef.current.rotation.z = manualRotation[2]
      carRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={carRef} position={[0, 0, 0]} scale={[1, 1, 1]}>
      {/* Corpo principal do carro - Otimizado para mobile */}
      <mesh position={[0, 0.5, 0]} castShadow receiveShadow>
        <boxGeometry args={[4, 1, 2]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.7}
          roughness={0.3}
        />
      </mesh>

      {/* Teto do carro */}
      <mesh position={[0, 1.2, 0]} castShadow>
        <boxGeometry args={[3, 0.8, 1.8]} />
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.6}
          roughness={0.4}
        />
      </mesh>

      {/* Para-brisa frontal */}
      <mesh position={[1.3, 1.2, 0]} rotation={[0, 0, -0.2]} castShadow>
        <boxGeometry args={[0.8, 0.7, 1.6]} />
        <meshStandardMaterial
          color="#87CEEB"
          transparent
          opacity={0.3}
          metalness={0.1}
          roughness={0.1}
        />
      </mesh>

      {/* Rodas simplificadas */}
      {[
        [1.5, -0.2, 1.2],
        [1.5, -0.2, -1.2],
        [-1.5, -0.2, 1.2],
        [-1.5, -0.2, -1.2]
      ].map((position, index) => (
        <group key={index} position={position as [number, number, number]}>
          <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
            <cylinderGeometry args={[0.4, 0.4, 0.3]} />
            <meshStandardMaterial color="#1a1a1a" metalness={0.1} roughness={0.9} />
          </mesh>
        </group>
      ))}

      {/* Faróis simplificados */}
      <mesh position={[2.1, 0.4, 0.7]} castShadow>
        <sphereGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.5}
        />
      </mesh>
      <mesh position={[2.1, 0.4, -0.7]} castShadow>
        <sphereGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.5}
        />
      </mesh>

      {/* Logo MobiDrive simplificado */}
      <mesh position={[0, 0.8, 1.05]} rotation={[0, 0, 0]}>
        <circleGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#3B82F6"
          emissive="#3B82F6"
          emissiveIntensity={0.3}
        />
      </mesh>
      <mesh position={[0, 0.8, -1.05]} rotation={[0, Math.PI, 0]}>
        <circleGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#3B82F6"
          emissive="#3B82F6"
          emissiveIntensity={0.3}
        />
      </mesh>
    </group>
  )
}

// Loader simplificado para mobile
const MobileLoader: React.FC = () => {
  const loaderRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (loaderRef.current) {
      loaderRef.current.rotation.y = state.clock.elapsedTime * 1
    }
  })

  return (
    <group ref={loaderRef}>
      <mesh position={[0, 0, 0]}>
        <torusGeometry args={[0.8, 0.15, 6, 12]} />
        <meshStandardMaterial
          color="#3B82F6"
          emissive="#3B82F6"
          emissiveIntensity={0.4}
        />
      </mesh>
    </group>
  )
}

// Componente principal mobile
interface Car3DMobileProps {
  className?: string
}

export const Car3DMobile: React.FC<Car3DMobileProps> = ({ className = "" }) => {
  const [rotation, setRotation] = useState<[number, number, number]>([0, 0, 0])
  const [isDragging, setIsDragging] = useState(false)
  const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 })

  // Handlers para touch (mobile)
  const handleTouchStart = (event: React.TouchEvent) => {
    event.preventDefault()
    setIsDragging(true)
    const touch = event.touches[0]
    setLastPosition({ x: touch.clientX, y: touch.clientY })
  }

  const handleTouchMove = (event: React.TouchEvent) => {
    if (!isDragging) return
    event.preventDefault()

    const touch = event.touches[0]
    const deltaX = touch.clientX - lastPosition.x
    const deltaY = touch.clientY - lastPosition.y

    setRotation(prev => [
      prev[0] + deltaY * 0.01,
      prev[1] + deltaX * 0.01,
      prev[2]
    ])

    setLastPosition({ x: touch.clientX, y: touch.clientY })
  }

  const handleTouchEnd = () => {
    setIsDragging(false)
  }

  return (
    <div
      className={`relative w-full h-full ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <Canvas
        shadows
        camera={{ position: [4, 3, 4], fov: 60 }}
        style={{
          background: 'transparent',
          width: '100%',
          height: '100%'
        }}
        gl={{
          antialias: false,
          alpha: true,
          powerPreference: "low-power"
        }}
        dpr={[1, 1.5]} // Menor DPR para mobile
      >
        {/* Iluminação simplificada para mobile */}
        <ambientLight intensity={0.5} />
        <directionalLight
          position={[5, 5, 5]}
          intensity={1}
          castShadow
        />

        {/* Ambiente simplificado */}
        <Environment preset="sunset" background={false} />

        {/* Carro com Suspense */}
        <Suspense fallback={<MobileLoader />}>
          <SimpleMobileCar manualRotation={rotation} />
        </Suspense>

        {/* Chão simplificado */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -0.5, 0]} receiveShadow>
          <planeGeometry args={[15, 15]} />
          <meshStandardMaterial
            color="#1a1a1a"
            transparent
            opacity={0.1}
          />
        </mesh>
      </Canvas>

      {/* Status mobile */}
      <div className="absolute top-2 right-2 bg-black/50 text-white p-1 rounded text-xs">
        📱 Mobile
      </div>

      {/* Instruções touch */}
      <div className="absolute bottom-2 left-2 bg-black/50 text-white p-2 rounded text-xs">
        👆 Toque e arraste para rotacionar
      </div>
    </div>
  )
}

export default Car3DMobile
