@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Configurações globais de zoom e viewport */
html {
  zoom: 100%;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  zoom: 100%;
  transform-origin: 0 0;
}

/* MobiDrive Advanced Design System */
:root {
  /* Primary Colors - Blue Gradient */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-950: #172554;

  /* Gradient Colors */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Advanced Shadows */
  --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-large: 0 10px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.15);

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode variables */
@media (prefers-color-scheme: dark) {
  :root {
    --glass-bg: rgba(0, 0, 0, 0.08);
    --glass-border: rgba(255, 255, 255, 0.08);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--font-primary);
  font-weight: 400;
  line-height: 1.6;
  color: #0f172a;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Advanced Component Classes */
@layer components {
  /* Glassmorphism Card */
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-normal);
  }

  .glass-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow), var(--shadow-glow);
  }

  /* Modern Button Styles */
  .btn-modern {
    @apply relative overflow-hidden font-semibold transition-all duration-300 transform;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-soft);
  }

  .btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
  }

  .btn-modern:active {
    transform: translateY(0);
  }

  .btn-primary-modern {
    @apply btn-modern bg-gradient-to-r from-blue-600 to-blue-700 text-white;
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
  }

  .btn-primary-modern:hover {
    background-position: right center;
  }

  /* Floating Action Button */
  .fab {
    @apply fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-lg;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    z-index: 1000;
  }

  .fab:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-large);
  }

  /* Advanced Input Styles */
  .input-modern {
    @apply w-full px-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl;
    @apply text-gray-900 placeholder-gray-500 transition-all duration-300;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent;
  }

  /* Card with Hover Effects */
  .card-interactive {
    @apply bg-white rounded-2xl p-6 shadow-soft transition-all duration-300;
    @apply hover:shadow-medium hover:-translate-y-1;
  }

  /* Status Indicators */
  .status-online {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
    @apply bg-green-100 text-green-800 border border-green-200;
  }

  .status-offline {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
    @apply bg-gray-100 text-gray-800 border border-gray-200;
  }

  /* Loading States */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  .shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
}

/* Advanced Animations */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Utility Classes */
@layer utilities {
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
  }

  .animate-pulse-soft {
    animation: pulse 2s ease-in-out infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-success {
    background: var(--gradient-success);
  }

  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }

  .shadow-large {
    box-shadow: var(--shadow-large);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }
}
