/* 📱 DEVICE WRAPPER CSS - MOCKUP IPHONE */

.device-wrapper-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.device-wrapper-background {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.device-iphone-container {
  position: relative;
  width: 375px;
  height: 812px;
  transform: scale(0.8);
}

.device-iphone-shadow {
  position: absolute;
  top: 20px;
  left: 20px;
  right: -20px;
  bottom: -20px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50px;
  filter: blur(30px);
  z-index: 1;
}

.device-iphone-body {
  position: relative;
  width: 100%;
  height: 100%;
  background: #1a1a1a;
  border-radius: 45px;
  padding: 8px;
  z-index: 2;
}

.device-iphone-frame {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 37px;
  overflow: hidden;
  position: relative;
}

.device-iphone-screen {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 37px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.device-notch {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 30px;
  background: #000;
  border-radius: 0 0 20px 20px;
  z-index: 10;
}

.device-notch-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
}

.device-notch-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.device-camera-left,
.device-camera-right {
  width: 6px;
  height: 6px;
  background: #333;
  border-radius: 50%;
}

.device-speaker {
  width: 50px;
  height: 4px;
  background: #333;
  border-radius: 2px;
}

.device-status-bar {
  height: 44px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: transparent;
  position: relative;
  z-index: 5;
}

.device-status-content {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.device-status-left {
  display: flex;
  align-items: center;
}

.device-time {
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.device-status-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.device-signal {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  height: 12px;
}

.device-signal-bar {
  width: 3px;
  background: white;
  border-radius: 1px;
}

.device-signal-1 { height: 3px; }
.device-signal-2 { height: 6px; }
.device-signal-3 { height: 9px; }
.device-signal-4 { height: 12px; }

.device-wifi {
  width: 15px;
  height: 15px;
  color: white;
}

.device-battery {
  display: flex;
  align-items: center;
  gap: 1px;
}

.device-battery-body {
  width: 22px;
  height: 11px;
  border: 1px solid white;
  border-radius: 2px;
  position: relative;
}

.device-battery-level {
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background: white;
  border-radius: 1px;
  width: 80%;
}

.device-battery-tip {
  width: 2px;
  height: 6px;
  background: white;
  border-radius: 0 1px 1px 0;
}

.device-content-area {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.device-touch-container {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.device-home-indicator {
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-home-bar {
  width: 134px;
  height: 5px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

/* Botões físicos */
.device-button-volume-1,
.device-button-volume-2,
.device-button-power {
  position: absolute;
  background: #333;
  border-radius: 2px;
}

.device-button-volume-1 {
  left: -3px;
  top: 120px;
  width: 3px;
  height: 30px;
}

.device-button-volume-2 {
  left: -3px;
  top: 160px;
  width: 3px;
  height: 30px;
}

.device-button-power {
  right: -3px;
  top: 140px;
  width: 3px;
  height: 60px;
}

/* Badges informativos */
.device-info-badge-left,
.device-info-badge-right {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 12px;
  color: white;
  font-size: 12px;
  max-width: 200px;
  z-index: 3;
}

.device-info-badge-left {
  left: 20px;
}

.device-info-badge-right {
  right: 20px;
}

.device-badge-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.device-badge-title {
  font-weight: 600;
  font-size: 10px;
  opacity: 0.7;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.device-badge-row {
  display: flex;
  align-items: center;
  gap: 6px;
}

.device-badge-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.device-badge-green { background: #10b981; }
.device-badge-blue { background: #3b82f6; }
.device-badge-purple { background: #8b5cf6; }

.device-badge-text {
  font-size: 11px;
  font-weight: 500;
}

.device-badge-subtitle {
  font-size: 10px;
  opacity: 0.6;
}

/* Mobile real */
.device-content-mobile {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Responsivo */
@media (max-height: 900px) {
  .device-iphone-container {
    transform: scale(0.7);
  }
}

@media (max-height: 700px) {
  .device-iphone-container {
    transform: scale(0.6);
  }
}
