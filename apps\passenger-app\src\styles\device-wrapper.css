/* 📱 DEVICE WRAPPER - ZOOM FIXO GLOBAL 100% */

/* 🔒 FORÇA ZOOM 100% GLOBAL - MÁXIMA PRIORIDADE */
/* Impede que o zoom do browser afete qualquer elemento */
html, body {
  zoom: 1 !important;
  transform: scale(1) !important;
  transform-origin: top left !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;

  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;

  /* Previne zoom por CSS */
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}

/* FORÇA ZOOM 100% EM TODOS OS ELEMENTOS */
* {
  zoom: 1 !important;
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}

/* ===== CONTAINER PRINCIPAL ===== */
.device-wrapper-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;

  /* ZOOM FIXO NO CONTAINER */
  zoom: 1 !important;
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;

  /* Previne zoom */
  touch-action: manipulation !important;
  user-select: none !important;
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}

/* ===== BACKGROUND DO DESKTOP ===== */
.device-wrapper-background {
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #000000 100%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 32px !important;

  /* ZOOM FIXO NO BACKGROUND */
  zoom: 1 !important;
  transform: scale(1) !important;
}

/* ===== CONTAINER DO IPHONE ===== */
.device-iphone-container {
  position: relative !important;

  /* 📱 MOCKUP ZOOM 75% - TAMANHO REDUZIDO PARA DESKTOP */
  zoom: 1 !important;
  transform: scale(0.75) !important;
  -webkit-transform: scale(0.75) !important;
  -moz-transform: scale(0.75) !important;
  -ms-transform: scale(0.75) !important;
  -o-transform: scale(0.75) !important;
  transform-origin: center center !important;
}

/* ===== SOMBRA DO DISPOSITIVO ===== */
.device-iphone-shadow {
  position: absolute !important;
  inset: 0 !important;
  background: rgba(0, 0, 0, 0.2) !important;
  filter: blur(40px) !important;
  transform: translateY(16px) scale(1.05) !important;
  border-radius: 48px !important;

  /* ZOOM FIXO NA SOMBRA */
  zoom: 1 !important;
}

/* ===== CORPO DO IPHONE ===== */
.device-iphone-body {
  position: relative !important;
  background: #000000 !important;
  border-radius: 48px !important;
  padding: 8px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5) !important;

  /* ZOOM FIXO NO CORPO */
  zoom: 1 !important;
  transform: scale(1) !important;
}

/* ===== MOLDURA INTERNA ===== */
.device-iphone-frame {
  background: #1f2937 !important;
  border-radius: 40px !important;
  padding: 4px !important;

  /* ZOOM FIXO NA MOLDURA */
  zoom: 1 !important;
  transform: scale(1) !important;
}

/* ===== TELA DO IPHONE ===== */
.device-iphone-screen {
  position: relative !important;
  background: #000000 !important;
  border-radius: 32px !important;
  overflow: hidden !important;

  /* DIMENSÕES FIXAS DO IPHONE */
  width: 375px !important;
  height: 812px !important;

  /* ZOOM FIXO NA TELA */
  zoom: 1 !important;
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* ===== NOTCH ===== */
.device-notch {
  position: absolute !important;
  top: 0 !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 50 !important;
  zoom: 1 !important;
}

.device-notch-content {
  background: #000000 !important;
  border-radius: 0 0 16px 16px !important;
  padding: 24px 4px !important;
  zoom: 1 !important;
}

.device-notch-inner {
  width: 128px !important;
  height: 24px !important;
  background: #000000 !important;
  border-radius: 0 0 12px 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  zoom: 1 !important;
}

.device-camera-left, .device-camera-right {
  width: 8px !important;
  height: 8px !important;
  background: #374151 !important;
  border-radius: 50% !important;
  zoom: 1 !important;
}

.device-speaker {
  width: 48px !important;
  height: 6px !important;
  background: #374151 !important;
  border-radius: 3px !important;
  zoom: 1 !important;
}

/* ===== STATUS BAR ===== */
.device-status-bar {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 40 !important;
  padding: 12px 24px 0 24px !important;
  zoom: 1 !important;
}

.device-status-content {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  color: white !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  zoom: 1 !important;
}

.device-status-left, .device-status-right {
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  zoom: 1 !important;
}

.device-time {
  color: white !important;
  font-size: 14px !important;
  zoom: 1 !important;
}

.device-signal {
  display: flex !important;
  align-items: flex-end !important;
  gap: 2px !important;
  height: 16px !important;
  zoom: 1 !important;
}

.device-signal-bar {
  background: white !important;
  border-radius: 2px !important;
  zoom: 1 !important;
}

.device-signal-1 { width: 4px !important; height: 4px !important; }
.device-signal-2 { width: 4px !important; height: 8px !important; }
.device-signal-3 { width: 4px !important; height: 12px !important; }
.device-signal-4 { width: 4px !important; height: 16px !important; }

.device-wifi {
  width: 16px !important;
  height: 16px !important;
  color: white !important;
  zoom: 1 !important;
}

.device-battery {
  display: flex !important;
  align-items: center !important;
  gap: 2px !important;
  zoom: 1 !important;
}

.device-battery-body {
  width: 24px !important;
  height: 12px !important;
  border: 1px solid white !important;
  border-radius: 2px !important;
  position: relative !important;
  zoom: 1 !important;
}

.device-battery-level {
  width: 16px !important;
  height: 6px !important;
  background: white !important;
  border-radius: 1px !important;
  position: absolute !important;
  top: 2px !important;
  left: 2px !important;
  zoom: 1 !important;
}

.device-battery-tip {
  width: 2px !important;
  height: 6px !important;
  background: white !important;
  border-radius: 0 1px 1px 0 !important;
  zoom: 1 !important;
}

/* ===== ÁREA DE CONTEÚDO - LIVRE COM TOUCH GESTURES ===== */
.device-content-area {
  position: absolute !important;
  top: 48px !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 34px !important; /* Espaço para home indicator */

  /* NÃO APLICA ZOOM FIXO AQUI - CONTEÚDO LIVRE */
  /* zoom: auto; */
  /* transform: auto; */
}

/* ===== TOUCH SCROLL CONTAINER ===== */
.device-touch-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.touch-scroll-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  /* Previne seleção de texto durante drag */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

.touch-scroll-content {
  width: 100%;
  min-height: 100%;
  position: relative;

  /* Smooth rendering para animações */
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;

  /* Otimizações de performance */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* ===== CONTEÚDO MOBILE REAL ===== */
.device-content-mobile {
  width: 100% !important;
  height: 100% !important;
  /* NÃO APLICA ZOOM FIXO - MOBILE REAL */
}

/* ===== HOME INDICATOR ===== */
.device-home-indicator {
  position: absolute !important;
  bottom: 8px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 50 !important;
  zoom: 1 !important;
}

.device-home-bar {
  width: 128px !important;
  height: 4px !important;
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: 2px !important;
  zoom: 1 !important;
}

/* ===== BOTÕES FÍSICOS ===== */
.device-button-volume-1 {
  position: absolute !important;
  left: 0 !important;
  top: 128px !important;
  width: 4px !important;
  height: 32px !important;
  background: #374151 !important;
  border-radius: 2px 0 0 2px !important;
  zoom: 1 !important;
}

.device-button-volume-2 {
  position: absolute !important;
  left: 0 !important;
  top: 176px !important;
  width: 4px !important;
  height: 32px !important;
  background: #374151 !important;
  border-radius: 2px 0 0 2px !important;
  zoom: 1 !important;
}

.device-button-power {
  position: absolute !important;
  right: 0 !important;
  top: 144px !important;
  width: 4px !important;
  height: 48px !important;
  background: #374151 !important;
  border-radius: 0 2px 2px 0 !important;
  zoom: 1 !important;
}

/* ===== BADGES INFORMATIVOS ===== */
.device-info-badge-left {
  position: absolute !important;
  bottom: 32px !important;
  left: 32px !important;
  color: rgba(255, 255, 255, 0.6) !important;
  font-size: 14px !important;
  zoom: 1 !important;
}

.device-info-badge-right {
  position: absolute !important;
  bottom: 32px !important;
  right: 32px !important;
  color: rgba(255, 255, 255, 0.6) !important;
  font-size: 14px !important;
  zoom: 1 !important;
}

.device-badge-content {
  background: rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(12px) !important;
  border-radius: 8px !important;
  padding: 16px !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 8px !important;
  zoom: 1 !important;
}

.device-badge-row {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  zoom: 1 !important;
}

.device-badge-indicator {
  border-radius: 50% !important;
  zoom: 1 !important;
}

.device-badge-green {
  width: 12px !important;
  height: 12px !important;
  background: #10b981 !important;
  animation: pulse 2s infinite !important;
}

.device-badge-blue {
  width: 8px !important;
  height: 8px !important;
  background: #3b82f6 !important;
}

.device-badge-purple {
  width: 8px !important;
  height: 8px !important;
  background: #8b5cf6 !important;
}

.device-badge-title {
  font-size: 12px !important;
  color: rgba(255, 255, 255, 0.6) !important;
  margin-bottom: 8px !important;
  zoom: 1 !important;
}

.device-badge-subtitle {
  font-size: 12px !important;
  color: rgba(255, 255, 255, 0.4) !important;
  zoom: 1 !important;
}

.device-badge-text {
  font-size: 12px !important;
  zoom: 1 !important;
}

/* ===== ANIMAÇÕES ===== */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
