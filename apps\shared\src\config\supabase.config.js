/**
 * Configuração centralizada do Supabase para todos os aplicativos MobiDrive
 * 
 * Este módulo fornece uma configuração unificada do Supabase que pode ser
 * importada e usada por qualquer aplicativo no monorepo.
 */

// Detectar se estamos no navegador ou no servidor
const isBrowser = typeof window !== 'undefined';

// Obter variáveis de ambiente com valores de fallback
const getEnv = (key, fallback) => {
  if (isBrowser) {
    // No navegador, tentar obter do import.meta.env (Vite)
    if (import.meta && import.meta.env && import.meta.env[key]) {
      return import.meta.env[key];
    }
    
    // Tentar obter de window.env (injetado no HTML)
    if (window.env && window.env[key]) {
      return window.env[key];
    }
  }
  
  // Tentar obter de process.env (Node.js)
  if (typeof process !== 'undefined' && process.env && process.env[key]) {
    return process.env[key];
  }
  
  // Retornar valor de fallback
  return fallback;
};

// URL e chave do Supabase
export const SUPABASE_URL = getEnv(
  'VITE_SUPABASE_URL', 
  'https://udquhavmgqtpkubrfzdm.supabase.co'
);

export const SUPABASE_ANON_KEY = getEnv(
  'VITE_SUPABASE_ANON_KEY', 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'
);

// Configurações de autenticação otimizadas
export const AUTH_CONFIG = {
  autoRefreshToken: true,
  persistSession: true,
  storage: isBrowser ? localStorage : null,
  detectSessionInUrl: false,
  storageKey: 'mobidrive-auth-token',
  flowType: 'pkce',
  debug: process.env.NODE_ENV === 'development'
};

// Definição das tabelas comuns
export const TABLES = {
  USERS: 'users',
  PROFILES: 'profiles',
  RIDES: 'rides',
  DRIVERS: 'drivers',
  DRIVER_LOCATIONS: 'driver_locations',
  RIDE_RATINGS: 'ride_ratings',
  CHAT_MESSAGES: 'chat_messages',
  NOTIFICATIONS: 'notifications',
  APP_SETTINGS: 'app_settings',
  PAYMENTS: 'payments',
  PAYMENT_METHODS: 'payment_methods'
};

// Configurações de armazenamento
export const STORAGE_BUCKETS = {
  PROFILE_IMAGES: 'profile_images',
  RIDE_RECORDINGS: 'ride_recordings',
  DOCUMENTS: 'documents'
};

// Configurações de realtime
export const REALTIME_CONFIG = {
  AUTOCONNECT: false, // Conectar apenas quando necessário para economizar recursos
  HEALTH_CHECK_INTERVAL: 30000, // 30 segundos
  MAX_RECONNECT_ATTEMPTS: 5
};

// Configurações de cache
export const CACHE_CONFIG = {
  ENABLED: true,
  STORAGE_PREFIX: 'mobidrive-cache',
  DEFAULT_TTL: 300000, // 5 minutos
  MAX_ITEMS: 100
};

// Configurações de retry
export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  INITIAL_DELAY: 1000, // 1 segundo
  MAX_DELAY: 30000, // 30 segundos
  BACKOFF_FACTOR: 2
};

// Configurações globais para o cliente Supabase
export const GLOBAL_CONFIG = {
  headers: {
    'X-Client-Info': 'MobiDrive/1.0.0',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  },
  fetch: isBrowser ? window.fetch.bind(window) : undefined
};

// Configuração completa para criar o cliente Supabase
export const SUPABASE_CONFIG = {
  url: SUPABASE_URL,
  key: SUPABASE_ANON_KEY,
  auth: AUTH_CONFIG,
  global: GLOBAL_CONFIG,
  realtime: {
    autoconnect: REALTIME_CONFIG.AUTOCONNECT
  },
  db: {
    schema: 'public'
  }
};

// Exportar configuração completa
export default SUPABASE_CONFIG;
