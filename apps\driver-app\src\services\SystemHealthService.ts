import { supabase } from '../lib/supabase';
import { integrationTestService, IntegrationTestReport } from './IntegrationTestService';
import { simpleNotificationService } from './SimpleNotificationService';
import { analyticsService } from './AnalyticsService';
import { MemoryCleanup, createSafeInterval } from '../utils/MemoryCleanup';

export interface HealthCheck {
  component: string;
  status: 'healthy' | 'degraded' | 'down';
  lastCheck: string;
  responseTime: number;
  errorCount: number;
  details?: any;
}

export interface SystemStatus {
  overall: 'healthy' | 'degraded' | 'down';
  components: HealthCheck[];
  lastFullCheck: string;
  uptime: number;
  activeUsers: number;
  criticalIssues: string[];
  recommendations: string[];
}

export interface AutoFixResult {
  issue: string;
  attempted: boolean;
  success: boolean;
  message: string;
  details?: any;
}

export class SystemHealthService {
  private static instance: SystemHealthService;
  private healthChecks: Map<string, HealthCheck> = new Map();
  private startTime: number = Date.now();
  private autoFixEnabled: boolean = true;

  static getInstance(): SystemHealthService {
    if (!SystemHealthService.instance) {
      SystemHealthService.instance = new SystemHealthService();
    }
    return SystemHealthService.instance;
  }

  constructor() {
    this.startPeriodicHealthChecks();
  }

  // Iniciar verificações periódicas de saúde (com cleanup automático)
  private startPeriodicHealthChecks() {
    // Verificação rápida a cada 30 segundos
    createSafeInterval(() => {
      this.quickHealthCheck();
    }, 30000);

    // Verificação completa a cada 5 minutos
    createSafeInterval(() => {
      this.fullHealthCheck();
    }, 300000);

    // Primeira verificação imediata
    setTimeout(() => this.fullHealthCheck(), 5000);
  }

  // Verificação rápida de componentes críticos
  async quickHealthCheck(): Promise<void> {
    const checks = [
      this.checkDatabaseHealth(),
      this.checkAuthHealth(),
      this.checkRealtimeHealth()
    ];

    const results = await Promise.allSettled(checks);
    
    results.forEach((result, index) => {
      const componentNames = ['database', 'auth', 'realtime'];
      if (result.status === 'fulfilled') {
        this.healthChecks.set(componentNames[index], result.value);
      }
    });
  }

  // Verificação completa do sistema
  async fullHealthCheck(): Promise<SystemStatus> {
    console.log('🏥 Executando verificação completa de saúde do sistema...');

    const startTime = Date.now();
    
    // Executar testes de integração
    const integrationReport = await integrationTestService.runFullIntegrationTest();
    
    // Verificações individuais de componentes
    const componentChecks = await Promise.allSettled([
      this.checkDatabaseHealth(),
      this.checkAuthHealth(),
      this.checkRealtimeHealth(),
      this.checkMapboxHealth(),
      this.checkNotificationHealth(),
      this.checkAnalyticsHealth(),
      this.checkPWAHealth(),
      this.checkCacheHealth()
    ]);

    const components: HealthCheck[] = [];
    const componentNames = ['database', 'auth', 'realtime', 'mapbox', 'notifications', 'analytics', 'pwa', 'cache'];

    componentChecks.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        components.push(result.value);
        this.healthChecks.set(componentNames[index], result.value);
      } else {
        components.push({
          component: componentNames[index],
          status: 'down',
          lastCheck: new Date().toISOString(),
          responseTime: 0,
          errorCount: 1,
          details: { error: result.reason }
        });
      }
    });

    // Determinar status geral
    const downComponents = components.filter(c => c.status === 'down').length;
    const degradedComponents = components.filter(c => c.status === 'degraded').length;
    
    let overall: 'healthy' | 'degraded' | 'down';
    if (downComponents > 2) {
      overall = 'down';
    } else if (downComponents > 0 || degradedComponents > 2) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    // Identificar problemas críticos
    const criticalIssues: string[] = [];
    const recommendations: string[] = [];

    if (integrationReport.failed > 0) {
      criticalIssues.push(`${integrationReport.failed} testes de integração falharam`);
      recommendations.push('Executar correções automáticas de integração');
    }

    if (downComponents > 0) {
      criticalIssues.push(`${downComponents} componentes offline`);
      recommendations.push('Verificar conectividade e configurações');
    }

    // Executar correções automáticas se habilitado
    if (this.autoFixEnabled && (criticalIssues.length > 0 || integrationReport.failed > 0)) {
      await this.attemptAutoFix(integrationReport, components);
    }

    const systemStatus: SystemStatus = {
      overall,
      components,
      lastFullCheck: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      activeUsers: await this.getActiveUsersCount(),
      criticalIssues,
      recommendations
    };

    // Registrar no analytics
    analyticsService.track('system_health_check', {
      overall_status: overall,
      components_down: downComponents,
      components_degraded: degradedComponents,
      critical_issues: criticalIssues.length,
      check_duration: Date.now() - startTime
    });

    console.log(`🏥 Verificação concluída: ${overall} (${Date.now() - startTime}ms)`);
    
    return systemStatus;
  }

  // Verificar saúde do banco de dados
  private async checkDatabaseHealth(): Promise<HealthCheck> {
    const startTime = Date.now();
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);

      const responseTime = Date.now() - startTime;

      if (error) {
        return {
          component: 'database',
          status: 'down',
          lastCheck: new Date().toISOString(),
          responseTime,
          errorCount: 1,
          details: { error: error.message }
        };
      }

      return {
        component: 'database',
        status: responseTime > 1000 ? 'degraded' : 'healthy',
        lastCheck: new Date().toISOString(),
        responseTime,
        errorCount: 0
      };
    } catch (error) {
      return {
        component: 'database',
        status: 'down',
        lastCheck: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        errorCount: 1,
        details: { error: String(error) }
      };
    }
  }

  // Verificar saúde da autenticação
  private async checkAuthHealth(): Promise<HealthCheck> {
    const startTime = Date.now();
    
    try {
      const { data, error } = await supabase.auth.getSession();
      const responseTime = Date.now() - startTime;

      return {
        component: 'auth',
        status: error ? 'degraded' : 'healthy',
        lastCheck: new Date().toISOString(),
        responseTime,
        errorCount: error ? 1 : 0,
        details: error ? { error: error.message } : { hasSession: !!data.session }
      };
    } catch (error) {
      return {
        component: 'auth',
        status: 'down',
        lastCheck: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        errorCount: 1,
        details: { error: String(error) }
      };
    }
  }

  // Verificar saúde do realtime (desabilitado)
  private async checkRealtimeHealth(): Promise<HealthCheck> {
    const startTime = Date.now();

    // Realtime desabilitado para evitar stack overflow
    return {
      component: 'realtime',
      status: 'disabled',
      lastCheck: new Date().toISOString(),
      responseTime: Date.now() - startTime,
      errorCount: 0,
      details: { message: 'Realtime desabilitado para estabilidade' }
    };
  }

  // Verificar saúde do Mapbox
  private async checkMapboxHealth(): Promise<HealthCheck> {
    const startTime = Date.now();
    
    try {
      // Teste simples de geocoding
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/test.json?access_token=${import.meta.env.VITE_MAPBOX_ACCESS_TOKEN}&limit=1`
      );
      
      const responseTime = Date.now() - startTime;
      
      if (!response.ok) {
        return {
          component: 'mapbox',
          status: 'down',
          lastCheck: new Date().toISOString(),
          responseTime,
          errorCount: 1,
          details: { httpStatus: response.status }
        };
      }

      return {
        component: 'mapbox',
        status: responseTime > 2000 ? 'degraded' : 'healthy',
        lastCheck: new Date().toISOString(),
        responseTime,
        errorCount: 0
      };
    } catch (error) {
      return {
        component: 'mapbox',
        status: 'down',
        lastCheck: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        errorCount: 1,
        details: { error: String(error) }
      };
    }
  }

  // Verificar saúde das notificações
  private async checkNotificationHealth(): Promise<HealthCheck> {
    const startTime = Date.now();
    
    try {
      const permission = Notification.permission;
      const hasServiceWorker = 'serviceWorker' in navigator;
      
      let status: 'healthy' | 'degraded' | 'down';
      if (permission === 'granted' && hasServiceWorker) {
        status = 'healthy';
      } else if (permission !== 'denied') {
        status = 'degraded';
      } else {
        status = 'down';
      }

      return {
        component: 'notifications',
        status,
        lastCheck: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        errorCount: status === 'down' ? 1 : 0,
        details: { permission, hasServiceWorker }
      };
    } catch (error) {
      return {
        component: 'notifications',
        status: 'down',
        lastCheck: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        errorCount: 1,
        details: { error: String(error) }
      };
    }
  }

  // Verificar saúde do analytics
  private async checkAnalyticsHealth(): Promise<HealthCheck> {
    const startTime = Date.now();
    
    try {
      const stats = analyticsService.getSessionStats();
      
      return {
        component: 'analytics',
        status: 'healthy',
        lastCheck: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        errorCount: 0,
        details: stats
      };
    } catch (error) {
      return {
        component: 'analytics',
        status: 'down',
        lastCheck: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        errorCount: 1,
        details: { error: String(error) }
      };
    }
  }

  // Verificar saúde do PWA
  private async checkPWAHealth(): Promise<HealthCheck> {
    const startTime = Date.now();
    
    try {
      const hasServiceWorker = 'serviceWorker' in navigator;
      let swActive = false;
      
      if (hasServiceWorker) {
        const registration = await navigator.serviceWorker.getRegistration();
        swActive = !!registration?.active;
      }

      const status = hasServiceWorker && swActive ? 'healthy' : 'degraded';

      return {
        component: 'pwa',
        status,
        lastCheck: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        errorCount: status === 'degraded' ? 1 : 0,
        details: { hasServiceWorker, swActive }
      };
    } catch (error) {
      return {
        component: 'pwa',
        status: 'down',
        lastCheck: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        errorCount: 1,
        details: { error: String(error) }
      };
    }
  }

  // Verificar saúde do cache
  private async checkCacheHealth(): Promise<HealthCheck> {
    const startTime = Date.now();
    
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        const hasAppCache = cacheNames.some(name => 
          name.includes('mobidrive') || name.includes('passenger-app')
        );

        return {
          component: 'cache',
          status: hasAppCache ? 'healthy' : 'degraded',
          lastCheck: new Date().toISOString(),
          responseTime: Date.now() - startTime,
          errorCount: hasAppCache ? 0 : 1,
          details: { cacheCount: cacheNames.length, hasAppCache }
        };
      } else {
        return {
          component: 'cache',
          status: 'degraded',
          lastCheck: new Date().toISOString(),
          responseTime: Date.now() - startTime,
          errorCount: 1,
          details: { error: 'Cache API not supported' }
        };
      }
    } catch (error) {
      return {
        component: 'cache',
        status: 'down',
        lastCheck: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        errorCount: 1,
        details: { error: String(error) }
      };
    }
  }

  // Obter contagem de usuários ativos
  private async getActiveUsersCount(): Promise<number> {
    try {
      // Implementação simplificada - em produção seria mais complexa
      const { data: { user } } = await supabase.auth.getUser();
      return user ? 1 : 0;
    } catch {
      return 0;
    }
  }

  // Tentar correções automáticas
  private async attemptAutoFix(
    integrationReport: IntegrationTestReport, 
    components: HealthCheck[]
  ): Promise<AutoFixResult[]> {
    console.log('🔧 Tentando correções automáticas...');
    
    const fixes: AutoFixResult[] = [];

    // Correção para problemas de cache
    const cacheComponent = components.find(c => c.component === 'cache');
    if (cacheComponent?.status !== 'healthy') {
      fixes.push(await this.fixCacheIssues());
    }

    // Correção para problemas de Service Worker
    const pwaComponent = components.find(c => c.component === 'pwa');
    if (pwaComponent?.status !== 'healthy') {
      fixes.push(await this.fixServiceWorkerIssues());
    }

    // Correção para problemas de notificações
    const notificationComponent = components.find(c => c.component === 'notifications');
    if (notificationComponent?.status === 'degraded') {
      fixes.push(await this.fixNotificationIssues());
    }

    return fixes;
  }

  // Corrigir problemas de cache
  private async fixCacheIssues(): Promise<AutoFixResult> {
    try {
      if ('caches' in window) {
        // Limpar caches antigos
        const cacheNames = await caches.keys();
        const oldCaches = cacheNames.filter(name => 
          name.includes('old') || name.includes('v0')
        );
        
        await Promise.all(oldCaches.map(name => caches.delete(name)));
        
        return {
          issue: 'Cache Issues',
          attempted: true,
          success: true,
          message: `Limpou ${oldCaches.length} caches antigos`,
          details: { deletedCaches: oldCaches }
        };
      } else {
        return {
          issue: 'Cache Issues',
          attempted: false,
          success: false,
          message: 'Cache API não suportada'
        };
      }
    } catch (error) {
      return {
        issue: 'Cache Issues',
        attempted: true,
        success: false,
        message: `Erro ao corrigir cache: ${error}`,
        details: { error }
      };
    }
  }

  // Corrigir problemas de Service Worker
  private async fixServiceWorkerIssues(): Promise<AutoFixResult> {
    try {
      if ('serviceWorker' in navigator) {
        // Tentar registrar novamente o Service Worker
        await navigator.serviceWorker.register('/sw.js');
        
        return {
          issue: 'Service Worker Issues',
          attempted: true,
          success: true,
          message: 'Service Worker re-registrado com sucesso'
        };
      } else {
        return {
          issue: 'Service Worker Issues',
          attempted: false,
          success: false,
          message: 'Service Worker não suportado'
        };
      }
    } catch (error) {
      return {
        issue: 'Service Worker Issues',
        attempted: true,
        success: false,
        message: `Erro ao corrigir Service Worker: ${error}`,
        details: { error }
      };
    }
  }

  // Corrigir problemas de notificações
  private async fixNotificationIssues(): Promise<AutoFixResult> {
    try {
      const permission = await simpleNotificationService.requestPermission();
      
      return {
        issue: 'Notification Issues',
        attempted: true,
        success: permission,
        message: permission ? 'Permissão concedida' : 'Permissão negada pelo usuário'
      };
    } catch (error) {
      return {
        issue: 'Notification Issues',
        attempted: true,
        success: false,
        message: `Erro ao solicitar permissão: ${error}`,
        details: { error }
      };
    }
  }

  // Obter status atual do sistema
  getSystemStatus(): SystemStatus | null {
    const components = Array.from(this.healthChecks.values());
    
    if (components.length === 0) {
      return null;
    }

    const downComponents = components.filter(c => c.status === 'down').length;
    const degradedComponents = components.filter(c => c.status === 'degraded').length;
    
    let overall: 'healthy' | 'degraded' | 'down';
    if (downComponents > 2) {
      overall = 'down';
    } else if (downComponents > 0 || degradedComponents > 2) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    return {
      overall,
      components,
      lastFullCheck: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      activeUsers: 0, // Será atualizado na próxima verificação completa
      criticalIssues: [],
      recommendations: []
    };
  }

  // Habilitar/desabilitar correções automáticas
  setAutoFixEnabled(enabled: boolean): void {
    this.autoFixEnabled = enabled;
    console.log(`🔧 Correções automáticas ${enabled ? 'habilitadas' : 'desabilitadas'}`);
  }
}

export const systemHealthService = SystemHealthService.getInstance();
