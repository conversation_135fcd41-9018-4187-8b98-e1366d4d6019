import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Clock, CheckCircle, Car, MapPin, AlertCircle, Phone, MessageCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContextSimple'

interface RideStatusManagerProps {
  rideId?: string
  onStatusChange?: (status: RideStatus) => void
}

interface RideStatus {
  id: string
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled'
  driver_id?: string
  driver_info?: {
    name: string
    phone: string
    vehicle: string
    rating: number
    photo?: string
  }
  origin_address: string
  destination_address: string
  estimated_price: number
  created_at: string
  accepted_at?: string
  eta?: number
}

export const RideStatusManager: React.FC<RideStatusManagerProps> = ({
  rideId,
  onStatusChange
}) => {
  const { user } = useAuth()
  const [currentRide, setCurrentRide] = useState<RideStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Buscar ride atual
  useEffect(() => {
    if (!user?.id) return

    const fetchCurrentRide = async () => {
      try {
        setIsLoading(true)
        
        // Buscar ride ativa do usuário
        const { data: rides, error } = await supabase
          .from('ride_requests')
          .select(`
            *,
            profiles!ride_requests_driver_id_fkey(full_name, phone)
          `)
          .eq('user_id', user.id)
          .in('status', ['pending', 'accepted', 'in_progress'])
          .order('created_at', { ascending: false })
          .limit(1)

        if (error) {
          console.error('Erro ao buscar ride:', error)
          setError('Erro ao carregar informações da corrida')
          return
        }

        if (rides && rides.length > 0) {
          const ride = rides[0]
          const rideStatus: RideStatus = {
            id: ride.id,
            status: ride.status,
            driver_id: ride.driver_id,
            driver_info: ride.profiles ? {
              name: ride.profiles.full_name || 'Motorista',
              phone: ride.profiles.phone || '',
              vehicle: 'Veículo',
              rating: 4.8,
              photo: undefined
            } : undefined,
            origin_address: ride.origin_address,
            destination_address: ride.destination_address,
            estimated_price: ride.estimated_price,
            created_at: ride.created_at,
            accepted_at: ride.accepted_at
          }

          setCurrentRide(rideStatus)
          onStatusChange?.(rideStatus)
        } else {
          setCurrentRide(null)
        }
      } catch (error) {
        console.error('Erro ao buscar ride:', error)
        setError('Erro inesperado')
      } finally {
        setIsLoading(false)
      }
    }

    fetchCurrentRide()
  }, [user?.id, onStatusChange])

  // Monitorar mudanças em tempo real
  useEffect(() => {
    if (!user?.id) return

    const channel = supabase
      .channel(`ride-updates:${user.id}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ride_requests',
          filter: `user_id=eq.${user.id}`
        },
        async (payload) => {
          console.log('🔄 Ride update:', payload)
          
          if (payload.eventType === 'UPDATE') {
            const updatedRide = payload.new as any
            
            // Buscar informações do motorista se foi aceita
            if (updatedRide.status === 'accepted' && updatedRide.driver_id) {
              const { data: driverProfile } = await supabase
                .from('profiles')
                .select('full_name, phone')
                .eq('id', updatedRide.driver_id)
                .single()

              const rideStatus: RideStatus = {
                id: updatedRide.id,
                status: updatedRide.status,
                driver_id: updatedRide.driver_id,
                driver_info: driverProfile ? {
                  name: driverProfile.full_name || 'Motorista',
                  phone: driverProfile.phone || '',
                  vehicle: 'Veículo',
                  rating: 4.8
                } : undefined,
                origin_address: updatedRide.origin_address,
                destination_address: updatedRide.destination_address,
                estimated_price: updatedRide.estimated_price,
                created_at: updatedRide.created_at,
                accepted_at: updatedRide.accepted_at
              }

              setCurrentRide(rideStatus)
              onStatusChange?.(rideStatus)
            }
          }
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [user?.id, onStatusChange])

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          icon: <Clock className="w-6 h-6 text-blue-500" />,
          title: 'Procurando motorista...',
          description: 'Aguarde enquanto encontramos um motorista próximo',
          color: 'from-blue-500 to-blue-600'
        }
      case 'accepted':
        return {
          icon: <CheckCircle className="w-6 h-6 text-green-500" />,
          title: 'Motorista encontrado!',
          description: 'Seu motorista está a caminho',
          color: 'from-green-500 to-green-600'
        }
      case 'in_progress':
        return {
          icon: <Car className="w-6 h-6 text-purple-500" />,
          title: 'Corrida em andamento',
          description: 'Você está a caminho do destino',
          color: 'from-purple-500 to-purple-600'
        }
      case 'completed':
        return {
          icon: <CheckCircle className="w-6 h-6 text-green-500" />,
          title: 'Corrida finalizada',
          description: 'Obrigado por usar o MobiDrive!',
          color: 'from-green-500 to-green-600'
        }
      default:
        return {
          icon: <AlertCircle className="w-6 h-6 text-gray-500" />,
          title: 'Status desconhecido',
          description: '',
          color: 'from-gray-500 to-gray-600'
        }
    }
  }

  const cancelRide = async () => {
    if (!currentRide) return

    try {
      const { error } = await supabase
        .from('ride_requests')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString()
        })
        .eq('id', currentRide.id)

      if (error) {
        console.error('Erro ao cancelar corrida:', error)
        return
      }

      setCurrentRide(null)
    } catch (error) {
      console.error('Erro ao cancelar corrida:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-lg">
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          <span className="text-gray-600">Carregando informações da corrida...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-lg">
        <div className="flex items-center space-x-3 text-red-600">
          <AlertCircle className="w-6 h-6" />
          <span>{error}</span>
        </div>
      </div>
    )
  }

  if (!currentRide) {
    return null
  }

  const statusInfo = getStatusInfo(currentRide.status)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-2xl p-6 shadow-lg"
    >
      {/* Header */}
      <div className="flex items-center space-x-4 mb-4">
        <motion.div
          className={`w-12 h-12 bg-gradient-to-r ${statusInfo.color} rounded-full flex items-center justify-center`}
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          {statusInfo.icon}
        </motion.div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">{statusInfo.title}</h3>
          <p className="text-gray-600">{statusInfo.description}</p>
        </div>
      </div>

      {/* Trip Details */}
      <div className="space-y-3 mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-gray-600 text-sm truncate">{currentRide.origin_address}</span>
        </div>
        <div className="flex items-center space-x-3">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <span className="text-gray-600 text-sm truncate">{currentRide.destination_address}</span>
        </div>
      </div>

      {/* Driver Info */}
      {currentRide.driver_info && (
        <div className="border-t border-gray-100 pt-4 mb-4">
          <h4 className="font-medium text-gray-900 mb-2">Seu Motorista</h4>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">{currentRide.driver_info.name}</p>
              <p className="text-sm text-gray-600">{currentRide.driver_info.vehicle}</p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => window.open(`tel:${currentRide.driver_info?.phone}`)}
                className="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              >
                <Phone className="w-4 h-4" />
              </button>
              <button className="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                <MessageCircle className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Price */}
      <div className="border-t border-gray-100 pt-4 flex items-center justify-between">
        <span className="text-gray-600">Valor estimado:</span>
        <span className="text-lg font-bold text-gray-900">
          R$ {currentRide.estimated_price.toFixed(2)}
        </span>
      </div>

      {/* Cancel Button */}
      {currentRide.status === 'pending' && (
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={cancelRide}
          className="w-full mt-4 bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-xl transition-colors"
        >
          Cancelar Corrida
        </motion.button>
      )}
    </motion.div>
  )
}

export default RideStatusManager
