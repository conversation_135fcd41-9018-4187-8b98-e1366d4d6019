/**
 * 🎯 CONFIGURAÇÕES GLOBAIS DO MOBIDRIVE
 * Configurações centralizadas para toda a aplicação
 */

export interface PaymentConfig {
  cash: {
    defaultChangeLimit: number
    notes: string
    enabled: boolean
  }
  card: {
    enabled: boolean
    supportedBrands: string[]
    minAmount: number
    maxAmount: number
  }
  pix: {
    enabled: boolean
    minAmount: number
    maxAmount: number
  }
  fees: {
    processingFee: number
    serviceFee: number
  }
}

export interface RideConfig {
  pricing: {
    baseFare: number
    perKm: number
    perMinute: number
    minimumFare: number
    surgePricing: {
      enabled: boolean
      maxMultiplier: number
      thresholds: {
        low: number
        medium: number
        high: number
      }
    }
  }
  vehicles: {
    economy: {
      baseFare: number
      perKm: number
      perMinute: number
    }
    comfort: {
      baseFare: number
      perKm: number
      perMinute: number
    }
    premium: {
      baseFare: number
      perKm: number
      perMinute: number
    }
    moto: {
      baseFare: number
      perKm: number
      perMinute: number
    }
  }
}

export interface AppConfig {
  app: {
    name: string
    version: string
    environment: 'development' | 'staging' | 'production'
  }
  payment: PaymentConfig
  ride: RideConfig
  features: {
    realTimeTracking: boolean
    emergencyButton: boolean
    chatSupport: boolean
    rideSharing: boolean
    scheduledRides: boolean
  }
  limits: {
    maxRideDistance: number // km
    maxRideDuration: number // minutes
    maxWaitTime: number // minutes
    maxCancelTime: number // minutes
  }
}

// Configurações principais da aplicação
export const APP_SETTINGS: AppConfig = {
  app: {
    name: 'MobiDrive',
    version: '1.0.0',
    environment: process.env.NODE_ENV === 'production' ? 'production' : 'development'
  },

  payment: {
    cash: {
      defaultChangeLimit: 50.00,
      notes: 'Tenha o valor exato ou próximo. Motoristas podem não ter troco para valores altos.',
      enabled: true
    },
    card: {
      enabled: true,
      supportedBrands: ['visa', 'mastercard', 'elo', 'amex'],
      minAmount: 5.00,
      maxAmount: 500.00
    },
    pix: {
      enabled: true,
      minAmount: 1.00,
      maxAmount: 1000.00
    },
    fees: {
      processingFee: 0.39, // R$ 0,39 fixo
      serviceFee: 0.029 // 2,9%
    }
  },

  ride: {
    pricing: {
      baseFare: 5.00,
      perKm: 2.50,
      perMinute: 0.30,
      minimumFare: 5.00,
      surgePricing: {
        enabled: true,
        maxMultiplier: 3.0,
        thresholds: {
          low: 1.2,
          medium: 1.5,
          high: 2.0
        }
      }
    },
    vehicles: {
      economy: {
        baseFare: 5.00,
        perKm: 2.50,
        perMinute: 0.30
      },
      comfort: {
        baseFare: 8.00,
        perKm: 3.50,
        perMinute: 0.40
      },
      premium: {
        baseFare: 12.00,
        perKm: 5.00,
        perMinute: 0.60
      },
      moto: {
        baseFare: 3.00,
        perKm: 1.80,
        perMinute: 0.20
      }
    }
  },

  features: {
    realTimeTracking: true,
    emergencyButton: true,
    chatSupport: true,
    rideSharing: false, // Futuro
    scheduledRides: false // Futuro
  },

  limits: {
    maxRideDistance: 100, // 100km
    maxRideDuration: 180, // 3 horas
    maxWaitTime: 15, // 15 minutos
    maxCancelTime: 5 // 5 minutos
  }
}

// Configurações específicas por ambiente
export const ENVIRONMENT_CONFIGS = {
  development: {
    ...APP_SETTINGS,
    payment: {
      ...APP_SETTINGS.payment,
      cash: {
        ...APP_SETTINGS.payment.cash,
        defaultChangeLimit: 20.00 // Menor limite em dev
      }
    }
  },
  staging: {
    ...APP_SETTINGS,
    ride: {
      ...APP_SETTINGS.ride,
      pricing: {
        ...APP_SETTINGS.ride.pricing,
        baseFare: 3.00 // Preços reduzidos para teste
      }
    }
  },
  production: APP_SETTINGS
}

// Função para obter configuração por ambiente
export function getAppConfig(environment?: string): AppConfig {
  const env = environment || process.env.NODE_ENV || 'development'
  return ENVIRONMENT_CONFIGS[env as keyof typeof ENVIRONMENT_CONFIGS] || APP_SETTINGS
}

// Exportar configuração padrão
export default APP_SETTINGS
