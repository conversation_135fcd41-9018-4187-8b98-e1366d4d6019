import React, { ReactNode, useEffect } from 'react'
import '../styles/modern-theme.css'

// 🎨 LAYOUT MODERNO PADRÃO 2025
// Componente reutilizável para todas as páginas com design consistente

interface ModernLayoutProps {
  children: ReactNode
  title?: string
  subtitle?: string
  showHeader?: boolean
  showFooter?: boolean
  className?: string
  pageIcon?: string
  animatedSymbol?: string
}

export const ModernLayout: React.FC<ModernLayoutProps> = ({
  children,
  title = "MobiDrive",
  subtitle = "Transporte Inteligente",
  showHeader = true,
  showFooter = true,
  className = "",
  pageIcon = "🚗",
  animatedSymbol = "⚡"
}) => {
  
  // Configurações mobile nativas
  useEffect(() => {
    // Meta viewport
    let metaViewport = document.querySelector('meta[name="viewport"]')
    if (!metaViewport) {
      metaViewport = document.createElement('meta')
      metaViewport.setAttribute('name', 'viewport')
      document.head.appendChild(metaViewport)
    }
    metaViewport.setAttribute('content', 
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )

    // Força configurações mobile
    document.body.style.margin = '0'
    document.body.style.padding = '0'
    document.body.style.overflow = 'hidden'
    document.documentElement.style.margin = '0'
    document.documentElement.style.padding = '0'
    document.documentElement.style.overflow = 'hidden'

    return () => {
      // Cleanup se necessário
    }
  }, [])

  return (
    <div className={`modern-page ${className}`}>
      
      {/* Header moderno com glassmorphism */}
      {showHeader && (
        <div className="modern-header">
          <div className="modern-header-content">
            <div className="modern-brand-container">
              <div className="modern-brand-icon-container">
                <div className="modern-brand-icon">{pageIcon}</div>
                <div className="modern-animated-symbol">{animatedSymbol}</div>
              </div>
              <h1 className="modern-header-title">{title}</h1>
            </div>
            <p className="modern-header-subtitle">{subtitle}</p>
          </div>
        </div>
      )}

      {/* Conteúdo principal */}
      <div className="modern-content">
        {children}
      </div>

      {/* Footer */}
      {showFooter && (
        <div className="modern-footer">
          <p className="modern-footer-text">
            © 2024 MobiDrive. Todos os direitos reservados.
          </p>
        </div>
      )}
    </div>
  )
}

// Componente de Card Moderno
interface ModernCardProps {
  children: ReactNode
  className?: string
  glass?: boolean
  title?: string
  icon?: string
}

export const ModernCard: React.FC<ModernCardProps> = ({
  children,
  className = "",
  glass = false,
  title,
  icon
}) => {
  const cardClass = glass ? "modern-glass-card" : "modern-card"
  
  return (
    <div className={`${cardClass} ${className}`}>
      {title && (
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '12px', 
          marginBottom: '16px',
          paddingBottom: '12px',
          borderBottom: glass ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.1)'
        }}>
          {icon && <span style={{ fontSize: '20px' }}>{icon}</span>}
          <h3 style={{ 
            margin: 0, 
            fontSize: '18px', 
            fontWeight: '600',
            color: glass ? '#fff' : '#1f2937'
          }}>
            {title}
          </h3>
        </div>
      )}
      {children}
    </div>
  )
}

// Componente de Input Moderno
interface ModernInputProps {
  label?: string
  icon?: string
  type?: string
  placeholder?: string
  value?: string
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  className?: string
}

export const ModernInput: React.FC<ModernInputProps> = ({
  label,
  icon,
  type = "text",
  placeholder,
  value,
  onChange,
  className = ""
}) => {
  return (
    <div className={`modern-input-group ${className}`}>
      {label && (
        <label className="modern-input-label">{label}</label>
      )}
      <div className="modern-input-container">
        {icon && <span className="modern-input-icon">{icon}</span>}
        <input
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          className="modern-input"
        />
      </div>
    </div>
  )
}

// Componente de Botão Moderno
interface ModernButtonProps {
  children: ReactNode
  onClick?: () => void
  icon?: string
  loading?: boolean
  disabled?: boolean
  className?: string
  variant?: 'primary' | 'secondary' | 'success' | 'danger'
}

export const ModernButton: React.FC<ModernButtonProps> = ({
  children,
  onClick,
  icon,
  loading = false,
  disabled = false,
  className = "",
  variant = 'primary'
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'secondary':
        return {
          background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
          boxShadow: '0 8px 24px rgba(107, 114, 128, 0.4)'
        }
      case 'success':
        return {
          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
          boxShadow: '0 8px 24px rgba(16, 185, 129, 0.4)'
        }
      case 'danger':
        return {
          background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
          boxShadow: '0 8px 24px rgba(239, 68, 68, 0.4)'
        }
      default:
        return {}
    }
  }

  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className={`modern-button ${className}`}
      style={{
        ...getVariantStyles(),
        opacity: disabled ? 0.6 : 1,
        cursor: disabled ? 'not-allowed' : 'pointer',
        transform: loading ? 'scale(0.98)' : 'scale(1)'
      }}
    >
      {loading ? (
        <>
          <span className="modern-button-icon">⏳</span>
          Carregando...
        </>
      ) : (
        <>
          {icon && <span className="modern-button-icon">{icon}</span>}
          {children}
        </>
      )}
    </button>
  )
}

export default ModernLayout
