// Tipos de usuário
export interface BaseUser {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminUser extends BaseUser {
  role: 'admin' | 'super_admin';
  permissions: string[];
}

export interface DriverUser extends BaseUser {
  licenseNumber: string;
  vehicleId?: string;
  status: 'active' | 'inactive' | 'suspended';
  rating: number;
  totalRides: number;
}

export interface PassengerUser extends BaseUser {
  preferredPaymentMethod?: string;
  rating: number;
  totalRides: number;
}

export type UserType = 'admin' | 'driver' | 'passenger';