import React from 'react'
import { Car } from 'lucide-react'

interface MobiDriveLogoProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'driver' | 'passenger' | 'admin'
  showText?: boolean
  animated?: boolean
}

export const MobiDriveLogo: React.FC<MobiDriveLogoProps> = ({ 
  size = 'md', 
  variant = 'driver',
  showText = true,
  animated = true 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  }

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  const textSizes = {
    sm: 'text-lg',
    md: 'text-2xl',
    lg: 'text-3xl'
  }

  const gradients = {
    driver: 'from-green-500 to-emerald-600',
    passenger: 'from-blue-500 to-indigo-600',
    admin: 'from-purple-500 to-violet-600'
  }

  const glowColors = {
    driver: 'shadow-green-500/50',
    passenger: 'shadow-blue-500/50',
    admin: 'shadow-purple-500/50'
  }

  return (
    <div className="flex items-center gap-3">
      <div className={`
        ${sizeClasses[size]} 
        bg-gradient-to-br ${gradients[variant]}
        rounded-2xl flex items-center justify-center
        shadow-lg ${glowColors[variant]}
        border-2 border-white/30
        ${animated ? 'animate-pulse' : ''}
        relative
      `}>
        <Car className={`${iconSizes[size]} text-white`} />
        
        {/* Animated dot */}
        {animated && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
        )}
      </div>
      
      {showText && (
        <div>
          <h1 className={`${textSizes[size]} font-bold text-white`}>
            MobiDrive
          </h1>
          <p className="text-white/80 text-sm">
            {variant === 'driver' && 'Motorista'}
            {variant === 'passenger' && 'Passageiro'}
            {variant === 'admin' && 'Administrador'}
          </p>
        </div>
      )}
    </div>
  )
}
