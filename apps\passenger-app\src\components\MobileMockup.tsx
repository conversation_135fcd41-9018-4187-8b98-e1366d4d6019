import React, { ReactNode, useEffect } from 'react'
import '../styles/mockup-fixed.css'

// 📱 MOCKUP DE CELULAR PARA DESKTOP - ZOOM FIXO
// Simula a experiência mobile com zoom fixo, não afetado pelo desktop

interface MobileMockupProps {
  children: ReactNode
  className?: string
}

export const MobileMockup: React.FC<MobileMockupProps> = ({ children, className = "" }) => {
  // FORÇA ZOOM FIXO 100% - GARANTIA TOTAL
  useEffect(() => {
    // 1. Meta viewport para zoom fixo
    const metaViewport = document.querySelector('meta[name="viewport"]')
    if (metaViewport) {
      metaViewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover')
    }

    // 2. Força zoom 1 no body e html
    document.documentElement.style.zoom = '1'
    document.body.style.zoom = '1'
    document.documentElement.style.transform = 'scale(1)'
    document.body.style.transform = 'scale(1)'

    // 3. Previne zoom por teclado (Ctrl + scroll)
    const preventZoom = (e: WheelEvent) => {
      if (e.ctrlKey) {
        e.preventDefault()
        return false
      }
    }

    // 4. Previne zoom por teclado (Ctrl + +/-)
    const preventKeyboardZoom = (e: KeyboardEvent) => {
      if (e.ctrlKey && (e.key === '+' || e.key === '-' || e.key === '0')) {
        e.preventDefault()
        return false
      }
    }

    // 5. Força zoom 1 em todos os elementos do mockup
    const forceZoomOnElements = () => {
      const mockupContainer = document.querySelector('.mobile-mockup-container')
      if (mockupContainer) {
        const allElements = mockupContainer.querySelectorAll('*')
        allElements.forEach((element: Element) => {
          const htmlElement = element as HTMLElement
          htmlElement.style.zoom = '1'
          htmlElement.style.transform = 'scale(1)'
          htmlElement.style.webkitTransform = 'scale(1)'
        })
      }
    }

    // Aplicar listeners
    document.addEventListener('wheel', preventZoom, { passive: false })
    document.addEventListener('keydown', preventKeyboardZoom)

    // Força zoom inicial
    forceZoomOnElements()

    // Força zoom a cada 100ms para garantir
    const zoomInterval = setInterval(forceZoomOnElements, 100)

    // 6. Observer para detectar mudanças de zoom
    const resizeObserver = new ResizeObserver(() => {
      forceZoomOnElements()
    })

    // 7. Mutation observer para novos elementos
    const mutationObserver = new MutationObserver(() => {
      forceZoomOnElements()
    })

    // Observar mudanças no container
    const mockupContainer = document.querySelector('.mobile-mockup-container')
    if (mockupContainer) {
      resizeObserver.observe(mockupContainer)
      mutationObserver.observe(mockupContainer, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
      })
    }

    // 8. Listener para mudanças de orientação/resize
    const handleResize = () => {
      setTimeout(forceZoomOnElements, 50)
    }
    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)

    // Cleanup
    return () => {
      document.removeEventListener('wheel', preventZoom)
      document.removeEventListener('keydown', preventKeyboardZoom)
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
      clearInterval(zoomInterval)
      resizeObserver.disconnect()
      mutationObserver.disconnect()
    }
  }, [])

  return (
    <div
      className={`mobile-mockup-container flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-800 via-gray-900 to-black p-8 ${className}`}
      style={{
        zoom: 1,
        transform: 'scale(1)',
        WebkitTransform: 'scale(1)',
        MozTransform: 'scale(1)',
        msTransform: 'scale(1)',
        OTransform: 'scale(1)'
      }}
      data-zoom-fixed="true"
    >
      {/* Mockup do iPhone */}
      <div
        className="relative"
        style={{ zoom: 1, transform: 'scale(1)' }}
        data-zoom-fixed="true"
      >
        {/* Sombra do dispositivo */}
        <div
          className="absolute inset-0 bg-black/20 blur-xl transform translate-y-4 scale-105 rounded-[3rem]"
          style={{ zoom: 1 }}
          data-zoom-fixed="true"
        ></div>

        {/* Corpo do celular */}
        <div
          className="relative bg-black rounded-[3rem] p-2 shadow-2xl"
          style={{ zoom: 1, transform: 'scale(1)' }}
          data-zoom-fixed="true"
        >
          {/* Moldura interna */}
          <div
            className="bg-gray-900 rounded-[2.5rem] p-1"
            style={{ zoom: 1, transform: 'scale(1)' }}
            data-zoom-fixed="true"
          >
            {/* Tela do celular */}
            <div
              className="mobile-mockup-screen relative bg-black rounded-[2rem] overflow-hidden"
              style={{
                zoom: 1,
                transform: 'scale(1)',
                width: '375px',
                height: '812px',
                WebkitTransform: 'scale(1)',
                MozTransform: 'scale(1)',
                msTransform: 'scale(1)',
                OTransform: 'scale(1)'
              }}
              data-zoom-fixed="true"
            >

              {/* Notch (entalhe superior) */}
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 z-50">
                <div className="bg-black rounded-b-2xl px-6 py-1">
                  <div className="w-32 h-6 bg-black rounded-b-xl flex items-center justify-center">
                    {/* Câmera e sensores */}
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-gray-800 rounded-full"></div>
                      <div className="w-12 h-1.5 bg-gray-800 rounded-full"></div>
                      <div className="w-2 h-2 bg-gray-800 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status bar */}
              <div className="absolute top-0 left-0 right-0 z-40 pt-3 px-6">
                <div className="flex justify-between items-center text-white text-sm font-medium">
                  <div className="flex items-center space-x-1">
                    <span>9:41</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {/* Sinal */}
                    <div className="flex space-x-0.5">
                      <div className="w-1 h-2 bg-white rounded-full"></div>
                      <div className="w-1 h-3 bg-white rounded-full"></div>
                      <div className="w-1 h-4 bg-white rounded-full"></div>
                      <div className="w-1 h-4 bg-white rounded-full"></div>
                    </div>
                    {/* WiFi */}
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.24 0 1 1 0 01-1.415-1.414 5 5 0 017.07 0 1 1 0 01-1.415 1.414zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                    {/* Bateria */}
                    <div className="flex items-center">
                      <div className="w-6 h-3 border border-white rounded-sm relative">
                        <div className="w-4 h-1.5 bg-white rounded-sm absolute top-0.5 left-0.5"></div>
                      </div>
                      <div className="w-0.5 h-1.5 bg-white rounded-r-sm ml-0.5"></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Conteúdo do app */}
              <div className="mobile-mockup-content absolute inset-0 pt-12">
                {children}
              </div>

              {/* Home indicator */}
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 z-50">
                <div className="w-32 h-1 bg-white/30 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Botões laterais */}
        {/* Volume */}
        <div className="absolute left-0 top-32 w-1 h-8 bg-gray-700 rounded-l-lg"></div>
        <div className="absolute left-0 top-44 w-1 h-8 bg-gray-700 rounded-l-lg"></div>

        {/* Power */}
        <div className="absolute right-0 top-36 w-1 h-12 bg-gray-700 rounded-r-lg"></div>
      </div>

      {/* Informações do mockup */}
      <div className="absolute bottom-8 left-8 text-white/60 text-sm">
        <div className="bg-black/40 backdrop-blur-md rounded-lg p-4 space-y-2">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span>📱 Simulação Mobile</span>
          </div>
          <div className="text-xs text-white/40">
            Zoom fixo • Fundo estável
          </div>
        </div>
      </div>

      {/* Controles do mockup */}
      <div className="absolute bottom-8 right-8 text-white/60 text-sm">
        <div className="bg-black/40 backdrop-blur-md rounded-lg p-4 space-y-2">
          <div className="text-xs text-white/60 mb-2">DISPOSITIVO SIMULADO:</div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-xs">iPhone 14 Pro</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span className="text-xs">375 × 812px</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-xs">Zoom Fixo</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MobileMockup
