import { supabase } from '../lib/supabase'
import { notificationService } from './NotificationService'
import { analyticsService } from './AnalyticsService'

export interface EmergencyContact {
  id: string
  name: string
  phone: string
  relationship: string
  is_primary: boolean
}

export interface SafetyAlert {
  id: string
  user_id: string
  ride_id?: string
  alert_type: 'sos' | 'route_deviation' | 'speed_violation' | 'panic' | 'check_in_missed'
  severity: 'low' | 'medium' | 'high' | 'critical'
  location: {
    latitude: number
    longitude: number
    accuracy: number
    timestamp: string
  }
  description?: string
  status: 'active' | 'resolved' | 'false_alarm'
  created_at: string
  resolved_at?: string
}

export interface SafetyFeatures {
  share_trip: boolean
  emergency_contacts: boolean
  route_monitoring: boolean
  speed_monitoring: boolean
  check_in_reminders: boolean
  panic_button: boolean
  audio_recording: boolean
  photo_verification: boolean
}

export interface TripSharing {
  id: string
  ride_id: string
  shared_with: string[] // phone numbers or emails
  tracking_url: string
  expires_at: string
  is_active: boolean
}

class SafetyService {
  private watchId: number | null = null
  private currentRideId: string | null = null
  private emergencyContacts: EmergencyContact[] = []
  private safetyFeatures: SafetyFeatures = {
    share_trip: true,
    emergency_contacts: true,
    route_monitoring: true,
    speed_monitoring: true,
    check_in_reminders: true,
    panic_button: true,
    audio_recording: false,
    photo_verification: true
  }
  private lastKnownLocation: GeolocationPosition | null = null
  private routeDeviationThreshold = 500 // meters
  private speedViolationThreshold = 120 // km/h

  /**
   * Initialize safety service
   */
  async initialize(userId: string): Promise<void> {
    try {
      // Load emergency contacts
      await this.loadEmergencyContacts(userId)
      
      // Load safety preferences
      await this.loadSafetyPreferences(userId)
      
      // Start location monitoring if enabled
      if (this.safetyFeatures.route_monitoring) {
        this.startLocationMonitoring()
      }
      
      console.log('🛡️ Safety service initialized')
    } catch (error) {
      console.error('❌ Failed to initialize safety service:', error)
      throw error
    }
  }

  /**
   * Trigger emergency alert
   */
  async triggerEmergency(
    alertType: SafetyAlert['alert_type'] = 'sos',
    description?: string
  ): Promise<SafetyAlert> {
    console.log('🚨 EMERGENCY TRIGGERED:', alertType)
    
    try {
      // Get current location
      const location = await this.getCurrentLocation()
      
      // Create emergency alert
      const alert: Omit<SafetyAlert, 'id' | 'created_at'> = {
        user_id: await this.getCurrentUserId(),
        ride_id: this.currentRideId,
        alert_type: alertType,
        severity: this.getSeverityLevel(alertType),
        location: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          accuracy: location.coords.accuracy,
          timestamp: new Date(location.timestamp).toISOString()
        },
        description,
        status: 'active'
      }

      // Save to database
      const { data, error } = await supabase
        .from('safety_alerts')
        .insert(alert)
        .select()
        .single()

      if (error) throw error

      const savedAlert = data as SafetyAlert

      // Execute emergency protocol
      await this.executeEmergencyProtocol(savedAlert)
      
      // Track emergency activation
      analyticsService.trackEmergencyActivated(this.currentRideId || undefined)
      
      return savedAlert

    } catch (error) {
      console.error('❌ Failed to trigger emergency:', error)
      throw error
    }
  }

  /**
   * Execute emergency protocol
   */
  private async executeEmergencyProtocol(alert: SafetyAlert): Promise<void> {
    console.log('🚨 Executing emergency protocol for:', alert.alert_type)

    try {
      // 1. Notify emergency contacts
      await this.notifyEmergencyContacts(alert)
      
      // 2. Notify authorities if critical
      if (alert.severity === 'critical') {
        await this.notifyAuthorities(alert)
      }
      
      // 3. Share location with emergency services
      await this.shareLocationWithEmergencyServices(alert)
      
      // 4. Start continuous location tracking
      this.startEmergencyLocationTracking(alert.id)
      
      // 5. Notify driver if in ride
      if (alert.ride_id) {
        await this.notifyDriverOfEmergency(alert.ride_id)
      }
      
      // 6. Show emergency UI
      this.showEmergencyUI(alert)
      
      // 7. Start audio recording if enabled
      if (this.safetyFeatures.audio_recording) {
        await this.startEmergencyRecording(alert.id)
      }

    } catch (error) {
      console.error('❌ Failed to execute emergency protocol:', error)
    }
  }

  /**
   * Share trip with contacts
   */
  async shareTripWithContacts(rideId: string, contactIds: string[]): Promise<TripSharing> {
    try {
      const trackingUrl = `${window.location.origin}/track/${rideId}`
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      
      // Get contact details
      const contacts = this.emergencyContacts.filter(c => contactIds.includes(c.id))
      const phoneNumbers = contacts.map(c => c.phone)
      
      // Create trip sharing record
      const tripSharing: Omit<TripSharing, 'id'> = {
        ride_id: rideId,
        shared_with: phoneNumbers,
        tracking_url: trackingUrl,
        expires_at: expiresAt.toISOString(),
        is_active: true
      }

      const { data, error } = await supabase
        .from('trip_sharing')
        .insert(tripSharing)
        .select()
        .single()

      if (error) throw error

      // Send sharing notifications
      await this.sendTripSharingNotifications(data as TripSharing, contacts)
      
      console.log('📱 Trip shared with contacts:', phoneNumbers)
      return data as TripSharing

    } catch (error) {
      console.error('❌ Failed to share trip:', error)
      throw error
    }
  }

  /**
   * Monitor route deviation
   */
  async monitorRouteDeviation(expectedRoute: any): Promise<void> {
    if (!this.safetyFeatures.route_monitoring) return

    try {
      const currentLocation = await this.getCurrentLocation()
      const deviation = this.calculateRouteDeviation(currentLocation, expectedRoute)
      
      if (deviation > this.routeDeviationThreshold) {
        await this.triggerEmergency('route_deviation', 
          `Desvio de rota detectado: ${deviation.toFixed(0)}m da rota planejada`
        )
      }
    } catch (error) {
      console.error('❌ Route monitoring error:', error)
    }
  }

  /**
   * Monitor speed violations
   */
  async monitorSpeed(currentSpeed: number): Promise<void> {
    if (!this.safetyFeatures.speed_monitoring) return

    if (currentSpeed > this.speedViolationThreshold) {
      await this.triggerEmergency('speed_violation',
        `Velocidade excessiva detectada: ${currentSpeed.toFixed(0)} km/h`
      )
    }
  }

  /**
   * Set up check-in reminders
   */
  setupCheckInReminders(rideId: string, estimatedDuration: number): void {
    if (!this.safetyFeatures.check_in_reminders) return

    // Set reminder for halfway through ride
    const halfwayTime = (estimatedDuration / 2) * 60 * 1000
    setTimeout(() => {
      this.sendCheckInReminder('halfway')
    }, halfwayTime)

    // Set reminder for expected arrival + buffer
    const arrivalTime = estimatedDuration * 60 * 1000 + 10 * 60 * 1000 // +10 min buffer
    setTimeout(() => {
      this.sendCheckInReminder('arrival')
    }, arrivalTime)
  }

  /**
   * Add emergency contact
   */
  async addEmergencyContact(contact: Omit<EmergencyContact, 'id'>): Promise<EmergencyContact> {
    try {
      const { data, error } = await supabase
        .from('emergency_contacts')
        .insert({
          ...contact,
          user_id: await this.getCurrentUserId()
        })
        .select()
        .single()

      if (error) throw error

      const newContact = data as EmergencyContact
      this.emergencyContacts.push(newContact)
      
      return newContact
    } catch (error) {
      console.error('❌ Failed to add emergency contact:', error)
      throw error
    }
  }

  /**
   * Update safety preferences
   */
  async updateSafetyPreferences(preferences: Partial<SafetyFeatures>): Promise<void> {
    try {
      this.safetyFeatures = { ...this.safetyFeatures, ...preferences }
      
      const { error } = await supabase
        .from('user_safety_preferences')
        .upsert({
          user_id: await this.getCurrentUserId(),
          preferences: this.safetyFeatures
        })

      if (error) throw error

      console.log('🛡️ Safety preferences updated')
    } catch (error) {
      console.error('❌ Failed to update safety preferences:', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async loadEmergencyContacts(userId: string): Promise<void> {
    const { data, error } = await supabase
      .from('emergency_contacts')
      .select('*')
      .eq('user_id', userId)

    if (error) throw error
    this.emergencyContacts = data || []
  }

  private async loadSafetyPreferences(userId: string): Promise<void> {
    const { data, error } = await supabase
      .from('user_safety_preferences')
      .select('preferences')
      .eq('user_id', userId)
      .single()

    if (!error && data) {
      this.safetyFeatures = { ...this.safetyFeatures, ...data.preferences }
    }
  }

  private startLocationMonitoring(): void {
    if (navigator.geolocation) {
      this.watchId = navigator.geolocation.watchPosition(
        (position) => {
          this.lastKnownLocation = position
        },
        (error) => {
          console.warn('Location monitoring error:', error)
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 30000
        }
      )
    }
  }

  private async getCurrentLocation(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      if (this.lastKnownLocation) {
        resolve(this.lastKnownLocation)
        return
      }

      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000
      })
    })
  }

  private getSeverityLevel(alertType: SafetyAlert['alert_type']): SafetyAlert['severity'] {
    const severityMap = {
      sos: 'critical',
      panic: 'critical',
      route_deviation: 'high',
      speed_violation: 'medium',
      check_in_missed: 'medium'
    } as const

    return severityMap[alertType] || 'medium'
  }

  private async notifyEmergencyContacts(alert: SafetyAlert): Promise<void> {
    const primaryContacts = this.emergencyContacts.filter(c => c.is_primary)
    const allContacts = primaryContacts.length > 0 ? primaryContacts : this.emergencyContacts

    for (const contact of allContacts) {
      // Send SMS notification (would integrate with SMS service)
      console.log(`📱 Notifying emergency contact: ${contact.name} (${contact.phone})`)
      
      // In a real implementation, integrate with SMS service like Twilio
      // await this.sendEmergencySMS(contact.phone, alert)
    }
  }

  private async notifyAuthorities(alert: SafetyAlert): Promise<void> {
    // In a real implementation, integrate with emergency services API
    console.log('🚨 Notifying authorities for critical emergency')
    
    // Log for emergency services integration
    await supabase
      .from('emergency_authority_notifications')
      .insert({
        alert_id: alert.id,
        notification_type: 'automatic',
        location: alert.location,
        timestamp: new Date().toISOString()
      })
  }

  private async shareLocationWithEmergencyServices(alert: SafetyAlert): Promise<void> {
    // Create shareable emergency location link
    const emergencyLink = `${window.location.origin}/emergency/${alert.id}`
    console.log('📍 Emergency location link:', emergencyLink)
  }

  private startEmergencyLocationTracking(alertId: string): void {
    // Start high-frequency location updates during emergency
    const emergencyWatchId = navigator.geolocation.watchPosition(
      async (position) => {
        await supabase
          .from('emergency_location_updates')
          .insert({
            alert_id: alertId,
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date().toISOString()
          })
      },
      (error) => console.error('Emergency location tracking error:', error),
      {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 0
      }
    )

    // Stop after 1 hour
    setTimeout(() => {
      navigator.geolocation.clearWatch(emergencyWatchId)
    }, 60 * 60 * 1000)
  }

  private async notifyDriverOfEmergency(rideId: string): Promise<void> {
    // Notify driver through realtime channel
    await supabase.channel('driver-notifications')
      .send({
        type: 'broadcast',
        event: 'passenger_emergency',
        payload: {
          ride_id: rideId,
          message: 'Passageiro ativou alerta de emergência',
          timestamp: new Date().toISOString()
        }
      })
  }

  private showEmergencyUI(alert: SafetyAlert): void {
    // Show emergency overlay UI
    notificationService.notifyEmergencyActivated()
    
    // Add emergency mode to body
    document.body.classList.add('emergency-mode')
    
    // Create emergency overlay
    const overlay = document.createElement('div')
    overlay.id = 'emergency-overlay'
    overlay.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(220, 38, 38, 0.9);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        animation: pulse 1s infinite;
      ">
        🚨 EMERGÊNCIA ATIVADA 🚨<br>
        <small style="font-size: 16px;">Autoridades foram notificadas</small>
      </div>
    `
    document.body.appendChild(overlay)
  }

  private async startEmergencyRecording(alertId: string): Promise<void> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream)
      
      mediaRecorder.start()
      console.log('🎤 Emergency audio recording started')
      
      // Stop recording after 5 minutes
      setTimeout(() => {
        mediaRecorder.stop()
        stream.getTracks().forEach(track => track.stop())
      }, 5 * 60 * 1000)
      
    } catch (error) {
      console.error('❌ Failed to start emergency recording:', error)
    }
  }

  private calculateRouteDeviation(currentLocation: GeolocationPosition, expectedRoute: any): number {
    // Simplified calculation - in real implementation, use proper route matching
    return Math.random() * 100 // Placeholder
  }

  private sendCheckInReminder(type: 'halfway' | 'arrival'): void {
    const messages = {
      halfway: 'Como está sua viagem? Toque para confirmar que está tudo bem.',
      arrival: 'Você chegou ao destino? Confirme sua segurança.'
    }

    notificationService.showNotification({
      title: '🛡️ Check-in de Segurança',
      message: messages[type],
      type: 'info',
      priority: 'high',
      sound: true,
      vibrate: true,
      actions: [
        { action: 'safe', title: 'Estou bem', icon: '/icons/check.png' },
        { action: 'help', title: 'Preciso de ajuda', icon: '/icons/help.png' }
      ]
    })
  }

  private async sendTripSharingNotifications(tripSharing: TripSharing, contacts: EmergencyContact[]): Promise<void> {
    for (const contact of contacts) {
      // In real implementation, send SMS with tracking link
      console.log(`📱 Sending trip sharing to ${contact.name}: ${tripSharing.tracking_url}`)
    }
  }

  private async getCurrentUserId(): Promise<string> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')
    return user.id
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.watchId) {
      navigator.geolocation.clearWatch(this.watchId)
      this.watchId = null
    }
    
    // Remove emergency UI
    const overlay = document.getElementById('emergency-overlay')
    if (overlay) {
      overlay.remove()
    }
    
    document.body.classList.remove('emergency-mode')
    
    console.log('🛡️ Safety service cleaned up')
  }

  /**
   * Get safety status
   */
  getSafetyStatus(): {
    isMonitoring: boolean
    emergencyContactsCount: number
    featuresEnabled: SafetyFeatures
    currentRide: string | null
  } {
    return {
      isMonitoring: this.watchId !== null,
      emergencyContactsCount: this.emergencyContacts.length,
      featuresEnabled: this.safetyFeatures,
      currentRide: this.currentRideId
    }
  }
}

export const safetyService = new SafetyService()
export default safetyService
