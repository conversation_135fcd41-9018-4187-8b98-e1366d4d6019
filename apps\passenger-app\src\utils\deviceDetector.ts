// 📱 DETECTOR DE DISPOSITIVO AVANÇADO
// Detecta se é mobile real, tablet ou desktop para escolher a UI apropriada

export interface DeviceInfo {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isTouchDevice: boolean
  screenWidth: number
  screenHeight: number
  userAgent: string
  platform: string
  isAndroid: boolean
  isIOS: boolean
  isWebMobile: boolean
}

export const detectDevice = (): DeviceInfo => {
  const userAgent = navigator.userAgent || ''
  const platform = navigator.platform || ''

  // Detecta dimensões da tela
  const screenWidth = window.screen.width
  const screenHeight = window.screen.height
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  // Detecta se é dispositivo touch
  const isTouchDevice = 'ontouchstart' in window ||
                       navigator.maxTouchPoints > 0 ||
                       (navigator as any).msMaxTouchPoints > 0

  // Detecta plataformas específicas
  const isAndroid = /Android/i.test(userAgent)
  const isIOS = /iPad|iPhone|iPod/.test(userAgent)
  const isWebMobile = /Mobi|Android/i.test(userAgent)

  // Detecta tipo de dispositivo baseado em múltiplos fatores
  const isMobileBySize = viewportWidth <= 768
  const isTabletBySize = viewportWidth > 768 && viewportWidth <= 1024
  const isDesktopBySize = viewportWidth > 1024

  // Detecta mobile real (não simulado)
  const isRealMobile = (isAndroid || isIOS) && isTouchDevice && isMobileBySize

  // Detecta tablet
  const isTablet = (isTabletBySize && isTouchDevice) ||
                   /iPad/.test(userAgent) ||
                   (isAndroid && !isWebMobile)

  // Detecta desktop
  const isDesktop = !isRealMobile && !isTablet && isDesktopBySize

  return {
    isMobile: isRealMobile,
    isTablet,
    isDesktop,
    isTouchDevice,
    screenWidth,
    screenHeight,
    userAgent,
    platform,
    isAndroid,
    isIOS,
    isWebMobile
  }
}

// Hook React para usar o detector
import { useState, useEffect } from 'react'

export const useDeviceDetection = () => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(() => detectDevice())

  useEffect(() => {
    const handleResize = () => {
      setDeviceInfo(detectDevice())
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
    }
  }, [])

  return deviceInfo
}

// Função para determinar qual UI usar
export const shouldUseMobileNativeUI = (): boolean => {
  const device = detectDevice()

  // Usa UI mobile nativa se:
  // 1. É um dispositivo mobile real (Android/iOS)
  // 2. É um dispositivo touch com tela pequena
  // 3. Está em modo mobile (viewport pequeno)

  return device.isMobile ||
         (device.isTouchDevice && device.screenWidth <= 768) ||
         device.isWebMobile
}

// Função para determinar se deve usar mockup desktop
export const shouldUseDesktopMockup = (): boolean => {
  const device = detectDevice()

  // Usa mockup desktop se:
  // 1. É desktop (não touch, tela grande)
  // 2. É tablet em modo landscape
  // 3. É browser desktop simulando mobile

  return device.isDesktop ||
         (device.isTablet && window.innerWidth > 1024) ||
         (!device.isTouchDevice && window.innerWidth > 768)
}

// Função para determinar o tipo de experiência
export const getExperienceType = (): 'mobile' | 'desktop' => {
  // 🎯 NOVA LÓGICA: Desktop sempre usa mockup, mobile usa nativo
  return shouldUseMobileNativeUI() ? 'mobile' : 'desktop'
}

// Debug: Log informações do dispositivo
export const logDeviceInfo = () => {
  const device = detectDevice()
  console.log('🔍 DEVICE DETECTION:', {
    ...device,
    shouldUseMobileUI: shouldUseMobileNativeUI(),
    shouldUseDesktopMockup: shouldUseDesktopMockup(),
    viewport: `${window.innerWidth}×${window.innerHeight}`,
    screen: `${window.screen.width}×${window.screen.height}`
  })
}

export default {
  detectDevice,
  useDeviceDetection,
  shouldUseMobileNativeUI,
  shouldUseDesktopMockup,
  logDeviceInfo
}
