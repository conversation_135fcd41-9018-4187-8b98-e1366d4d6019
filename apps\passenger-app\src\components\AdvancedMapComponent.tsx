import React, { useEffect, useRef, useState } from 'react'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import { RealtimeLocationService } from '../lib/supabase'

// Set Mapbox access token - SOLUÇÃO PARA VITE
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'

console.log('🔧 AdvancedMapComponent: Configurando token...')
mapboxgl.accessToken = MAPBOX_TOKEN
console.log('✅ AdvancedMapComponent: Token configurado:', mapboxgl.accessToken?.substring(0, 20) + '...')

interface AdvancedMapComponentProps {
  userLocation?: { lat: number; lng: number }
  onLocationSelect?: (location: { lat: number; lng: number }) => void
  showDrivers?: boolean
  showTraffic?: boolean
  showRoute?: boolean
  routeCoordinates?: [number, number][]
}

export const AdvancedMapComponent: React.FC<AdvancedMapComponentProps> = ({
  userLocation,
  onLocationSelect,
  showDrivers = true,
  showTraffic = true,
  showRoute = false,
  routeCoordinates = []
}) => {
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const [drivers, setDrivers] = useState<any[]>([])
  const [locationService] = useState(() => new RealtimeLocationService())
  const driversMarkersRef = useRef<mapboxgl.Marker[]>([])

  useEffect(() => {
    if (!mapContainer.current) return

    // Initialize map
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/dark-v11', // 🌙 TEMA PRETO: Aplicado no AdvancedMapComponent
      center: userLocation ? [userLocation.lng, userLocation.lat] : [-43.2161, -12.1811],
      zoom: 13,
      pitch: 45, // 3D effect
      bearing: 0
    })

    // Add navigation controls
    map.current.addControl(new mapboxgl.NavigationControl(), 'top-right')

    // Add geolocate control
    const geolocate = new mapboxgl.GeolocateControl({
      positionOptions: {
        enableHighAccuracy: true
      },
      trackUserLocation: true,
      showUserHeading: true
    })
    map.current.addControl(geolocate, 'top-right')

    // Add traffic layer if enabled
    if (showTraffic) {
      map.current.on('load', () => {
        if (map.current) {
          map.current.addSource('mapbox-traffic', {
            type: 'vector',
            url: 'mapbox://mapbox.mapbox-traffic-v1'
          })

          map.current.addLayer({
            id: 'traffic',
            type: 'line',
            source: 'mapbox-traffic',
            'source-layer': 'traffic',
            paint: {
              'line-width': 2,
              'line-color': [
                'case',
                ['==', ['get', 'congestion'], 'low'], '#00ff00',
                ['==', ['get', 'congestion'], 'moderate'], '#ffff00',
                ['==', ['get', 'congestion'], 'heavy'], '#ff8000',
                ['==', ['get', 'congestion'], 'severe'], '#ff0000',
                '#000000'
              ]
            }
          })
        }
      })
    }

    // Add click handler for location selection
    if (onLocationSelect) {
      map.current.on('click', (e) => {
        onLocationSelect({
          lat: e.lngLat.lat,
          lng: e.lngLat.lng
        })
      })
    }

    return () => {
      if (map.current) {
        map.current.remove()
      }
    }
  }, [])

  // Update user location
  useEffect(() => {
    if (map.current && userLocation) {
      map.current.flyTo({
        center: [userLocation.lng, userLocation.lat],
        zoom: 15,
        duration: 2000
      })

      // Add user marker
      new mapboxgl.Marker({ color: '#3b82f6' })
        .setLngLat([userLocation.lng, userLocation.lat])
        .addTo(map.current)
    }
  }, [userLocation])

  // DESABILITADO: Tracking de motoristas desativado
  // Start driver tracking
  useEffect(() => {
    if (false && showDrivers && userLocation) { // Funcionalidade desabilitada
      locationService.startDriverTracking(userLocation, (nearbyDrivers) => {
        setDrivers(nearbyDrivers)
      })

      return () => {
        locationService.stopDriverTracking()
      }
    }
  }, [showDrivers, userLocation])

  // Update driver markers
  useEffect(() => {
    if (!map.current) return

    // Clear existing markers
    driversMarkersRef.current.forEach(marker => marker.remove())
    driversMarkersRef.current = []

    // Add new driver markers
    drivers.forEach(driver => {
      const el = document.createElement('div')
      el.className = 'driver-marker'
      el.style.cssText = `
        background-image: url('data:image/svg+xml;base64,${btoa(`
          <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
            <circle cx="15" cy="15" r="12" fill="#10b981" stroke="#ffffff" stroke-width="2"/>
            <text x="15" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">🚗</text>
          </svg>
        `)}');
        width: 30px;
        height: 30px;
        background-size: 100%;
        cursor: pointer;
      `

      const marker = new mapboxgl.Marker(el)
        .setLngLat([driver.longitude, driver.latitude])
        .setPopup(new mapboxgl.Popup({ offset: 25 })
          .setHTML(`
            <div class="p-2">
              <h3 class="font-bold">${driver.driver_name || 'Motorista'}</h3>
              <p class="text-sm text-gray-600">Distância: ${driver.distance?.toFixed(1)}km</p>
              <p class="text-sm text-gray-600">Avaliação: ⭐ ${driver.rating || 'N/A'}</p>
            </div>
          `))
        .addTo(map.current)

      driversMarkersRef.current.push(marker)
    })
  }, [drivers])

  // Add route if provided
  useEffect(() => {
    if (!map.current || !showRoute || routeCoordinates.length === 0) return

    map.current.on('load', () => {
      if (!map.current) return

      // Add route source
      map.current.addSource('route', {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: routeCoordinates
          }
        }
      })

      // Add route layer
      map.current.addLayer({
        id: 'route',
        type: 'line',
        source: 'route',
        layout: {
          'line-join': 'round',
          'line-cap': 'round'
        },
        paint: {
          'line-color': '#3b82f6',
          'line-width': 5,
          'line-opacity': 0.8
        }
      })
    })

    return () => {
      if (map.current && map.current.getLayer('route')) {
        map.current.removeLayer('route')
        map.current.removeSource('route')
      }
    }
  }, [showRoute, routeCoordinates])

  return (
    <div className="relative w-full h-full">
      <div ref={mapContainer} className="w-full h-full rounded-lg" />

      {/* DESABILITADO: Controles de mapa com info de motoristas desativados */}
      {/* Map controls overlay */}
      <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-2 space-y-2" style={{ display: 'none' }}>
        <div className="text-sm font-medium text-gray-700">
          Motoristas próximos: {drivers.length}
        </div>
        {showTraffic && (
          <div className="text-xs text-gray-500">
            🚦 Trânsito em tempo real
          </div>
        )}
      </div>

      {/* DESABILITADO: Lista de motoristas desativada */}
      {/* Driver list overlay */}
      {false && drivers.length > 0 && (
        <div className="absolute bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg p-4 max-h-32 overflow-y-auto">
          <h3 className="font-bold text-sm mb-2">Motoristas Disponíveis</h3>
          <div className="space-y-1">
            {drivers.slice(0, 3).map((driver, index) => (
              <div key={index} className="flex justify-between items-center text-xs">
                <span>{driver.driver_name || `Motorista ${index + 1}`}</span>
                <span className="text-gray-500">{driver.distance?.toFixed(1)}km</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default AdvancedMapComponent
