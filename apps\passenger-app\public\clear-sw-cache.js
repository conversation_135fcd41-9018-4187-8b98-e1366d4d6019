// Script para limpar cache do Service Worker
// Execute no console do navegador para limpar todos os caches

async function clearServiceWorkerCache() {
  try {
    console.log('🧹 Limpando cache do Service Worker...');
    
    // 1. Limpar todos os caches
    const cacheNames = await caches.keys();
    console.log('📦 Caches encontrados:', cacheNames);
    
    await Promise.all(
      cacheNames.map(cacheName => {
        console.log('🗑️ Removendo cache:', cacheName);
        return caches.delete(cacheName);
      })
    );
    
    // 2. Desregistrar Service Worker
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();
      
      await Promise.all(
        registrations.map(registration => {
          console.log('🔄 Desregistrando SW:', registration.scope);
          return registration.unregister();
        })
      );
    }
    
    console.log('✅ Cache limpo com sucesso!');
    console.log('🔄 Recarregue a página para aplicar as mudanças.');
    
    // 3. <PERSON>carregar a página
    setTimeout(() => {
      window.location.reload();
    }, 1000);
    
  } catch (error) {
    console.error('❌ Erro ao limpar cache:', error);
  }
}

// Executar automaticamente
clearServiceWorkerCache();
