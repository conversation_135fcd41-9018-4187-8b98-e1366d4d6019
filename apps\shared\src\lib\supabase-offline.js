/**
 * Sistema de gerenciamento de operações offline para o Supabase
 * 
 * Este módulo permite que o aplicativo continue funcionando mesmo sem conexão
 * com a internet, armazenando operações para sincronização posterior.
 */

// Detectar se estamos no navegador ou no servidor
const isBrowser = typeof window !== 'undefined';

// Configurações padrão
const DEFAULT_CONFIG = {
  enabled: true,
  storageKey: 'supabase-offline-queue',
  syncInterval: 30000, // 30 segundos
  maxQueueSize: 100,
  debug: false
};

// Tipos de operações suportadas
const OPERATION_TYPES = {
  INSERT: 'insert',
  UPDATE: 'update',
  DELETE: 'delete',
  UPSERT: 'upsert'
};

/**
 * Classe para gerenciar operações offline com o Supabase
 */
class SupabaseOfflineManager {
  constructor(supabaseClient, config = {}) {
    this.supabase = supabaseClient;
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.queue = [];
    this.isOnline = isBrowser ? navigator.onLine : true;
    this.isSyncing = false;
    this.syncIntervalId = null;
    
    // Inicializar
    this._init();
  }
  
  /**
   * Inicializa o gerenciador de operações offline
   * @private
   */
  _init() {
    if (!isBrowser || !this.config.enabled) return;
    
    // Carregar fila do localStorage
    this._loadQueue();
    
    // Adicionar event listeners para status de conexão
    window.addEventListener('online', this._handleOnline.bind(this));
    window.addEventListener('offline', this._handleOffline.bind(this));
    
    // Iniciar sincronização periódica se estiver online
    if (this.isOnline) {
      this._startSyncInterval();
    }
    
    this._log('Gerenciador de operações offline inicializado');
  }
  
  /**
   * Carrega a fila de operações do localStorage
   * @private
   */
  _loadQueue() {
    try {
      const storedQueue = localStorage.getItem(this.config.storageKey);
      if (storedQueue) {
        this.queue = JSON.parse(storedQueue);
        this._log(`Fila carregada do localStorage: ${this.queue.length} operações`);
      }
    } catch (error) {
      console.error('Erro ao carregar fila do localStorage:', error);
      this.queue = [];
    }
  }
  
  /**
   * Salva a fila de operações no localStorage
   * @private
   */
  _saveQueue() {
    if (!isBrowser) return;
    
    try {
      localStorage.setItem(this.config.storageKey, JSON.stringify(this.queue));
      this._log(`Fila salva no localStorage: ${this.queue.length} operações`);
    } catch (error) {
      console.error('Erro ao salvar fila no localStorage:', error);
    }
  }
  
  /**
   * Manipula evento de conexão online
   * @private
   */
  _handleOnline() {
    this.isOnline = true;
    this._log('Conexão online detectada');
    
    // Iniciar sincronização
    this._startSyncInterval();
    this.sync();
  }
  
  /**
   * Manipula evento de conexão offline
   * @private
   */
  _handleOffline() {
    this.isOnline = false;
    this._log('Conexão offline detectada');
    
    // Parar sincronização
    this._stopSyncInterval();
  }
  
  /**
   * Inicia o intervalo de sincronização
   * @private
   */
  _startSyncInterval() {
    if (this.syncIntervalId) return;
    
    this.syncIntervalId = setInterval(() => {
      this.sync();
    }, this.config.syncInterval);
    
    this._log(`Intervalo de sincronização iniciado: ${this.config.syncInterval}ms`);
  }
  
  /**
   * Para o intervalo de sincronização
   * @private
   */
  _stopSyncInterval() {
    if (this.syncIntervalId) {
      clearInterval(this.syncIntervalId);
      this.syncIntervalId = null;
      this._log('Intervalo de sincronização parado');
    }
  }
  
  /**
   * Registra mensagens de debug se o modo debug estiver ativado
   * @private
   * @param {string} message Mensagem de debug
   * @param {any} data Dados adicionais (opcional)
   */
  _log(message, data) {
    if (this.config.debug) {
      console.log(`[SupabaseOffline] ${message}`, data || '');
    }
  }
  
  /**
   * Adiciona uma operação à fila
   * @param {string} type Tipo de operação (insert, update, delete, upsert)
   * @param {string} table Nome da tabela
   * @param {Object} data Dados da operação
   * @param {Object} options Opções adicionais
   * @returns {string} ID da operação
   */
  addOperation(type, table, data, options = {}) {
    // Verificar se o tipo de operação é válido
    if (!Object.values(OPERATION_TYPES).includes(type)) {
      throw new Error(`Tipo de operação inválido: ${type}`);
    }
    
    // Verificar se a fila está cheia
    if (this.queue.length >= this.config.maxQueueSize) {
      this._log('Fila cheia, removendo operação mais antiga');
      this.queue.shift();
    }
    
    // Criar ID único para a operação
    const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Criar operação
    const operation = {
      id,
      type,
      table,
      data,
      options,
      timestamp: Date.now(),
      retries: 0
    };
    
    // Adicionar à fila
    this.queue.push(operation);
    this._log(`Operação adicionada à fila: ${type} em ${table}`, operation);
    
    // Salvar fila
    this._saveQueue();
    
    // Tentar sincronizar se estiver online
    if (this.isOnline && !this.isSyncing) {
      this.sync();
    }
    
    return id;
  }
  
  /**
   * Sincroniza a fila de operações com o servidor
   * @returns {Promise<Object>} Resultado da sincronização
   */
  async sync() {
    // Verificar se já está sincronizando
    if (this.isSyncing) {
      this._log('Sincronização já em andamento');
      return { success: false, reason: 'already_syncing' };
    }
    
    // Verificar se está online
    if (!this.isOnline) {
      this._log('Não é possível sincronizar: offline');
      return { success: false, reason: 'offline' };
    }
    
    // Verificar se há operações na fila
    if (this.queue.length === 0) {
      this._log('Nenhuma operação para sincronizar');
      return { success: true, processed: 0 };
    }
    
    this.isSyncing = true;
    this._log(`Iniciando sincronização: ${this.queue.length} operações`);
    
    const results = {
      success: true,
      total: this.queue.length,
      processed: 0,
      failed: 0,
      errors: []
    };
    
    // Processar operações
    const newQueue = [];
    
    for (const operation of this.queue) {
      try {
        const success = await this._processOperation(operation);
        
        if (success) {
          results.processed++;
        } else {
          // Incrementar contagem de tentativas
          operation.retries++;
          
          // Adicionar de volta à fila se não excedeu o número máximo de tentativas
          if (operation.retries < 3) {
            newQueue.push(operation);
            results.failed++;
          } else {
            results.errors.push({
              id: operation.id,
              type: operation.type,
              table: operation.table,
              reason: 'max_retries_exceeded'
            });
          }
        }
      } catch (error) {
        results.failed++;
        results.errors.push({
          id: operation.id,
          type: operation.type,
          table: operation.table,
          error: error.message
        });
        
        // Adicionar de volta à fila se não excedeu o número máximo de tentativas
        if (operation.retries < 3) {
          operation.retries++;
          newQueue.push(operation);
        }
      }
    }
    
    // Atualizar fila
    this.queue = newQueue;
    this._saveQueue();
    
    this.isSyncing = false;
    this._log('Sincronização concluída', results);
    
    return results;
  }
  
  /**
   * Processa uma operação
   * @private
   * @param {Object} operation Operação a ser processada
   * @returns {Promise<boolean>} True se a operação foi processada com sucesso
   */
  async _processOperation(operation) {
    const { type, table, data, options } = operation;
    
    try {
      let result;
      
      switch (type) {
        case OPERATION_TYPES.INSERT:
          result = await this.supabase
            .from(table)
            .insert(data, { returning: 'minimal', ...options });
          break;
          
        case OPERATION_TYPES.UPDATE:
          result = await this.supabase
            .from(table)
            .update(data, { returning: 'minimal', ...options });
          break;
          
        case OPERATION_TYPES.DELETE:
          result = await this.supabase
            .from(table)
            .delete({ returning: 'minimal', ...options });
          break;
          
        case OPERATION_TYPES.UPSERT:
          result = await this.supabase
            .from(table)
            .upsert(data, { returning: 'minimal', ...options });
          break;
          
        default:
          throw new Error(`Tipo de operação não suportado: ${type}`);
      }
      
      if (result.error) {
        this._log(`Erro ao processar operação: ${result.error.message}`, operation);
        return false;
      }
      
      this._log(`Operação processada com sucesso: ${type} em ${table}`, operation);
      return true;
    } catch (error) {
      this._log(`Exceção ao processar operação: ${error.message}`, operation);
      throw error;
    }
  }
  
  /**
   * Limpa a fila de operações
   */
  clearQueue() {
    this.queue = [];
    this._saveQueue();
    this._log('Fila limpa');
  }
  
  /**
   * Obtém o status atual
   * @returns {Object} Status atual
   */
  getStatus() {
    return {
      isOnline: this.isOnline,
      isSyncing: this.isSyncing,
      queueSize: this.queue.length,
      maxQueueSize: this.config.maxQueueSize
    };
  }
}

// Exportar classe
export default SupabaseOfflineManager;
