/* 🚫 NO ZOOM CSS - IMPEDE ZOOM COMPLETAMENTE */

.no-zoom-page {
  /* Impede zoom via CSS */
  zoom: 1 !important;
  transform: scale(1) !important;
  transform-origin: 0 0 !important;
  
  /* Configurações de viewport */
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  
  /* Impede overflow */
  overflow-x: hidden !important;
  overflow-y: auto !important;
  
  /* Configurações de touch */
  touch-action: pan-y !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  
  /* Impede seleção de texto */
  -webkit-tap-highlight-color: transparent !important;
  
  /* Configurações de fonte */
  font-size: 16px !important;
  line-height: 1.5 !important;
}

/* Configurações globais para impedir zoom */
html, body {
  zoom: 1 !important;
  transform: scale(1) !important;
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}

/* Impede zoom em inputs */
input, textarea, select {
  font-size: 16px !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* Configurações específicas para mobile */
@media screen and (max-width: 768px) {
  .no-zoom-page {
    /* Força altura total em mobile */
    min-height: 100vh !important;
    min-height: -webkit-fill-available !important;
  }
  
  /* Impede zoom duplo toque */
  * {
    touch-action: manipulation !important;
  }
}

/* Configurações para WebKit (Safari/Chrome) */
@supports (-webkit-touch-callout: none) {
  .no-zoom-page {
    -webkit-overflow-scrolling: touch !important;
  }
}
