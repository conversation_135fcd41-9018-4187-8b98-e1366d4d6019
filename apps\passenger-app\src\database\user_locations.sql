-- =====================================================
-- SCRIPT PARA CRIAR TABELA user_locations NO SUPABASE
-- =====================================================
--
-- Execute este script no SQL Editor do Supabase Dashboard
-- URL: https://udquhavmgqtpkubrfzdm.supabase.co/project/default/sql
--
-- =====================================================

-- Tabela para armazenar localizações dos usuários
CREATE TABLE IF NOT EXISTS user_locations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  accuracy DECIMAL(8, 2),
  heading DECIMAL(5, 2),
  speed DECIMAL(8, 2),
  altitude DECIMAL(8, 2),
  timestamp TIMESTAMPTZ NOT NULL,
  address TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_user_locations_user_id ON user_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_locations_is_active ON user_locations(is_active);
CREATE INDEX IF NOT EXISTS idx_user_locations_timestamp ON user_locations(timestamp);
CREATE INDEX IF NOT EXISTS idx_user_locations_created_at ON user_locations(created_at);

-- Índice geoespacial para consultas por proximidade
CREATE INDEX IF NOT EXISTS idx_user_locations_coordinates ON user_locations USING GIST (
  ll_to_earth(latitude, longitude)
);

-- RLS (Row Level Security)
ALTER TABLE user_locations ENABLE ROW LEVEL SECURITY;

-- Política para usuários verem apenas suas próprias localizações
CREATE POLICY "Users can view own locations" ON user_locations
  FOR SELECT USING (auth.uid() = user_id);

-- Política para usuários inserirem suas próprias localizações
CREATE POLICY "Users can insert own locations" ON user_locations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Política para usuários atualizarem suas próprias localizações
CREATE POLICY "Users can update own locations" ON user_locations
  FOR UPDATE USING (auth.uid() = user_id);

-- Política para usuários deletarem suas próprias localizações
CREATE POLICY "Users can delete own locations" ON user_locations
  FOR DELETE USING (auth.uid() = user_id);

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para atualizar updated_at
CREATE TRIGGER update_user_locations_updated_at
  BEFORE UPDATE ON user_locations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Função para limpar localizações antigas (opcional)
CREATE OR REPLACE FUNCTION cleanup_old_locations()
RETURNS void AS $$
BEGIN
  -- Manter apenas as últimas 1000 localizações por usuário
  DELETE FROM user_locations
  WHERE id IN (
    SELECT id FROM (
      SELECT id, ROW_NUMBER() OVER (
        PARTITION BY user_id
        ORDER BY created_at DESC
      ) as rn
      FROM user_locations
    ) t
    WHERE rn > 1000
  );

  -- Deletar localizações muito antigas (mais de 30 dias)
  DELETE FROM user_locations
  WHERE created_at < NOW() - INTERVAL '30 days';
END;
$$ language 'plpgsql';

-- Comentários para documentação
COMMENT ON TABLE user_locations IS 'Armazena as localizações dos usuários em tempo real';
COMMENT ON COLUMN user_locations.user_id IS 'ID do usuário (referência para auth.users)';
COMMENT ON COLUMN user_locations.latitude IS 'Latitude da localização';
COMMENT ON COLUMN user_locations.longitude IS 'Longitude da localização';
COMMENT ON COLUMN user_locations.accuracy IS 'Precisão da localização em metros';
COMMENT ON COLUMN user_locations.heading IS 'Direção em graus (0-360)';
COMMENT ON COLUMN user_locations.speed IS 'Velocidade em m/s';
COMMENT ON COLUMN user_locations.altitude IS 'Altitude em metros';
COMMENT ON COLUMN user_locations.timestamp IS 'Timestamp da captura da localização';
COMMENT ON COLUMN user_locations.address IS 'Endereço obtido via reverse geocoding';
COMMENT ON COLUMN user_locations.is_active IS 'Indica se é a localização atual ativa do usuário';
