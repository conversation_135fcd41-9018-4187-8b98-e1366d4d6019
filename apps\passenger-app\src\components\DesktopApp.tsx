import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { ProtectedRoute } from './ProtectedRoute';
import { DeviceWrapper } from './DeviceWrapper';

// 🎯 NOVA ABORDAGEM: Usar páginas MOBILE dentro do mockup
import LoginMobile from '../pages/LoginMobile';
import RegisterMobile from '../pages/RegisterMobile';
import DashboardMobile from '../pages/DashboardMobile';
import RequestRideMobile from '../pages/RequestRideMobile';
import RideTrackingMobile from '../pages/RideTrackingMobile';
import SetupMobile from '../pages/SetupMobile';

// 💻 DESKTOP APP - EXPERIÊNCIA DESKTOP COM MOCKUP
// 🎯 NOVA ABORDAGEM: Páginas MOBILE rodando DENTRO do mockup iPhone
// Isso permite que o desktop veja a experiência mobile em um container visual

export const DesktopApp: React.FC = () => {
  return (
    <DeviceWrapper>
      <div className="desktop-app">
        <Routes>
          {/* 🎯 Rotas públicas usando páginas MOBILE dentro do mockup */}
          <Route path="/desktop/login" element={<LoginMobile />} />
          <Route path="/desktop/register" element={<RegisterMobile />} />

          {/* 🎯 Rotas protegidas usando páginas MOBILE dentro do mockup */}
          <Route path="/desktop/dashboard" element={
            <ProtectedRoute>
              <DashboardMobile />
            </ProtectedRoute>
          } />

          <Route path="/desktop/request-ride" element={
            <ProtectedRoute>
              <RequestRideMobile />
            </ProtectedRoute>
          } />

          <Route path="/desktop/ride-tracking" element={
            <ProtectedRoute>
              <RideTrackingMobile />
            </ProtectedRoute>
          } />

          <Route path="/desktop/setup" element={
            <ProtectedRoute>
              <SetupMobile />
            </ProtectedRoute>
          } />

          {/* Redirect padrão */}
          <Route path="/desktop/*" element={<LoginMobile />} />
        </Routes>
      </div>
    </DeviceWrapper>
  );
};

export default DesktopApp;
