import { supabase } from '../lib/supabase'

export interface DemandPrediction {
  area: string
  coordinates: { lat: number; lng: number }
  predicted_demand: number
  confidence: number
  factors: {
    historical: number
    weather: number
    events: number
    time_of_day: number
    day_of_week: number
    seasonality: number
  }
  surge_probability: number
  recommended_drivers: number
  prediction_time: string
  valid_until: string
}

export interface HistoricalPattern {
  hour: number
  day_of_week: number
  average_rides: number
  peak_multiplier: number
  weather_sensitivity: number
}

export interface EventImpact {
  event_type: string
  location: { lat: number; lng: number }
  radius_km: number
  demand_multiplier: number
  start_time: string
  end_time: string
}

export interface WeatherImpact {
  condition: string
  temperature: number
  precipitation: number
  demand_multiplier: number
  affected_areas: string[]
}

class DemandPredictionService {
  private readonly PREDICTION_AREAS = [
    { name: 'Centro', lat: -23.5505, lng: -46.6333, radius: 2 },
    { name: '<PERSON><PERSON>', lat: -23.5629, lng: -46.6544, radius: 1.5 },
    { name: '<PERSON><PERSON>', lat: -23.5505, lng: -46.6914, radius: 1 },
    { name: '<PERSON><PERSON><PERSON>', lat: -23.5751, lng: -46.6742, radius: 1 },
    { name: '<PERSON><PERSON>', lat: -23.6062, lng: -46.6634, radius: 1.5 },
    { name: 'Pinheiros', lat: -23.5677, lng: -46.7019, radius: 1.5 },
    { name: 'Brooklin', lat: -23.6108, lng: -46.6926, radius: 1.5 },
    { name: 'Aeroporto Guarulhos', lat: -23.4356, lng: -46.4731, radius: 3 },
    { name: 'Aeroporto Congonhas', lat: -23.6266, lng: -46.6556, radius: 2 }
  ]

  private historicalPatterns: Map<string, HistoricalPattern[]> = new Map()
  private eventImpacts: EventImpact[] = []
  private weatherCache: WeatherImpact | null = null
  private lastPredictionUpdate = 0
  private predictionCache: Map<string, DemandPrediction> = new Map()

  /**
   * Get demand predictions for all areas
   */
  async getDemandPredictions(forceRefresh: boolean = false): Promise<DemandPrediction[]> {
    const now = Date.now()
    
    // Use cache if recent (5 minutes)
    if (!forceRefresh && now - this.lastPredictionUpdate < 5 * 60 * 1000) {
      return Array.from(this.predictionCache.values())
    }

    console.log('🔮 Generating demand predictions...')

    try {
      // Load required data
      await this.loadHistoricalPatterns()
      await this.loadEventImpacts()
      await this.loadWeatherImpact()

      const predictions: DemandPrediction[] = []

      for (const area of this.PREDICTION_AREAS) {
        const prediction = await this.predictDemandForArea(area)
        predictions.push(prediction)
        this.predictionCache.set(area.name, prediction)
      }

      this.lastPredictionUpdate = now
      console.log(`🔮 Generated ${predictions.length} demand predictions`)

      return predictions

    } catch (error) {
      console.error('❌ Error generating demand predictions:', error)
      return []
    }
  }

  /**
   * Predict demand for specific area
   */
  private async predictDemandForArea(area: { name: string; lat: number; lng: number; radius: number }): Promise<DemandPrediction> {
    const now = new Date()
    const hour = now.getHours()
    const dayOfWeek = now.getDay()

    // Get historical pattern for this area
    const patterns = this.historicalPatterns.get(area.name) || []
    const currentPattern = patterns.find(p => p.hour === hour && p.day_of_week === dayOfWeek)
    
    // Base demand from historical data
    let baseDemand = currentPattern?.average_rides || this.estimateBaseDemand(hour, dayOfWeek)
    
    // Calculate factors
    const factors = {
      historical: this.calculateHistoricalFactor(area.name, hour, dayOfWeek),
      weather: this.calculateWeatherFactor(area.name),
      events: this.calculateEventFactor(area.lat, area.lng),
      time_of_day: this.calculateTimeOfDayFactor(hour),
      day_of_week: this.calculateDayOfWeekFactor(dayOfWeek),
      seasonality: this.calculateSeasonalityFactor(now)
    }

    // Apply factors to base demand
    let predictedDemand = baseDemand
    predictedDemand *= factors.historical
    predictedDemand *= factors.weather
    predictedDemand *= factors.events
    predictedDemand *= factors.time_of_day
    predictedDemand *= factors.day_of_week
    predictedDemand *= factors.seasonality

    // Calculate confidence based on data quality
    const confidence = this.calculateConfidence(area.name, factors)

    // Calculate surge probability
    const surgeProbability = this.calculateSurgeProbability(predictedDemand, baseDemand)

    // Recommend driver count
    const recommendedDrivers = Math.ceil(predictedDemand * 1.2) // 20% buffer

    const validUntil = new Date(now.getTime() + 15 * 60 * 1000) // Valid for 15 minutes

    return {
      area: area.name,
      coordinates: { lat: area.lat, lng: area.lng },
      predicted_demand: Math.round(predictedDemand),
      confidence,
      factors,
      surge_probability: surgeProbability,
      recommended_drivers: recommendedDrivers,
      prediction_time: now.toISOString(),
      valid_until: validUntil.toISOString()
    }
  }

  /**
   * Load historical patterns from database
   */
  private async loadHistoricalPatterns(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('demand_patterns')
        .select('*')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days

      if (error) throw error

      // Group patterns by area
      this.historicalPatterns.clear()
      
      if (data) {
        for (const pattern of data) {
          const areaPatterns = this.historicalPatterns.get(pattern.area) || []
          areaPatterns.push({
            hour: pattern.hour,
            day_of_week: pattern.day_of_week,
            average_rides: pattern.average_rides,
            peak_multiplier: pattern.peak_multiplier,
            weather_sensitivity: pattern.weather_sensitivity
          })
          this.historicalPatterns.set(pattern.area, areaPatterns)
        }
      }

    } catch (error) {
      console.warn('⚠️ Failed to load historical patterns:', error)
    }
  }

  /**
   * Load event impacts
   */
  private async loadEventImpacts(): Promise<void> {
    try {
      const now = new Date()
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)

      const { data, error } = await supabase
        .from('event_impacts')
        .select('*')
        .gte('start_time', now.toISOString())
        .lte('start_time', tomorrow.toISOString())
        .eq('is_active', true)

      if (error) throw error

      this.eventImpacts = data || []

    } catch (error) {
      console.warn('⚠️ Failed to load event impacts:', error)
      this.eventImpacts = []
    }
  }

  /**
   * Load weather impact
   */
  private async loadWeatherImpact(): Promise<void> {
    try {
      // In a real implementation, integrate with weather API
      // For now, simulate weather conditions
      
      const hour = new Date().getHours()
      const random = Math.random()
      
      let condition = 'clear'
      let multiplier = 1.0
      
      // Simulate rain probability
      if ((hour >= 16 && hour <= 19) && random < 0.3) {
        condition = 'rain'
        multiplier = 1.4
      } else if (random < 0.1) {
        condition = 'storm'
        multiplier = 1.8
      } else if (hour >= 22 || hour <= 6) {
        condition = 'clear_night'
        multiplier = 0.7
      }

      this.weatherCache = {
        condition,
        temperature: 20 + random * 15, // 20-35°C
        precipitation: condition === 'rain' ? 50 : condition === 'storm' ? 80 : 0,
        demand_multiplier: multiplier,
        affected_areas: condition !== 'clear' ? ['all'] : []
      }

    } catch (error) {
      console.warn('⚠️ Failed to load weather impact:', error)
      this.weatherCache = {
        condition: 'clear',
        temperature: 25,
        precipitation: 0,
        demand_multiplier: 1.0,
        affected_areas: []
      }
    }
  }

  /**
   * Calculate various factors
   */
  private calculateHistoricalFactor(area: string, hour: number, dayOfWeek: number): number {
    const patterns = this.historicalPatterns.get(area) || []
    const pattern = patterns.find(p => p.hour === hour && p.day_of_week === dayOfWeek)
    
    return pattern?.peak_multiplier || 1.0
  }

  private calculateWeatherFactor(area: string): number {
    if (!this.weatherCache) return 1.0
    
    const { condition, demand_multiplier, affected_areas } = this.weatherCache
    
    if (affected_areas.includes('all') || affected_areas.includes(area)) {
      return demand_multiplier
    }
    
    return 1.0
  }

  private calculateEventFactor(lat: number, lng: number): number {
    let maxMultiplier = 1.0
    
    for (const event of this.eventImpacts) {
      const distance = this.calculateDistance(
        lat, lng, 
        event.location.lat, event.location.lng
      )
      
      if (distance <= event.radius_km) {
        maxMultiplier = Math.max(maxMultiplier, event.demand_multiplier)
      }
    }
    
    return maxMultiplier
  }

  private calculateTimeOfDayFactor(hour: number): number {
    // Rush hours
    if ((hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19)) {
      return 1.5
    }
    
    // Lunch time
    if (hour >= 11 && hour <= 14) {
      return 1.2
    }
    
    // Evening entertainment
    if (hour >= 20 && hour <= 23) {
      return 1.3
    }
    
    // Late night/early morning
    if (hour >= 0 && hour <= 6) {
      return 0.4
    }
    
    return 1.0
  }

  private calculateDayOfWeekFactor(dayOfWeek: number): number {
    // Weekend (Friday night, Saturday, Sunday)
    if (dayOfWeek === 5 || dayOfWeek === 6 || dayOfWeek === 0) {
      return 1.3
    }
    
    // Monday (lower demand)
    if (dayOfWeek === 1) {
      return 0.8
    }
    
    return 1.0
  }

  private calculateSeasonalityFactor(date: Date): number {
    const month = date.getMonth()
    
    // Holiday season (December)
    if (month === 11) {
      return 1.4
    }
    
    // Summer vacation (January, February)
    if (month === 0 || month === 1) {
      return 0.8
    }
    
    // Carnival season (February/March)
    if (month === 1 || month === 2) {
      return 1.6
    }
    
    return 1.0
  }

  private calculateConfidence(area: string, factors: any): number {
    let confidence = 0.7 // Base confidence
    
    // Increase confidence if we have historical data
    if (this.historicalPatterns.has(area)) {
      confidence += 0.2
    }
    
    // Decrease confidence for extreme factors
    const extremeFactors = Object.values(factors).filter(f => f > 2.0 || f < 0.5)
    confidence -= extremeFactors.length * 0.05
    
    return Math.max(0.3, Math.min(1.0, confidence))
  }

  private calculateSurgeProbability(predictedDemand: number, baseDemand: number): number {
    const ratio = predictedDemand / baseDemand
    
    if (ratio >= 2.0) return 0.9
    if (ratio >= 1.5) return 0.6
    if (ratio >= 1.2) return 0.3
    
    return 0.1
  }

  private estimateBaseDemand(hour: number, dayOfWeek: number): number {
    // Simple estimation based on typical patterns
    const baseHourlyDemand = [
      2, 1, 1, 1, 2, 4, 8, 15, 12, 8, 6, 10,  // 0-11
      12, 8, 6, 8, 12, 18, 15, 12, 10, 8, 6, 4  // 12-23
    ]
    
    let demand = baseHourlyDemand[hour] || 5
    
    // Weekend adjustment
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      if (hour >= 10 && hour <= 14) demand *= 1.3 // Weekend brunch/lunch
      if (hour >= 20 && hour <= 2) demand *= 1.5 // Weekend nightlife
    }
    
    return demand
  }

  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371 // Earth's radius in km
    const dLat = this.deg2rad(lat2 - lat1)
    const dLng = this.deg2rad(lng2 - lng1)
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * 
      Math.sin(dLng/2) * Math.sin(dLng/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI/180)
  }

  /**
   * Update historical patterns with new data
   */
  async updateHistoricalPatterns(rideData: {
    area: string
    hour: number
    day_of_week: number
    ride_count: number
    weather_condition: string
  }): Promise<void> {
    try {
      await supabase
        .from('demand_patterns')
        .upsert({
          area: rideData.area,
          hour: rideData.hour,
          day_of_week: rideData.day_of_week,
          average_rides: rideData.ride_count,
          weather_condition: rideData.weather_condition,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'area,hour,day_of_week'
        })

    } catch (error) {
      console.error('❌ Error updating historical patterns:', error)
    }
  }

  /**
   * Get surge areas based on predictions
   */
  getSurgeAreas(predictions: DemandPrediction[]): Array<{
    area: string
    coordinates: { lat: number; lng: number }
    surge_level: 'low' | 'medium' | 'high'
    multiplier: number
  }> {
    return predictions
      .filter(p => p.surge_probability > 0.3)
      .map(p => ({
        area: p.area,
        coordinates: p.coordinates,
        surge_level: p.surge_probability > 0.7 ? 'high' : 
                    p.surge_probability > 0.5 ? 'medium' : 'low',
        multiplier: 1 + (p.surge_probability * 1.5) // 1.0x to 2.5x
      }))
      .sort((a, b) => b.multiplier - a.multiplier)
  }

  /**
   * Get driver recommendations
   */
  getDriverRecommendations(predictions: DemandPrediction[]): Array<{
    area: string
    recommended_drivers: number
    current_demand: number
    priority: 'low' | 'medium' | 'high'
  }> {
    return predictions
      .map(p => ({
        area: p.area,
        recommended_drivers: p.recommended_drivers,
        current_demand: p.predicted_demand,
        priority: p.predicted_demand > 15 ? 'high' : 
                 p.predicted_demand > 8 ? 'medium' : 'low'
      }))
      .sort((a, b) => b.current_demand - a.current_demand)
  }

  /**
   * Clear prediction cache
   */
  clearCache(): void {
    this.predictionCache.clear()
    this.lastPredictionUpdate = 0
    console.log('🔮 Demand prediction cache cleared')
  }
}

export const demandPredictionService = new DemandPredictionService()
export default demandPredictionService
