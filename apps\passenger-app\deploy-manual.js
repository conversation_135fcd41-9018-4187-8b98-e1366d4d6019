#!/usr/bin/env node

/**
 * 🚀 SCRIPT DE DEPLOY MANUAL PARA MOBIDRIVE
 *
 * Este script faz o deploy da aplicação MobiDrive para a Vercel
 * usando uma abordagem mais robusta e com melhor tratamento de erros.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 Iniciando deploy manual do MobiDrive...\n');

// Função para executar comandos com tratamento de erro
function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    const result = execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
      encoding: 'utf8'
    });
    console.log(`✅ ${description} concluído!\n`);
    return result;
  } catch (error) {
    console.error(`❌ Erro em: ${description}`);
    console.error(`Comando: ${command}`);
    console.error(`Erro: ${error.message}\n`);
    throw error;
  }
}

// Função para verificar se o arquivo existe
function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${description} encontrado: ${filePath}`);
    return true;
  } else {
    console.log(`❌ ${description} não encontrado: ${filePath}`);
    return false;
  }
}

async function deployMobiDrive() {
  try {
    // 1. Verificar estrutura do projeto
    console.log('🔍 Verificando estrutura do projeto...');

    const requiredFiles = [
      { path: 'package.json', desc: 'Package.json' },
      { path: 'vercel.json', desc: 'Configuração Vercel' },
      { path: 'dist/index.html', desc: 'Build da aplicação' }
    ];

    for (const file of requiredFiles) {
      if (!checkFile(file.path, file.desc)) {
        throw new Error(`Arquivo obrigatório não encontrado: ${file.path}`);
      }
    }

    console.log('✅ Estrutura do projeto verificada!\n');

    // 2. Limpar cache do npm
    console.log('🧹 Limpando cache...');
    try {
      runCommand('npm cache clean --force', 'Limpeza do cache npm');
    } catch (error) {
      console.log('⚠️  Aviso: Não foi possível limpar o cache, continuando...\n');
    }

    // 3. Verificar se o build existe e está atualizado
    if (!fs.existsSync('dist') || !fs.existsSync('dist/index.html')) {
      console.log('🔨 Build não encontrado, criando...');
      runCommand('npm run build:prod', 'Build da aplicação');
    } else {
      console.log('✅ Build encontrado, usando existente\n');
    }

    // 4. Fazer deploy usando vercel
    console.log('🚀 Iniciando deploy para Vercel...');

    // Primeiro, tentar fazer login (se necessário)
    try {
      runCommand('npx vercel whoami', 'Verificação de autenticação');
    } catch (error) {
      console.log('🔐 Fazendo login na Vercel...');
      runCommand('npx vercel login', 'Login na Vercel');
    }

    // Deploy com configurações específicas
    const deployCommand = 'npx vercel --prod --yes --force';
    runCommand(deployCommand, 'Deploy para produção');

    console.log('\n🎉 DEPLOY CONCLUÍDO COM SUCESSO!');
    console.log('🌐 Sua aplicação MobiDrive está agora online!');
    console.log('📱 Acesse: https://passenger-app.vercel.app (ou URL fornecida acima)');

  } catch (error) {
    console.error('\n❌ ERRO NO DEPLOY:');
    console.error(error.message);
    console.error('\n🔧 Soluções possíveis:');
    console.error('1. Verificar se todas as dependências estão instaladas');
    console.error('2. Verificar se o build foi criado corretamente');
    console.error('3. Verificar autenticação na Vercel');
    console.error('4. Verificar configurações no vercel.json');

    process.exit(1);
  }
}

// Executar deploy
deployMobiDrive();
