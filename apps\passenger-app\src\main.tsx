import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// ⚡ OPTIMIZED MAIN.TSX - LAZY SERVICE INITIALIZATION
// Services are now loaded asynchronously to improve initial load time

// Configure Mapbox token immediately (required for map components)
const MAPBOX_TOKEN = 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'

if (typeof window !== 'undefined') {
  (window as any).MAPBOX_ACCESS_TOKEN = MAPBOX_TOKEN
}

// Initialize services after app renders (non-blocking)
const initializeServices = async () => {
  try {
    // Import services asynchronously to avoid blocking initial render
    const { analyticsService } = await import('./services/AnalyticsService')
    const { cacheService } = await import('./services/CacheService')

    // Initialize services with a small delay to not block UI
    setTimeout(() => {
      analyticsService.trackPageView('app_start', performance.now())
      cacheService.preloadCommonData()
    }, 100)

    // Load test utilities only in development
    if (import.meta.env.DEV) {
      import('./utils/testRealtime')
    }

  } catch (error) {
    console.warn('⚠️ Service initialization failed:', error)
  }
}

// Render app immediately
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)

// Initialize services after render
initializeServices()
