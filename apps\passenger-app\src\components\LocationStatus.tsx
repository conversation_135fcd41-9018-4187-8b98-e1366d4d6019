import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { MapPin, Navigation, Clock, Wifi, WifiOff, RefreshCw, Database, HardDrive } from 'lucide-react'
import { useLocationTracking } from '../hooks/useLocationTracking'

interface LocationStatusProps {
  className?: string
  showDetails?: boolean
}

const LocationStatus: React.FC<LocationStatusProps> = ({
  className = '',
  showDetails = false
}) => {
  const {
    isTracking,
    currentLocation,
    loading,
    error,
    startTracking,
    stopTracking,
    getCurrentLocation,
    hasLocation,
    locationAccuracy,
    locationAddress,
    lastLocationUpdate
  } = useLocationTracking()

  const [storageMode, setStorageMode] = useState<'supabase' | 'local' | 'unknown'>('unknown')

  // Atualizar localização ao montar o componente
  useEffect(() => {
    if (isTracking && !currentLocation) {
      getCurrentLocation()
    }
  }, [isTracking, currentLocation, getCurrentLocation])

  // Detectar modo de armazenamento baseado nos logs do console
  useEffect(() => {
    const originalLog = console.log
    console.log = (...args) => {
      const message = args.join(' ')
      if (message.includes('Tabela user_locations confirmada')) {
        setStorageMode('supabase')
      } else if (message.includes('usando localStorage') || message.includes('salva localmente')) {
        setStorageMode('local')
      }
      originalLog.apply(console, args)
    }

    return () => {
      console.log = originalLog
    }
  }, [])

  const formatLastUpdate = (timestamp?: string) => {
    if (!timestamp) return 'Nunca'

    const now = new Date()
    const updateTime = new Date(timestamp)
    const diffMs = now.getTime() - updateTime.getTime()
    const diffMinutes = Math.floor(diffMs / 60000)

    if (diffMinutes < 1) return 'Agora'
    if (diffMinutes < 60) return `${diffMinutes}min atrás`

    const diffHours = Math.floor(diffMinutes / 60)
    if (diffHours < 24) return `${diffHours}h atrás`

    return updateTime.toLocaleDateString()
  }

  const getAccuracyColor = (accuracy?: number) => {
    if (!accuracy) return 'text-gray-400'
    if (accuracy <= 10) return 'text-green-500'
    if (accuracy <= 50) return 'text-yellow-500'
    return 'text-red-500'
  }

  const getAccuracyText = (accuracy?: number) => {
    if (!accuracy) return 'Desconhecida'
    if (accuracy <= 10) return 'Excelente'
    if (accuracy <= 50) return 'Boa'
    return 'Baixa'
  }

  if (!showDetails) {
    // Versão compacta para header
    return (
      <motion.div
        className={`flex items-center space-x-2 ${className}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        {isTracking ? (
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 text-green-600">
              <Wifi className="w-4 h-4" />
              <span className="text-sm font-medium">GPS Ativo</span>
            </div>
            {storageMode !== 'unknown' && (
              <div className={`flex items-center space-x-1 ${storageMode === 'supabase' ? 'text-blue-600' : 'text-orange-600'}`}>
                {storageMode === 'supabase' ? (
                  <Database className="w-3 h-3" />
                ) : (
                  <HardDrive className="w-3 h-3" />
                )}
                <span className="text-xs">{storageMode === 'supabase' ? 'DB' : 'Local'}</span>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center space-x-1 text-gray-400">
            <WifiOff className="w-4 h-4" />
            <span className="text-sm">GPS Inativo</span>
          </div>
        )}

        {loading && (
          <RefreshCw className="w-3 h-3 text-blue-500 animate-spin" />
        )}
      </motion.div>
    )
  }

  // Versão detalhada
  return (
    <motion.div
      className={`bg-white rounded-2xl shadow-lg border border-gray-200 p-6 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-xl ${isTracking ? 'bg-green-100' : 'bg-gray-100'}`}>
            {isTracking ? (
              <Navigation className="w-5 h-5 text-green-600" />
            ) : (
              <MapPin className="w-5 h-5 text-gray-400" />
            )}
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Localização GPS</h3>
            <p className="text-sm text-gray-500">
              {isTracking ? 'Rastreamento ativo' : 'Rastreamento inativo'}
            </p>
          </div>
        </div>

        {/* Toggle Button */}
        <motion.button
          onClick={isTracking ? stopTracking : startTracking}
          disabled={loading}
          className={`px-4 py-2 rounded-xl font-medium transition-all ${
            isTracking
              ? 'bg-red-100 text-red-600 hover:bg-red-200'
              : 'bg-green-100 text-green-600 hover:bg-green-200'
          } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
          whileHover={{ scale: loading ? 1 : 1.05 }}
          whileTap={{ scale: loading ? 1 : 0.95 }}
        >
          {loading ? (
            <RefreshCw className="w-4 h-4 animate-spin" />
          ) : isTracking ? (
            'Parar'
          ) : (
            'Iniciar'
          )}
        </motion.button>
      </div>

      {/* Status Details */}
      {isTracking && (
        <div className="space-y-3">
          {/* Current Location */}
          {hasLocation && currentLocation && (
            <div className="bg-gray-50 rounded-xl p-4">
              <div className="flex items-start space-x-3">
                <MapPin className="w-4 h-4 text-blue-500 mt-1" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">Localização Atual</p>
                  <p className="text-xs text-gray-600 mt-1">
                    {locationAddress || `${currentLocation.latitude.toFixed(6)}, ${currentLocation.longitude.toFixed(6)}`}
                  </p>
                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                    <span className={`flex items-center space-x-1 ${getAccuracyColor(locationAccuracy)}`}>
                      <div className="w-2 h-2 rounded-full bg-current"></div>
                      <span>Precisão: {getAccuracyText(locationAccuracy)}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{formatLastUpdate(lastLocationUpdate)}</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4">
              <p className="text-sm text-red-600">
                ⚠️ {error}
              </p>
            </div>
          )}

          {/* Loading State */}
          {loading && (
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
              <div className="flex items-center space-x-2">
                <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
                <p className="text-sm text-blue-600">
                  Atualizando localização...
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Info when not tracking */}
      {!isTracking && (
        <div className="bg-green-50 rounded-xl p-4 border border-green-200">
          <p className="text-sm text-green-700 font-medium">
            🚗 Location Tracking Otimizado
          </p>
          <p className="text-xs text-green-600 mt-2">
            O rastreamento será ativado automaticamente quando você solicitar uma corrida. Isso melhora a performance e economiza bateria.
          </p>
        </div>
      )}
    </motion.div>
  )
}

export default LocationStatus
