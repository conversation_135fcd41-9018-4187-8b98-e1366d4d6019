import { supabase } from '../lib/supabase';

export interface SimpleNotification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'ride_request' | 'ride_accepted' | 'ride_started' | 'ride_completed' | 'payment' | 'chat_message' | 'system';
  data?: {
    ride_id?: string;
    driver_id?: string;
    amount?: number;
    [key: string]: any;
  };
  read_at?: string;
  created_at: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

export class SimpleNotificationService {
  private static instance: SimpleNotificationService;

  static getInstance(): SimpleNotificationService {
    if (!SimpleNotificationService.instance) {
      SimpleNotificationService.instance = new SimpleNotificationService();
    }
    return SimpleNotificationService.instance;
  }

  constructor() {
    this.requestPermission();
  }

  // Solicitar permissão para notificações
  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('Este navegador não suporta notificações');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }

    return false;
  }

  // Criar notificação no banco
  async createNotification(
    userId: string,
    title: string,
    message: string,
    type: SimpleNotification['type'],
    data?: SimpleNotification['data'],
    priority: SimpleNotification['priority'] = 'normal'
  ): Promise<SimpleNotification | null> {
    try {
      const { data: notification, error } = await supabase
        .from('notifications')
        .insert({
          user_id: userId,
          title,
          message,
          type,
          data,
          priority
        })
        .select()
        .single();

      if (error) {
        console.error('Erro ao criar notificação:', error);
        return null;
      }

      // Mostrar notificação nativa
      await this.showNativeNotification(notification);

      return notification;
    } catch (error) {
      console.error('Erro ao criar notificação:', error);
      return null;
    }
  }

  // Mostrar notificação nativa do navegador
  private async showNativeNotification(notification: SimpleNotification) {
    try {
      if (Notification.permission !== 'granted') return;

      const notif = new Notification(notification.title, {
        body: notification.message,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        tag: notification.type,
        requireInteraction: notification.priority === 'urgent',
        data: notification.data
      });

      // Configurar clique na notificação
      notif.onclick = () => {
        window.focus();
        notif.close();
        
        // Navegar para página relevante baseado no tipo
        this.handleNotificationClick(notification);
      };

      // Auto-fechar notificações normais após 5 segundos
      if (notification.priority !== 'urgent') {
        setTimeout(() => notif.close(), 5000);
      }

    } catch (error) {
      console.error('Erro ao mostrar notificação nativa:', error);
    }
  }

  // Lidar com clique na notificação
  private handleNotificationClick(notification: SimpleNotification) {
    const { type, data } = notification;

    switch (type) {
      case 'ride_request':
      case 'ride_accepted':
      case 'ride_started':
        if (data?.ride_id) {
          window.location.href = `/ride-tracking/${data.ride_id}`;
        }
        break;
      case 'chat_message':
        if (data?.ride_id) {
          window.location.href = `/ride-tracking/${data.ride_id}?tab=chat`;
        }
        break;
      case 'payment':
        window.location.href = '/payment-history';
        break;
      default:
        window.location.href = '/dashboard';
    }
  }

  // Obter notificações do usuário
  async getUserNotifications(limit: number = 20, offset: number = 0): Promise<SimpleNotification[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Erro ao obter notificações:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Erro ao obter notificações:', error);
      return [];
    }
  }

  // Marcar notificação como lida
  async markAsRead(notificationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read_at: new Date().toISOString() })
        .eq('id', notificationId);

      if (error) {
        console.error('Erro ao marcar como lida:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao marcar como lida:', error);
      return false;
    }
  }

  // Marcar todas como lidas
  async markAllAsRead(): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return false;

      const { error } = await supabase
        .from('notifications')
        .update({ read_at: new Date().toISOString() })
        .eq('user_id', user.id)
        .is('read_at', null);

      if (error) {
        console.error('Erro ao marcar todas como lidas:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error);
      return false;
    }
  }

  // Obter contagem de não lidas
  async getUnreadCount(): Promise<number> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return 0;

      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .is('read_at', null);

      if (error) {
        console.error('Erro ao obter contagem:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Erro ao obter contagem:', error);
      return 0;
    }
  }

  // Subscrever a notificações em tempo real
  subscribeToNotifications(callback: (notification: SimpleNotification) => void) {
    return supabase
      .channel('user_notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications'
        },
        (payload) => {
          const notification = payload.new as SimpleNotification;
          callback(notification);
          
          // Mostrar notificação nativa automaticamente
          this.showNativeNotification(notification);
        }
      )
      .subscribe();
  }

  // Deletar notificação
  async deleteNotification(notificationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) {
        console.error('Erro ao deletar notificação:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao deletar notificação:', error);
      return false;
    }
  }

  // Notificações específicas para corridas
  async notifyRideAccepted(userId: string, rideId: string, driverName: string): Promise<void> {
    await this.createNotification(
      userId,
      '🎉 Corrida Aceita!',
      `${driverName} aceitou sua corrida e está a caminho.`,
      'ride_accepted',
      { ride_id: rideId },
      'high'
    );
  }

  async notifyRideStarted(userId: string, rideId: string): Promise<void> {
    await this.createNotification(
      userId,
      '🚗 Corrida Iniciada!',
      'Sua corrida foi iniciada. Tenha uma boa viagem!',
      'ride_started',
      { ride_id: rideId },
      'high'
    );
  }

  async notifyRideCompleted(userId: string, rideId: string, amount: number): Promise<void> {
    await this.createNotification(
      userId,
      '✅ Corrida Finalizada!',
      `Corrida finalizada. Total: R$ ${amount.toFixed(2)}`,
      'ride_completed',
      { ride_id: rideId, amount },
      'normal'
    );
  }

  async notifyNewMessage(userId: string, rideId: string, senderName: string): Promise<void> {
    await this.createNotification(
      userId,
      '💬 Nova Mensagem',
      `${senderName} enviou uma mensagem`,
      'chat_message',
      { ride_id: rideId },
      'normal'
    );
  }

  async notifyPaymentProcessed(userId: string, amount: number, status: 'success' | 'failed'): Promise<void> {
    const title = status === 'success' ? '💳 Pagamento Processado' : '❌ Falha no Pagamento';
    const message = status === 'success' 
      ? `Pagamento de R$ ${amount.toFixed(2)} processado com sucesso`
      : `Falha ao processar pagamento de R$ ${amount.toFixed(2)}`;

    await this.createNotification(
      userId,
      title,
      message,
      'payment',
      { amount, status },
      status === 'failed' ? 'high' : 'normal'
    );
  }
}

export const simpleNotificationService = SimpleNotificationService.getInstance();
