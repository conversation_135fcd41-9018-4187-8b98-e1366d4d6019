import React, { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '../lib/supabase'

// 🔐 AUTH CONTEXT SIMPLIFICADO - VERSÃO ROBUSTA
// Versão simplificada que funciona de forma mais confiável

interface Profile {
  id: string
  email: string
  full_name: string
  user_type: string
  created_at?: string
  updated_at?: string
}

interface AuthState {
  user: User | null
  profile: Profile | null
  loading: boolean
  error: string | null
}

interface AuthContextType extends AuthState {
  signIn: (email: string, password: string) => Promise<{ error?: any }>
  signUp: (email: string, password: string, userData: any) => Promise<{ error?: any }>
  signOut: () => Promise<void>
  updateProfile: (data: Partial<Profile>) => Promise<{ error?: any }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<AuthState>({
    user: null,
    profile: null,
    loading: true,
    error: null,
  })

  useEffect(() => {
    let mounted = true

    // Função para definir loading: false de forma segura
    const setNotLoading = (newState?: Partial<AuthState>) => {
      if (mounted) {
        setState(prev => ({
          ...prev,
          loading: false,
          ...newState
        }))
      }
    }

    // Timeout de segurança - força loading: false após 3 segundos
    const timeoutId = setTimeout(() => {
      console.warn('⚠️ AuthContext: Timeout - forçando loading: false')
      setNotLoading()
    }, 3000)

    // Verificar sessão atual
    const checkSession = async () => {
      try {
        console.log('🔐 AuthContext: Verificando sessão...')
        
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('❌ Erro ao obter sessão:', error)
          setNotLoading({ error: error.message })
          return
        }

        if (session?.user) {
          console.log('✅ Sessão encontrada:', session.user.email)
          
          // Tentar buscar perfil rapidamente
          try {
            const { data: profile } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', session.user.id)
              .single()

            setNotLoading({
              user: session.user,
              profile: profile || {
                id: session.user.id,
                email: session.user.email || '',
                full_name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'Usuário',
                user_type: 'passenger'
              }
            })
          } catch (profileError) {
            console.warn('⚠️ Erro ao buscar perfil, continuando sem perfil')
            setNotLoading({
              user: session.user,
              profile: {
                id: session.user.id,
                email: session.user.email || '',
                full_name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'Usuário',
                user_type: 'passenger'
              }
            })
          }
        } else {
          console.log('ℹ️ Nenhuma sessão encontrada')
          setNotLoading()
        }

        clearTimeout(timeoutId)
      } catch (error: any) {
        console.error('❌ Erro geral na verificação de sessão:', error)
        setNotLoading({ error: error.message })
        clearTimeout(timeoutId)
      }
    }

    // Inicializar
    checkSession()

    // Listener para mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 Auth state change:', event, session?.user?.email)
      
      if (event === 'SIGNED_IN' && session?.user) {
        setNotLoading({
          user: session.user,
          profile: {
            id: session.user.id,
            email: session.user.email || '',
            full_name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'Usuário',
            user_type: 'passenger'
          }
        })
      } else if (event === 'SIGNED_OUT') {
        setNotLoading({
          user: null,
          profile: null
        })
      }
    })

    return () => {
      mounted = false
      clearTimeout(timeoutId)
      subscription.unsubscribe()
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    try {
      console.log('🔐 Fazendo login:', email)
      setState(prev => ({ ...prev, loading: true, error: null }))

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error('❌ Erro no login:', error)
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        return { error }
      }

      console.log('✅ Login bem-sucedido!')
      // O onAuthStateChange vai atualizar o estado
      
      // Redirecionar após um delay
      setTimeout(() => {
        window.location.href = '/dashboard'
      }, 500)

      return {}
    } catch (error: any) {
      console.error('❌ Erro no login:', error)
      setState(prev => ({ ...prev, loading: false, error: error.message }))
      return { error }
    }
  }

  const signUp = async (email: string, password: string, userData: any) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))

      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.full_name,
            user_type: 'passenger',
          },
        },
      })

      if (error) {
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        return { error }
      }

      setState(prev => ({ ...prev, loading: false }))
      return {}
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false, error: error.message }))
      return { error }
    }
  }

  const signOut = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }))
      await supabase.auth.signOut()
      // O onAuthStateChange vai atualizar o estado
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false, error: error.message }))
    }
  }

  const updateProfile = async (data: Partial<Profile>) => {
    try {
      if (!state.user) return { error: { message: 'Usuário não autenticado' } }

      setState(prev => ({ ...prev, loading: true, error: null }))

      const { data: updatedProfile, error } = await supabase
        .from('profiles')
        .update(data)
        .eq('id', state.user.id)
        .select()
        .single()

      if (error) {
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        return { error }
      }

      setState(prev => ({
        ...prev,
        profile: updatedProfile,
        loading: false,
      }))

      return {}
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false, error: error.message }))
      return { error }
    }
  }

  return (
    <AuthContext.Provider
      value={{
        ...state,
        signIn,
        signUp,
        signOut,
        updateProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider')
  }
  return context
}
