// =====================================================
// SCRIPT PARA TESTAR TABELAS DO SUPABASE
// Verifica se as tabelas existem e estão acessíveis
// =====================================================

import { createClient } from '@supabase/supabase-js'

// Configuração do Supabase
const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testSupabaseTables() {
  console.log('🗄️ Testando tabelas do Supabase...')
  console.log('🔗 URL:', supabaseUrl)

  const tablesToTest = [
    'profiles',
    'ride_requests', 
    'payment_methods',
    'drivers',
    'driver_locations',
    'rides',
    'payments',
    'notifications',
    'chat_messages'
  ]

  for (const tableName of tablesToTest) {
    try {
      console.log(`\n🧪 Testando tabela: ${tableName}`)
      
      // Tentar fazer uma consulta simples
      const { data, error, count } = await supabase
        .from(tableName)
        .select('*', { count: 'exact' })
        .limit(1)

      if (error) {
        console.log(`❌ ${tableName}: ${error.message}`)
        console.log(`📋 Código do erro: ${error.code}`)
        console.log(`💡 Detalhes: ${error.details || 'Nenhum detalhe'}`)
      } else {
        console.log(`✅ ${tableName}: OK`)
        console.log(`📊 Total de registros: ${count || 0}`)
        if (data && data.length > 0) {
          console.log(`📋 Campos disponíveis: ${Object.keys(data[0]).join(', ')}`)
        }
      }
    } catch (error) {
      console.log(`💥 ${tableName}: Erro fatal - ${error.message}`)
    }
  }

  // Testar conexão geral
  console.log('\n🔍 Testando conexão geral...')
  try {
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error) {
      console.log('❌ Auth falhou:', error.message)
    } else {
      console.log('✅ Auth funcionando!')
      console.log('👤 Usuário:', user?.email || 'Não logado')
    }
  } catch (error) {
    console.log('💥 Erro na auth:', error.message)
  }

  // Testar RPC functions
  console.log('\n🔧 Testando funções RPC...')
  const rpcFunctions = [
    'get_nearby_drivers',
    'get_table_columns',
    'exec_sql'
  ]

  for (const funcName of rpcFunctions) {
    try {
      console.log(`\n🧪 Testando RPC: ${funcName}`)
      
      let result
      if (funcName === 'get_nearby_drivers') {
        result = await supabase.rpc(funcName, {
          user_lat: -23.5505,
          user_lng: -46.6333,
          radius_km: 10
        })
      } else if (funcName === 'get_table_columns') {
        result = await supabase.rpc(funcName, {
          table_name: 'payment_methods'
        })
      } else {
        result = await supabase.rpc(funcName, {
          sql: 'SELECT 1 as test'
        })
      }

      if (result.error) {
        console.log(`❌ ${funcName}: ${result.error.message}`)
      } else {
        console.log(`✅ ${funcName}: OK`)
        console.log(`📊 Resultado: ${JSON.stringify(result.data).substring(0, 100)}...`)
      }
    } catch (error) {
      console.log(`💥 ${funcName}: Erro fatal - ${error.message}`)
    }
  }
}

// Executar teste
testSupabaseTables()
  .then(() => {
    console.log('\n🏁 Teste das tabelas concluído!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Erro fatal:', error)
    process.exit(1)
  })
