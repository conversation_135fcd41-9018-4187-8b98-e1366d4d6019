// =====================================================
// SCRIPT PARA TESTAR TOKEN DO MAPBOX
// Verifica se o token está válido e funcionando
// =====================================================

const MAPBOX_TOKEN = 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'

async function testMapboxToken() {
  console.log('🔑 Testando token do Mapbox...')
  console.log('📋 Token:', MAPBOX_TOKEN.substring(0, 20) + '...')

  try {
    // 1. Testar API de Geocoding (mais simples)
    console.log('\n🧪 Teste 1: Geocoding API')
    const geocodingUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/São Paulo.json?access_token=${MAPBOX_TOKEN}`
    
    const geocodingResponse = await fetch(geocodingUrl)
    console.log('📊 Status Geocoding:', geocodingResponse.status)
    
    if (geocodingResponse.ok) {
      const geocodingData = await geocodingResponse.json()
      console.log('✅ Geocoding funcionando!')
      console.log('📍 Resultado:', geocodingData.features?.[0]?.place_name || 'Sem resultado')
    } else {
      const errorText = await geocodingResponse.text()
      console.log('❌ Geocoding falhou:', errorText)
    }

    // 2. Testar API de Styles
    console.log('\n🧪 Teste 2: Styles API')
    const stylesUrl = `https://api.mapbox.com/styles/v1/mapbox/streets-v11?access_token=${MAPBOX_TOKEN}`
    
    const stylesResponse = await fetch(stylesUrl)
    console.log('📊 Status Styles:', stylesResponse.status)
    
    if (stylesResponse.ok) {
      console.log('✅ Styles API funcionando!')
    } else {
      const errorText = await stylesResponse.text()
      console.log('❌ Styles falhou:', errorText)
    }

    // 3. Testar API de Directions
    console.log('\n🧪 Teste 3: Directions API')
    const directionsUrl = `https://api.mapbox.com/directions/v5/mapbox/driving/-46.6333,-23.5505;-46.6433,-23.5605?access_token=${MAPBOX_TOKEN}`
    
    const directionsResponse = await fetch(directionsUrl)
    console.log('📊 Status Directions:', directionsResponse.status)
    
    if (directionsResponse.ok) {
      const directionsData = await directionsResponse.json()
      console.log('✅ Directions funcionando!')
      console.log('🛣️ Rotas encontradas:', directionsData.routes?.length || 0)
    } else {
      const errorText = await directionsResponse.text()
      console.log('❌ Directions falhou:', errorText)
    }

    // 4. Verificar informações do token
    console.log('\n🔍 Teste 4: Token Info')
    const tokenInfoUrl = `https://api.mapbox.com/tokens/v2?access_token=${MAPBOX_TOKEN}`
    
    const tokenResponse = await fetch(tokenInfoUrl)
    console.log('📊 Status Token Info:', tokenResponse.status)
    
    if (tokenResponse.ok) {
      const tokenData = await tokenResponse.json()
      console.log('✅ Token válido!')
      console.log('📋 Informações do token:')
      console.log('  - ID:', tokenData.id)
      console.log('  - Usage:', tokenData.usage)
      console.log('  - Scopes:', tokenData.scopes)
    } else {
      const errorText = await tokenResponse.text()
      console.log('❌ Token Info falhou:', errorText)
    }

  } catch (error) {
    console.error('💥 Erro geral:', error.message)
  }
}

// Executar teste
testMapboxToken()
  .then(() => {
    console.log('\n🏁 Teste do token concluído!')
  })
  .catch((error) => {
    console.error('\n💥 Erro fatal:', error)
  })
