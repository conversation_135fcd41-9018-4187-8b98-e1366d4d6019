/**
 * 🔧 GERENCIADOR ROBUSTO DE SUBSCRIPTIONS SUPABASE
 * Previne stack overflow e memory leaks
 */

import { supabase } from '../lib/supabase';

interface SubscriptionInfo {
  id: string;
  channel: any;
  callback: Function;
  createdAt: number;
  isActive: boolean;
}

export class SupabaseSubscriptionManager {
  private static instance: SupabaseSubscriptionManager;
  private subscriptions: Map<string, SubscriptionInfo> = new Map();
  private cleanupInterval: NodeJS.Timeout | null = null;
  private maxSubscriptions = 10; // Limite máximo de subscriptions ativas
  private subscriptionTimeout = 300000; // 5 minutos timeout

  static getInstance(): SupabaseSubscriptionManager {
    if (!SupabaseSubscriptionManager.instance) {
      SupabaseSubscriptionManager.instance = new SupabaseSubscriptionManager();
    }
    return SupabaseSubscriptionManager.instance;
  }

  constructor() {
    this.startCleanupInterval();
    this.setupGlobalCleanup();
  }

  // Criar subscription segura
  createSafeSubscription(
    channelName: string,
    callback: Function,
    options: { timeout?: number; maxRetries?: number } = {}
  ): string {
    const subscriptionId = `${channelName}_${Date.now()}_${Math.random()}`;
    
    try {
      // Verificar limite de subscriptions
      if (this.subscriptions.size >= this.maxSubscriptions) {
        console.warn('⚠️ Limite de subscriptions atingido, limpando antigas...');
        this.cleanupOldSubscriptions();
      }

      // Criar canal único
      const channel = supabase.channel(channelName, {
        config: {
          presence: { key: subscriptionId }
        }
      });

      // Wrapper seguro para callback
      const safeCallback = this.createSafeCallback(callback, subscriptionId);

      // Configurar subscription com timeout
      const timeoutId = setTimeout(() => {
        this.unsubscribe(subscriptionId);
      }, options.timeout || this.subscriptionTimeout);

      // Registrar subscription
      const subscriptionInfo: SubscriptionInfo = {
        id: subscriptionId,
        channel,
        callback: safeCallback,
        createdAt: Date.now(),
        isActive: true
      };

      this.subscriptions.set(subscriptionId, subscriptionInfo);

      // Configurar subscription
      channel.subscribe((status: string) => {
        if (status === 'SUBSCRIBED') {
          console.log(`✅ Subscription ativa: ${subscriptionId}`);
        } else if (status === 'CLOSED' || status === 'CHANNEL_ERROR') {
          console.log(`❌ Subscription fechada: ${subscriptionId}`);
          this.unsubscribe(subscriptionId);
        }
      });

      // Cleanup automático do timeout
      setTimeout(() => {
        clearTimeout(timeoutId);
      }, options.timeout || this.subscriptionTimeout);

      console.log(`🔗 Subscription criada: ${subscriptionId}`);
      return subscriptionId;

    } catch (error) {
      console.error('❌ Erro ao criar subscription:', error);
      return '';
    }
  }

  // Callback seguro que previne loops
  private createSafeCallback(originalCallback: Function, subscriptionId: string): Function {
    let isExecuting = false;
    let executionCount = 0;
    const maxExecutions = 100; // Limite de execuções por minuto

    return (...args: any[]) => {
      // Prevenir execução simultânea
      if (isExecuting) {
        console.warn(`⚠️ Callback já executando para ${subscriptionId}`);
        return;
      }

      // Verificar limite de execuções
      executionCount++;
      if (executionCount > maxExecutions) {
        console.error(`🚨 Limite de execuções atingido para ${subscriptionId}, removendo subscription`);
        this.unsubscribe(subscriptionId);
        return;
      }

      // Reset contador a cada minuto
      setTimeout(() => {
        executionCount = Math.max(0, executionCount - 1);
      }, 60000);

      isExecuting = true;

      try {
        originalCallback(...args);
      } catch (error) {
        console.error(`❌ Erro no callback ${subscriptionId}:`, error);
      } finally {
        isExecuting = false;
      }
    };
  }

  // Unsubscribe seguro
  unsubscribe(subscriptionId: string): boolean {
    const subscription = this.subscriptions.get(subscriptionId);
    
    if (!subscription) {
      console.warn(`⚠️ Subscription não encontrada: ${subscriptionId}`);
      return false;
    }

    try {
      // Marcar como inativa primeiro
      subscription.isActive = false;

      // Tentar unsubscribe do canal
      if (subscription.channel && typeof subscription.channel.unsubscribe === 'function') {
        subscription.channel.unsubscribe();
      }

      // Remover da lista
      this.subscriptions.delete(subscriptionId);
      
      console.log(`✅ Subscription removida: ${subscriptionId}`);
      return true;

    } catch (error) {
      console.error(`❌ Erro ao remover subscription ${subscriptionId}:`, error);
      
      // Forçar remoção mesmo com erro
      this.subscriptions.delete(subscriptionId);
      return false;
    }
  }

  // Limpar subscriptions antigas
  private cleanupOldSubscriptions(): void {
    const now = Date.now();
    const maxAge = 300000; // 5 minutos

    for (const [id, subscription] of this.subscriptions.entries()) {
      if (now - subscription.createdAt > maxAge || !subscription.isActive) {
        this.unsubscribe(id);
      }
    }
  }

  // Limpar todas as subscriptions
  unsubscribeAll(): void {
    console.log('🧹 Limpando todas as subscriptions...');
    
    const subscriptionIds = Array.from(this.subscriptions.keys());
    
    for (const id of subscriptionIds) {
      this.unsubscribe(id);
    }

    console.log(`✅ ${subscriptionIds.length} subscriptions removidas`);
  }

  // Iniciar limpeza automática
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldSubscriptions();
      
      // Log de status
      if (this.subscriptions.size > 0) {
        console.log(`📊 Subscriptions ativas: ${this.subscriptions.size}`);
      }
    }, 60000); // A cada minuto
  }

  // Configurar limpeza global
  private setupGlobalCleanup(): void {
    // Limpeza quando a página é fechada
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.unsubscribeAll();
      });

      window.addEventListener('pagehide', () => {
        this.unsubscribeAll();
      });

      // Limpeza quando a página perde foco (mobile)
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          this.cleanupOldSubscriptions();
        }
      });
    }
  }

  // Obter estatísticas
  getStats(): {
    total: number;
    active: number;
    inactive: number;
    oldest: number;
    newest: number;
  } {
    const now = Date.now();
    const subscriptions = Array.from(this.subscriptions.values());
    
    const active = subscriptions.filter(s => s.isActive).length;
    const inactive = subscriptions.length - active;
    
    const ages = subscriptions.map(s => now - s.createdAt);
    const oldest = ages.length > 0 ? Math.max(...ages) : 0;
    const newest = ages.length > 0 ? Math.min(...ages) : 0;

    return {
      total: subscriptions.length,
      active,
      inactive,
      oldest,
      newest
    };
  }

  // Verificar saúde do sistema
  checkHealth(): {
    healthy: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const stats = this.getStats();
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Verificar se há muitas subscriptions
    if (stats.total > this.maxSubscriptions * 0.8) {
      issues.push(`Muitas subscriptions ativas: ${stats.total}`);
      recommendations.push('Considere reduzir o número de subscriptions simultâneas');
    }

    // Verificar subscriptions muito antigas
    if (stats.oldest > this.subscriptionTimeout) {
      issues.push('Subscriptions muito antigas detectadas');
      recommendations.push('Execute limpeza manual de subscriptions antigas');
    }

    // Verificar subscriptions inativas
    if (stats.inactive > 0) {
      issues.push(`${stats.inactive} subscriptions inativas encontradas`);
      recommendations.push('Execute limpeza de subscriptions inativas');
    }

    return {
      healthy: issues.length === 0,
      issues,
      recommendations
    };
  }

  // Destruir o manager
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    this.unsubscribeAll();
    console.log('🗑️ SupabaseSubscriptionManager destruído');
  }
}

// Hook React para usar o manager
import { useEffect, useRef } from 'react';

export const useSupabaseSubscription = () => {
  const manager = useRef(SupabaseSubscriptionManager.getInstance());

  useEffect(() => {
    return () => {
      // Cleanup automático quando o componente é desmontado
      manager.current.cleanupOldSubscriptions();
    };
  }, []);

  return {
    createSubscription: (channelName: string, callback: Function, options?: any) =>
      manager.current.createSafeSubscription(channelName, callback, options),
    unsubscribe: (id: string) => manager.current.unsubscribe(id),
    unsubscribeAll: () => manager.current.unsubscribeAll(),
    getStats: () => manager.current.getStats(),
    checkHealth: () => manager.current.checkHealth()
  };
};

export default SupabaseSubscriptionManager;
