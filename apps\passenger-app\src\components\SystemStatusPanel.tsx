import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Activity, 
  Wifi, 
  Database, 
  Zap, 
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  RefreshCw
} from 'lucide-react'
import { mapboxService } from '../services/MapboxService'
import { mapboxAnalyticsService } from '../services/MapboxAnalyticsService'
import { mapboxCacheService } from '../services/MapboxCacheService'

interface SystemStatus {
  mapbox: 'online' | 'offline' | 'limited'
  supabase: 'online' | 'offline' | 'limited'
  cache: 'active' | 'inactive'
  analytics: 'active' | 'inactive'
  rateLimiting: {
    current: number
    max: number
    remaining: number
  }
}

export const SystemStatusPanel: React.FC = () => {
  const [status, setStatus] = useState<SystemStatus>({
    mapbox: 'online',
    supabase: 'online',
    cache: 'active',
    analytics: 'active',
    rateLimiting: { current: 0, max: 0, remaining: 0 }
  })
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  const checkSystemStatus = async () => {
    setIsLoading(true)
    
    try {
      // Check Mapbox status
      let mapboxStatus: 'online' | 'offline' | 'limited' = 'online'
      try {
        await mapboxService.searchPlaces('test', { limit: 1 })
      } catch (error: any) {
        if (error.code === 'RATE_LIMIT') {
          mapboxStatus = 'limited'
        } else {
          mapboxStatus = 'offline'
        }
      }

      // Check Supabase status (simplified)
      const supabaseStatus = import.meta.env.VITE_SUPABASE_URL ? 'online' : 'offline'

      // Get cache stats
      const cacheStats = mapboxCacheService.getStats()
      const cacheStatus = cacheStats.searchCache.size > 0 || cacheStats.routeCache.size > 0 ? 'active' : 'inactive'

      // Get analytics status
      const analyticsData = mapboxAnalyticsService.getPerformanceSummary()
      const analyticsStatus = analyticsData.searchPerformance.totalRequests > 0 ? 'active' : 'inactive'

      // Get rate limiting info
      const rateLimitStats = (mapboxService as any).getRateLimitStats?.() || { current: 0, max: 200, remaining: 200 }

      setStatus({
        mapbox: mapboxStatus,
        supabase: supabaseStatus as any,
        cache: cacheStatus,
        analytics: analyticsStatus,
        rateLimiting: rateLimitStats
      })

      setLastUpdate(new Date())
    } catch (error) {
      console.error('Error checking system status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    checkSystemStatus()
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(checkSystemStatus, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'active':
        return 'text-green-500'
      case 'limited':
        return 'text-yellow-500'
      case 'offline':
      case 'inactive':
        return 'text-red-500'
      default:
        return 'text-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'active':
        return <CheckCircle className="w-4 h-4" />
      case 'limited':
        return <AlertTriangle className="w-4 h-4" />
      case 'offline':
      case 'inactive':
        return <AlertTriangle className="w-4 h-4" />
      default:
        return <Activity className="w-4 h-4" />
    }
  }

  const getStatusText = (service: string, status: string) => {
    if (service === 'mapbox') {
      switch (status) {
        case 'online': return 'Operacional'
        case 'limited': return 'Rate Limited'
        case 'offline': return 'Offline'
      }
    }
    
    switch (status) {
      case 'online': return 'Conectado'
      case 'active': return 'Ativo'
      case 'limited': return 'Limitado'
      case 'offline': return 'Desconectado'
      case 'inactive': return 'Inativo'
      default: return 'Desconhecido'
    }
  }

  return (
    <motion.div
      className="bg-white rounded-xl shadow-lg border border-gray-100 p-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Activity className="w-5 h-5 text-blue-500" />
          <h3 className="font-semibold text-gray-900">Status do Sistema</h3>
        </div>
        <motion.button
          onClick={checkSystemStatus}
          disabled={isLoading}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
        </motion.button>
      </div>

      <div className="space-y-3">
        {/* Mapbox Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Wifi className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-700">Mapbox API</span>
          </div>
          <div className={`flex items-center space-x-2 ${getStatusColor(status.mapbox)}`}>
            {getStatusIcon(status.mapbox)}
            <span className="text-sm font-medium">
              {getStatusText('mapbox', status.mapbox)}
            </span>
          </div>
        </div>

        {/* Supabase Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Database className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-700">Supabase</span>
          </div>
          <div className={`flex items-center space-x-2 ${getStatusColor(status.supabase)}`}>
            {getStatusIcon(status.supabase)}
            <span className="text-sm font-medium">
              {getStatusText('supabase', status.supabase)}
            </span>
          </div>
        </div>

        {/* Cache Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Zap className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-700">Cache</span>
          </div>
          <div className={`flex items-center space-x-2 ${getStatusColor(status.cache)}`}>
            {getStatusIcon(status.cache)}
            <span className="text-sm font-medium">
              {getStatusText('cache', status.cache)}
            </span>
          </div>
        </div>

        {/* Analytics Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <TrendingUp className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-700">Analytics</span>
          </div>
          <div className={`flex items-center space-x-2 ${getStatusColor(status.analytics)}`}>
            {getStatusIcon(status.analytics)}
            <span className="text-sm font-medium">
              {getStatusText('analytics', status.analytics)}
            </span>
          </div>
        </div>

        {/* Rate Limiting */}
        <div className="pt-2 border-t border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-700">Rate Limiting</span>
            <span className="text-xs text-gray-500">
              {status.rateLimiting.current}/{status.rateLimiting.max}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                status.rateLimiting.current / status.rateLimiting.max > 0.8
                  ? 'bg-red-500'
                  : status.rateLimiting.current / status.rateLimiting.max > 0.6
                  ? 'bg-yellow-500'
                  : 'bg-green-500'
              }`}
              style={{
                width: `${(status.rateLimiting.current / status.rateLimiting.max) * 100}%`
              }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Disponível: {status.rateLimiting.remaining}</span>
            <span>Máximo: {status.rateLimiting.max}</span>
          </div>
        </div>

        {/* Last Update */}
        <div className="pt-2 border-t border-gray-100">
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <Clock className="w-3 h-3" />
            <span>
              Última atualização: {lastUpdate.toLocaleTimeString('pt-BR')}
            </span>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default SystemStatusPanel
