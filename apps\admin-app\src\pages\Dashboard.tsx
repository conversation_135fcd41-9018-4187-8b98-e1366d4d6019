import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Navigate } from 'react-router-dom'
import { Users, Car, DollarSign, TrendingUp, MapPin, Clock, Star, AlertTriangle, Shield, Settings, LogOut, BarChart3, User<PERSON>he<PERSON> } from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { GradientBackground } from '../components/GradientBackground'

import { useNoZoom } from '../hooks/useNoZoom'
import '../styles/no-zoom.css'

// 📱 DASHBOARD MOBILE ADMIN - DESIGN FIEL AO LOGIN + CONVERSÃO ANDROID NATIVA

export const DashboardMobile: React.FC = () => {
  const { user, signOut, loading } = useAuth()
  const [totalUsers, setTotalUsers] = useState(1247)
  const [totalDrivers, setTotalDrivers] = useState(89)
  const [totalRides, setTotalRides] = useState(3456)
  const [totalRevenue, setTotalRevenue] = useState(87650.50)

  // 🚫 DESABILITA ZOOM COMPLETAMENTE + CONFIGURAÇÕES ANDROID NATIVAS
  useNoZoom()

  // Configurações Android nativas (mantendo design original)
  useEffect(() => {
    // Meta viewport para Android nativo
    let metaViewport = document.querySelector('meta[name="viewport"]')
    if (!metaViewport) {
      metaViewport = document.createElement('meta')
      metaViewport.setAttribute('name', 'viewport')
      document.head.appendChild(metaViewport)
    }
    metaViewport.setAttribute('content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )

    // Configurações de overflow para Android
    document.body.style.margin = '0'
    document.body.style.padding = '0'
    document.body.style.overflow = 'hidden'
    document.documentElement.style.margin = '0'
    document.documentElement.style.padding = '0'
    document.documentElement.style.overflow = 'hidden'
  }, [])

  // Redirect se não logado
  if (!user && !loading) {
    return <Navigate to="/login" replace />
  }

  const handleLogout = async () => {
    await signOut()
  }

  // Mock data para corridas recentes
  const recentRides = [
    { id: 1, passenger: 'João Silva', driver: 'Maria Santos', status: 'Concluída', value: 'R$ 25,50', time: '14:30' },
    { id: 2, passenger: 'Ana Costa', driver: 'Pedro Lima', status: 'Em andamento', value: 'R$ 18,00', time: '14:25' },
    { id: 3, passenger: 'Carlos Oliveira', driver: 'Lucia Ferreira', status: 'Concluída', value: 'R$ 32,80', time: '14:20' },
  ]

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">
      {/* Background Gradient Roxo (TEMA ADMIN) */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-violet-600 opacity-70"></div>

      {/* Overlay muito sutil para legibilidade */}
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Conteúdo Principal */}
      <div className="relative z-10 flex flex-col h-full min-h-screen">

        {/* Header com Logo (DESIGN FIEL AO LOGIN) */}
        <motion.div
          className="pt-8 pb-6 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-violet-500 rounded-xl flex items-center justify-center">
              <Shield className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-purple-400">DRIVE</span>
              </h1>
              <p className="text-xs text-white/70">Painel Administrativo</p>
            </div>
          </div>
        </motion.div>

        {/* Dashboard Principal */}
        <div className="flex-1 px-4 pb-4">
          <div className="max-w-xs mx-auto space-y-4">

            {/* Estatísticas Principais (DESIGN FIEL) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h3 className="text-lg font-semibold text-white mb-4 text-center">📊 Estatísticas Gerais</h3>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-400 mb-1">{totalUsers.toLocaleString()}</div>
                  <div className="text-xs text-white/70">Usuários</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-400 mb-1">{totalDrivers}</div>
                  <div className="text-xs text-white/70">Motoristas</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-400 mb-1">{totalRides.toLocaleString()}</div>
                  <div className="text-xs text-white/70">Corridas</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-400 mb-1">R$ {totalRevenue.toLocaleString('pt-BR', { minimumFractionDigits: 0 })}</div>
                  <div className="text-xs text-white/70">Receita</div>
                </div>
              </div>
            </motion.div>

            {/* Corridas Recentes (DESIGN FIEL) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <h3 className="text-lg font-semibold text-white mb-4 text-center">🚗 Corridas Recentes</h3>
              <div className="space-y-3">
                {recentRides.map((ride) => (
                  <div key={ride.id} className="bg-white/10 rounded-xl p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-white text-sm">{ride.passenger}</p>
                        <p className="text-xs text-white/70">{ride.driver}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-white text-sm">{ride.value}</p>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          ride.status === 'Concluída'
                            ? 'bg-green-500/20 text-green-300'
                            : 'bg-yellow-500/20 text-yellow-300'
                        }`}>
                          {ride.status}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Testar Sistema de Corridas (NOVO SISTEMA 5 TELAS) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <h3 className="text-lg font-semibold text-white mb-4 text-center">🧪 Testar Sistema</h3>
              <motion.button
                onClick={() => window.location.href = '/ride-request/map'}
                className="w-full bg-gradient-to-r from-purple-500 to-violet-500 hover:from-purple-600 hover:to-violet-600 text-white py-4 px-6 rounded-xl font-semibold flex items-center justify-center space-x-3 transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <MapPin className="w-6 h-6" />
                <div className="text-left">
                  <div className="text-base font-semibold">Testar Corrida</div>
                  <div className="text-sm opacity-90">Sistema de 5 telas</div>
                </div>
              </motion.button>
            </motion.div>

            {/* Ações Administrativas (DESIGN FIEL) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <h3 className="text-lg font-semibold text-white mb-4 text-center">⚡ Ações Administrativas</h3>
              <div className="space-y-3">
                <motion.button
                  className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-xl flex items-center space-x-3 transition-all duration-200"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Users className="w-5 h-5" />
                  <div className="text-left">
                    <div className="text-sm font-medium">Gerenciar Usuários</div>
                    <div className="text-xs opacity-80">Visualizar passageiros</div>
                  </div>
                </motion.button>

                <motion.button
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl flex items-center space-x-3 transition-all duration-200"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Car className="w-5 h-5" />
                  <div className="text-left">
                    <div className="text-sm font-medium">Gerenciar Motoristas</div>
                    <div className="text-xs opacity-80">Aprovar motoristas</div>
                  </div>
                </motion.button>

                <motion.button
                  className="w-full bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-4 rounded-xl flex items-center space-x-3 transition-all duration-200"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <BarChart3 className="w-5 h-5" />
                  <div className="text-left">
                    <div className="text-sm font-medium">Relatórios</div>
                    <div className="text-xs opacity-80">Analytics detalhados</div>
                  </div>
                </motion.button>
              </div>
            </motion.div>

            {/* Informações do Usuário (DESIGN FIEL) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <h3 className="text-lg font-semibold text-white mb-4 text-center">👤 Minha Conta</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-white/70">Email:</span>
                  <span className="text-sm font-medium text-white">{user?.email}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-white/70">Nome:</span>
                  <span className="text-sm font-medium text-white">
                    {user?.user_metadata?.full_name || 'Administrador'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-white/70">Tipo:</span>
                  <span className="text-sm font-medium text-purple-400">Administrador</span>
                </div>
              </div>
            </motion.div>

            {/* Botão de Logout (DESIGN FIEL) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
            >
              <motion.button
                onClick={handleLogout}
                className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2 transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <LogOut className="w-4 h-4" />
                <span>Sair da Conta</span>
              </motion.button>
            </motion.div>

          </div>
        </div>

        {/* Footer Simples (DESIGN FIEL) */}
        <motion.div
          className="pb-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <p className="text-white/50 text-xs">
            © 2024 MobiDrive Admin. Todos os direitos reservados.
          </p>
        </motion.div>
      </div>
    </div>
  )
}

export const Dashboard = DashboardMobile
export default DashboardMobile
