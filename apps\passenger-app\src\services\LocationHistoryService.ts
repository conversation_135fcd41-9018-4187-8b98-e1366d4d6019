import { SearchResult } from './MapboxService'

export interface FavoriteLocation {
  id: string
  name: string
  address: string
  coordinates: [number, number]
  type: 'home' | 'work' | 'custom'
  icon: string
  createdAt: number
  lastUsed: number
  useCount: number
}

export interface RecentSearch {
  id: string
  query: string
  result: SearchResult
  timestamp: number
  selected: boolean
}

export interface TripHistory {
  id: string
  origin: SearchResult
  destination: SearchResult
  distance: number
  duration: number
  price: number
  timestamp: number
  completed: boolean
}

class LocationHistoryService {
  private readonly FAVORITES_KEY = 'mapbox_favorites'
  private readonly RECENT_SEARCHES_KEY = 'mapbox_recent_searches'
  private readonly TRIP_HISTORY_KEY = 'mapbox_trip_history'
  private readonly MAX_RECENT_SEARCHES = 20
  private readonly MAX_TRIP_HISTORY = 50

  /**
   * Add location to favorites
   */
  addFavorite(location: Omit<FavoriteLocation, 'id' | 'createdAt' | 'lastUsed' | 'useCount'>): FavoriteLocation {
    const favorites = this.getFavorites()
    
    const newFavorite: FavoriteLocation = {
      ...location,
      id: this.generateId(),
      createdAt: Date.now(),
      lastUsed: Date.now(),
      useCount: 1
    }

    favorites.push(newFavorite)
    this.saveFavorites(favorites)
    
    return newFavorite
  }

  /**
   * Remove favorite location
   */
  removeFavorite(id: string): boolean {
    const favorites = this.getFavorites()
    const index = favorites.findIndex(f => f.id === id)
    
    if (index === -1) return false
    
    favorites.splice(index, 1)
    this.saveFavorites(favorites)
    
    return true
  }

  /**
   * Update favorite location
   */
  updateFavorite(id: string, updates: Partial<FavoriteLocation>): FavoriteLocation | null {
    const favorites = this.getFavorites()
    const index = favorites.findIndex(f => f.id === id)
    
    if (index === -1) return null
    
    favorites[index] = { ...favorites[index], ...updates }
    this.saveFavorites(favorites)
    
    return favorites[index]
  }

  /**
   * Get all favorite locations
   */
  getFavorites(): FavoriteLocation[] {
    try {
      const stored = localStorage.getItem(this.FAVORITES_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Error loading favorites:', error)
      return []
    }
  }

  /**
   * Get favorites by type
   */
  getFavoritesByType(type: FavoriteLocation['type']): FavoriteLocation[] {
    return this.getFavorites().filter(f => f.type === type)
  }

  /**
   * Use favorite (increment use count and update last used)
   */
  useFavorite(id: string): FavoriteLocation | null {
    const favorites = this.getFavorites()
    const favorite = favorites.find(f => f.id === id)
    
    if (!favorite) return null
    
    favorite.useCount++
    favorite.lastUsed = Date.now()
    
    this.saveFavorites(favorites)
    return favorite
  }

  /**
   * Add recent search
   */
  addRecentSearch(query: string, result: SearchResult, selected: boolean = false): void {
    const recentSearches = this.getRecentSearches()
    
    // Remove duplicate if exists
    const existingIndex = recentSearches.findIndex(s => 
      s.query.toLowerCase() === query.toLowerCase() && 
      s.result.id === result.id
    )
    
    if (existingIndex !== -1) {
      recentSearches.splice(existingIndex, 1)
    }

    const newSearch: RecentSearch = {
      id: this.generateId(),
      query,
      result,
      timestamp: Date.now(),
      selected
    }

    recentSearches.unshift(newSearch)
    
    // Keep only recent searches
    if (recentSearches.length > this.MAX_RECENT_SEARCHES) {
      recentSearches.splice(this.MAX_RECENT_SEARCHES)
    }

    this.saveRecentSearches(recentSearches)
  }

  /**
   * Get recent searches
   */
  getRecentSearches(): RecentSearch[] {
    try {
      const stored = localStorage.getItem(this.RECENT_SEARCHES_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Error loading recent searches:', error)
      return []
    }
  }

  /**
   * Clear recent searches
   */
  clearRecentSearches(): void {
    localStorage.removeItem(this.RECENT_SEARCHES_KEY)
  }

  /**
   * Add trip to history
   */
  addTripHistory(trip: Omit<TripHistory, 'id' | 'timestamp'>): TripHistory {
    const history = this.getTripHistory()
    
    const newTrip: TripHistory = {
      ...trip,
      id: this.generateId(),
      timestamp: Date.now()
    }

    history.unshift(newTrip)
    
    // Keep only recent trips
    if (history.length > this.MAX_TRIP_HISTORY) {
      history.splice(this.MAX_TRIP_HISTORY)
    }

    this.saveTripHistory(history)
    return newTrip
  }

  /**
   * Get trip history
   */
  getTripHistory(): TripHistory[] {
    try {
      const stored = localStorage.getItem(this.TRIP_HISTORY_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Error loading trip history:', error)
      return []
    }
  }

  /**
   * Get frequent destinations
   */
  getFrequentDestinations(limit: number = 5): SearchResult[] {
    const history = this.getTripHistory()
    const destinationCounts = new Map<string, { result: SearchResult, count: number }>()

    history.forEach(trip => {
      const key = `${trip.destination.center[0]},${trip.destination.center[1]}`
      const existing = destinationCounts.get(key)
      
      if (existing) {
        existing.count++
      } else {
        destinationCounts.set(key, { result: trip.destination, count: 1 })
      }
    })

    return Array.from(destinationCounts.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, limit)
      .map(item => item.result)
  }

  /**
   * Get suggestions based on history and favorites
   */
  getSuggestions(query: string = '', limit: number = 8): Array<SearchResult & { source: 'favorite' | 'recent' | 'frequent' }> {
    const suggestions: Array<SearchResult & { source: 'favorite' | 'recent' | 'frequent' }> = []
    const queryLower = query.toLowerCase()

    // Add matching favorites
    const favorites = this.getFavorites()
      .filter(f => 
        !query || 
        f.name.toLowerCase().includes(queryLower) || 
        f.address.toLowerCase().includes(queryLower)
      )
      .sort((a, b) => b.useCount - a.useCount)
      .slice(0, 3)
      .map(f => ({
        id: f.id,
        place_name: f.address,
        center: f.coordinates,
        place_type: ['favorite'],
        properties: { category: f.type },
        context: [],
        source: 'favorite' as const
      }))

    suggestions.push(...favorites)

    // Add recent searches if space available
    if (suggestions.length < limit) {
      const recentSearches = this.getRecentSearches()
        .filter(s => 
          !query || 
          s.query.toLowerCase().includes(queryLower) ||
          s.result.place_name.toLowerCase().includes(queryLower)
        )
        .slice(0, limit - suggestions.length)
        .map(s => ({
          ...s.result,
          source: 'recent' as const
        }))

      suggestions.push(...recentSearches)
    }

    // Add frequent destinations if space available
    if (suggestions.length < limit) {
      const frequentDestinations = this.getFrequentDestinations(limit - suggestions.length)
        .filter(d => 
          !query || 
          d.place_name.toLowerCase().includes(queryLower)
        )
        .map(d => ({
          ...d,
          source: 'frequent' as const
        }))

      suggestions.push(...frequentDestinations)
    }

    return suggestions.slice(0, limit)
  }

  /**
   * Clear all data
   */
  clearAllData(): void {
    localStorage.removeItem(this.FAVORITES_KEY)
    localStorage.removeItem(this.RECENT_SEARCHES_KEY)
    localStorage.removeItem(this.TRIP_HISTORY_KEY)
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private saveFavorites(favorites: FavoriteLocation[]): void {
    try {
      localStorage.setItem(this.FAVORITES_KEY, JSON.stringify(favorites))
    } catch (error) {
      console.error('Error saving favorites:', error)
    }
  }

  private saveRecentSearches(searches: RecentSearch[]): void {
    try {
      localStorage.setItem(this.RECENT_SEARCHES_KEY, JSON.stringify(searches))
    } catch (error) {
      console.error('Error saving recent searches:', error)
    }
  }

  private saveTripHistory(history: TripHistory[]): void {
    try {
      localStorage.setItem(this.TRIP_HISTORY_KEY, JSON.stringify(history))
    } catch (error) {
      console.error('Error saving trip history:', error)
    }
  }
}

// Singleton instance
export const locationHistoryService = new LocationHistoryService()
export default locationHistoryService
