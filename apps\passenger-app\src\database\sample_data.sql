-- Dad<PERSON> de exemplo para testar o sistema de corridas
-- Execute este script após criar a estrutura principal

-- Inserir perfis de exemplo (motoristas)
INSERT INTO profiles (id, full_name, phone, user_type, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440001', '<PERSON>', '(11) 99999-0001', 'driver', NOW()),
('550e8400-e29b-41d4-a716-446655440002', '<PERSON>', '(11) 99999-0002', 'driver', NOW()),
('550e8400-e29b-41d4-a716-446655440003', '<PERSON>', '(11) 99999-0003', 'driver', NOW()),
('550e8400-e29b-41d4-a716-446655440004', 'Ana Costa', '(11) 99999-0004', 'driver', NOW()),
('550e8400-e29b-41d4-a716-446655440005', '<PERSON>', '(11) 99999-0005', 'driver', NOW())
ON CONFLICT (id) DO NOTHING;

-- Inserir dados dos motoristas
INSERT INTO drivers (user_id, vehicle_type, vehicle_make, vehicle_model, vehicle_year, vehicle_color, license_plate, rating, total_rides, is_verified) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'economy', 'Honda', 'Civic', 2020, 'Prata', 'ABC-1234', 4.8, 150, true),
('550e8400-e29b-41d4-a716-446655440002', 'comfort', 'Toyota', 'Corolla', 2021, 'Branco', 'DEF-5678', 4.9, 200, true),
('550e8400-e29b-41d4-a716-446655440003', 'moto', 'Honda', 'CG 160', 2022, 'Vermelha', 'GHI-9012', 4.7, 300, true),
('550e8400-e29b-41d4-a716-446655440004', 'premium', 'BMW', 'X3', 2023, 'Preto', 'JKL-3456', 4.9, 80, true),
('550e8400-e29b-41d4-a716-446655440005', 'economy', 'Volkswagen', 'Gol', 2019, 'Azul', 'MNO-7890', 4.6, 120, true)
ON CONFLICT (user_id) DO NOTHING;

-- Inserir localizações dos motoristas (região de São Paulo)
INSERT INTO driver_locations (user_id, location, heading, speed, is_active, is_available, last_ping, updated_at) VALUES
-- Centro de São Paulo
('550e8400-e29b-41d4-a716-446655440001', POINT(-46.6333, -23.5505), 45.0, 0.0, true, true, NOW(), NOW()),
-- Vila Madalena
('550e8400-e29b-41d4-a716-446655440002', POINT(-46.6875, -23.5447), 120.0, 15.0, true, true, NOW(), NOW()),
-- Moema
('550e8400-e29b-41d4-a716-446655440003', POINT(-46.6632, -23.5928), 90.0, 25.0, true, true, NOW(), NOW()),
-- Jardins
('550e8400-e29b-41d4-a716-446655440004', POINT(-46.6692, -23.5613), 180.0, 0.0, true, true, NOW(), NOW()),
-- Pinheiros
('550e8400-e29b-41d4-a716-446655440005', POINT(-46.7019, -23.5629), 270.0, 30.0, true, true, NOW(), NOW())
ON CONFLICT (user_id) DO UPDATE SET
  location = EXCLUDED.location,
  heading = EXCLUDED.heading,
  speed = EXCLUDED.speed,
  is_active = EXCLUDED.is_active,
  is_available = EXCLUDED.is_available,
  last_ping = EXCLUDED.last_ping,
  updated_at = EXCLUDED.updated_at;

-- Inserir algumas corridas de exemplo
INSERT INTO ride_requests (
  id,
  user_id,
  origin_address,
  origin_coords,
  destination_address,
  destination_coords,
  distance,
  duration,
  estimated_price,
  vehicle_type,
  payment_method,
  status,
  created_at
) VALUES
(
  '660e8400-e29b-41d4-a716-446655440001',
  '550e8400-e29b-41d4-a716-446655440001', -- Assumindo que este é um passageiro
  'Avenida Paulista, 1000 - Bela Vista, São Paulo - SP',
  POINT(-46.6566, -23.5613),
  'Shopping Ibirapuera - Av. Ibirapuera, 3103 - Moema, São Paulo - SP',
  POINT(-46.6632, -23.5928),
  5.2,
  15,
  18.50,
  'economy',
  'credit_card',
  'pending',
  NOW() - INTERVAL '5 minutes'
),
(
  '660e8400-e29b-41d4-a716-446655440002',
  '550e8400-e29b-41d4-a716-446655440002',
  'Estação Vila Madalena - R. Fradique Coutinho, 1632 - Vila Madalena, São Paulo - SP',
  POINT(-46.6875, -23.5447),
  'Aeroporto de Congonhas - Av. Washington Luís, s/n - Vila Congonhas, São Paulo - SP',
  POINT(-46.6656, -23.6267),
  12.8,
  25,
  35.00,
  'comfort',
  'cash',
  'accepted',
  NOW() - INTERVAL '10 minutes'
)
ON CONFLICT (id) DO NOTHING;

-- Inserir algumas notificações de exemplo
INSERT INTO notifications (user_id, title, message, type, data, priority, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Nova Corrida Disponível', 'Uma nova corrida está disponível próxima a você', 'ride_request', '{"ride_id": "660e8400-e29b-41d4-a716-446655440001", "distance": "2.3 km"}', 'high', NOW() - INTERVAL '3 minutes'),
('550e8400-e29b-41d4-a716-446655440002', 'Corrida Aceita', 'Sua corrida foi aceita pelo motorista Maria Santos', 'ride_update', '{"ride_id": "660e8400-e29b-41d4-a716-446655440002", "driver_name": "Maria Santos"}', 'normal', NOW() - INTERVAL '8 minutes'),
('550e8400-e29b-41d4-a716-446655440003', 'Promoção Especial', 'Ganhe 20% de desconto na sua próxima corrida!', 'promotion', '{"discount": 20, "code": "PROMO20"}', 'low', NOW() - INTERVAL '1 hour');

-- Inserir configurações de notificação padrão para os motoristas
INSERT INTO notification_settings (user_id, ride_updates, promotions, chat_messages, payment_updates, system_updates, push_enabled, email_enabled, sms_enabled) VALUES
('550e8400-e29b-41d4-a716-446655440001', true, true, true, true, true, true, false, false),
('550e8400-e29b-41d4-a716-446655440002', true, false, true, true, true, true, true, false),
('550e8400-e29b-41d4-a716-446655440003', true, true, true, true, true, true, false, true),
('550e8400-e29b-41d4-a716-446655440004', true, false, true, true, true, true, true, false),
('550e8400-e29b-41d4-a716-446655440005', true, true, true, true, true, true, false, false)
ON CONFLICT (user_id) DO NOTHING;

-- Função para simular movimento de motoristas (para testes)
CREATE OR REPLACE FUNCTION simulate_driver_movement()
RETURNS void AS $$
DECLARE
    driver_record RECORD;
    new_lat DECIMAL;
    new_lng DECIMAL;
    new_heading DECIMAL;
    new_speed DECIMAL;
BEGIN
    -- Atualizar localização de todos os motoristas ativos
    FOR driver_record IN 
        SELECT user_id, ST_X(location) as lng, ST_Y(location) as lat, heading, speed
        FROM driver_locations 
        WHERE is_active = true
    LOOP
        -- Simular movimento aleatório pequeno (máximo 0.001 graus = ~100m)
        new_lat := driver_record.lat + (RANDOM() - 0.5) * 0.001;
        new_lng := driver_record.lng + (RANDOM() - 0.5) * 0.001;
        new_heading := (driver_record.heading + (RANDOM() - 0.5) * 30) % 360;
        new_speed := GREATEST(0, LEAST(60, driver_record.speed + (RANDOM() - 0.5) * 10));
        
        -- Atualizar localização
        UPDATE driver_locations SET
            location = POINT(new_lng, new_lat),
            heading = new_heading,
            speed = new_speed,
            last_ping = NOW(),
            updated_at = NOW()
        WHERE user_id = driver_record.user_id;
    END LOOP;
    
    RAISE NOTICE 'Localizações dos motoristas atualizadas';
END;
$$ LANGUAGE plpgsql;

-- Função para testar busca de motoristas próximos
CREATE OR REPLACE FUNCTION test_nearby_drivers()
RETURNS TABLE (
    driver_name TEXT,
    distance_km DECIMAL,
    vehicle_info TEXT,
    rating DECIMAL,
    is_available BOOLEAN
) AS $$
BEGIN
    -- Testar busca de motoristas próximos ao centro de São Paulo
    RETURN QUERY
    SELECT 
        p.full_name as driver_name,
        nd.distance_km,
        CONCAT(d.vehicle_make, ' ', d.vehicle_model, ' (', d.license_plate, ')') as vehicle_info,
        nd.rating,
        nd.is_available
    FROM find_nearby_drivers(-23.5505, -46.6333, 10) nd
    JOIN profiles p ON p.id = nd.driver_id
    LEFT JOIN drivers d ON d.user_id = nd.driver_id;
END;
$$ LANGUAGE plpgsql;

-- Comentários para uso
-- Para simular movimento dos motoristas: SELECT simulate_driver_movement();
-- Para testar busca de motoristas: SELECT * FROM test_nearby_drivers();
-- Para ver motoristas ativos: SELECT p.full_name, ST_X(dl.location) as lng, ST_Y(dl.location) as lat, dl.is_available FROM driver_locations dl JOIN profiles p ON p.id = dl.user_id WHERE dl.is_active = true;
