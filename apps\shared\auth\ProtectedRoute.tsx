import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from './AuthContext';

export interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
  requiredRole?: 'admin' | 'driver' | 'passenger';
  loadingComponent?: React.ReactNode;
}

/**
 * Shared Protected Route Component
 * 
 * This component provides route protection for authenticated routes across all MobiDrive apps.
 * It handles authentication checks, role-based access control, and loading states.
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  redirectTo = '/login',
  requiredRole,
  loadingComponent = <DefaultLoadingComponent />,
}) => {
  const { user, profile, isLoading, isAdmin, isDriver, isPassenger } = useAuth();
  const location = useLocation();

  // Show loading component while authentication state is being determined
  if (isLoading) {
    return <>{loadingComponent}</>;
  }

  // Check if user is authenticated
  if (!user) {
    // Redirect to login page with return URL
    return <Navigate to={redirectTo} state={{ from: location.pathname }} replace />;
  }

  // Check role-based access if required
  if (requiredRole) {
    let hasRequiredRole = false;

    switch (requiredRole) {
      case 'admin':
        hasRequiredRole = isAdmin();
        break;
      case 'driver':
        hasRequiredRole = isDriver();
        break;
      case 'passenger':
        hasRequiredRole = isPassenger();
        break;
      default:
        hasRequiredRole = true;
    }

    if (!hasRequiredRole) {
      // Redirect to access denied page
      return <Navigate to="/access-denied" replace />;
    }
  }

  // User is authenticated and has required role, render children
  return <>{children}</>;
};

// Default loading component
const DefaultLoadingComponent: React.FC = () => (
  <div className="min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
    <div className="flex flex-col items-center">
      <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      <p className="mt-4 text-slate-600 dark:text-slate-400">Loading...</p>
    </div>
  </div>
);

export default ProtectedRoute;
