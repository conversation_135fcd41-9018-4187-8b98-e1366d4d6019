import { useEffect, useState } from 'react'
import { mapboxService } from '../services/MapboxService'

interface PreloadStatus {
  mapbox: boolean
  userLocation: boolean
  paymentMethods: boolean
  isComplete: boolean
}

/**
 * Hook para preload de recursos críticos da aplicação
 * Melhora a performance carregando recursos em background
 */
export const usePreloadResources = () => {
  const [status, setStatus] = useState<PreloadStatus>({
    mapbox: false,
    userLocation: false,
    paymentMethods: false,
    isComplete: false
  })

  useEffect(() => {
    const preloadResources = async () => {
      try {
        // 1. Preload Mapbox (verificar se token está válido)
        try {
          await mapboxService.getCurrentLocation()
          setStatus(prev => ({ ...prev, mapbox: true }))
        } catch (error) {
          console.warn('Mapbox preload failed:', error)
          setStatus(prev => ({ ...prev, mapbox: true })) // Continue mesmo com erro
        }

        // 2. Preload localização do usuário
        try {
          if ('geolocation' in navigator) {
            navigator.geolocation.getCurrentPosition(
              () => setStatus(prev => ({ ...prev, userLocation: true })),
              () => setStatus(prev => ({ ...prev, userLocation: true })) // Continue mesmo com erro
            )
          } else {
            setStatus(prev => ({ ...prev, userLocation: true }))
          }
        } catch (error) {
          setStatus(prev => ({ ...prev, userLocation: true }))
        }

        // 3. Preload métodos de pagamento (cache)
        try {
          // Simular preload de métodos de pagamento
          await new Promise(resolve => setTimeout(resolve, 100))
          setStatus(prev => ({ ...prev, paymentMethods: true }))
        } catch (error) {
          setStatus(prev => ({ ...prev, paymentMethods: true }))
        }

      } catch (error) {
        console.error('Erro no preload de recursos:', error)
        // Marcar como completo mesmo com erros
        setStatus({
          mapbox: true,
          userLocation: true,
          paymentMethods: true,
          isComplete: true
        })
      }
    }

    preloadResources()
  }, [])

  // Verificar se todos os recursos foram carregados
  useEffect(() => {
    const { mapbox, userLocation, paymentMethods } = status
    if (mapbox && userLocation && paymentMethods && !status.isComplete) {
      setStatus(prev => ({ ...prev, isComplete: true }))
    }
  }, [status])

  return {
    status,
    isComplete: status.isComplete,
    progress: Object.values(status).filter(Boolean).length / 4 * 100
  }
}

/**
 * Hook para preload específico do mapa
 */
export const useMapPreload = () => {
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
    const preloadMap = async () => {
      try {
        // Preload do Mapbox GL JS
        const mapboxgl = await import('mapbox-gl')
        
        // Verificar se o token está configurado
        if (mapboxgl.default.accessToken) {
          setIsReady(true)
        } else {
          console.warn('Mapbox token não configurado')
          setIsReady(true) // Continue mesmo sem token
        }
      } catch (error) {
        console.error('Erro no preload do mapa:', error)
        setIsReady(true) // Continue mesmo com erro
      }
    }

    preloadMap()
  }, [])

  return isReady
}

/**
 * Hook para preload de componentes críticos
 */
export const useComponentPreload = () => {
  const [componentsReady, setComponentsReady] = useState(false)

  useEffect(() => {
    const preloadComponents = async () => {
      try {
        // Preload de componentes pesados
        await Promise.all([
          import('../components/EnhancedMapboxComponent'),
          import('../components/PaymentMethodSelector'),
          import('../services/RideService')
        ])
        
        setComponentsReady(true)
      } catch (error) {
        console.error('Erro no preload de componentes:', error)
        setComponentsReady(true) // Continue mesmo com erro
      }
    }

    // Delay para não bloquear o carregamento inicial
    const timer = setTimeout(preloadComponents, 1000)
    
    return () => clearTimeout(timer)
  }, [])

  return componentsReady
}

export default usePreloadResources
