import { useState, useCallback } from 'react'
import mapboxgl from 'mapbox-gl'

export interface RideEstimate {
  distance: string
  duration: string
  price: number
  route?: {
    distance: number
    duration: number
    geometry: any
  }
}

export interface UseRideEstimateReturn {
  rideEstimate: RideEstimate | null
  isCalculatingRide: boolean
  calculateRideEstimate: (origin: [number, number], destination: [number, number]) => Promise<void>
  clearEstimate: () => void
}

export const useRideEstimate = (): UseRideEstimateReturn => {
  const [rideEstimate, setRideEstimate] = useState<RideEstimate | null>(null)
  const [isCalculatingRide, setIsCalculatingRide] = useState(false)

  const calculateRideEstimate = useCallback(async (
    origin: [number, number], 
    destination: [number, number]
  ) => {
    if (!origin || !destination) return

    setIsCalculatingRide(true)

    try {
      console.log('🧮 Calculando estimativa da corrida...')
      console.log('📍 Origem:', origin)
      console.log('🎯 Destino:', destination)

      // Calcular rota usando Mapbox Directions API
      const accessToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'
      const directionsUrl = `https://api.mapbox.com/directions/v5/mapbox/driving/${origin[0]},${origin[1]};${destination[0]},${destination[1]}?geometries=geojson&access_token=${accessToken}`

      const response = await fetch(directionsUrl)
      const data = await response.json()

      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0]
        const distanceKm = route.distance / 1000
        const durationMin = route.duration / 60

        // Calcular preço baseado na distância (R$ 2,50 base + R$ 1,80 por km)
        const basePrice = 2.50
        const pricePerKm = 1.80
        const calculatedPrice = basePrice + (distanceKm * pricePerKm)

        const estimate: RideEstimate = {
          distance: `${distanceKm.toFixed(1)} km`,
          duration: `${Math.round(durationMin)} min`,
          price: Math.max(calculatedPrice, 5.00), // Preço mínimo R$ 5,00
          route: {
            distance: route.distance,
            duration: route.duration,
            geometry: route.geometry
          }
        }

        setRideEstimate(estimate)
        console.log('✅ Estimativa calculada:', estimate)
      } else {
        throw new Error('Não foi possível calcular a rota')
      }
    } catch (error) {
      console.error('❌ Erro ao calcular estimativa:', error)
      
      // Fallback: calcular estimativa básica usando distância euclidiana
      const lat1 = origin[1]
      const lon1 = origin[0]
      const lat2 = destination[1]
      const lon2 = destination[0]

      // Fórmula de Haversine para calcular distância
      const R = 6371 // Raio da Terra em km
      const dLat = (lat2 - lat1) * Math.PI / 180
      const dLon = (lon2 - lon1) * Math.PI / 180
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLon/2) * Math.sin(dLon/2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
      const distance = R * c

      // Estimativa de tempo (assumindo 30 km/h média na cidade)
      const estimatedDuration = (distance / 30) * 60

      // Calcular preço
      const basePrice = 2.50
      const pricePerKm = 1.80
      const calculatedPrice = basePrice + (distance * pricePerKm)

      const fallbackEstimate: RideEstimate = {
        distance: `${distance.toFixed(1)} km`,
        duration: `${Math.round(estimatedDuration)} min`,
        price: Math.max(calculatedPrice, 5.00)
      }

      setRideEstimate(fallbackEstimate)
      console.log('⚠️ Usando estimativa básica:', fallbackEstimate)
    } finally {
      setIsCalculatingRide(false)
    }
  }, [])

  const clearEstimate = useCallback(() => {
    setRideEstimate(null)
  }, [])

  return {
    rideEstimate,
    isCalculatingRide,
    calculateRideEstimate,
    clearEstimate
  }
}
