import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { Car, MapPin, Clock, User, Settings, LogOut } from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { useNotifications } from '../contexts/NotificationContext'
import { ModernLayout, ModernCard, ModernButton } from '../components/ModernLayout'
import EnhancedMapboxComponent from '../components/EnhancedMapboxComponent'
import LocationStatus from '../components/LocationStatus'
import RideStatusManager from '../components/RideStatusManager'
import AnalyticsDashboard from '../components/AnalyticsDashboard'
import FavoritesPanel from '../components/FavoritesPanel'
import SystemStatusPanel from '../components/SystemStatusPanel'

// 📊 DASHBOARD - DESIGN ORIGINAL MANTIDO
// Conversão Android nativa aplicada via wrapper adaptativo

// Mapbox imports
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'

// Set Mapbox access token
mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN

export const Dashboard: React.FC = () => {
  const { user, profile, signOut, loading } = useAuth()
  const navigate = useNavigate()
  const { addNotification } = useNotifications()

  const [activePanel, setActivePanel] = useState<'map' | 'analytics' | 'favorites'>('map')
  const [hasShownWelcome, setHasShownWelcome] = useState(false)

  // Show welcome notification
  useEffect(() => {
    if (profile && !hasShownWelcome && !loading) {
      setTimeout(() => {
        addNotification({
          type: 'success',
          title: `Bem-vindo, ${profile.full_name?.split(' ')[0]}! 🎉`,
          message: 'Sistema pronto para uso!',
          duration: 5000
        })
        setHasShownWelcome(true)
      }, 2000)
    }
  }, [profile, hasShownWelcome, loading, addNotification])

  // Loading state (MANTENDO DESIGN ORIGINAL)
  if (loading) {
    return (
      <ModernLayout
        title="Carregando..."
        subtitle="Verificando autenticação"
        pageIcon="⏳"
        animatedSymbol="🔄"
      >
        <ModernCard className="text-center">
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>⏳</div>
          <h2 style={{ fontSize: '24px', fontWeight: '700', marginBottom: '12px', color: '#1f2937' }}>
            Carregando Dashboard...
          </h2>
          <p style={{ color: '#6b7280', margin: 0 }}>Aguarde um momento</p>
        </ModernCard>
      </ModernLayout>
    )
  }

  const handleSignOut = async () => {
    await signOut()
  }

  // Animações simples e limpas (MANTENDO ORIGINAIS)
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <ModernLayout
      title="MobiDrive"
      subtitle={`Olá, ${profile?.full_name?.split(' ')[0] || 'Passageiro'}!`}
      pageIcon="🏠"
      animatedSymbol="🚗"
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >

        {/* Header com informações do usuário (MANTENDO DESIGN ORIGINAL) */}
        <motion.div variants={itemVariants}>
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <motion.div
                  className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center"
                  whileHover={{ scale: 1.05 }}
                >
                  <User className="w-6 h-6 text-white" />
                </motion.div>
                <div>
                  <h3 className="text-gray-900 text-lg font-semibold m-0">
                    {profile?.full_name || 'Usuário'}
                  </h3>
                  <p className="text-gray-600 text-sm m-0">
                    {user?.email}
                  </p>
                </div>
              </div>
              <motion.button
                onClick={handleSignOut}
                className="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-xl font-semibold flex items-center space-x-2 transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <LogOut className="w-4 h-4" />
                <span>Sair</span>
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Ride Status Manager (MANTENDO DESIGN ORIGINAL) */}
        <motion.div variants={itemVariants}>
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span className="mr-2">🚗</span>
              Status da Corrida
            </h3>
            <RideStatusManager
              onStatusChange={(status) => {
                console.log('🚗 Status da corrida atualizado:', status)
                if (status.status === 'accepted') {
                  navigate('/ride-tracking', { state: { rideId: status.id } })
                }
              }}
            />
          </div>
        </motion.div>

        {/* Panel Navigation (MANTENDO DESIGN ORIGINAL) */}
        <motion.div variants={itemVariants}>
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span className="mr-2">🧭</span>
              Navegação
            </h3>
            <div className="flex gap-2 mb-4">
              {[
                { id: 'map', label: 'Mapa', icon: '🗺️' },
                { id: 'favorites', label: 'Favoritos', icon: '❤️' },
                { id: 'analytics', label: 'Analytics', icon: '📊' }
              ].map((panel) => (
                <motion.button
                  key={panel.id}
                  onClick={() => setActivePanel(panel.id as any)}
                  className={`flex-1 py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-200 ${
                    activePanel === panel.id
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {panel.icon} {panel.label}
                </motion.button>
              ))}
            </div>

            {/* Conteúdo dos Painéis (MANTENDO DESIGN ORIGINAL) */}
            {activePanel === 'map' && (
              <div className="mt-4">
                <h4 className="text-lg font-semibold mb-3 text-gray-700">🗺️ Solicitar Corrida</h4>
                <EnhancedMapboxComponent
                  onRideRequest={(rideData) => {
                    addNotification({
                      type: 'success',
                      title: 'Corrida Solicitada!',
                      message: 'Redirecionando para confirmação...'
                    })
                    setTimeout(() => navigate('/request-ride', { state: rideData }), 1000)
                  }}
                  className="w-full"
                  destinationOnly={true}
                  showNearbyDriversInfo={false}
                />
              </div>
            )}

            {activePanel === 'favorites' && (
              <div className="mt-4">
                <h4 className="text-lg font-semibold mb-3 text-gray-700">❤️ Locais Favoritos</h4>
                <FavoritesPanel
                  onLocationSelect={(location) => {
                    addNotification({
                      type: 'info',
                      title: 'Local Selecionado',
                      message: `${location.place_name} foi selecionado como destino`
                    })
                    setActivePanel('map')
                  }}
                  className="w-full"
                />
              </div>
            )}

            {activePanel === 'analytics' && (
              <div className="mt-4">
                <h4 className="text-lg font-semibold mb-3 text-gray-700">📊 Analytics</h4>
                <AnalyticsDashboard />
              </div>
            )}
          </div>
        </motion.div>

        {/* Ações Rápidas (MANTENDO DESIGN ORIGINAL) */}
        <motion.div variants={itemVariants}>
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span className="mr-2">⚡</span>
              Ações Rápidas
            </h3>
            <div className="grid grid-cols-2 gap-3">
              <motion.button
                onClick={() => navigate('/request-ride')}
                className="bg-gradient-to-r from-blue-500 to-purple-500 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Car className="w-6 h-6" />
                <span className="text-sm font-medium">Solicitar Corrida</span>
              </motion.button>

              <motion.button
                onClick={() => setActivePanel('favorites')}
                className="bg-gradient-to-r from-gray-500 to-gray-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <MapPin className="w-6 h-6" />
                <span className="text-sm font-medium">Locais Salvos</span>
              </motion.button>

              <motion.button
                onClick={() => {/* Implementar agendamento */}}
                className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Clock className="w-6 h-6" />
                <span className="text-sm font-medium">Agendar Corrida</span>
              </motion.button>

              <motion.button
                onClick={() => navigate('/setup')}
                className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Settings className="w-6 h-6" />
                <span className="text-sm font-medium">Configurações</span>
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Estatísticas (MANTENDO DESIGN ORIGINAL) */}
        <motion.div variants={itemVariants}>
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span className="mr-2">📊</span>
              Suas Estatísticas
            </h3>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl mb-1">🚗</div>
                <div className="text-xl font-bold text-gray-900">0</div>
                <div className="text-xs text-gray-600">Corridas</div>
              </div>
              <div>
                <div className="text-2xl mb-1">💰</div>
                <div className="text-xl font-bold text-gray-900">R$ 0</div>
                <div className="text-xs text-gray-600">Economia</div>
              </div>
              <div>
                <div className="text-2xl mb-1">⭐</div>
                <div className="text-xl font-bold text-gray-900">5.0</div>
                <div className="text-xs text-gray-600">Avaliação</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Status do Sistema (MANTENDO DESIGN ORIGINAL) */}
        <motion.div variants={itemVariants}>
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span className="mr-2">📡</span>
              Status do Sistema
            </h3>
            <LocationStatus showDetails={true} />
          </div>
        </motion.div>

        <motion.div variants={itemVariants}>
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span className="mr-2">🔧</span>
              Monitoramento
            </h3>
            <SystemStatusPanel />
          </div>
        </motion.div>
      </motion.div>
    </ModernLayout>
  )
}

export default Dashboard
