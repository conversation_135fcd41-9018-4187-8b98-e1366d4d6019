interface AnalyticsEvent {
  type: 'search' | 'route' | 'error' | 'performance' | 'user_action'
  timestamp: number
  data: any
  sessionId: string
  userId?: string
}

interface PerformanceMetrics {
  searchLatency: number[]
  routeLatency: number[]
  errorRate: number
  cacheHitRate: number
  apiCallsCount: number
}

interface SearchMetrics {
  totalSearches: number
  successfulSearches: number
  failedSearches: number
  averageResultsCount: number
  popularQueries: Map<string, number>
}

interface RouteMetrics {
  totalRoutes: number
  successfulRoutes: number
  failedRoutes: number
  averageDistance: number
  averageDuration: number
  averagePrice: number
}

class MapboxAnalyticsService {
  private events: AnalyticsEvent[] = []
  private sessionId: string
  private maxEvents = 1000
  private performanceMetrics: PerformanceMetrics = {
    searchLatency: [],
    routeLatency: [],
    errorRate: 0,
    cacheHitRate: 0,
    apiCallsCount: 0
  }

  constructor() {
    this.sessionId = this.generateSessionId()
    this.startPeriodicReporting()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Track search event
   */
  trackSearch(query: string, results: any[], latency: number, fromCache: boolean = false): void {
    this.addEvent({
      type: 'search',
      timestamp: Date.now(),
      data: {
        query: query.substring(0, 100), // Limit query length for privacy
        resultsCount: results.length,
        latency,
        fromCache,
        hasResults: results.length > 0
      },
      sessionId: this.sessionId
    })

    // Update performance metrics
    this.performanceMetrics.searchLatency.push(latency)
    this.performanceMetrics.apiCallsCount += fromCache ? 0 : 1
    this.updateCacheHitRate(fromCache)
  }

  /**
   * Track route calculation
   */
  trackRoute(origin: [number, number], destination: [number, number], routes: any[], latency: number, fromCache: boolean = false): void {
    const route = routes[0]
    
    this.addEvent({
      type: 'route',
      timestamp: Date.now(),
      data: {
        hasRoute: routes.length > 0,
        distance: route?.distance || 0,
        duration: route?.duration || 0,
        latency,
        fromCache,
        alternativesCount: routes.length
      },
      sessionId: this.sessionId
    })

    // Update performance metrics
    this.performanceMetrics.routeLatency.push(latency)
    this.performanceMetrics.apiCallsCount += fromCache ? 0 : 1
    this.updateCacheHitRate(fromCache)
  }

  /**
   * Track error
   */
  trackError(errorType: string, errorMessage: string, context?: any): void {
    this.addEvent({
      type: 'error',
      timestamp: Date.now(),
      data: {
        errorType,
        errorMessage: errorMessage.substring(0, 200), // Limit message length
        context: context ? JSON.stringify(context).substring(0, 500) : null
      },
      sessionId: this.sessionId
    })

    this.updateErrorRate()
  }

  /**
   * Track user action
   */
  trackUserAction(action: string, data?: any): void {
    this.addEvent({
      type: 'user_action',
      timestamp: Date.now(),
      data: {
        action,
        ...data
      },
      sessionId: this.sessionId
    })
  }

  /**
   * Track performance metric
   */
  trackPerformance(metric: string, value: number, context?: any): void {
    this.addEvent({
      type: 'performance',
      timestamp: Date.now(),
      data: {
        metric,
        value,
        context
      },
      sessionId: this.sessionId
    })
  }

  private addEvent(event: AnalyticsEvent): void {
    this.events.push(event)
    
    // Keep only recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents)
    }
  }

  private updateCacheHitRate(fromCache: boolean): void {
    const totalRequests = this.performanceMetrics.searchLatency.length + this.performanceMetrics.routeLatency.length
    const cacheHits = this.events.filter(e => 
      (e.type === 'search' || e.type === 'route') && e.data.fromCache
    ).length
    
    this.performanceMetrics.cacheHitRate = totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0
  }

  private updateErrorRate(): void {
    const totalEvents = this.events.length
    const errorEvents = this.events.filter(e => e.type === 'error').length
    
    this.performanceMetrics.errorRate = totalEvents > 0 ? (errorEvents / totalEvents) * 100 : 0
  }

  /**
   * Get search metrics
   */
  getSearchMetrics(): SearchMetrics {
    const searchEvents = this.events.filter(e => e.type === 'search')
    const successfulSearches = searchEvents.filter(e => e.data.hasResults)
    const popularQueries = new Map<string, number>()

    searchEvents.forEach(event => {
      const query = event.data.query.toLowerCase()
      popularQueries.set(query, (popularQueries.get(query) || 0) + 1)
    })

    const totalResults = searchEvents.reduce((sum, e) => sum + e.data.resultsCount, 0)

    return {
      totalSearches: searchEvents.length,
      successfulSearches: successfulSearches.length,
      failedSearches: searchEvents.length - successfulSearches.length,
      averageResultsCount: searchEvents.length > 0 ? totalResults / searchEvents.length : 0,
      popularQueries
    }
  }

  /**
   * Get route metrics
   */
  getRouteMetrics(): RouteMetrics {
    const routeEvents = this.events.filter(e => e.type === 'route')
    const successfulRoutes = routeEvents.filter(e => e.data.hasRoute)

    const totalDistance = successfulRoutes.reduce((sum, e) => sum + e.data.distance, 0)
    const totalDuration = successfulRoutes.reduce((sum, e) => sum + e.data.duration, 0)

    return {
      totalRoutes: routeEvents.length,
      successfulRoutes: successfulRoutes.length,
      failedRoutes: routeEvents.length - successfulRoutes.length,
      averageDistance: successfulRoutes.length > 0 ? totalDistance / successfulRoutes.length : 0,
      averageDuration: successfulRoutes.length > 0 ? totalDuration / successfulRoutes.length : 0,
      averagePrice: 0 // Will be calculated based on distance/duration
    }
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    const searchLatency = this.performanceMetrics.searchLatency
    const routeLatency = this.performanceMetrics.routeLatency

    return {
      searchPerformance: {
        averageLatency: searchLatency.length > 0 ? 
          searchLatency.reduce((a, b) => a + b, 0) / searchLatency.length : 0,
        minLatency: searchLatency.length > 0 ? Math.min(...searchLatency) : 0,
        maxLatency: searchLatency.length > 0 ? Math.max(...searchLatency) : 0,
        totalRequests: searchLatency.length
      },
      routePerformance: {
        averageLatency: routeLatency.length > 0 ? 
          routeLatency.reduce((a, b) => a + b, 0) / routeLatency.length : 0,
        minLatency: routeLatency.length > 0 ? Math.min(...routeLatency) : 0,
        maxLatency: routeLatency.length > 0 ? Math.max(...routeLatency) : 0,
        totalRequests: routeLatency.length
      },
      cacheHitRate: this.performanceMetrics.cacheHitRate,
      errorRate: this.performanceMetrics.errorRate,
      totalApiCalls: this.performanceMetrics.apiCallsCount,
      sessionDuration: Date.now() - parseInt(this.sessionId.split('_')[1])
    }
  }

  /**
   * Export analytics data
   */
  exportData() {
    return {
      sessionId: this.sessionId,
      events: this.events,
      searchMetrics: this.getSearchMetrics(),
      routeMetrics: this.getRouteMetrics(),
      performanceSummary: this.getPerformanceSummary(),
      exportedAt: new Date().toISOString()
    }
  }

  /**
   * Clear analytics data
   */
  clearData(): void {
    this.events = []
    this.performanceMetrics = {
      searchLatency: [],
      routeLatency: [],
      errorRate: 0,
      cacheHitRate: 0,
      apiCallsCount: 0
    }
  }

  private startPeriodicReporting(): void {
    // Report metrics every 5 minutes
    setInterval(() => {
      const summary = this.getPerformanceSummary()
      console.log('📊 Mapbox Analytics Summary:', summary)
      
      // Here you could send data to your analytics service
      // this.sendToAnalyticsService(summary)
    }, 5 * 60 * 1000)
  }
}

// Singleton instance
export const mapboxAnalyticsService = new MapboxAnalyticsService()
export default mapboxAnalyticsService
