import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Navigate, useNavigate, Link } from 'react-router-dom'
import { Mail, Lock, Eye, EyeOff, User, Phone, ArrowRight } from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { ModernLayout, ModernCard, ModernInput, ModernButton } from '../components/ModernLayout'
import { GradientBackground } from '../components/GradientBackground'
import { useNoZoom } from '../hooks/useNoZoom'
import '../styles/no-zoom.css'

// Variantes de animação
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 }
  }
}

// 📱 PÁGINA DE REGISTRO MOBILE ANDROID NATIVA
// MANTÉM DESIGN ORIGINAL + CONVERSÃO ANDROID NATIVA

interface RegisterMobileProps {
  onRegister?: (data: any) => void
}

export const RegisterMobile: React.FC<RegisterMobileProps> = ({ onRegister }) => {
  const { signUp, loading, error, user } = useAuth()
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    full_name: '',
    phone: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [passwordErrors, setPasswordErrors] = useState<string[]>([])
  const [isSuccess, setIsSuccess] = useState(false)

  // 🚫 DESABILITA ZOOM COMPLETAMENTE
  useNoZoom()

  // Configurações Android nativas (mantendo design original)
  useEffect(() => {
    // Meta viewport para Android nativo
    let metaViewport = document.querySelector('meta[name="viewport"]')
    if (!metaViewport) {
      metaViewport = document.createElement('meta')
      metaViewport.setAttribute('name', 'viewport')
      document.head.appendChild(metaViewport)
    }
    metaViewport.setAttribute('content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )

    // Configurações de overflow para Android
    document.body.style.margin = '0'
    document.body.style.padding = '0'
    document.body.style.overflow = 'hidden'
    document.documentElement.style.margin = '0'
    document.documentElement.style.padding = '0'
    document.documentElement.style.overflow = 'hidden'
  }, [])

  // Se o usuário já está logado, redirecionar para dashboard
  if (user) {
    return <Navigate to="/dashboard" replace />
  }

  const validatePassword = (password: string): string[] => {
    const errors: string[] = []

    if (password.length < 8) {
      errors.push('Mínimo 8 caracteres')
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Uma letra maiúscula')
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Uma letra minúscula')
    }
    if (!/\d/.test(password)) {
      errors.push('Um número')
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Um caractere especial')
    }

    return errors
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    if (field === 'password') {
      setPasswordErrors(validatePassword(value))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (passwordErrors.length > 0) {
      return
    }

    if (formData.password !== formData.confirmPassword) {
      return
    }

    try {
      if (onRegister) {
        onRegister(formData)
      } else {
        await signUp(formData.email, formData.password, {
          full_name: formData.full_name,
          phone: formData.phone
        })
        setIsSuccess(true)
      }
    } catch (err) {
      console.error('Erro no cadastro:', err)
    }
  }

  // Página de sucesso (MANTENDO DESIGN ORIGINAL)
  if (isSuccess) {
    return (
      <ModernLayout
        title="Sucesso!"
        subtitle="Conta criada com sucesso"
        pageIcon="✅"
        animatedSymbol="🎉"
      >
        <ModernCard className="text-center">
          <div style={{ fontSize: '64px', marginBottom: '16px' }}>✅</div>
          <h2 style={{ fontSize: '24px', fontWeight: '700', marginBottom: '12px', color: '#1f2937' }}>
            Conta Criada!
          </h2>
          <p style={{ color: '#6b7280', marginBottom: '24px', lineHeight: '1.5' }}>
            Verifique seu email para confirmar sua conta e fazer login.
          </p>
          <ModernButton
            onClick={() => navigate('/login')}
            icon="🚀"
            variant="success"
          >
            Ir para Login
          </ModernButton>
        </ModernCard>
      </ModernLayout>
    )
  }

  // Layout principal (FIEL AO LOGIN)
  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">

      {/* Background Gradient Sutil (FIEL AO LOGIN) */}
      <GradientBackground
        variant="static"
        opacity={0.7}
      />

      {/* Overlay muito sutil para legibilidade (FIEL AO LOGIN) */}
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Conteúdo Principal (FIEL AO LOGIN) */}
      <div className="relative z-10 flex flex-col h-full min-h-screen">

        {/* Header com Logo (FIEL AO LOGIN) */}
        <motion.div
          className="pt-8 pb-6 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <img
                src="/icons/icon-48x48.png"
                alt="MobiDrive"
                className="w-6 h-6"
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-xs text-white/70">Criar Nova Conta</p>
            </div>
          </div>
        </motion.div>

        {/* Formulário Central (FIEL AO LOGIN) */}
        <div className="flex-1 flex items-center justify-center px-4">
          <motion.div
            className="w-full max-w-xs"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
              variants={itemVariants}
            >
              {/* Título do Formulário (FIEL AO LOGIN) */}
              <motion.div
                className="text-center mb-6"
                variants={itemVariants}
              >
                <h2 className="text-xl font-semibold text-white mb-2">
                  Criar Conta
                </h2>
                <p className="text-white/70 text-sm">
                  Preencha os dados para começar
                </p>
              </motion.div>

              {/* Formulário (FIEL AO LOGIN) */}
              <motion.form
                onSubmit={handleSubmit}
                className="space-y-4"
                variants={itemVariants}
              >
                {/* Campo Nome Completo (FIEL AO LOGIN) */}
                <motion.div variants={itemVariants}>
                  <label htmlFor="full_name" className="block text-sm font-medium text-white/90 mb-2">
                    Nome Completo
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                    <input
                      id="full_name"
                      type="text"
                      value={formData.full_name}
                      onChange={(e) => handleInputChange('full_name', e.target.value)}
                      required
                      className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      placeholder="Seu nome completo"
                    />
                  </div>
                </motion.div>

                {/* Campo Email (FIEL AO LOGIN) */}
                <motion.div variants={itemVariants}>
                  <label htmlFor="email" className="block text-sm font-medium text-white/90 mb-2">
                    Email
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                    <input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                      className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </motion.div>

                {/* Campo Telefone (FIEL AO LOGIN) */}
                <motion.div variants={itemVariants}>
                  <label htmlFor="phone" className="block text-sm font-medium text-white/90 mb-2">
                    Telefone
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                    <input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                      className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      placeholder="(11) 99999-9999"
                    />
                  </div>
                </motion.div>

                {/* Campo Senha (FIEL AO LOGIN) */}
                <motion.div variants={itemVariants}>
                  <label htmlFor="password" className="block text-sm font-medium text-white/90 mb-2">
                    Senha
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                    <input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      required
                      className="w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      placeholder="••••••••"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                    >
                      {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                  {formData.password && (
                    <div className="mt-2 text-xs">
                      {passwordErrors.map((error, index) => (
                        <p key={index} className="text-red-300 mb-1">• {error}</p>
                      ))}
                      {passwordErrors.length === 0 && (
                        <p className="text-green-300">✓ Senha válida</p>
                      )}
                    </div>
                  )}
                </motion.div>

                {/* Campo Confirmar Senha (FIEL AO LOGIN) */}
                <motion.div variants={itemVariants}>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-white/90 mb-2">
                    Confirmar Senha
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                    <input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      required
                      className="w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      placeholder="••••••••"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                    >
                      {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                  {formData.confirmPassword && (
                    <div className="mt-2 text-xs">
                      {formData.password === formData.confirmPassword ? (
                        <p className="text-green-300">✓ Senhas coincidem</p>
                      ) : (
                        <p className="text-red-300">• Senhas não coincidem</p>
                      )}
                    </div>
                  )}
                </motion.div>

                {/* Mensagem de Erro (FIEL AO LOGIN) */}
                <AnimatePresence>
                  {error && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="bg-red-500/20 border border-red-400/30 rounded-xl p-3"
                    >
                      <p className="text-red-200 text-sm">{error}</p>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Botão de Submit (FIEL AO LOGIN) */}
                <motion.button
                  type="submit"
                  disabled={loading || passwordErrors.length > 0 || formData.password !== formData.confirmPassword}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {loading ? (
                    <motion.div
                      className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                  ) : (
                    <>
                      <span>Criar Conta</span>
                      <ArrowRight className="w-4 h-4" />
                    </>
                  )}
                </motion.button>
              </motion.form>

              {/* Link de Login (FIEL AO LOGIN) */}
              <motion.div
                className="mt-6 text-center"
                variants={itemVariants}
              >
                <p className="text-white/70 text-sm">
                  Já tem uma conta?{' '}
                  <Link
                    to="/login"
                    className="text-blue-400 font-medium hover:text-blue-300 transition-colors"
                  >
                    Faça login
                  </Link>
                </p>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>

        {/* Footer Simples (FIEL AO LOGIN) */}
        <motion.div
          className="pb-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <p className="text-white/50 text-xs">
            © 2024 MobiDrive. Todos os direitos reservados.
          </p>
        </motion.div>
      </div>
    </div>
  )
}

export default RegisterMobile
