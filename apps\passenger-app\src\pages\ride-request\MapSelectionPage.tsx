import React, { useState, useEffect, useCallback, useRef } from 'react'
import { motion } from 'framer-motion'
import { Navigate, useNavigate } from 'react-router-dom'
import mapboxgl from 'mapbox-gl'
import { 
  ArrowLeft,
  Search,
  Target,
  Mic,
  MicOff,
  Loader2,
  CheckCircle,
  MapPin
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContextSimple'
import { useMapboxSearch } from '../../hooks/useMapboxSearch'
import { useNoZoom } from '../../hooks/useNoZoom'
import 'mapbox-gl/dist/mapbox-gl.css'
import '../../styles/no-zoom.css'

// Configure Mapbox token
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'
mapboxgl.accessToken = MAPBOX_TOKEN

export const MapSelectionPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  
  // Hooks
  const {
    searchQuery,
    searchResults,
    isSearching,
    searchPlaces,
    selectResult,
    getCurrentLocation
  } = useMapboxSearch()

  useNoZoom()

  // Redirect if not logged in
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // State management
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [destination, setDestination] = useState<any>(null)
  const [currentLocation, setCurrentLocation] = useState<[number, number] | null>(null)
  const [sortedResults, setSortedResults] = useState<any[]>([])
  const [isListening, setIsListening] = useState(false)
  const [speechSupported, setSpeechSupported] = useState(false)

  // Map states
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const [isMapLoaded, setIsMapLoaded] = useState(false)
  const destinationMarker = useRef<mapboxgl.Marker | null>(null)
  const userMarker = useRef<mapboxgl.Marker | null>(null)

  // Calculate distance between two coordinates (Haversine formula)
  const calculateDistance = useCallback((coord1: [number, number], coord2: [number, number]): number => {
    const R = 6371 // Earth's radius in kilometers
    const dLat = (coord2[1] - coord1[1]) * Math.PI / 180
    const dLon = (coord2[0] - coord1[0]) * Math.PI / 180
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(coord1[1] * Math.PI / 180) * Math.cos(coord2[1] * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c // Distance in kilometers
  }, [])

  // Sort search results by distance from user location
  const sortResultsByDistance = useCallback((results: any[]) => {
    console.log('🔄 Sorting results by distance...')
    console.log('📍 Current location:', currentLocation)
    console.log('📋 Results to sort:', results.length, 'items')

    if (!currentLocation || !results.length) {
      console.log('⚠️ Cannot sort: missing location or no results')
      return results
    }

    const sortedResults = results
      .map(result => {
        const distance = calculateDistance(currentLocation, result.center)
        console.log(`📏 Distance to ${result.place_name}: ${distance.toFixed(2)}km`)
        return {
          ...result,
          distance
        }
      })
      .sort((a, b) => a.distance - b.distance)

    console.log('✅ Results sorted by distance')
    return sortedResults
  }, [currentLocation, calculateDistance])

  // Reverse geocoding for address preview
  const reverseGeocode = useCallback(async (coordinates: [number, number]) => {
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${coordinates[0]},${coordinates[1]}.json?access_token=${MAPBOX_TOKEN}&language=pt-BR&limit=1`
      )
      const data = await response.json()
      if (data.features && data.features.length > 0) {
        return data.features[0].place_name
      }
    } catch (error) {
      console.warn('Reverse geocoding failed:', error)
    }
    return `${coordinates[1].toFixed(4)}, ${coordinates[0].toFixed(4)}`
  }, [])

  // Create draggable destination marker with route projection
  const createDestinationMarker = useCallback((coordinates: [number, number]) => {
    // Remove existing marker
    if (destinationMarker.current) {
      destinationMarker.current.remove()
    }

    // Create draggable marker
    destinationMarker.current = new mapboxgl.Marker({
      color: '#ef4444',
      scale: 1.2,
      draggable: true
    })
      .setLngLat(coordinates)
      .addTo(map.current!)

    // Add drag event listeners
    destinationMarker.current.on('dragstart', () => {
      // Remove route while dragging
      if (map.current?.getSource('route')) {
        map.current.removeLayer('route')
        map.current.removeSource('route')
      }
    })

    destinationMarker.current.on('dragend', async () => {
      const lngLat = destinationMarker.current!.getLngLat()
      const coords: [number, number] = [lngLat.lng, lngLat.lat]
      
      // Update destination with new coordinates
      const address = await reverseGeocode(coords)
      const destinationObj = {
        id: `destination-${Date.now()}`,
        place_name: address,
        center: coords,
        geometry: {
          type: 'Point' as const,
          coordinates: coords
        },
        properties: {},
        context: []
      }

      setDestination(destinationObj)
      
      // Project route to new destination
      await projectRoute(currentLocation!, coords)
    })

    return destinationMarker.current
  }, [reverseGeocode, currentLocation])

  // Project route between origin and destination using Mapbox Directions API
  const projectRoute = useCallback(async (origin: [number, number], destination: [number, number]) => {
    if (!map.current || !isMapLoaded) {
      console.warn('⚠️ Map not ready for route projection')
      return
    }

    if (!origin || !destination) {
      console.warn('⚠️ Missing origin or destination coordinates')
      return
    }

    console.log('🗺️ Projecting route from:', origin, 'to:', destination)

    try {
      // Call Mapbox Directions API
      const directionsUrl = `https://api.mapbox.com/directions/v5/mapbox/driving/${origin[0]},${origin[1]};${destination[0]},${destination[1]}?geometries=geojson&overview=full&steps=false&access_token=${MAPBOX_TOKEN}`

      console.log('🌐 Calling Directions API:', directionsUrl)

      const response = await fetch(directionsUrl)
      const data = await response.json()

      console.log('📡 Directions API response:', data)

      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0]
        console.log('🛣️ Route found:', route)

        // Wait for map to be fully loaded before adding route
        const addRouteToMap = () => {
          if (!map.current) return

          // Remove existing route if any
          if (map.current.getSource('route')) {
            console.log('🗑️ Removing existing route')
            try {
              map.current.removeLayer('route')
              map.current.removeSource('route')
            } catch (e) {
              console.warn('⚠️ Error removing existing route:', e)
            }
          }

          // Add route source with real route geometry
          console.log('📍 Adding route source with real geometry')
          map.current.addSource('route', {
            type: 'geojson',
            data: {
              type: 'Feature',
              properties: {},
              geometry: route.geometry
            }
          })

          // Add route layer
          console.log('🎨 Adding route layer')
          map.current.addLayer({
            id: 'route',
            type: 'line',
            source: 'route',
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': '#3b82f6',
              'line-width': 6,
              'line-opacity': 0.9
            }
          })

          console.log('✅ Route layer added successfully')

          // Fit map to show entire route
          const coordinates = route.geometry.coordinates
          const bounds = coordinates.reduce((bounds: any, coord: any) => {
            return bounds.extend(coord)
          }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]))

          map.current.fitBounds(bounds, {
            padding: 80,
            duration: 1000
          })

          console.log('🎯 Map fitted to route bounds')
        }

        // Ensure map style is loaded before adding route
        if (map.current.isStyleLoaded()) {
          addRouteToMap()
        } else {
          console.log('⏳ Waiting for map style to load...')
          map.current.once('styledata', addRouteToMap)
        }

      } else {
        console.warn('⚠️ No routes found in API response')
        // Fallback to straight line if no route found
        const fallbackRoute = {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: [origin, destination]
          }
        }

        if (map.current.getSource('route')) {
          map.current.removeLayer('route')
          map.current.removeSource('route')
        }

        map.current.addSource('route', {
          type: 'geojson',
          data: fallbackRoute
        })

        map.current.addLayer({
          id: 'route',
          type: 'line',
          source: 'route',
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': '#ef4444', // Red for fallback
            'line-width': 4,
            'line-opacity': 0.7
          }
        })

        console.log('🔴 Added fallback straight line route')
      }

    } catch (error) {
      console.error('❌ Failed to project route:', error)

      // Fallback to straight line on error
      try {
        const fallbackRoute = {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: [origin, destination]
          }
        }

        if (map.current?.getSource('route')) {
          map.current.removeLayer('route')
          map.current.removeSource('route')
        }

        map.current?.addSource('route', {
          type: 'geojson',
          data: fallbackRoute
        })

        map.current?.addLayer({
          id: 'route',
          type: 'line',
          source: 'route',
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': '#ef4444',
            'line-width': 4,
            'line-opacity': 0.7
          }
        })

        console.log('🔴 Added error fallback route')
      } catch (fallbackError) {
        console.error('❌ Even fallback route failed:', fallbackError)
      }
    }
  }, [isMapLoaded])

  // Initialize map
  const initializeMap = useCallback((coords: [number, number]) => {
    if (!mapContainer.current || map.current) return

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/dark-v11',
      center: coords,
      zoom: 14,
      attributionControl: false,
      antialias: true,
      pitch: 0,
      bearing: 0
    })

    map.current.on('load', () => {
      setIsMapLoaded(true)
      
      // Add user location marker with custom styling
      const userElement = document.createElement('div')
      userElement.innerHTML = `
        <div style="
          width: 20px;
          height: 20px;
          background: #3b82f6;
          border: 3px solid white;
          border-radius: 50%;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
        "></div>
      `
      
      userMarker.current = new mapboxgl.Marker({
        element: userElement
      })
        .setLngLat(coords)
        .addTo(map.current!)

      // Add click handler for destination selection
      map.current!.on('click', (e) => {
        const coordinates: [number, number] = [e.lngLat.lng, e.lngLat.lat]
        handleMapClick(coordinates)
      })

      // Add cursor change on hover
      map.current!.on('mouseenter', () => {
        map.current!.getCanvas().style.cursor = 'crosshair'
      })

      map.current!.on('mouseleave', () => {
        map.current!.getCanvas().style.cursor = ''
      })
    })

    // Add navigation controls positioned to avoid UI overlap
    map.current.addControl(new mapboxgl.NavigationControl(), 'bottom-right')
  }, [])

  // Handle map click for destination selection
  const handleMapClick = useCallback(async (coordinates: [number, number]) => {
    // Create draggable marker
    createDestinationMarker(coordinates)
    
    // Get address for the location
    const address = await reverseGeocode(coordinates)
    
    // Create destination object
    const destinationObj = {
      id: `destination-${Date.now()}`,
      place_name: address,
      center: coordinates,
      geometry: {
        type: 'Point' as const,
        coordinates
      },
      properties: {},
      context: []
    }

    setDestination(destinationObj)
    
    // Project route from current location to destination
    if (currentLocation) {
      await projectRoute(currentLocation, coordinates)
    }
  }, [createDestinationMarker, reverseGeocode, currentLocation, projectRoute])

  // Get user location on mount and initialize map
  useEffect(() => {
    const initLocation = async () => {
      try {
        console.log('🌍 Getting user location...')
        const coords = await getCurrentLocation()
        console.log('📍 User location obtained:', coords)
        setCurrentLocation(coords)
        initializeMap(coords)
      } catch (error) {
        console.warn('⚠️ Could not get user location:', error)
        // Fallback to São Paulo
        const fallbackCoords: [number, number] = [-46.6333, -23.5505]
        console.log('🏙️ Using fallback location (São Paulo):', fallbackCoords)
        setCurrentLocation(fallbackCoords)
        initializeMap(fallbackCoords)
      }
    }
    initLocation()
  }, [getCurrentLocation, initializeMap])

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      setSpeechSupported(true)
    }
  }, [])

  // Sort search results by distance whenever they change
  useEffect(() => {
    console.log('🔄 Search results changed:', searchResults)
    const sorted = sortResultsByDistance(searchResults)
    console.log('📊 Setting sorted results:', sorted)
    setSortedResults(sorted)
  }, [searchResults, sortResultsByDistance])

  // Cleanup map on unmount
  useEffect(() => {
    return () => {
      if (map.current) {
        map.current.remove()
      }
    }
  }, [])

  // Voice search functionality
  const startVoiceSearch = useCallback(() => {
    if (!speechSupported) return

    setIsListening(true)
    const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition
    const recognition = new SpeechRecognition()

    recognition.lang = 'pt-BR'
    recognition.continuous = false
    recognition.interimResults = false

    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript
      searchPlaces(transcript)
      setIsListening(false)
    }

    recognition.onerror = () => {
      setIsListening(false)
      setError('Erro no reconhecimento de voz')
    }

    recognition.onend = () => {
      setIsListening(false)
    }

    recognition.start()
  }, [speechSupported, searchPlaces])

  // Event handlers
  const handleDestinationSelect = useCallback(async (place: any) => {
    setDestination(place)
    selectResult(place)
    
    // Add draggable marker to map
    if (map.current && place.center) {
      createDestinationMarker(place.center)

      // Project route from current location to destination
      if (currentLocation) {
        await projectRoute(currentLocation, place.center)
      }

      // Center map on destination with smooth animation
      map.current.flyTo({
        center: place.center,
        zoom: 16,
        duration: 1000,
        essential: true
      })
    }
  }, [selectResult, createDestinationMarker, currentLocation, projectRoute])

  const handleConfirmDestination = useCallback(() => {
    if (destination) {
      // Store destination in sessionStorage for next page
      sessionStorage.setItem('rideDestination', JSON.stringify(destination))
      sessionStorage.setItem('rideOrigin', JSON.stringify({
        center: currentLocation,
        place_name: 'Sua localização atual'
      }))
      
      // Navigate to trip details page
      navigate('/ride-request/details')
    }
  }, [destination, currentLocation, navigate])

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-black/40"></div>
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

      <div className="relative z-10 flex flex-col h-full min-h-screen">
        {/* Header */}
        <motion.div
          className="pt-8 pb-6 px-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="flex items-center justify-between">
            <motion.button
              onClick={() => navigate('/dashboard')}
              className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowLeft className="w-5 h-5" />
            </motion.button>

            <div className="flex-1 text-center">
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-sm text-white/70">Selecione o destino</p>
            </div>

            <div className="w-9"></div>
          </div>
        </motion.div>

        {/* Content */}
        <div className="flex-1 px-4 pb-8">
          {/* Map Container */}
          <div className="relative h-[70vh] rounded-2xl overflow-hidden border border-white/20 shadow-2xl bg-gray-900">
            {/* Map Container */}
            <div ref={mapContainer} className="w-full h-full" />

            {/* Search Box Overlay */}
            <div className="absolute top-4 left-4 right-4 z-10">
              <div className="bg-white/10 backdrop-blur-md rounded-xl p-3 border border-white/20 shadow-lg">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => searchPlaces(e.target.value)}
                    placeholder="Para onde vamos?"
                    className="w-full pl-10 pr-16 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                  />

                  {/* Voice Search Button */}
                  {speechSupported && (
                    <button
                      onClick={startVoiceSearch}
                      disabled={isListening}
                      className="absolute right-8 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors disabled:opacity-50"
                    >
                      {isListening ? (
                        <MicOff className="w-4 h-4 text-red-400 animate-pulse" />
                      ) : (
                        <Mic className="w-4 h-4" />
                      )}
                    </button>
                  )}

                  {/* Loading Indicator */}
                  {isSearching && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
                    </div>
                  )}
                </div>

                {/* Search Results - Ordered by Distance */}
                {sortedResults.length > 0 && (
                  <div className="mt-3 bg-white/10 rounded-lg border border-white/20 overflow-hidden max-h-48 overflow-y-auto">
                    {sortedResults.map((result, index) => (
                      <button
                        key={index}
                        onClick={() => handleDestinationSelect(result)}
                        className="w-full text-left px-3 py-2 text-white hover:bg-white/10 transition-colors border-b border-white/10 last:border-b-0"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2 flex-1">
                            <MapPin className="w-3 h-3 text-white/60 flex-shrink-0" />
                            <div className="flex-1">
                              <p className="text-sm font-medium">{result.text}</p>
                              <p className="text-xs text-white/60 truncate">{result.place_name}</p>
                            </div>
                          </div>
                          {result.distance && (
                            <div className="text-xs text-white/50 ml-2">
                              {result.distance < 1
                                ? `${Math.round(result.distance * 1000)}m`
                                : `${result.distance.toFixed(1)}km`
                              }
                            </div>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Selection Mode Indicator */}
            {!destination && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
                <div className="bg-black/60 backdrop-blur-sm rounded-full px-4 py-2">
                  <span className="text-white text-sm">Toque no mapa</span>
                </div>
              </div>
            )}

            {/* Map Loading Indicator */}
            {!isMapLoaded && (
              <div className="absolute inset-0 bg-gray-900/80 flex items-center justify-center z-30">
                <div className="text-center text-white">
                  <Loader2 className="w-12 h-12 animate-spin mx-auto mb-4" />
                  <p className="text-lg font-medium">Carregando mapa...</p>
                  <p className="text-sm text-white/70">Aguarde um momento</p>
                </div>
              </div>
            )}
          </div>

          {/* Bottom UI Panel - Only show when destination is selected */}
          {destination && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6"
            >
              <div className="space-y-4">
                {/* Simple Destination Card */}
                <div className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20 shadow-2xl">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                        <MapPin className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-white text-sm font-medium line-clamp-1">{destination.place_name}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => {
                        setDestination(null)
                        if (destinationMarker.current) {
                          destinationMarker.current.remove()
                          destinationMarker.current = null
                        }
                        // Remove route
                        if (map.current?.getSource('route')) {
                          map.current.removeLayer('route')
                          map.current.removeSource('route')
                        }
                      }}
                      className="text-white/60 hover:text-white transition-colors p-1"
                    >
                      ✕
                    </button>
                  </div>
                </div>

                {/* Simple Confirm Button */}
                <motion.button
                  onClick={handleConfirmDestination}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Confirmar destino
                </motion.button>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  )
}

export default MapSelectionPage
