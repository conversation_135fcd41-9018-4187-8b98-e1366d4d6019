// =====================================================
// MONITOR ESPECÍFICO PARA DEBUG DO LOGIN
// Ana<PERSON><PERSON> em detalhes o processo de login e erros 400
// =====================================================

import puppeteer from 'puppeteer'

class LoginDebugMonitor {
  constructor() {
    this.browser = null
    this.page = null
    this.networkRequests = []
    this.consoleMessages = []
    this.errors = []
  }

  async init() {
    console.log('🔍 INICIANDO DEBUG ESPECÍFICO DO LOGIN')
    console.log('=' .repeat(60))
    
    this.browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--start-maximized'
      ]
    })
    
    this.page = await this.browser.newPage()
    await this.page.setViewport({ width: 1280, height: 720 })
    
    this.setupDetailedMonitoring()
    console.log('✅ Monitor de debug configurado')
  }

  setupDetailedMonitoring() {
    // Capturar TODAS as requisições de rede
    this.page.on('request', (request) => {
      const requestData = {
        type: 'request',
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        postData: request.postData(),
        timestamp: new Date().toISOString()
      }
      
      this.networkRequests.push(requestData)
      
      if (request.url().includes('auth') || request.url().includes('login') || request.url().includes('supabase')) {
        console.log(`🌐 REQUEST: ${request.method()} ${request.url()}`)
        if (request.postData()) {
          console.log(`📤 POST DATA: ${request.postData()}`)
        }
      }
    })

    // Capturar TODAS as respostas
    this.page.on('response', (response) => {
      const responseData = {
        type: 'response',
        url: response.url(),
        status: response.status(),
        statusText: response.statusText(),
        headers: response.headers(),
        timestamp: new Date().toISOString()
      }
      
      this.networkRequests.push(responseData)
      
      if (response.url().includes('auth') || response.url().includes('login') || response.url().includes('supabase')) {
        console.log(`📥 RESPONSE: ${response.status()} ${response.url()}`)
        
        if (response.status() >= 400) {
          console.log(`🔴 ERROR RESPONSE: ${response.status()} ${response.statusText()}`)
          
          // Tentar capturar o corpo da resposta de erro
          response.text().then(body => {
            console.log(`📄 ERROR BODY: ${body}`)
          }).catch(() => {
            console.log('⚠️ Não foi possível ler o corpo da resposta de erro')
          })
        }
      }
    })

    // Capturar falhas de requisição
    this.page.on('requestfailed', (request) => {
      const failureData = {
        type: 'requestfailed',
        url: request.url(),
        method: request.method(),
        failure: request.failure(),
        timestamp: new Date().toISOString()
      }
      
      this.networkRequests.push(failureData)
      console.log(`💥 REQUEST FAILED: ${request.method()} ${request.url()}`)
      console.log(`📋 Failure reason: ${request.failure()?.errorText}`)
    })

    // Capturar logs do console com mais detalhes
    this.page.on('console', (msg) => {
      const logData = {
        type: msg.type(),
        text: msg.text(),
        location: msg.location(),
        timestamp: new Date().toISOString()
      }
      
      this.consoleMessages.push(logData)
      
      const time = new Date().toLocaleTimeString()
      
      if (msg.type() === 'error') {
        this.errors.push(logData)
        console.log(`[${time}] ❌ CONSOLE ERROR: ${msg.text()}`)
        if (msg.location()) {
          console.log(`📍 Location: ${msg.location().url}:${msg.location().lineNumber}`)
        }
      } else if (msg.text().includes('login') || msg.text().includes('auth') || msg.text().includes('400')) {
        console.log(`[${time}] 🎯 AUTH LOG: ${msg.text()}`)
      }
    })

    // Capturar erros de página
    this.page.on('pageerror', (error) => {
      const errorData = {
        type: 'pageerror',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }
      
      this.errors.push(errorData)
      console.log(`💥 PAGE ERROR: ${error.message}`)
      console.log(`📚 Stack: ${error.stack}`)
    })
  }

  async debugLogin() {
    console.log('\n🔐 INICIANDO DEBUG DETALHADO DO LOGIN')
    console.log('-' .repeat(50))
    
    try {
      // Navegar para login
      console.log('🌐 Navegando para página de login...')
      await this.page.goto('http://localhost:3000/login', { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })
      
      console.log('⏱️ Aguardando página carregar completamente...')
      await new Promise(resolve => setTimeout(resolve, 5000))
      
      // Analisar a página de login
      console.log('\n🔍 ANALISANDO PÁGINA DE LOGIN:')
      
      const pageInfo = await this.page.evaluate(() => {
        return {
          title: document.title,
          url: window.location.href,
          forms: Array.from(document.forms).map(form => ({
            action: form.action,
            method: form.method,
            elements: Array.from(form.elements).map(el => ({
              name: el.name,
              type: el.type,
              id: el.id
            }))
          })),
          inputs: Array.from(document.querySelectorAll('input')).map(input => ({
            name: input.name,
            type: input.type,
            id: input.id,
            placeholder: input.placeholder
          })),
          buttons: Array.from(document.querySelectorAll('button')).map(btn => ({
            text: btn.textContent,
            type: btn.type,
            id: btn.id
          }))
        }
      })
      
      console.log(`📄 Título: ${pageInfo.title}`)
      console.log(`🔗 URL: ${pageInfo.url}`)
      console.log(`📝 Formulários encontrados: ${pageInfo.forms.length}`)
      console.log(`🔤 Inputs encontrados: ${pageInfo.inputs.length}`)
      console.log(`🔘 Botões encontrados: ${pageInfo.buttons.length}`)
      
      // Mostrar detalhes dos inputs
      pageInfo.inputs.forEach((input, i) => {
        console.log(`  Input ${i + 1}: ${input.type} - ${input.name || input.id} - "${input.placeholder}"`)
      })
      
      // Mostrar detalhes dos botões
      pageInfo.buttons.forEach((btn, i) => {
        console.log(`  Botão ${i + 1}: "${btn.text}" - ${btn.type}`)
      })
      
      // Tentar fazer login
      console.log('\n📝 TENTANDO FAZER LOGIN:')
      
      const emailField = await this.page.$('input[type="email"], input[name="email"], input[placeholder*="email" i]')
      const passwordField = await this.page.$('input[type="password"], input[name="password"]')
      
      if (emailField && passwordField) {
        console.log('✅ Campos de email e senha encontrados')
        
        // Limpar campos primeiro
        await emailField.click({ clickCount: 3 })
        await emailField.type('<EMAIL>', { delay: 100 })
        console.log('📧 Email preenchido')
        
        await passwordField.click({ clickCount: 3 })
        await passwordField.type('123456', { delay: 100 })
        console.log('🔒 Senha preenchida')
        
        // Aguardar um pouco antes de submeter
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        console.log('🔍 Procurando botão de submit...')
        
        // Tentar diferentes formas de submeter
        let submitButton = await this.page.$('button[type="submit"]')
        
        if (!submitButton) {
          const buttons = await this.page.$x('//button[contains(text(), "Login") or contains(text(), "Entrar") or contains(text(), "Sign")]')
          submitButton = buttons[0] || null
        }
        
        if (submitButton) {
          console.log('🖱️ Clicando no botão de submit...')
          
          // Monitorar requisições durante o clique
          console.log('\n📡 MONITORANDO REQUISIÇÕES DURANTE LOGIN:')
          
          await submitButton.click()
          
          // Aguardar e monitorar
          console.log('⏱️ Aguardando resposta do servidor...')
          await new Promise(resolve => setTimeout(resolve, 8000))
          
        } else {
          console.log('⚠️ Botão de submit não encontrado, tentando Enter...')
          await passwordField.press('Enter')
          await new Promise(resolve => setTimeout(resolve, 5000))
        }
        
      } else {
        console.log('❌ Campos de login não encontrados')
      }
      
    } catch (error) {
      console.error('💥 Erro durante debug do login:', error.message)
    }
  }

  async generateDetailedReport() {
    console.log('\n' + '=' .repeat(80))
    console.log('📊 RELATÓRIO DETALHADO DO DEBUG DO LOGIN')
    console.log('=' .repeat(80))
    
    // Analisar requisições de autenticação
    const authRequests = this.networkRequests.filter(req => 
      req.url && (
        req.url.includes('auth') || 
        req.url.includes('login') || 
        req.url.includes('supabase') ||
        req.url.includes('sign')
      )
    )
    
    console.log(`\n🌐 REQUISIÇÕES DE AUTENTICAÇÃO (${authRequests.length}):`)
    authRequests.forEach((req, i) => {
      console.log(`\n${i + 1}. ${req.type?.toUpperCase()} - ${req.method || 'N/A'} ${req.url}`)
      if (req.status) {
        console.log(`   Status: ${req.status} ${req.statusText || ''}`)
      }
      if (req.postData) {
        console.log(`   POST Data: ${req.postData}`)
      }
      if (req.failure) {
        console.log(`   Failure: ${req.failure.errorText}`)
      }
    })
    
    // Analisar erros
    console.log(`\n❌ ERROS ENCONTRADOS (${this.errors.length}):`)
    this.errors.forEach((error, i) => {
      console.log(`\n${i + 1}. [${error.type}] ${error.text || error.message}`)
      if (error.location) {
        console.log(`   📍 ${error.location.url}:${error.location.lineNumber}`)
      }
    })
    
    // Analisar logs relacionados ao auth
    const authLogs = this.consoleMessages.filter(msg => 
      msg.text.toLowerCase().includes('auth') ||
      msg.text.toLowerCase().includes('login') ||
      msg.text.toLowerCase().includes('supabase') ||
      msg.text.includes('400') ||
      msg.text.includes('error')
    )
    
    console.log(`\n📝 LOGS RELACIONADOS À AUTENTICAÇÃO (${authLogs.length}):`)
    authLogs.forEach((log, i) => {
      console.log(`${i + 1}. [${log.type}] ${log.text}`)
    })
    
    return {
      authRequests,
      errors: this.errors,
      authLogs,
      totalRequests: this.networkRequests.length,
      totalMessages: this.consoleMessages.length
    }
  }

  async close() {
    if (this.browser) {
      console.log('\n🔒 Fechando navegador...')
      await this.browser.close()
    }
  }

  async runDebug() {
    try {
      await this.init()
      await this.debugLogin()
      const report = await this.generateDetailedReport()
      
      return report
      
    } finally {
      await this.close()
    }
  }
}

// Executar debug
const debugMonitor = new LoginDebugMonitor()

debugMonitor.runDebug()
  .then((report) => {
    console.log('\n🏁 DEBUG DO LOGIN CONCLUÍDO!')
    console.log(`📊 Resumo: ${report.authRequests.length} requisições auth, ${report.errors.length} erros`)
    process.exit(report.errors.length > 0 ? 1 : 0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal no debug:', error)
    process.exit(1)
  })
