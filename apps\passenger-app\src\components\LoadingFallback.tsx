import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { Al<PERSON><PERSON><PERSON>gle, RefreshCw, Home } from 'lucide-react'

interface LoadingFallbackProps {
  timeout?: number
  onTimeout?: () => void
  message?: string
}

export const LoadingFallback: React.FC<LoadingFallbackProps> = ({ 
  timeout = 30000, // 30 segundos
  onTimeout,
  message = "Carregando..."
}) => {
  const [timeLeft, setTimeLeft] = useState(timeout / 1000)
  const [hasTimedOut, setHasTimedOut] = useState(false)

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          setHasTimedOut(true)
          onTimeout?.()
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [onTimeout])

  const handleReload = () => {
    window.location.reload()
  }

  const handleGoHome = () => {
    window.location.href = '/'
  }

  if (hasTimedOut) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
        <motion.div
          className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6"
            initial={{ rotate: 0 }}
            animate={{ rotate: 360 }}
            transition={{ duration: 1 }}
          >
            <AlertTriangle className="w-8 h-8 text-red-600" />
          </motion.div>

          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Tempo Limite Excedido
          </h1>

          <p className="text-gray-600 mb-6">
            O carregamento está demorando mais que o esperado. Isso pode indicar um problema de conexão ou configuração.
          </p>

          <div className="space-y-3">
            <motion.button
              onClick={handleReload}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-xl font-medium flex items-center justify-center space-x-2 hover:bg-blue-700 transition-colors"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <RefreshCw className="w-5 h-5" />
              <span>Tentar Novamente</span>
            </motion.button>

            <motion.button
              onClick={handleGoHome}
              className="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-xl font-medium flex items-center justify-center space-x-2 hover:bg-gray-200 transition-colors"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Home className="w-5 h-5" />
              <span>Voltar ao Início</span>
            </motion.button>
          </div>

          <p className="text-xs text-gray-500 mt-6">
            Se o problema persistir, verifique sua conexão com a internet ou entre em contato com o suporte.
          </p>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4">
      <motion.div
        className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 max-w-md w-full text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <motion.div
          className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-6"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />

        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          {message}
        </h2>

        <p className="text-gray-600 mb-6">
          Aguarde enquanto preparamos tudo para você...
        </p>

        <div className="bg-gray-100 rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Tempo restante:</span>
            <span className="font-mono text-blue-600">{timeLeft}s</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <motion.div
              className="bg-blue-500 h-2 rounded-full"
              initial={{ width: "100%" }}
              animate={{ width: `${(timeLeft / (timeout / 1000)) * 100}%` }}
              transition={{ duration: 1, ease: "linear" }}
            />
          </div>
        </div>

        <p className="text-xs text-gray-500">
          Se o carregamento demorar muito, a página será recarregada automaticamente.
        </p>
      </motion.div>
    </div>
  )
}

export default LoadingFallback
