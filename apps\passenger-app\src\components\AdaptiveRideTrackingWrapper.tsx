import React, { useEffect } from 'react'
import { RideTrackingMobile } from '../pages/RideTrackingMobile'
import RideTracking from '../pages/RideTracking'
import { DeviceWrapper } from './DeviceWrapper'
import { useDeviceDetection, shouldUseMobileNativeUI, shouldUseDesktopMockup, logDeviceInfo } from '../utils/deviceDetector'

// 🔄 WRAPPER ADAPTATIVO DE RIDE TRACKING
// Escolhe automaticamente entre UI mobile nativa e mockup desktop
// Baseado na detecção real do dispositivo
// MANTÉM DESIGN ORIGINAL + CONVERSÃO ANDROID NATIVA

export const AdaptiveRideTrackingWrapper: React.FC = () => {
  const deviceInfo = useDeviceDetection()

  // Log informações do dispositivo para debug
  useEffect(() => {
    logDeviceInfo()
  }, [])

  // Decide qual UI usar baseado no dispositivo
  const useMobileNativeUI = shouldUseMobileNativeUI()
  const useDesktopMockup = shouldUseDesktopMockup()

  // Debug removido para console limpo

  // MOBILE NATIVO: Usa React Native Elements diretamente
  if (useMobileNativeUI) {
    return (
      <div style={{ 
        width: '100vw', 
        height: '100vh', 
        overflow: 'hidden',
        backgroundColor: '#f5f5f5'
      }}>
        <RideTrackingMobile />
      </div>
    )
  }

  // DESKTOP MOCKUP: Usa o sistema de mockup iPhone
  if (useDesktopMockup) {
    return (
      <DeviceWrapper>
        <RideTracking />
      </DeviceWrapper>
    )
  }

  // FALLBACK: Usa mobile nativo como padrão
  return (
    <div style={{ 
      width: '100vw', 
      height: '100vh', 
      overflow: 'hidden',
      backgroundColor: '#f5f5f5'
    }}>
      <RideTrackingMobile />
    </div>
  )
}

export default AdaptiveRideTrackingWrapper
