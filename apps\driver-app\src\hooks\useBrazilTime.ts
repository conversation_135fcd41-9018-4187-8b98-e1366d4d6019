import { useState, useEffect } from 'react'

// 🇧🇷 HOOK PARA HORÁRIO DE BRASÍLIA EM TEMPO REAL
// Atualiza a cada segundo com o horário correto do Brasil

export const useBrazilTime = () => {
  const [time, setTime] = useState('')

  useEffect(() => {
    const updateTime = () => {
      // Cria data atual e converte para horário de Brasília
      const now = new Date()
      
      // Brasília está em UTC-3 (hor<PERSON><PERSON> padr<PERSON>) ou UTC-2 (hor<PERSON>rio de verão)
      // Usando Intl.DateTimeFormat para lidar automaticamente com horário de verão
      const brazilTime = new Intl.DateTimeFormat('pt-BR', {
        timeZone: 'America/Sao_Paulo',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false // Formato 24h como no iPhone
      }).format(now)

      setTime(brazilTime)
    }

    // Atualiza imediatamente
    updateTime()

    // Atualiza a cada segundo
    const interval = setInterval(updateTime, 1000)

    // Cleanup
    return () => clearInterval(interval)
  }, [])

  return time
}

// Hook alternativo que retorna objeto com mais informações
export const useBrazilTimeDetailed = () => {
  const [timeData, setTimeData] = useState({
    time: '',
    date: '',
    dayOfWeek: '',
    timezone: ''
  })

  useEffect(() => {
    const updateTime = () => {
      const now = new Date()
      
      // Formatadores para diferentes partes da data/hora
      const timeFormatter = new Intl.DateTimeFormat('pt-BR', {
        timeZone: 'America/Sao_Paulo',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })

      const dateFormatter = new Intl.DateTimeFormat('pt-BR', {
        timeZone: 'America/Sao_Paulo',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      })

      const dayFormatter = new Intl.DateTimeFormat('pt-BR', {
        timeZone: 'America/Sao_Paulo',
        weekday: 'long'
      })

      // Detecta se está em horário de verão
      const january = new Date(now.getFullYear(), 0, 1)
      const july = new Date(now.getFullYear(), 6, 1)
      const januaryOffset = january.getTimezoneOffset()
      const julyOffset = july.getTimezoneOffset()
      const isDST = Math.max(januaryOffset, julyOffset) !== now.getTimezoneOffset()
      
      setTimeData({
        time: timeFormatter.format(now),
        date: dateFormatter.format(now),
        dayOfWeek: dayFormatter.format(now),
        timezone: isDST ? 'UTC-2' : 'UTC-3'
      })
    }

    updateTime()
    const interval = setInterval(updateTime, 1000)

    return () => clearInterval(interval)
  }, [])

  return timeData
}

export default useBrazilTime
