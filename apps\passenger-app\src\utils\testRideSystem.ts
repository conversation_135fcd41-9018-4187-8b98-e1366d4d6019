import { rideService } from '../services/RideService'
import { driverLocationService } from '../services/DriverLocationService'
import { mapboxService } from '../services/MapboxService'
import { supabase } from '../lib/supabase'

/**
 * Script para testar o sistema completo de corridas
 */
export class RideSystemTester {
  
  /**
   * Testar criação de corrida com atribuição automática de motorista
   */
  static async testRideCreation() {
    console.log('🧪 Testando criação de corrida...')
    
    try {
      // Coordenadas de teste (São Paulo)
      const origin: [number, number] = [-46.6333, -23.5505] // Centro de SP
      const destination: [number, number] = [-46.6632, -23.5928] // Moema
      
      const ride = await rideService.createRideRequest(
        'Avenida Paulista, 1000 - Bela Vista, São Paulo - SP',
        origin,
        'Shopping Ibirapuera - Av. Ibirapuera, 3103 - Moema, São Paulo - SP',
        destination,
        'economy',
        'credit_card'
      )
      
      if (ride) {
        console.log('✅ Corrida criada com sucesso:', {
          id: ride.id,
          status: ride.status,
          driver_assigned: !!ride.driver_id,
          estimated_price: ride.estimated_price
        })
        
        return ride
      } else {
        console.log('❌ Falha ao criar corrida')
        return null
      }
      
    } catch (error) {
      console.error('❌ Erro ao testar criação de corrida:', error)
      return null
    }
  }
  
  /**
   * Testar busca de motoristas próximos
   */
  static async testNearbyDrivers() {
    console.log('🧪 Testando busca de motoristas próximos...')
    
    try {
      // Centro de São Paulo
      const userLocation: [number, number] = [-46.6333, -23.5505]
      
      const drivers = await rideService.findNearbyDrivers(userLocation, 10)
      
      console.log(`✅ Encontrados ${drivers.length} motoristas próximos:`)
      drivers.forEach((driver, index) => {
        console.log(`  ${index + 1}. ${driver.name} - ${driver.distance}km - Rating: ${driver.rating} - ETA: ${driver.eta}min`)
      })
      
      return drivers
      
    } catch (error) {
      console.error('❌ Erro ao buscar motoristas próximos:', error)
      return []
    }
  }
  
  /**
   * Testar cálculo de preço dinâmico
   */
  static async testDynamicPricing() {
    console.log('🧪 Testando cálculo de preço dinâmico...')
    
    try {
      const origin: [number, number] = [-46.6333, -23.5505]
      const destination: [number, number] = [-46.6632, -23.5928]
      
      const vehicleTypes = ['moto', 'economy', 'comfort', 'premium']
      
      for (const vehicleType of vehicleTypes) {
        const estimate = await mapboxService.calculateRideEstimate(origin, destination, vehicleType)
        
        if (estimate) {
          console.log(`✅ ${vehicleType.toUpperCase()}: R$ ${estimate.price.toFixed(2)} - ${(estimate.distance/1000).toFixed(1)}km - ${(estimate.duration/60).toFixed(1)}min`)
        }
      }
      
    } catch (error) {
      console.error('❌ Erro ao testar preços dinâmicos:', error)
    }
  }
  
  /**
   * Testar atualizações em tempo real
   */
  static async testRealTimeUpdates(rideId: string) {
    console.log('🧪 Testando atualizações em tempo real...')
    
    try {
      // Subscrever a atualizações da corrida
      const subscription = rideService.subscribeToRideUpdates(rideId, (ride) => {
        console.log('📡 Update recebido:', {
          id: ride.id,
          status: ride.status,
          driver_id: ride.driver_id
        })
      })
      
      console.log('✅ Subscription criada para ride:', rideId)
      
      // Retornar função de cleanup
      return () => {
        subscription.unsubscribe()
        console.log('🛑 Subscription cancelada')
      }
      
    } catch (error) {
      console.error('❌ Erro ao testar real-time updates:', error)
      return () => {}
    }
  }
  
  /**
   * Testar localização de motorista em tempo real
   */
  static async testDriverLocationTracking(driverId: string) {
    console.log('🧪 Testando rastreamento de motorista...')
    
    try {
      // Buscar localização atual
      const currentLocation = await driverLocationService.getDriverLocation(driverId)
      
      if (currentLocation) {
        console.log('📍 Localização atual do motorista:', {
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
          speed: currentLocation.speed,
          heading: currentLocation.heading
        })
      }
      
      // Subscrever a atualizações
      const unsubscribe = driverLocationService.subscribeToDriverLocation(driverId, (update) => {
        console.log('📍 Localização atualizada:', {
          location: update.location,
          speed: update.speed,
          heading: update.heading,
          timestamp: update.timestamp
        })
      })
      
      console.log('✅ Tracking iniciado para motorista:', driverId)
      
      return unsubscribe
      
    } catch (error) {
      console.error('❌ Erro ao testar tracking de motorista:', error)
      return () => {}
    }
  }
  
  /**
   * Simular movimento de motorista
   */
  static async simulateDriverMovement(driverId: string, duration: number = 30000) {
    console.log('🧪 Simulando movimento de motorista...')
    
    try {
      let currentLat = -23.5505 + (Math.random() - 0.5) * 0.01
      let currentLng = -46.6333 + (Math.random() - 0.5) * 0.01
      let heading = Math.random() * 360
      
      const interval = setInterval(async () => {
        // Simular movimento
        currentLat += (Math.random() - 0.5) * 0.0005
        currentLng += (Math.random() - 0.5) * 0.0005
        heading = (heading + (Math.random() - 0.5) * 30) % 360
        const speed = Math.random() * 50 + 10 // 10-60 km/h
        
        await driverLocationService.updateDriverLocation(
          driverId,
          currentLat,
          currentLng,
          heading,
          speed,
          true
        )
        
        console.log(`📍 Motorista ${driverId} movido para: ${currentLat.toFixed(6)}, ${currentLng.toFixed(6)}`)
        
      }, 2000) // Atualizar a cada 2 segundos
      
      // Parar simulação após duração especificada
      setTimeout(() => {
        clearInterval(interval)
        console.log('🛑 Simulação de movimento finalizada')
      }, duration)
      
      return () => clearInterval(interval)
      
    } catch (error) {
      console.error('❌ Erro ao simular movimento:', error)
      return () => {}
    }
  }
  
  /**
   * Executar todos os testes
   */
  static async runAllTests() {
    console.log('🚀 Iniciando testes completos do sistema...')
    
    try {
      // 1. Testar busca de motoristas
      await this.testNearbyDrivers()
      
      // 2. Testar preços dinâmicos
      await this.testDynamicPricing()
      
      // 3. Testar criação de corrida
      const ride = await this.testRideCreation()
      
      if (ride) {
        // 4. Testar real-time updates
        const unsubscribeRide = await this.testRealTimeUpdates(ride.id)
        
        // 5. Se há motorista atribuído, testar tracking
        if (ride.driver_id) {
          const unsubscribeDriver = await this.testDriverLocationTracking(ride.driver_id)
          
          // 6. Simular movimento do motorista
          const stopSimulation = await this.simulateDriverMovement(ride.driver_id, 20000)
          
          // Cleanup após 25 segundos
          setTimeout(() => {
            unsubscribeRide()
            unsubscribeDriver()
            stopSimulation()
            console.log('🧹 Testes finalizados e cleanup realizado')
          }, 25000)
        }
      }
      
      console.log('✅ Todos os testes iniciados com sucesso!')
      
    } catch (error) {
      console.error('❌ Erro durante execução dos testes:', error)
    }
  }
  
  /**
   * Testar funções do banco de dados
   */
  static async testDatabaseFunctions() {
    console.log('🧪 Testando funções do banco de dados...')
    
    try {
      // Testar busca de motoristas próximos via RPC
      const { data: nearbyDrivers, error: nearbyError } = await supabase
        .rpc('find_nearby_drivers', {
          user_lat: -23.5505,
          user_lng: -46.6333,
          radius_km: 10
        })
      
      if (nearbyError) {
        console.error('❌ Erro na função find_nearby_drivers:', nearbyError)
      } else {
        console.log(`✅ Função find_nearby_drivers: ${nearbyDrivers?.length || 0} motoristas encontrados`)
      }
      
      // Testar simulação de movimento
      const { error: simulationError } = await supabase
        .rpc('simulate_driver_movement')
      
      if (simulationError) {
        console.error('❌ Erro na função simulate_driver_movement:', simulationError)
      } else {
        console.log('✅ Função simulate_driver_movement executada com sucesso')
      }
      
    } catch (error) {
      console.error('❌ Erro ao testar funções do banco:', error)
    }
  }
}

// Exportar função para uso fácil no console
export const testRideSystem = () => RideSystemTester.runAllTests()
export const testDatabaseFunctions = () => RideSystemTester.testDatabaseFunctions()

// Para usar no console do navegador:
// import { testRideSystem } from './utils/testRideSystem'
// testRideSystem()
