/**
 * 🧹 SISTEMA DE LIMPEZA DE MEMÓRIA E PREVENÇÃO DE MEMORY LEAKS
 * Gerencia subscriptions, timers e event listeners para evitar vazamentos
 */

export class MemoryCleanup {
  private static instance: MemoryCleanup;
  private subscriptions: Set<() => void> = new Set();
  private timers: Set<NodeJS.Timeout> = new Set();
  private intervals: Set<NodeJS.Timeout> = new Set();
  private eventListeners: Set<{ element: EventTarget; event: string; handler: EventListener }> = new Set();

  static getInstance(): MemoryCleanup {
    if (!MemoryCleanup.instance) {
      MemoryCleanup.instance = new MemoryCleanup();
    }
    return MemoryCleanup.instance;
  }

  // Registrar subscription para cleanup automático
  registerSubscription(unsubscribe: () => void): void {
    this.subscriptions.add(unsubscribe);
  }

  // Registrar timer para cleanup automático
  registerTimer(timer: NodeJS.Timeout): void {
    this.timers.add(timer);
  }

  // Registrar interval para cleanup automático
  registerInterval(interval: NodeJS.Timeout): void {
    this.intervals.add(interval);
  }

  // Registrar event listener para cleanup automático
  registerEventListener(element: EventTarget, event: string, handler: EventListener): void {
    this.eventListeners.add({ element, event, handler });
  }

  // Limpar subscription específica
  cleanupSubscription(unsubscribe: () => void): void {
    try {
      unsubscribe();
      this.subscriptions.delete(unsubscribe);
    } catch (error) {
      console.warn('Erro ao limpar subscription:', error);
    }
  }

  // Limpar timer específico
  cleanupTimer(timer: NodeJS.Timeout): void {
    try {
      clearTimeout(timer);
      this.timers.delete(timer);
    } catch (error) {
      console.warn('Erro ao limpar timer:', error);
    }
  }

  // Limpar interval específico
  cleanupInterval(interval: NodeJS.Timeout): void {
    try {
      clearInterval(interval);
      this.intervals.delete(interval);
    } catch (error) {
      console.warn('Erro ao limpar interval:', error);
    }
  }

  // Limpar event listener específico
  cleanupEventListener(element: EventTarget, event: string, handler: EventListener): void {
    try {
      element.removeEventListener(event, handler);
      this.eventListeners.delete({ element, event, handler });
    } catch (error) {
      console.warn('Erro ao limpar event listener:', error);
    }
  }

  // Limpar todas as subscriptions
  cleanupAllSubscriptions(): void {
    this.subscriptions.forEach(unsubscribe => {
      try {
        unsubscribe();
      } catch (error) {
        console.warn('Erro ao limpar subscription:', error);
      }
    });
    this.subscriptions.clear();
  }

  // Limpar todos os timers
  cleanupAllTimers(): void {
    this.timers.forEach(timer => {
      try {
        clearTimeout(timer);
      } catch (error) {
        console.warn('Erro ao limpar timer:', error);
      }
    });
    this.timers.clear();
  }

  // Limpar todos os intervals
  cleanupAllIntervals(): void {
    this.intervals.forEach(interval => {
      try {
        clearInterval(interval);
      } catch (error) {
        console.warn('Erro ao limpar interval:', error);
      }
    });
    this.intervals.clear();
  }

  // Limpar todos os event listeners
  cleanupAllEventListeners(): void {
    this.eventListeners.forEach(({ element, event, handler }) => {
      try {
        element.removeEventListener(event, handler);
      } catch (error) {
        console.warn('Erro ao limpar event listener:', error);
      }
    });
    this.eventListeners.clear();
  }

  // Limpeza completa de tudo
  cleanupAll(): void {
    console.log('🧹 Executando limpeza completa de memória...');
    
    this.cleanupAllSubscriptions();
    this.cleanupAllTimers();
    this.cleanupAllIntervals();
    this.cleanupAllEventListeners();
    
    console.log('✅ Limpeza de memória concluída');
  }

  // Obter estatísticas de uso
  getStats(): {
    subscriptions: number;
    timers: number;
    intervals: number;
    eventListeners: number;
  } {
    return {
      subscriptions: this.subscriptions.size,
      timers: this.timers.size,
      intervals: this.intervals.size,
      eventListeners: this.eventListeners.size
    };
  }

  // Verificar se há vazamentos potenciais
  checkForLeaks(): boolean {
    const stats = this.getStats();
    const totalItems = stats.subscriptions + stats.timers + stats.intervals + stats.eventListeners;
    
    if (totalItems > 50) {
      console.warn('⚠️ Possível memory leak detectado:', stats);
      return true;
    }
    
    return false;
  }

  // Limpeza automática periódica
  startAutoCleanup(intervalMs: number = 60000): void {
    const cleanupInterval = setInterval(() => {
      if (this.checkForLeaks()) {
        console.log('🧹 Executando limpeza automática devido a possível memory leak...');
        this.cleanupAll();
      }
    }, intervalMs);

    this.registerInterval(cleanupInterval);
  }
}

// Hook React para usar o sistema de cleanup
import { useEffect, useRef } from 'react';

export const useMemoryCleanup = () => {
  const cleanup = useRef(MemoryCleanup.getInstance());

  useEffect(() => {
    return () => {
      // Cleanup automático quando o componente é desmontado
      cleanup.current.cleanupAll();
    };
  }, []);

  return {
    registerSubscription: (unsubscribe: () => void) => cleanup.current.registerSubscription(unsubscribe),
    registerTimer: (timer: NodeJS.Timeout) => cleanup.current.registerTimer(timer),
    registerInterval: (interval: NodeJS.Timeout) => cleanup.current.registerInterval(interval),
    registerEventListener: (element: EventTarget, event: string, handler: EventListener) => 
      cleanup.current.registerEventListener(element, event, handler),
    cleanup: () => cleanup.current.cleanupAll(),
    getStats: () => cleanup.current.getStats()
  };
};

// Função utilitária para criar subscription segura
export const createSafeSubscription = (subscriptionFn: () => () => void): (() => void) => {
  const cleanup = MemoryCleanup.getInstance();
  
  try {
    const unsubscribe = subscriptionFn();
    cleanup.registerSubscription(unsubscribe);
    return unsubscribe;
  } catch (error) {
    console.error('Erro ao criar subscription:', error);
    return () => {}; // Retorna função vazia em caso de erro
  }
};

// Função utilitária para criar timer seguro
export const createSafeTimer = (callback: () => void, delay: number): NodeJS.Timeout => {
  const cleanup = MemoryCleanup.getInstance();
  const timer = setTimeout(callback, delay);
  cleanup.registerTimer(timer);
  return timer;
};

// Função utilitária para criar interval seguro
export const createSafeInterval = (callback: () => void, delay: number): NodeJS.Timeout => {
  const cleanup = MemoryCleanup.getInstance();
  const interval = setInterval(callback, delay);
  cleanup.registerInterval(interval);
  return interval;
};

// Função utilitária para adicionar event listener seguro
export const addSafeEventListener = (
  element: EventTarget, 
  event: string, 
  handler: EventListener, 
  options?: AddEventListenerOptions
): void => {
  const cleanup = MemoryCleanup.getInstance();
  element.addEventListener(event, handler, options);
  cleanup.registerEventListener(element, event, handler);
};

// Inicializar limpeza automática global
if (typeof window !== 'undefined') {
  const globalCleanup = MemoryCleanup.getInstance();
  
  // Limpeza quando a página é fechada
  window.addEventListener('beforeunload', () => {
    globalCleanup.cleanupAll();
  });
  
  // Limpeza quando a página perde foco (mobile)
  window.addEventListener('pagehide', () => {
    globalCleanup.cleanupAll();
  });
  
  // Iniciar limpeza automática a cada minuto
  globalCleanup.startAutoCleanup(60000);
}

export default MemoryCleanup;
