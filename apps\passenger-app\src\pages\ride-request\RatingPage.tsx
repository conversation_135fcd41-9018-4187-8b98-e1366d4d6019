import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Navigate, useNavigate } from 'react-router-dom'
import { 
  Star,
  CheckCircle,
  Navigation,
  Clock,
  DollarSign,
  Send
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContextSimple'
import { useNoZoom } from '../../hooks/useNoZoom'

export const RatingPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  
  useNoZoom()

  // Redirect if not logged in
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // State management
  const [completedRide, setCompletedRide] = useState<any>(null)
  const [rating, setRating] = useState(0)
  const [comment, setComment] = useState('')
  const [loading, setLoading] = useState(false)

  // Load completed ride data from sessionStorage
  useEffect(() => {
    const savedRideData = sessionStorage.getItem('completedRide')
    
    if (savedRideData) {
      setCompletedRide(JSON.parse(savedRideData))
    } else {
      // If no ride data, redirect back to dashboard
      navigate('/dashboard')
    }
  }, [navigate])

  // Event handlers
  const handleRating = useCallback((stars: number) => {
    setRating(stars)
  }, [])

  const handleSubmitRating = useCallback(async () => {
    if (rating === 0) return

    setLoading(true)
    
    // Simulate API call to submit rating
    setTimeout(() => {
      // Clear all ride-related session storage
      sessionStorage.removeItem('rideDestination')
      sessionStorage.removeItem('rideOrigin')
      sessionStorage.removeItem('tripData')
      sessionStorage.removeItem('rideData')
      sessionStorage.removeItem('completedRide')
      
      setLoading(false)
      
      // Navigate back to dashboard
      navigate('/dashboard')
    }, 1500)
  }, [rating, navigate])

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  const starVariants = {
    empty: { scale: 1, color: '#ffffff40' },
    filled: { scale: 1.1, color: '#fbbf24' },
    hover: { scale: 1.2 }
  }

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-black/40"></div>
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

      <div className="relative z-10 flex flex-col h-full min-h-screen">
        {/* Header */}
        <motion.div
          className="pt-8 pb-6 px-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="text-center">
            <h1 className="text-2xl font-bold text-white">
              MOBI<span className="text-blue-400">DRIVE</span>
            </h1>
            <p className="text-sm text-white/70">Avalie sua corrida</p>
          </div>
        </motion.div>

        {/* Content */}
        <div className="flex-1 px-4 pb-8">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {/* Trip Completion */}
            <motion.div variants={itemVariants} className="text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <CheckCircle className="w-10 h-10 text-white" />
              </motion.div>
              <h2 className="text-2xl font-bold text-white mb-2">Corrida finalizada!</h2>
              <p className="text-white/70">Obrigado por usar o MobiDrive</p>
            </motion.div>

            {/* Trip Summary */}
            {completedRide && (
              <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                <h3 className="text-lg font-bold text-white mb-4">Resumo da Viagem</h3>
                
                <div className="space-y-4">
                  {/* Route */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-white/70 text-sm">Origem</span>
                    </div>
                    <p className="text-white text-sm ml-6">{completedRide.origin?.place_name || 'Sua localização'}</p>
                    
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="text-white/70 text-sm">Destino</span>
                    </div>
                    <p className="text-white text-sm ml-6">{completedRide.destination?.place_name}</p>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 pt-4 border-t border-white/20">
                    <div className="text-center">
                      <Navigation className="w-5 h-5 text-blue-400 mx-auto mb-1" />
                      <p className="text-white/70 text-xs">Distância</p>
                      <p className="text-white font-bold">{completedRide.finalDistance} km</p>
                    </div>
                    <div className="text-center">
                      <Clock className="w-5 h-5 text-blue-400 mx-auto mb-1" />
                      <p className="text-white/70 text-xs">Tempo</p>
                      <p className="text-white font-bold">{completedRide.finalDuration} min</p>
                    </div>
                    <div className="text-center">
                      <DollarSign className="w-5 h-5 text-blue-400 mx-auto mb-1" />
                      <p className="text-white/70 text-xs">Valor</p>
                      <p className="text-white font-bold">R$ {completedRide.finalPrice?.toFixed(2)}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Driver Rating */}
            {completedRide?.driver && (
              <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                <h3 className="text-lg font-bold text-white mb-4">Avalie o motorista</h3>
                
                {/* Driver Info */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-2xl">
                    {completedRide.driver.photo}
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-white">{completedRide.driver.name}</h4>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-white/70">{completedRide.driver.rating}</span>
                    </div>
                    <p className="text-white/60 text-sm">
                      {completedRide.driver.vehicle.color} {completedRide.driver.vehicle.model}
                    </p>
                  </div>
                </div>

                {/* Star Rating */}
                <div className="text-center mb-6">
                  <p className="text-white mb-4">Como foi sua experiência?</p>
                  <div className="flex justify-center space-x-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <motion.button
                        key={star}
                        onClick={() => handleRating(star)}
                        className="focus:outline-none"
                        whileHover="hover"
                        whileTap={{ scale: 0.9 }}
                      >
                        <motion.div
                          variants={starVariants}
                          animate={rating >= star ? "filled" : "empty"}
                          transition={{ duration: 0.2 }}
                        >
                          <Star 
                            className={`w-8 h-8 ${rating >= star ? 'text-yellow-400 fill-current' : 'text-white/40'}`}
                          />
                        </motion.div>
                      </motion.button>
                    ))}
                  </div>
                  {rating > 0 && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-white/70 text-sm mt-2"
                    >
                      {rating === 1 && "Muito ruim"}
                      {rating === 2 && "Ruim"}
                      {rating === 3 && "Regular"}
                      {rating === 4 && "Bom"}
                      {rating === 5 && "Excelente"}
                    </motion.p>
                  )}
                </div>

                {/* Comment */}
                <div className="mb-6">
                  <label className="block text-white mb-2">Comentário (opcional)</label>
                  <textarea
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    placeholder="Conte-nos sobre sua experiência..."
                    className="w-full p-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                    rows={3}
                    maxLength={200}
                  />
                  <p className="text-white/50 text-xs mt-1">{comment.length}/200 caracteres</p>
                </div>
              </motion.div>
            )}

            {/* Submit Button */}
            <motion.button
              onClick={handleSubmitRating}
              disabled={rating === 0 || loading}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"
              whileHover={{ scale: rating > 0 && !loading ? 1.02 : 1 }}
              whileTap={{ scale: rating > 0 && !loading ? 0.98 : 1 }}
              variants={itemVariants}
            >
              {loading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>Enviando avaliação...</span>
                </>
              ) : (
                <>
                  <Send className="w-5 h-5" />
                  <span>Finalizar avaliação</span>
                </>
              )}
            </motion.button>

            {/* Skip Rating */}
            <motion.button
              onClick={() => navigate('/dashboard')}
              className="w-full text-white/60 hover:text-white transition-colors py-2"
              variants={itemVariants}
            >
              Pular avaliação
            </motion.button>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default RatingPage
