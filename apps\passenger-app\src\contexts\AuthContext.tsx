import React, { createContext, useContext, useEffect, useState, useRef } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase, TABLES } from '../lib/supabase'
import { locationService } from '../services/LocationService'

interface Profile {
  id: string
  full_name?: string
  email?: string
  phone?: string
  user_type?: string
  created_at?: string
  updated_at?: string
}

interface AuthState {
  user: User | null
  profile: Profile | null
  loading: boolean
  error: string | null
}

interface AuthContextType extends AuthState {
  signIn: (email: string, password: string) => Promise<{ error?: any }>
  signUp: (email: string, password: string, userData: any) => Promise<{ error?: any }>
  signOut: () => Promise<void>
  updateProfile: (data: Partial<Profile>) => Promise<{ error?: any }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Profile cache to avoid repeated database calls
const profileCache = new Map<string, { profile: Profile | null, timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Debug utilities
const debugAuth = {
  log: (message: string, data?: any) => {
    const timestamp = new Date().toISOString()
    console.log(`[${timestamp}] 🔐 AuthContext: ${message}`, data || '')
  },
  error: (message: string, error?: any) => {
    const timestamp = new Date().toISOString()
    console.error(`[${timestamp}] ❌ AuthContext: ${message}`, error || '')
  },
  warn: (message: string, data?: any) => {
    const timestamp = new Date().toISOString()
    console.warn(`[${timestamp}] ⚠️ AuthContext: ${message}`, data || '')
  },
  performance: {
    start: (label: string) => {
      performance.mark(`auth-${label}-start`)
      debugAuth.log(`Performance: ${label} started`)
    },
    end: (label: string) => {
      performance.mark(`auth-${label}-end`)
      performance.measure(`auth-${label}`, `auth-${label}-start`, `auth-${label}-end`)
      const measure = performance.getEntriesByName(`auth-${label}`)[0]
      debugAuth.log(`Performance: ${label} completed in ${measure.duration.toFixed(2)}ms`)
    }
  }
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<AuthState>({
    user: null,
    profile: null,
    loading: true, // Iniciar com loading true para verificar sessão
    error: null,
  })

  // Debug state changes
  const [debugInfo, setDebugInfo] = useState({
    initCount: 0,
    sessionCheckCount: 0,
    profileFetchCount: 0,
    authStateChangeCount: 0,
    lastUpdate: Date.now()
  })

  // Track realtime subscriptions to prevent memory leaks
  const subscriptionsRef = useRef<Set<any>>(new Set())
  const cleanupTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Cleanup function for subscriptions
  const cleanupSubscriptions = () => {
    subscriptionsRef.current.forEach(subscription => {
      try {
        if (subscription && typeof subscription.unsubscribe === 'function') {
          subscription.unsubscribe()
        }
      } catch (error) {
        console.warn('Erro ao limpar subscription:', error)
      }
    })
    subscriptionsRef.current.clear()
  }

  useEffect(() => {
    let isMounted = true
    let timeoutId: NodeJS.Timeout

    debugAuth.performance.start('initialization')
    setDebugInfo(prev => ({ ...prev, initCount: prev.initCount + 1, lastUpdate: Date.now() }))
    debugAuth.log('Inicializando...', {
      initCount: debugInfo.initCount + 1,
      supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
      hasSupabaseKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY
    })

    // Verificar configuração do Supabase
    if (!import.meta.env.VITE_SUPABASE_URL || !import.meta.env.VITE_SUPABASE_ANON_KEY) {
      debugAuth.warn('Configuração do Supabase incompleta - modo offline', {
        url: import.meta.env.VITE_SUPABASE_URL,
        hasKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY
      })
      if (isMounted) {
        // Permitir que o app funcione sem Supabase
        setState(prev => ({ ...prev, loading: false, error: null }))
        clearTimeout(timeoutId)
      }
      return
    }

    // Timeout de segurança mais agressivo para evitar loading infinito
    timeoutId = setTimeout(() => {
      if (isMounted) {
        debugAuth.warn('Timeout de inicialização - forçando loading: false')
        // Forçar loading: false independente do estado
        setState({
          user: null,
          profile: null,
          loading: false,
          error: null
        })
      }
    }, 3000) // 3 segundos (mais agressivo)

    // Verificar sessão atual
    const initializeAuth = async () => {
      try {
        debugAuth.performance.start('session-check')
        setDebugInfo(prev => ({ ...prev, sessionCheckCount: prev.sessionCheckCount + 1, lastUpdate: Date.now() }))
        debugAuth.log('Verificando sessão atual...', {
          sessionCheckCount: debugInfo.sessionCheckCount + 1,
          currentState: { loading: state.loading, hasUser: !!state.user },
          supabaseConfigured: !!(import.meta.env.VITE_SUPABASE_URL && import.meta.env.VITE_SUPABASE_ANON_KEY)
        })

        const { data: { session }, error } = await supabase.auth.getSession()
        debugAuth.performance.end('session-check')

        debugAuth.log('Resultado da getSession:', {
          hasSession: !!session,
          hasError: !!error,
          errorMessage: error?.message,
          userEmail: session?.user?.email
        })

        if (!isMounted) return

        if (error) {
          debugAuth.error('Erro ao obter sessão', error)
          setState(prev => ({ ...prev, loading: false, error: error.message }))
          clearTimeout(timeoutId)
          return
        }

        debugAuth.log('Resultado da verificação de sessão', {
          hasSession: !!session,
          hasUser: !!session?.user,
          userEmail: session?.user?.email,
          sessionId: session?.access_token?.substring(0, 10) + '...'
        })

        if (session?.user) {
          debugAuth.log('Sessão encontrada, buscando perfil...', { userEmail: session.user.email })
          // Buscar perfil com timeout mais curto
          try {
            await Promise.race([
              fetchUserProfile(session.user),
              new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 2000))
            ])
          } catch (error) {
            debugAuth.warn('Timeout ou erro no perfil, continuando sem perfil')
            setState({
              user: session.user,
              profile: null,
              loading: false,
              error: null
            })
          }
        } else {
          debugAuth.log('Nenhuma sessão encontrada, definindo loading: false')
          setState(prev => ({ ...prev, loading: false }))
        }

        clearTimeout(timeoutId)
      } catch (error: any) {
        if (!isMounted) return
        debugAuth.error('Erro geral na verificação de sessão', error)
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        clearTimeout(timeoutId)
      }
    }

    // Inicializar autenticação
    initializeAuth()

    // Listener para mudanças de autenticação (simplificado)
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!isMounted) return

        setDebugInfo(prev => ({ ...prev, authStateChangeCount: prev.authStateChangeCount + 1, lastUpdate: Date.now() }))
        debugAuth.log('Auth state change', {
          event,
          userEmail: session?.user?.email,
          authStateChangeCount: debugInfo.authStateChangeCount + 1,
          hasSession: !!session,
          currentLoadingState: state.loading
        })

        // Processar apenas eventos específicos para evitar loops
        switch (event) {
          case 'SIGNED_IN':
            if (session?.user) {
              debugAuth.log('Usuário logado via auth state change')
              await fetchUserProfile(session.user)
            }
            break

          case 'SIGNED_OUT':
            debugAuth.log('Usuário deslogado via auth state change')
            // Cleanup subscriptions on logout
            cleanupSubscriptions()
            setState({
              user: null,
              profile: null,
              loading: false,
              error: null,
            })
            break

          case 'TOKEN_REFRESHED':
            debugAuth.log('Token renovado')
            // Não fazer nada, manter estado atual
            break

          default:
            debugAuth.log('Evento ignorado para evitar loops', { event })
            break
        }
      }
    )

    debugAuth.performance.end('initialization')

    // Track subscription for cleanup
    subscriptionsRef.current.add(subscription)

    return () => {
      isMounted = false
      clearTimeout(timeoutId)
      if (cleanupTimeoutRef.current) {
        clearTimeout(cleanupTimeoutRef.current)
      }
      debugAuth.log('Cleanup - removendo listeners')
      cleanupSubscriptions()
    }
  }, [])

  const fetchUserProfile = async (user: User) => {
    try {
      debugAuth.log('🔐 Buscando perfil do usuário (otimizado)')

      // Verificar cache primeiro
      const cached = profileCache.get(user.id)
      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        debugAuth.log('📋 Usando perfil do cache')
        setState({
          user,
          profile: cached.profile,
          loading: false,
          error: null,
        })
        return
      }

      // Se Supabase não estiver configurado, continuar sem perfil
      if (!import.meta.env.VITE_SUPABASE_URL || !import.meta.env.VITE_SUPABASE_ANON_KEY) {
        debugAuth.warn('⚠️ Supabase não configurado - continuando sem perfil')
        setState({
          user,
          profile: null,
          loading: false,
          error: null,
        })
        return
      }

      // Buscar perfil com timeout mais curto (2 segundos) para evitar travamento
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 2000)

      try {
        const { data: profile, error } = await supabase
          .from(TABLES.profiles)
          .select('*')
          .eq('id', user.id)
          .abortSignal(controller.signal)
          .single()

        clearTimeout(timeoutId)

        // Se a tabela não existe ou há erro de permissão, criar perfil local
        if (error && (error.code === 'PGRST301' || error.code === '42P01' || error.message.includes('relation') || error.message.includes('permission'))) {
          debugAuth.warn('⚠️ Tabela profiles não existe ou sem permissão, criando perfil local')
          const localProfile = {
            id: user.id,
            email: user.email,
            full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Usuário',
            user_type: 'passenger',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }

          setState({
            user,
            profile: localProfile,
            loading: false,
            error: null,
          })
          debugAuth.log('✅ Perfil local criado com sucesso')
          return
        }

        if (error && error.code !== 'PGRST116') {
          throw error
        }

        // Se perfil não existe, tentar criar um básico
        if (!profile && error?.code === 'PGRST116') {
          debugAuth.log('📝 Perfil não encontrado, criando perfil básico')
          await createBasicProfile(user)
          return
        }

        // Cache o resultado
        profileCache.set(user.id, { profile, timestamp: Date.now() })

        // Sucesso - perfil encontrado
        const successState = {
          user,
          profile: profile || null,
          loading: false,
          error: null,
        }

        setState(successState)
        debugAuth.log('✅ Perfil carregado:', profile ? 'Encontrado' : 'Não encontrado')

      } catch (profileError: any) {
        clearTimeout(timeoutId)

        // Se der timeout ou erro, continuar sem perfil
        debugAuth.warn('⚠️ Erro/timeout no perfil, continuando sem perfil:', profileError.message)

        const noProfileState = {
          user,
          profile: null,
          loading: false,
          error: null,
        }

        setState(noProfileState)
        debugAuth.log('✅ Login concluído sem perfil')

        // Forçar redirecionamento mesmo sem perfil
        setTimeout(() => {
          if (window.location.pathname === '/' || window.location.pathname === '/login') {
            window.location.href = '/dashboard'
          }
        }, 500)
      }

    } catch (error: any) {
      debugAuth.error('Erro geral no login:', error.message)

      // Sempre continuar com o usuário logado
      const errorState = {
        user,
        profile: null,
        loading: false,
        error: null,
      }

      setState(errorState)
      debugAuth.log('✅ Usuário logado mesmo com erro geral')

      // Forçar redirecionamento mesmo com erro
      setTimeout(() => {
        if (window.location.pathname === '/' || window.location.pathname === '/login') {
          window.location.href = '/dashboard'
        }
      }, 500)
    }
  }

  const createBasicProfile = async (user: User) => {
    try {
      debugAuth.log('📝 Criando perfil básico para usuário')

      const basicProfile = {
        id: user.id,
        email: user.email,
        full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Usuário',
        user_type: 'passenger',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      try {
        const { data: newProfile, error } = await supabase
          .from(TABLES.profiles)
          .insert(basicProfile)
          .select()
          .single()

        if (error) {
          // Se erro de tabela/permissão, usar perfil local
          if (error.code === 'PGRST301' || error.code === '42P01' || error.message.includes('relation') || error.message.includes('permission')) {
            debugAuth.warn('⚠️ Tabela profiles não existe, usando perfil local:', error.message)
            setState({
              user,
              profile: basicProfile,
              loading: false,
              error: null,
            })
            debugAuth.log('✅ Perfil local criado com sucesso')
            return
          }
          throw error
        }

        // Sucesso - perfil criado no Supabase
        const successState = {
          user,
          profile: newProfile,
          loading: false,
          error: null,
        }

        setState(successState)
        debugAuth.log('✅ Perfil básico criado no Supabase com sucesso')

      } catch (supabaseError: any) {
        debugAuth.warn('⚠️ Erro ao criar perfil no Supabase, usando perfil local:', supabaseError.message)

        // Fallback para perfil local
        setState({
          user,
          profile: basicProfile,
          loading: false,
          error: null,
        })
        debugAuth.log('✅ Perfil local criado como fallback')
      }

    } catch (error: any) {
      debugAuth.warn('⚠️ Erro geral ao criar perfil:', error.message)

      // Último fallback - perfil mínimo
      const minimalProfile = {
        id: user.id,
        email: user.email,
        full_name: user.email?.split('@')[0] || 'Usuário',
        user_type: 'passenger'
      }

      setState({
        user,
        profile: minimalProfile,
        loading: false,
        error: null,
      })
      debugAuth.log('✅ Perfil mínimo criado como último fallback')
    }
  }

  const createUserProfile = async (user: User) => {
    try {
      debugAuth.log('Criando perfil para novo usuário', { userEmail: user.email })

      const { data: newProfile, error } = await supabase
        .from(TABLES.profiles)
        .insert({
          id: user.id,
          email: user.email,
          full_name: user.user_metadata?.full_name || user.email?.split('@')[0],
          user_type: 'passenger',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        debugAuth.error('Erro ao criar perfil', error)
        const errorState = {
          user,
          profile: null,
          loading: false,
          error: error.message,
        }
        setState(errorState)
        debugAuth.log('Estado definido após erro na criação - loading: false', errorState)
      } else {
        debugAuth.log('Perfil criado com sucesso', newProfile)
        const successState = {
          user,
          profile: newProfile,
          loading: false,
          error: null,
        }
        setState(successState)
        debugAuth.log('Estado definido após criação bem-sucedida - loading: false', successState)
      }
    } catch (error: any) {
      debugAuth.error('Erro geral ao criar perfil', error)
      const errorState = {
        user,
        profile: null,
        loading: false,
        error: error.message,
      }
      setState(errorState)
      debugAuth.log('Estado definido após erro geral na criação - loading: false', errorState)
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      console.log('🔐 Tentando fazer login com:', email)
      setState(prev => ({ ...prev, loading: true, error: null }))

      // Verificar se Supabase está configurado
      if (!import.meta.env.VITE_SUPABASE_URL || !import.meta.env.VITE_SUPABASE_ANON_KEY) {
        console.warn('⚠️ Supabase não configurado - simulando login')
        // Simular login para demonstração
        const mockUser = {
          id: 'demo-user',
          email: email,
          user_metadata: { full_name: 'Usuário Demo' }
        } as User

        setState({
          user: mockUser,
          profile: {
            id: 'demo-user',
            email: email,
            full_name: 'Usuário Demo',
            user_type: 'passenger'
          },
          loading: false,
          error: null
        })

        setTimeout(() => {
          window.location.href = '/dashboard'
        }, 100)
        return {}
      }

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error('❌ Erro no login:', error)
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        return { error }
      }

      if (data.user) {
        console.log('✅ Login bem-sucedido!')
        await fetchUserProfile(data.user)
        // Redirecionar após login bem-sucedido
        setTimeout(() => {
          window.location.href = '/dashboard'
        }, 100)
      }

      return {}
    } catch (error: any) {
      console.error('❌ Erro no login:', error)
      setState(prev => ({ ...prev, loading: false, error: error.message }))
      return { error }
    }
  }

  const signUp = async (email: string, password: string, userData: any) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))

      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.full_name,
            user_type: 'passenger',
          },
        },
      })

      if (error) {
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        return { error }
      }

      return {}
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false, error: error.message }))
      return { error }
    }
  }

  const signOut = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }))

      // Location tracking desabilitado - nada para parar
      debugAuth.log('🚪 Logout - location tracking não estava ativo')

      await supabase.auth.signOut()
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false, error: error.message }))
    }
  }

  const updateProfile = async (data: Partial<Profile>) => {
    try {
      if (!state.user) return { error: { message: 'Usuário não autenticado' } }

      setState(prev => ({ ...prev, loading: true, error: null }))

      const { data: updatedProfile, error } = await supabase
        .from(TABLES.profiles)
        .update(data)
        .eq('id', state.user.id)
        .select()
        .single()

      if (error) {
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        return { error }
      }

      setState(prev => ({
        ...prev,
        profile: updatedProfile,
        loading: false,
      }))

      return {}
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false, error: error.message }))
      return { error }
    }
  }

  return (
    <AuthContext.Provider
      value={{
        ...state,
        signIn,
        signUp,
        signOut,
        updateProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider')
  }
  return context
}
