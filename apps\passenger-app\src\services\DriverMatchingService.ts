import { supabase } from '../lib/supabase'
import { DriverLocation } from './RealtimeLocationService'

export interface RideRequest {
  id: string
  passenger_id: string
  pickup_lat: number
  pickup_lng: number
  destination_lat: number
  destination_lng: number
  vehicle_type: string
  estimated_price: number
  estimated_distance: number
  estimated_duration: number
  created_at: string
  expires_at: string
}

export interface MatchingCriteria {
  maxDistance: number // km
  maxETA: number // minutes
  vehicleTypes: string[]
  minRating: number
  preferredDrivers?: string[] // driver IDs
}

export interface DriverScore {
  driver: DriverLocation
  score: number
  eta: number
  distance: number
  factors: {
    proximity: number
    rating: number
    availability: number
    vehicleMatch: number
    efficiency: number
  }
}

class DriverMatchingService {
  private readonly EARTH_RADIUS_KM = 6371
  private readonly DEFAULT_SPEED_KMH = 30 // Average city speed

  /**
   * Find and rank the best drivers for a ride request
   */
  async findBestDrivers(
    pickupLat: number,
    pickupLng: number,
    criteria: MatchingCriteria,
    availableDrivers: DriverLocation[]
  ): Promise<DriverScore[]> {
    console.log(`🎯 Finding best drivers for pickup at ${pickupLat}, ${pickupLng}`)
    console.log(`📋 Criteria:`, criteria)

    // Filter drivers based on basic criteria
    const eligibleDrivers = this.filterEligibleDrivers(
      availableDrivers,
      pickupLat,
      pickupLng,
      criteria
    )

    console.log(`✅ Found ${eligibleDrivers.length} eligible drivers`)

    // Score and rank drivers
    const scoredDrivers = await Promise.all(
      eligibleDrivers.map(driver => this.scoreDriver(driver, pickupLat, pickupLng, criteria))
    )

    // Sort by score (highest first)
    const rankedDrivers = scoredDrivers.sort((a, b) => b.score - a.score)

    console.log(`🏆 Top 3 drivers:`, rankedDrivers.slice(0, 3).map(d => ({
      name: d.driver.driver_name,
      score: d.score.toFixed(2),
      eta: `${d.eta}min`,
      distance: `${d.distance.toFixed(1)}km`
    })))

    return rankedDrivers
  }

  /**
   * Filter drivers based on basic eligibility criteria
   */
  private filterEligibleDrivers(
    drivers: DriverLocation[],
    pickupLat: number,
    pickupLng: number,
    criteria: MatchingCriteria
  ): DriverLocation[] {
    return drivers.filter(driver => {
      // Must be available and active
      if (!driver.is_available || !driver.is_active) return false

      // Check vehicle type match
      if (!criteria.vehicleTypes.includes(driver.vehicle_type)) return false

      // Check minimum rating
      if (driver.driver_rating < criteria.minRating) return false

      // Check distance
      const distance = this.calculateDistance(
        pickupLat, pickupLng,
        driver.latitude, driver.longitude
      )
      if (distance > criteria.maxDistance) return false

      // Check ETA
      const eta = this.estimateETA(distance, driver.speed || 0)
      if (eta > criteria.maxETA) return false

      // Check if location is recent (within 2 minutes)
      const lastUpdate = new Date(driver.updated_at)
      const now = new Date()
      const minutesSinceUpdate = (now.getTime() - lastUpdate.getTime()) / (1000 * 60)
      if (minutesSinceUpdate > 2) return false

      return true
    })
  }

  /**
   * Score a driver based on multiple factors
   */
  private async scoreDriver(
    driver: DriverLocation,
    pickupLat: number,
    pickupLng: number,
    criteria: MatchingCriteria
  ): Promise<DriverScore> {
    const distance = this.calculateDistance(
      pickupLat, pickupLng,
      driver.latitude, driver.longitude
    )
    
    const eta = this.estimateETA(distance, driver.speed || 0)

    // Calculate individual factor scores (0-1)
    const factors = {
      proximity: this.calculateProximityScore(distance, criteria.maxDistance),
      rating: this.calculateRatingScore(driver.driver_rating),
      availability: this.calculateAvailabilityScore(driver),
      vehicleMatch: this.calculateVehicleMatchScore(driver.vehicle_type, criteria.vehicleTypes),
      efficiency: await this.calculateEfficiencyScore(driver.user_id)
    }

    // Weighted total score
    const weights = {
      proximity: 0.35,    // 35% - Most important for user experience
      rating: 0.25,       // 25% - Quality of service
      availability: 0.15, // 15% - Current status
      vehicleMatch: 0.15, // 15% - Vehicle preference match
      efficiency: 0.10    // 10% - Driver performance history
    }

    const score = Object.entries(factors).reduce((total, [factor, value]) => {
      return total + (value * weights[factor as keyof typeof weights])
    }, 0) * 100 // Scale to 0-100

    return {
      driver,
      score,
      eta,
      distance,
      factors
    }
  }

  /**
   * Calculate proximity score (closer = higher score)
   */
  private calculateProximityScore(distance: number, maxDistance: number): number {
    return Math.max(0, 1 - (distance / maxDistance))
  }

  /**
   * Calculate rating score (higher rating = higher score)
   */
  private calculateRatingScore(rating: number): number {
    // Normalize rating from 1-5 scale to 0-1 scale
    return Math.max(0, (rating - 1) / 4)
  }

  /**
   * Calculate availability score based on driver status
   */
  private calculateAvailabilityScore(driver: DriverLocation): number {
    let score = 1.0

    // Reduce score if driver is moving fast (might be in a ride)
    if (driver.speed && driver.speed > 40) {
      score *= 0.7
    }

    // Boost score for stationary drivers (likely waiting for rides)
    if (driver.speed && driver.speed < 5) {
      score *= 1.2
    }

    // Check how recent the location update is
    const lastUpdate = new Date(driver.updated_at)
    const now = new Date()
    const minutesSinceUpdate = (now.getTime() - lastUpdate.getTime()) / (1000 * 60)
    
    if (minutesSinceUpdate < 0.5) {
      score *= 1.1 // Very recent update
    } else if (minutesSinceUpdate > 1) {
      score *= 0.9 // Older update
    }

    return Math.min(1, score)
  }

  /**
   * Calculate vehicle type match score
   */
  private calculateVehicleMatchScore(driverVehicleType: string, preferredTypes: string[]): number {
    const typeIndex = preferredTypes.indexOf(driverVehicleType)
    if (typeIndex === -1) return 0
    
    // Higher score for preferred vehicle types (first in list gets highest score)
    return 1 - (typeIndex / preferredTypes.length)
  }

  /**
   * Calculate efficiency score based on driver's historical performance
   */
  private async calculateEfficiencyScore(driverId: string): Promise<number> {
    try {
      // Get driver's recent performance metrics
      const { data: metrics, error } = await supabase
        .from('ride_requests')
        .select('status, created_at, accepted_at, completed_at')
        .eq('driver_id', driverId)
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
        .limit(20)

      if (error || !metrics || metrics.length === 0) {
        return 0.5 // Default score for new drivers
      }

      let efficiencyScore = 0.5
      const completedRides = metrics.filter(m => m.status === 'completed')
      const acceptanceRate = completedRides.length / metrics.length

      // Boost score for high acceptance rate
      efficiencyScore += acceptanceRate * 0.3

      // Boost score for quick acceptance times
      const avgAcceptanceTime = metrics
        .filter(m => m.accepted_at)
        .reduce((sum, m) => {
          const acceptTime = new Date(m.accepted_at!).getTime() - new Date(m.created_at).getTime()
          return sum + acceptTime
        }, 0) / metrics.length

      if (avgAcceptanceTime < 30000) { // Less than 30 seconds
        efficiencyScore += 0.2
      }

      return Math.min(1, efficiencyScore)
    } catch (error) {
      console.error('Error calculating efficiency score:', error)
      return 0.5
    }
  }

  /**
   * Send ride request to selected drivers
   */
  async sendRideRequestToDrivers(
    rideRequest: RideRequest,
    selectedDrivers: DriverScore[],
    maxDrivers: number = 3
  ): Promise<string[]> {
    const driversToNotify = selectedDrivers.slice(0, maxDrivers)
    const notificationPromises: Promise<any>[] = []

    console.log(`📢 Sending ride request to ${driversToNotify.length} drivers`)

    for (const driverScore of driversToNotify) {
      const driver = driverScore.driver

      // Create notification payload
      const notificationPayload = {
        type: 'ride_request',
        ride_id: rideRequest.id,
        passenger_id: rideRequest.passenger_id,
        pickup_address: `${rideRequest.pickup_lat}, ${rideRequest.pickup_lng}`,
        destination_address: `${rideRequest.destination_lat}, ${rideRequest.destination_lng}`,
        pickup_lat: rideRequest.pickup_lat,
        pickup_lng: rideRequest.pickup_lng,
        destination_lat: rideRequest.destination_lat,
        destination_lng: rideRequest.destination_lng,
        estimated_price: rideRequest.estimated_price,
        estimated_distance: rideRequest.estimated_distance,
        estimated_duration: rideRequest.estimated_duration,
        vehicle_type: rideRequest.vehicle_type,
        eta_to_pickup: driverScore.eta,
        distance_to_pickup: driverScore.distance,
        driver_score: driverScore.score,
        expires_at: rideRequest.expires_at,
        created_at: rideRequest.created_at
      }

      // Send via Supabase Realtime
      const promise = supabase.channel('driver-notifications')
        .send({
          type: 'broadcast',
          event: 'ride_request',
          payload: {
            ...notificationPayload,
            target_driver_id: driver.user_id
          }
        })

      notificationPromises.push(promise)

      // Also insert into notifications table for persistence
      const dbPromise = supabase
        .from('driver_notifications')
        .insert({
          driver_id: driver.user_id,
          ride_id: rideRequest.id,
          type: 'ride_request',
          payload: notificationPayload,
          expires_at: rideRequest.expires_at
        })

      notificationPromises.push(dbPromise)
    }

    try {
      await Promise.all(notificationPromises)
      console.log('✅ All ride requests sent successfully')
      return driversToNotify.map(d => d.driver.user_id)
    } catch (error) {
      console.error('❌ Error sending ride requests:', error)
      throw error
    }
  }

  /**
   * Calculate distance between two points using Haversine formula
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const dLat = this.toRadians(lat2 - lat1)
    const dLon = this.toRadians(lon2 - lon1)
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return this.EARTH_RADIUS_KM * c
  }

  /**
   * Estimate ETA based on distance and current speed
   */
  private estimateETA(distanceKm: number, currentSpeedKmh: number): number {
    const effectiveSpeed = currentSpeedKmh > 5 ? currentSpeedKmh : this.DEFAULT_SPEED_KMH
    const timeHours = distanceKm / effectiveSpeed
    const timeMinutes = timeHours * 60
    
    // Add buffer time for traffic and pickup
    const bufferMinutes = Math.min(5, distanceKm * 0.5)
    
    return Math.round(timeMinutes + bufferMinutes)
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  /**
   * Get default matching criteria based on vehicle type
   */
  getDefaultCriteria(vehicleType: string): MatchingCriteria {
    const baseCriteria = {
      maxDistance: 10, // km
      maxETA: 15, // minutes
      minRating: 4.0,
      vehicleTypes: [vehicleType]
    }

    // Adjust criteria based on vehicle type
    switch (vehicleType) {
      case 'moto':
        return {
          ...baseCriteria,
          maxDistance: 8,
          maxETA: 10,
          vehicleTypes: ['moto']
        }
      case 'premium':
        return {
          ...baseCriteria,
          maxDistance: 15,
          maxETA: 20,
          minRating: 4.5,
          vehicleTypes: ['premium', 'comfort']
        }
      case 'comfort':
        return {
          ...baseCriteria,
          maxDistance: 12,
          maxETA: 18,
          vehicleTypes: ['comfort', 'economy']
        }
      default: // economy
        return {
          ...baseCriteria,
          vehicleTypes: ['economy', 'comfort']
        }
    }
  }
}

export const driverMatchingService = new DriverMatchingService()
export default driverMatchingService
