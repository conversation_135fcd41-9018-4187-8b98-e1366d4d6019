import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Shield, 
  Users, 
  Phone, 
  MapPin, 
  AlertTriangle, 
  CheckCircle,
  Settings,
  Share2,
  Mic,
  Camera,
  Clock,
  Navigation
} from 'lucide-react'
import { safetyService } from '../services/SafetyService'
import { useAuth } from '../contexts/AuthContextSimple'

interface SafetyDashboardProps {
  isVisible: boolean
  onClose: () => void
  currentRideId?: string
}

const SafetyDashboard: React.FC<SafetyDashboardProps> = ({
  isVisible,
  onClose,
  currentRideId
}) => {
  const { user } = useAuth()
  const [safetyStatus, setSafetyStatus] = useState<any>(null)
  const [emergencyContacts, setEmergencyContacts] = useState<any[]>([])
  const [isEmergencyMode, setIsEmergencyMode] = useState(false)
  const [lastCheckIn, setLastCheckIn] = useState<Date | null>(null)
  const [tripSharing, setTripSharing] = useState<any>(null)

  useEffect(() => {
    if (isVisible && user) {
      loadSafetyData()
    }
  }, [isVisible, user])

  const loadSafetyData = async () => {
    try {
      if (!user) return

      await safetyService.initialize(user.id)
      const status = safetyService.getSafetyStatus()
      setSafetyStatus(status)

      // Load emergency contacts
      // In a real implementation, this would come from the service
      setEmergencyContacts([
        { id: '1', name: 'Maria Silva', phone: '+55 11 99999-1234', relationship: 'Mãe', is_primary: true },
        { id: '2', name: 'João Santos', phone: '+55 11 99999-5678', relationship: 'Irmão', is_primary: false }
      ])

    } catch (error) {
      console.error('Error loading safety data:', error)
    }
  }

  const handleEmergencyActivation = async () => {
    try {
      setIsEmergencyMode(true)
      await safetyService.triggerEmergency('sos', 'Emergência ativada pelo usuário')
      
      // Show emergency confirmation
      setTimeout(() => {
        setIsEmergencyMode(false)
      }, 5000)

    } catch (error) {
      console.error('Error activating emergency:', error)
      setIsEmergencyMode(false)
    }
  }

  const handleShareTrip = async () => {
    if (!currentRideId) return

    try {
      const contactIds = emergencyContacts.filter(c => c.is_primary).map(c => c.id)
      const sharing = await safetyService.shareTripWithContacts(currentRideId, contactIds)
      setTripSharing(sharing)
    } catch (error) {
      console.error('Error sharing trip:', error)
    }
  }

  const handleCheckIn = () => {
    setLastCheckIn(new Date())
    // In a real implementation, this would update the backend
  }

  if (!isVisible) return null

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-3xl max-w-md w-full max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 rounded-t-3xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Shield className="w-8 h-8 text-white" />
              <div>
                <h2 className="text-xl font-bold text-white">Central de Segurança</h2>
                <p className="text-blue-100 text-sm">Sua proteção em primeiro lugar</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center text-white hover:bg-white/30 transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Emergency Mode Overlay */}
        <AnimatePresence>
          {isEmergencyMode && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute inset-0 bg-red-600 rounded-3xl flex items-center justify-center z-10"
            >
              <div className="text-center text-white p-8">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ repeat: Infinity, duration: 1 }}
                  className="text-6xl mb-4"
                >
                  🚨
                </motion.div>
                <h3 className="text-2xl font-bold mb-2">EMERGÊNCIA ATIVADA</h3>
                <p className="text-red-100">Autoridades foram notificadas</p>
                <p className="text-red-100">Seus contatos receberam sua localização</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <div className="p-6 space-y-6">
          {/* Status Cards */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-green-50 rounded-2xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-green-800 font-medium text-sm">Monitoramento</span>
              </div>
              <p className="text-green-600 text-xs">
                {safetyStatus?.isMonitoring ? 'Ativo' : 'Inativo'}
              </p>
            </div>

            <div className="bg-blue-50 rounded-2xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Users className="w-5 h-5 text-blue-600" />
                <span className="text-blue-800 font-medium text-sm">Contatos</span>
              </div>
              <p className="text-blue-600 text-xs">
                {emergencyContacts.length} configurados
              </p>
            </div>
          </div>

          {/* Emergency Button */}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleEmergencyActivation}
            disabled={isEmergencyMode}
            className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white rounded-2xl p-6 font-bold text-lg shadow-lg disabled:opacity-50"
          >
            <div className="flex items-center justify-center space-x-3">
              <AlertTriangle className="w-6 h-6" />
              <span>BOTÃO DE EMERGÊNCIA</span>
            </div>
            <p className="text-red-100 text-sm mt-2">
              Toque para ativar protocolo de emergência
            </p>
          </motion.button>

          {/* Quick Actions */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-800">Ações Rápidas</h3>
            
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={handleShareTrip}
                disabled={!currentRideId}
                className="bg-blue-50 hover:bg-blue-100 rounded-xl p-4 flex flex-col items-center space-y-2 transition-colors disabled:opacity-50"
              >
                <Share2 className="w-6 h-6 text-blue-600" />
                <span className="text-blue-800 text-sm font-medium">Compartilhar Viagem</span>
              </button>

              <button
                onClick={handleCheckIn}
                className="bg-green-50 hover:bg-green-100 rounded-xl p-4 flex flex-col items-center space-y-2 transition-colors"
              >
                <CheckCircle className="w-6 h-6 text-green-600" />
                <span className="text-green-800 text-sm font-medium">Check-in</span>
              </button>
            </div>

            {lastCheckIn && (
              <div className="bg-green-50 rounded-xl p-3">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-green-600" />
                  <span className="text-green-800 text-sm">
                    Último check-in: {lastCheckIn.toLocaleTimeString()}
                  </span>
                </div>
              </div>
            )}

            {tripSharing && (
              <div className="bg-blue-50 rounded-xl p-3">
                <div className="flex items-center space-x-2">
                  <Share2 className="w-4 h-4 text-blue-600" />
                  <span className="text-blue-800 text-sm">
                    Viagem compartilhada com {tripSharing.shared_with.length} contatos
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Emergency Contacts */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-800">Contatos de Emergência</h3>
              <button className="text-blue-600 text-sm font-medium">
                Gerenciar
              </button>
            </div>

            <div className="space-y-2">
              {emergencyContacts.map((contact) => (
                <div key={contact.id} className="bg-gray-50 rounded-xl p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-800">{contact.name}</p>
                        <p className="text-gray-500 text-sm">{contact.relationship}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {contact.is_primary && (
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                          Principal
                        </span>
                      )}
                      <button className="text-green-600">
                        <Phone className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Safety Features */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-800">Recursos de Segurança</h3>
            
            <div className="space-y-2">
              {[
                { icon: MapPin, label: 'Monitoramento de Rota', enabled: true },
                { icon: Navigation, label: 'Alertas de Velocidade', enabled: true },
                { icon: Clock, label: 'Check-ins Automáticos', enabled: true },
                { icon: Mic, label: 'Gravação de Emergência', enabled: false },
                { icon: Camera, label: 'Verificação por Foto', enabled: true }
              ].map((feature, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 rounded-xl p-3">
                  <div className="flex items-center space-x-3">
                    <feature.icon className={`w-5 h-5 ${feature.enabled ? 'text-green-600' : 'text-gray-400'}`} />
                    <span className="text-gray-800 text-sm">{feature.label}</span>
                  </div>
                  <div className={`w-10 h-6 rounded-full ${feature.enabled ? 'bg-green-500' : 'bg-gray-300'} relative transition-colors`}>
                    <div className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${feature.enabled ? 'translate-x-5' : 'translate-x-1'}`} />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Settings */}
          <button className="w-full bg-gray-100 hover:bg-gray-200 rounded-xl p-4 flex items-center justify-center space-x-2 transition-colors">
            <Settings className="w-5 h-5 text-gray-600" />
            <span className="text-gray-800 font-medium">Configurações de Segurança</span>
          </button>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default SafetyDashboard
