import { supabase } from '../lib/supabase'
import { analyticsService } from './AnalyticsService'
import { notificationService } from './NotificationService'

export interface UserLevel {
  level: number
  name: string
  minPoints: number
  maxPoints: number
  benefits: string[]
  badge: string
  color: string
}

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'rides' | 'social' | 'eco' | 'safety' | 'loyalty'
  points: number
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  requirements: {
    type: string
    value: number
    timeframe?: string
  }
  unlocked_at?: string
}

export interface UserRewards {
  user_id: string
  total_points: number
  current_level: number
  lifetime_rides: number
  streak_days: number
  achievements: string[]
  referrals_count: number
  eco_score: number
  safety_score: number
  created_at: string
  updated_at: string
}

export interface Reward {
  id: string
  name: string
  description: string
  type: 'discount' | 'free_ride' | 'upgrade' | 'cashback' | 'merchandise'
  value: number
  cost_points: number
  validity_days: number
  max_uses: number
  category: string
  image_url?: string
  terms: string
  is_active: boolean
}

export interface UserReward {
  id: string
  user_id: string
  reward_id: string
  redeemed_at: string
  expires_at: string
  used_at?: string
  ride_id?: string
  status: 'active' | 'used' | 'expired'
}

class RewardsService {
  private readonly LEVELS: UserLevel[] = [
    { level: 1, name: 'Novato', minPoints: 0, maxPoints: 99, benefits: ['Suporte básico'], badge: '🌱', color: '#10B981' },
    { level: 2, name: 'Explorador', minPoints: 100, maxPoints: 299, benefits: ['5% desconto', 'Suporte prioritário'], badge: '🗺️', color: '#3B82F6' },
    { level: 3, name: 'Viajante', minPoints: 300, maxPoints: 599, benefits: ['10% desconto', 'Upgrade gratuito'], badge: '✈️', color: '#8B5CF6' },
    { level: 4, name: 'Aventureiro', minPoints: 600, maxPoints: 999, benefits: ['15% desconto', 'Corridas grátis mensais'], badge: '🏔️', color: '#F59E0B' },
    { level: 5, name: 'Lenda', minPoints: 1000, maxPoints: Infinity, benefits: ['20% desconto', 'Acesso VIP', 'Concierge'], badge: '👑', color: '#EF4444' }
  ]

  private readonly ACHIEVEMENTS: Achievement[] = [
    {
      id: 'first_ride',
      name: 'Primeira Viagem',
      description: 'Complete sua primeira corrida',
      icon: '🚗',
      category: 'rides',
      points: 50,
      rarity: 'common',
      requirements: { type: 'rides_completed', value: 1 }
    },
    {
      id: 'frequent_rider',
      name: 'Passageiro Frequente',
      description: 'Complete 50 corridas',
      icon: '🎯',
      category: 'rides',
      points: 200,
      rarity: 'rare',
      requirements: { type: 'rides_completed', value: 50 }
    },
    {
      id: 'night_owl',
      name: 'Coruja Noturna',
      description: 'Complete 10 corridas entre 22h e 6h',
      icon: '🦉',
      category: 'rides',
      points: 150,
      rarity: 'rare',
      requirements: { type: 'night_rides', value: 10 }
    },
    {
      id: 'eco_warrior',
      name: 'Guerreiro Ecológico',
      description: 'Use apenas veículos elétricos por 1 mês',
      icon: '🌱',
      category: 'eco',
      points: 300,
      rarity: 'epic',
      requirements: { type: 'eco_rides', value: 20, timeframe: '30d' }
    },
    {
      id: 'safety_first',
      name: 'Segurança em Primeiro',
      description: 'Mantenha 5.0 de rating por 25 corridas',
      icon: '🛡️',
      category: 'safety',
      points: 250,
      rarity: 'epic',
      requirements: { type: 'perfect_rating', value: 25 }
    },
    {
      id: 'social_butterfly',
      name: 'Borboleta Social',
      description: 'Refira 10 amigos que completem pelo menos 1 corrida',
      icon: '🦋',
      category: 'social',
      points: 500,
      rarity: 'legendary',
      requirements: { type: 'successful_referrals', value: 10 }
    },
    {
      id: 'streak_master',
      name: 'Mestre da Sequência',
      description: 'Use o app por 30 dias consecutivos',
      icon: '🔥',
      category: 'loyalty',
      points: 400,
      rarity: 'epic',
      requirements: { type: 'daily_streak', value: 30 }
    }
  ]

  /**
   * Get user rewards data
   */
  async getUserRewards(userId: string): Promise<UserRewards> {
    try {
      const { data, error } = await supabase
        .from('user_rewards')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') {
        throw error
      }

      if (!data) {
        // Create initial rewards record
        return await this.initializeUserRewards(userId)
      }

      return data as UserRewards
    } catch (error) {
      console.error('❌ Error getting user rewards:', error)
      throw error
    }
  }

  /**
   * Award points for various actions
   */
  async awardPoints(
    userId: string, 
    points: number, 
    reason: string, 
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const userRewards = await this.getUserRewards(userId)
      const newTotalPoints = userRewards.total_points + points
      const oldLevel = userRewards.current_level
      const newLevel = this.calculateLevel(newTotalPoints)

      // Update user rewards
      const { error } = await supabase
        .from('user_rewards')
        .update({
          total_points: newTotalPoints,
          current_level: newLevel,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (error) throw error

      // Log points transaction
      await supabase
        .from('points_transactions')
        .insert({
          user_id: userId,
          points,
          reason,
          metadata,
          balance_after: newTotalPoints,
          created_at: new Date().toISOString()
        })

      // Check for level up
      if (newLevel > oldLevel) {
        await this.handleLevelUp(userId, newLevel)
      }

      // Check for new achievements
      await this.checkAchievements(userId, reason, metadata)

      console.log(`🎁 Awarded ${points} points to user ${userId} for: ${reason}`)

    } catch (error) {
      console.error('❌ Error awarding points:', error)
      throw error
    }
  }

  /**
   * Check and unlock achievements
   */
  async checkAchievements(
    userId: string, 
    action: string, 
    metadata?: Record<string, any>
  ): Promise<Achievement[]> {
    try {
      const userRewards = await this.getUserRewards(userId)
      const unlockedAchievements: Achievement[] = []

      for (const achievement of this.ACHIEVEMENTS) {
        // Skip if already unlocked
        if (userRewards.achievements.includes(achievement.id)) {
          continue
        }

        // Check if requirements are met
        const isMet = await this.checkAchievementRequirements(
          userId, 
          achievement, 
          userRewards, 
          action, 
          metadata
        )

        if (isMet) {
          await this.unlockAchievement(userId, achievement)
          unlockedAchievements.push(achievement)
        }
      }

      return unlockedAchievements
    } catch (error) {
      console.error('❌ Error checking achievements:', error)
      return []
    }
  }

  /**
   * Redeem reward with points
   */
  async redeemReward(userId: string, rewardId: string): Promise<UserReward> {
    try {
      const userRewards = await this.getUserRewards(userId)
      
      // Get reward details
      const { data: reward, error: rewardError } = await supabase
        .from('rewards')
        .select('*')
        .eq('id', rewardId)
        .eq('is_active', true)
        .single()

      if (rewardError || !reward) {
        throw new Error('Recompensa não encontrada ou inativa')
      }

      // Check if user has enough points
      if (userRewards.total_points < reward.cost_points) {
        throw new Error('Pontos insuficientes')
      }

      // Deduct points
      await this.awardPoints(userId, -reward.cost_points, `Resgate: ${reward.name}`)

      // Create user reward
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + reward.validity_days)

      const { data: userReward, error: userRewardError } = await supabase
        .from('user_rewards_redeemed')
        .insert({
          user_id: userId,
          reward_id: rewardId,
          redeemed_at: new Date().toISOString(),
          expires_at: expiresAt.toISOString(),
          status: 'active'
        })
        .select()
        .single()

      if (userRewardError) throw userRewardError

      // Send notification
      notificationService.showNotification({
        title: '🎁 Recompensa Resgatada!',
        message: `Você resgatou: ${reward.name}`,
        type: 'success',
        priority: 'normal',
        sound: true
      })

      // Track redemption
      analyticsService.track('reward_redeemed', {
        reward_id: rewardId,
        reward_name: reward.name,
        points_cost: reward.cost_points,
        user_level: userRewards.current_level
      })

      return userReward as UserReward

    } catch (error) {
      console.error('❌ Error redeeming reward:', error)
      throw error
    }
  }

  /**
   * Get available rewards for user level
   */
  async getAvailableRewards(userId: string): Promise<Reward[]> {
    try {
      const userRewards = await this.getUserRewards(userId)
      
      const { data: rewards, error } = await supabase
        .from('rewards')
        .select('*')
        .eq('is_active', true)
        .lte('min_level', userRewards.current_level)
        .order('cost_points', { ascending: true })

      if (error) throw error

      return rewards as Reward[]
    } catch (error) {
      console.error('❌ Error getting available rewards:', error)
      return []
    }
  }

  /**
   * Get user's redeemed rewards
   */
  async getUserRedeemedRewards(userId: string): Promise<UserReward[]> {
    try {
      const { data, error } = await supabase
        .from('user_rewards_redeemed')
        .select(`
          *,
          rewards (
            name,
            description,
            type,
            value,
            image_url
          )
        `)
        .eq('user_id', userId)
        .order('redeemed_at', { ascending: false })

      if (error) throw error

      return data as UserReward[]
    } catch (error) {
      console.error('❌ Error getting redeemed rewards:', error)
      return []
    }
  }

  /**
   * Get leaderboard
   */
  async getLeaderboard(period: 'week' | 'month' | 'all' = 'month'): Promise<any[]> {
    try {
      let query = supabase
        .from('user_rewards')
        .select(`
          user_id,
          total_points,
          current_level,
          profiles (
            full_name,
            avatar_url
          )
        `)
        .order('total_points', { ascending: false })
        .limit(50)

      if (period !== 'all') {
        const startDate = new Date()
        if (period === 'week') {
          startDate.setDate(startDate.getDate() - 7)
        } else {
          startDate.setMonth(startDate.getMonth() - 1)
        }
        
        // For period-based leaderboard, we'd need a separate table
        // For now, return all-time leaderboard
      }

      const { data, error } = await query

      if (error) throw error

      return data || []
    } catch (error) {
      console.error('❌ Error getting leaderboard:', error)
      return []
    }
  }

  /**
   * Private helper methods
   */
  private async initializeUserRewards(userId: string): Promise<UserRewards> {
    const initialRewards: Omit<UserRewards, 'created_at' | 'updated_at'> = {
      user_id: userId,
      total_points: 0,
      current_level: 1,
      lifetime_rides: 0,
      streak_days: 0,
      achievements: [],
      referrals_count: 0,
      eco_score: 100,
      safety_score: 100
    }

    const { data, error } = await supabase
      .from('user_rewards')
      .insert(initialRewards)
      .select()
      .single()

    if (error) throw error

    return data as UserRewards
  }

  private calculateLevel(points: number): number {
    for (let i = this.LEVELS.length - 1; i >= 0; i--) {
      if (points >= this.LEVELS[i].minPoints) {
        return this.LEVELS[i].level
      }
    }
    return 1
  }

  private async handleLevelUp(userId: string, newLevel: number): Promise<void> {
    const level = this.LEVELS.find(l => l.level === newLevel)
    if (!level) return

    // Award level up bonus
    const bonusPoints = newLevel * 50
    await supabase
      .from('points_transactions')
      .insert({
        user_id: userId,
        points: bonusPoints,
        reason: `Level Up: ${level.name}`,
        created_at: new Date().toISOString()
      })

    // Send notification
    notificationService.showNotification({
      title: `🎉 Level Up! ${level.badge}`,
      message: `Parabéns! Você alcançou o nível ${level.name}`,
      type: 'success',
      priority: 'high',
      sound: true,
      vibrate: true
    })

    // Track level up
    analyticsService.track('level_up', {
      new_level: newLevel,
      level_name: level.name,
      bonus_points: bonusPoints
    })
  }

  private async checkAchievementRequirements(
    userId: string,
    achievement: Achievement,
    userRewards: UserRewards,
    action: string,
    metadata?: Record<string, any>
  ): Promise<boolean> {
    const req = achievement.requirements

    switch (req.type) {
      case 'rides_completed':
        return userRewards.lifetime_rides >= req.value

      case 'night_rides':
        // Would need to query ride history
        return false // Placeholder

      case 'eco_rides':
        // Would need to check eco vehicle usage
        return false // Placeholder

      case 'perfect_rating':
        // Would need to check recent ratings
        return false // Placeholder

      case 'successful_referrals':
        return userRewards.referrals_count >= req.value

      case 'daily_streak':
        return userRewards.streak_days >= req.value

      default:
        return false
    }
  }

  private async unlockAchievement(userId: string, achievement: Achievement): Promise<void> {
    // Update user achievements
    const { error } = await supabase
      .from('user_rewards')
      .update({
        achievements: supabase.rpc('array_append', {
          array_col: 'achievements',
          new_element: achievement.id
        })
      })
      .eq('user_id', userId)

    if (error) {
      console.error('Error updating achievements:', error)
      return
    }

    // Award achievement points
    await this.awardPoints(userId, achievement.points, `Conquista: ${achievement.name}`)

    // Send notification
    notificationService.showNotification({
      title: `🏆 Conquista Desbloqueada! ${achievement.icon}`,
      message: `${achievement.name}: ${achievement.description}`,
      type: 'success',
      priority: 'high',
      sound: true,
      vibrate: true
    })

    // Track achievement
    analyticsService.track('achievement_unlocked', {
      achievement_id: achievement.id,
      achievement_name: achievement.name,
      points_awarded: achievement.points,
      rarity: achievement.rarity
    })
  }

  /**
   * Get user level info
   */
  getUserLevel(points: number): UserLevel {
    return this.LEVELS.find(level => 
      points >= level.minPoints && points <= level.maxPoints
    ) || this.LEVELS[0]
  }

  /**
   * Get all achievements
   */
  getAllAchievements(): Achievement[] {
    return [...this.ACHIEVEMENTS]
  }

  /**
   * Get progress to next level
   */
  getLevelProgress(points: number): { current: UserLevel; next: UserLevel | null; progress: number } {
    const current = this.getUserLevel(points)
    const nextLevelIndex = this.LEVELS.findIndex(l => l.level === current.level) + 1
    const next = nextLevelIndex < this.LEVELS.length ? this.LEVELS[nextLevelIndex] : null
    
    let progress = 0
    if (next) {
      const pointsInCurrentLevel = points - current.minPoints
      const pointsNeededForNext = next.minPoints - current.minPoints
      progress = pointsInCurrentLevel / pointsNeededForNext
    } else {
      progress = 1 // Max level
    }

    return { current, next, progress }
  }
}

export const rewardsService = new RewardsService()
export default rewardsService
