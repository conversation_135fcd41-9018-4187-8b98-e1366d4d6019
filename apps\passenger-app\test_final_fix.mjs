// =====================================================
// TESTE FINAL DA CORREÇÃO
// Verifica se todos os métodos de pagamento aparecem
// =====================================================

import puppeteer from 'puppeteer'

async function testFinalFix() {
  console.log('🎯 TESTE FINAL: MÉTODOS DE PAGAMENTO')
  console.log('=' .repeat(50))

  let browser = null
  
  try {
    browser = await puppeteer.launch({
      headless: false,
      devtools: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })

    const page = await browser.newPage()
    await page.setViewport({ width: 1280, height: 720 })

    console.log('\n🌐 Navegando diretamente para detalhes...')
    await page.goto('http://localhost:3000/ride-request/details', { 
      waitUntil: 'domcontentloaded',
      timeout: 15000 
    })
    
    console.log('⏱️ Aguardando carregamento completo...')
    await new Promise(resolve => setTimeout(resolve, 8000))
    
    // Contar todos os métodos de pagamento
    const paymentMethods = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'))
      const paymentButtons = buttons.filter(btn => {
        const text = btn.textContent || ''
        return text.includes('Dinheiro') || 
               text.includes('Cartão') || 
               text.includes('PIX') ||
               text.includes('Crédito') ||
               text.includes('Débito')
      })
      
      return paymentButtons.map(btn => ({
        text: btn.textContent.trim(),
        visible: btn.offsetParent !== null
      }))
    })
    
    console.log('\n📊 MÉTODOS ENCONTRADOS:')
    paymentMethods.forEach((method, index) => {
      console.log(`${index + 1}. ${method.text} ${method.visible ? '✅' : '❌'}`)
    })
    
    const visibleMethods = paymentMethods.filter(m => m.visible)
    console.log(`\n📈 TOTAL VISÍVEL: ${visibleMethods.length}`)
    
    // Verificar se temos pelo menos 3 métodos (Dinheiro, Cartão, PIX)
    const hasCash = visibleMethods.some(m => m.text.includes('Dinheiro'))
    const hasCard = visibleMethods.some(m => m.text.includes('Cartão'))
    const hasPix = visibleMethods.some(m => m.text.includes('PIX'))
    
    console.log('\n🔍 VERIFICAÇÃO:')
    console.log(`💵 Dinheiro: ${hasCash ? '✅' : '❌'}`)
    console.log(`💳 Cartão: ${hasCard ? '✅' : '❌'}`)
    console.log(`📱 PIX: ${hasPix ? '✅' : '❌'}`)
    
    if (visibleMethods.length >= 3 && hasCash && hasCard && hasPix) {
      console.log('\n🎉 CORREÇÃO FUNCIONOU!')
      console.log('✅ Todos os métodos de pagamento estão visíveis')
      return true
    } else if (visibleMethods.length > 1) {
      console.log('\n⚠️ MELHORIA PARCIAL')
      console.log(`✅ ${visibleMethods.length} métodos visíveis`)
      return false
    } else {
      console.log('\n❌ PROBLEMA PERSISTE')
      console.log('⚠️ Poucos métodos visíveis')
      return false
    }

  } catch (error) {
    console.error('💥 Erro no teste:', error.message)
    return false
  } finally {
    if (browser) {
      await browser.close()
    }
  }
}

testFinalFix()
  .then((success) => {
    console.log('\n🏁 TESTE FINAL CONCLUÍDO!')
    console.log(success ? '✅ SUCESSO' : '❌ FALHA')
    process.exit(success ? 0 : 1)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
