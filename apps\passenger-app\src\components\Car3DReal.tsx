import React, { useRef, useState, useEffect, Suspense } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Environment } from '@react-three/drei'
import * as THREE from 'three'

// Componente do carro 3D melhorado (fallback)
const ModernCar: React.FC<{ manualRotation: [number, number, number] }> = ({ manualRotation }) => {
  const carRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (carRef.current) {
      // Rotação baseada no scroll da página
      const scrollY = window.scrollY || 0
      const maxScroll = Math.max(document.body.scrollHeight - window.innerHeight, 1)
      const scrollProgress = Math.min(scrollY / maxScroll, 1)

      // Combinar rotação do scroll + rotação manual + rotação automática lenta
      carRef.current.rotation.x = manualRotation[0] + Math.sin(scrollProgress * Math.PI) * 0.1
      carRef.current.rotation.y = manualRotation[1] + scrollProgress * Math.PI * 2 + state.clock.elapsedTime * 0.02
      carRef.current.rotation.z = manualRotation[2]
      
      // Movimento sutil para cima e para baixo
      carRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={carRef} position={[0, 0, 0]} scale={[1.2, 1.2, 1.2]}>
      {/* Corpo principal do carro - Design moderno */}
      <mesh position={[0, 0.5, 0]} castShadow receiveShadow>
        <boxGeometry args={[4.5, 1.2, 2.2]} />
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.95}
          roughness={0.05}
          envMapIntensity={1.5}
        />
      </mesh>

      {/* Teto do carro - Mais aerodinâmico */}
      <mesh position={[0.2, 1.4, 0]} castShadow>
        <boxGeometry args={[3.2, 0.9, 2]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* Para-brisa frontal - Mais inclinado */}
      <mesh position={[1.5, 1.4, 0]} rotation={[0, 0, -0.3]} castShadow>
        <boxGeometry args={[0.9, 0.8, 1.8]} />
        <meshStandardMaterial
          color="#87CEEB"
          transparent
          opacity={0.2}
          metalness={0.1}
          roughness={0.1}
        />
      </mesh>

      {/* Para-brisa traseiro */}
      <mesh position={[-1.3, 1.4, 0]} rotation={[0, 0, 0.2]} castShadow>
        <boxGeometry args={[0.7, 0.7, 1.8]} />
        <meshStandardMaterial
          color="#87CEEB"
          transparent
          opacity={0.2}
          metalness={0.1}
          roughness={0.1}
        />
      </mesh>

      {/* Rodas - Design esportivo */}
      {[
        [1.6, -0.3, 1.3],
        [1.6, -0.3, -1.3],
        [-1.6, -0.3, 1.3],
        [-1.6, -0.3, -1.3]
      ].map((position, index) => (
        <group key={index} position={position as [number, number, number]}>
          {/* Pneu */}
          <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
            <cylinderGeometry args={[0.5, 0.5, 0.4]} />
            <meshStandardMaterial color="#1a1a1a" metalness={0.1} roughness={0.9} />
          </mesh>
          {/* Roda */}
          <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0.15]} castShadow>
            <cylinderGeometry args={[0.35, 0.35, 0.1]} />
            <meshStandardMaterial color="#c0c0c0" metalness={0.9} roughness={0.1} />
          </mesh>
          {/* Centro da roda */}
          <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0.2]} castShadow>
            <cylinderGeometry args={[0.15, 0.15, 0.05]} />
            <meshStandardMaterial color="#2a2a2a" metalness={0.8} roughness={0.2} />
          </mesh>
        </group>
      ))}

      {/* Faróis dianteiros - LED modernos */}
      <mesh position={[2.3, 0.4, 0.8]} castShadow>
        <sphereGeometry args={[0.2]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.5}
          metalness={0.1}
          roughness={0.1}
        />
      </mesh>
      <mesh position={[2.3, 0.4, -0.8]} castShadow>
        <sphereGeometry args={[0.2]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.5}
          metalness={0.1}
          roughness={0.1}
        />
      </mesh>

      {/* Lanternas traseiras */}
      <mesh position={[-2.3, 0.4, 0.8]} castShadow>
        <sphereGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#ff0000"
          emissive="#ff0000"
          emissiveIntensity={0.3}
        />
      </mesh>
      <mesh position={[-2.3, 0.4, -0.8]} castShadow>
        <sphereGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#ff0000"
          emissive="#ff0000"
          emissiveIntensity={0.3}
        />
      </mesh>

      {/* Grade frontal */}
      <mesh position={[2.25, 0.5, 0]} castShadow>
        <boxGeometry args={[0.1, 0.6, 1.4]} />
        <meshStandardMaterial
          color="#2a2a2a"
          metalness={0.8}
          roughness={0.3}
        />
      </mesh>

      {/* Spoiler traseiro */}
      <mesh position={[-2.1, 1.8, 0]} castShadow>
        <boxGeometry args={[0.3, 0.1, 1.8]} />
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* Detalhes adicionais - Espelhos retrovisores */}
      <mesh position={[1.8, 1.6, 1.2]} castShadow>
        <boxGeometry args={[0.1, 0.1, 0.2]} />
        <meshStandardMaterial
          color="#2a2a2a"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>
      <mesh position={[1.8, 1.6, -1.2]} castShadow>
        <boxGeometry args={[0.1, 0.1, 0.2]} />
        <meshStandardMaterial
          color="#2a2a2a"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>

      {/* Maçanetas das portas */}
      <mesh position={[0.5, 0.8, 1.15]} castShadow>
        <boxGeometry args={[0.3, 0.05, 0.1]} />
        <meshStandardMaterial
          color="#c0c0c0"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>
      <mesh position={[0.5, 0.8, -1.15]} castShadow>
        <boxGeometry args={[0.3, 0.05, 0.1]} />
        <meshStandardMaterial
          color="#c0c0c0"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* Antena */}
      <mesh position={[-1.5, 2.2, 0.5]} castShadow>
        <cylinderGeometry args={[0.02, 0.02, 0.3]} />
        <meshStandardMaterial
          color="#2a2a2a"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>
    </group>
  )
}

// Componente de loading
const CarLoader: React.FC = () => {
  return (
    <group>
      <mesh position={[0, 0.5, 0]} castShadow receiveShadow>
        <boxGeometry args={[4, 1, 2]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>
      
      {/* Indicador de loading */}
      <mesh position={[0, 2, 0]}>
        <sphereGeometry args={[0.1]} />
        <meshStandardMaterial
          color="#3B82F6"
          emissive="#3B82F6"
          emissiveIntensity={0.5}
        />
      </mesh>
    </group>
  )
}

// Componente principal
interface Car3DRealProps {
  className?: string
}

export const Car3DReal: React.FC<Car3DRealProps> = ({ className = "" }) => {
  const [rotation, setRotation] = useState<[number, number, number]>([0, 0, 0])
  const [isDragging, setIsDragging] = useState(false)
  const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 })

  // Handlers para mouse
  const handleMouseDown = (event: React.MouseEvent) => {
    event.preventDefault()
    setIsDragging(true)
    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isDragging) return
    event.preventDefault()

    const deltaX = event.clientX - lastPosition.x
    const deltaY = event.clientY - lastPosition.y

    setRotation(prev => [
      prev[0] + deltaY * 0.005,
      prev[1] + deltaX * 0.005,
      prev[2]
    ])

    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Event listeners globais
  useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false)
    const handleGlobalMouseMove = (event: MouseEvent) => {
      if (!isDragging) return

      const deltaX = event.clientX - lastPosition.x
      const deltaY = event.clientY - lastPosition.y

      setRotation(prev => [
        prev[0] + deltaY * 0.005,
        prev[1] + deltaX * 0.005,
        prev[2]
      ])

      setLastPosition({ x: event.clientX, y: event.clientY })
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }
  }, [isDragging, lastPosition])

  return (
    <div
      className={`w-full h-full ${className} select-none`}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
        pointerEvents: 'auto',
        cursor: isDragging ? 'grabbing' : 'grab',
        touchAction: 'none'
      }}
    >
      <Canvas
        shadows
        camera={{ position: [6, 4, 6], fov: 50 }}
        style={{
          background: 'transparent',
          width: '100%',
          height: '100%'
        }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]}
      >
        {/* Iluminação otimizada */}
        <ambientLight intensity={0.4} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1.2}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
        <pointLight position={[-5, 5, -5]} intensity={0.4} />
        <pointLight position={[5, 5, 5]} intensity={0.4} />

        {/* Ambiente */}
        <Environment preset="city" />

        {/* Carro */}
        <Suspense fallback={<CarLoader />}>
          <ModernCar manualRotation={rotation} />
        </Suspense>

        {/* Chão reflexivo */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1, 0]} receiveShadow>
          <planeGeometry args={[30, 30]} />
          <meshStandardMaterial
            color="#1a1a1a"
            transparent
            opacity={0.2}
            metalness={0.8}
            roughness={0.2}
          />
        </mesh>
      </Canvas>
    </div>
  )
}

export default Car3DReal
