// =====================================================
// MONITOR DE CONSOLE DO NAVEGADOR COM PUPPETEER
// Captura logs, erros e warnings do console do navegador
// =====================================================

import puppeteer from 'puppeteer'

class BrowserConsoleMonitor {
  constructor() {
    this.browser = null
    this.page = null
    this.logs = []
    this.errors = []
    this.warnings = []
    this.networkRequests = []
  }

  async init() {
    console.log('🚀 Iniciando Browser Console Monitor...')
    
    this.browser = await puppeteer.launch({
      headless: false, // Mostrar navegador para debug
      devtools: false, // Não abrir DevTools automaticamente
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    })

    this.page = await this.browser.newPage()
    
    // Configurar viewport
    await this.page.setViewport({ width: 1280, height: 720 })
    
    this.setupEventListeners()
    console.log('✅ Browser Console Monitor iniciado!')
  }

  setupEventListeners() {
    // Capturar logs do console
    this.page.on('console', (msg) => {
      const logEntry = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString(),
        location: msg.location()
      }

      this.logs.push(logEntry)

      // Categorizar por tipo
      if (msg.type() === 'error') {
        this.errors.push(logEntry)
        console.log(`❌ CONSOLE ERROR: ${msg.text()}`)
      } else if (msg.type() === 'warning') {
        this.warnings.push(logEntry)
        console.log(`⚠️ CONSOLE WARNING: ${msg.text()}`)
      } else if (msg.type() === 'log') {
        console.log(`📝 CONSOLE LOG: ${msg.text()}`)
      } else {
        console.log(`🔍 CONSOLE ${msg.type().toUpperCase()}: ${msg.text()}`)
      }
    })

    // Capturar erros de página
    this.page.on('pageerror', (error) => {
      const errorEntry = {
        type: 'pageerror',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }
      
      this.errors.push(errorEntry)
      console.log(`💥 PAGE ERROR: ${error.message}`)
    })

    // Capturar falhas de requisições
    this.page.on('requestfailed', (request) => {
      const failedRequest = {
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText,
        timestamp: new Date().toISOString()
      }
      
      this.networkRequests.push(failedRequest)
      console.log(`🌐 REQUEST FAILED: ${request.method()} ${request.url()} - ${request.failure()?.errorText}`)
    })

    // Capturar respostas com erro
    this.page.on('response', (response) => {
      if (response.status() >= 400) {
        const errorResponse = {
          url: response.url(),
          status: response.status(),
          statusText: response.statusText(),
          timestamp: new Date().toISOString()
        }
        
        this.networkRequests.push(errorResponse)
        console.log(`🔴 HTTP ERROR: ${response.status()} ${response.url()}`)
      }
    })
  }

  async navigateAndMonitor(url, waitTime = 5000) {
    console.log(`\n🌐 Navegando para: ${url}`)
    console.log('=' .repeat(60))
    
    // Limpar logs anteriores
    this.logs = []
    this.errors = []
    this.warnings = []
    this.networkRequests = []

    try {
      // Navegar para a página
      await this.page.goto(url, { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })

      console.log('✅ Página carregada, monitorando console...')
      
      // Aguardar um tempo para capturar logs
      await this.page.waitForTimeout(waitTime)
      
      // Executar JavaScript para capturar mais informações
      const pageInfo = await this.page.evaluate(() => {
        return {
          title: document.title,
          url: window.location.href,
          readyState: document.readyState,
          userAgent: navigator.userAgent,
          errors: window.errors || []
        }
      })

      console.log(`📄 Título da página: ${pageInfo.title}`)
      console.log(`🔗 URL atual: ${pageInfo.url}`)
      console.log(`📊 Estado: ${pageInfo.readyState}`)

      return this.generateReport()

    } catch (error) {
      console.error(`💥 Erro ao navegar: ${error.message}`)
      return this.generateReport()
    }
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalLogs: this.logs.length,
        errors: this.errors.length,
        warnings: this.warnings.length,
        networkIssues: this.networkRequests.length
      },
      logs: this.logs,
      errors: this.errors,
      warnings: this.warnings,
      networkRequests: this.networkRequests
    }

    console.log('\n📊 RELATÓRIO DO CONSOLE:')
    console.log('=' .repeat(40))
    console.log(`📝 Total de logs: ${report.summary.totalLogs}`)
    console.log(`❌ Erros: ${report.summary.errors}`)
    console.log(`⚠️ Warnings: ${report.summary.warnings}`)
    console.log(`🌐 Problemas de rede: ${report.summary.networkIssues}`)

    if (report.summary.errors > 0) {
      console.log('\n❌ ERROS ENCONTRADOS:')
      this.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error.text || error.message}`)
      })
    }

    if (report.summary.warnings > 0) {
      console.log('\n⚠️ WARNINGS ENCONTRADOS:')
      this.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning.text}`)
      })
    }

    if (report.summary.networkIssues > 0) {
      console.log('\n🌐 PROBLEMAS DE REDE:')
      this.networkRequests.forEach((req, index) => {
        console.log(`  ${index + 1}. ${req.status || 'FAILED'} ${req.url}`)
      })
    }

    return report
  }

  async close() {
    if (this.browser) {
      await this.browser.close()
      console.log('🔒 Browser fechado')
    }
  }
}

// Função para testar uma URL específica
async function testUrl(url, waitTime = 5000) {
  const monitor = new BrowserConsoleMonitor()
  
  try {
    await monitor.init()
    const report = await monitor.navigateAndMonitor(url, waitTime)
    return report
  } finally {
    await monitor.close()
  }
}

// Função para testar múltiplas URLs
async function testMultipleUrls(urls) {
  console.log('🎯 TESTANDO MÚLTIPLAS PÁGINAS')
  console.log('=' .repeat(60))
  
  const monitor = new BrowserConsoleMonitor()
  const reports = []
  
  try {
    await monitor.init()
    
    for (const url of urls) {
      const report = await monitor.navigateAndMonitor(url, 3000)
      reports.push(report)
      
      // Pequena pausa entre testes
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
    
    return reports
  } finally {
    await monitor.close()
  }
}

// Exportar para uso
export { BrowserConsoleMonitor, testUrl, testMultipleUrls }

// Se executado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const url = process.argv[2] || 'http://localhost:3000'
  const waitTime = parseInt(process.argv[3]) || 5000
  
  console.log(`🧪 Testando: ${url}`)
  console.log(`⏱️ Tempo de espera: ${waitTime}ms`)
  
  testUrl(url, waitTime)
    .then((report) => {
      console.log('\n🏁 Teste concluído!')
      process.exit(report.summary.errors > 0 ? 1 : 0)
    })
    .catch((error) => {
      console.error('💥 Erro fatal:', error)
      process.exit(1)
    })
}
