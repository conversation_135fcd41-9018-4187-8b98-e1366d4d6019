import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Heart, 
  Home, 
  Briefcase, 
  MapPin, 
  Clock, 
  Star,
  Plus,
  X,
  Edit3,
  Trash2,
  Navigation
} from 'lucide-react'
import { locationHistoryService, FavoriteLocation, RecentSearch } from '../services/LocationHistoryService'

interface FavoritesPanelProps {
  onLocationSelect: (location: any) => void
  className?: string
}

const iconMap = {
  home: Home,
  work: Briefcase,
  custom: MapPin
}

const colorMap = {
  home: 'bg-blue-500 text-white',
  work: 'bg-green-500 text-white',
  custom: 'bg-purple-500 text-white'
}

export const FavoritesPanel: React.FC<FavoritesPanelProps> = ({
  onLocationSelect,
  className = ""
}) => {
  const [favorites, setFavorites] = useState<FavoriteLocation[]>([])
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([])
  const [activeTab, setActiveTab] = useState<'favorites' | 'recent'>('favorites')
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingFavorite, setEditingFavorite] = useState<FavoriteLocation | null>(null)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = () => {
    setFavorites(locationHistoryService.getFavorites())
    setRecentSearches(locationHistoryService.getRecentSearches())
  }

  const handleAddFavorite = (favoriteData: Omit<FavoriteLocation, 'id' | 'createdAt' | 'lastUsed' | 'useCount'>) => {
    locationHistoryService.addFavorite(favoriteData)
    loadData()
    setShowAddForm(false)
  }

  const handleEditFavorite = (id: string, updates: Partial<FavoriteLocation>) => {
    locationHistoryService.updateFavorite(id, updates)
    loadData()
    setEditingFavorite(null)
  }

  const handleDeleteFavorite = (id: string) => {
    locationHistoryService.removeFavorite(id)
    loadData()
  }

  const handleLocationSelect = (location: any, type: 'favorite' | 'recent') => {
    if (type === 'favorite') {
      locationHistoryService.useFavorite(location.id)
      onLocationSelect({
        id: location.id,
        place_name: location.address,
        center: location.coordinates,
        place_type: ['favorite'],
        properties: { category: location.type },
        context: []
      })
    } else {
      onLocationSelect(location.result)
    }
    loadData()
  }

  return (
    <div className={`bg-white rounded-xl shadow-lg border border-gray-100 ${className}`}>
      {/* Header with Tabs */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900">Locais Salvos</h3>
          {activeTab === 'favorites' && (
            <motion.button
              onClick={() => setShowAddForm(true)}
              className="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Plus className="w-4 h-4" />
            </motion.button>
          )}
        </div>
        
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('favorites')}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'favorites'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Heart className="w-4 h-4 inline mr-1" />
            Favoritos
          </button>
          <button
            onClick={() => setActiveTab('recent')}
            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'recent'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Clock className="w-4 h-4 inline mr-1" />
            Recentes
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 max-h-96 overflow-y-auto">
        <AnimatePresence mode="wait">
          {activeTab === 'favorites' ? (
            <motion.div
              key="favorites"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="space-y-3"
            >
              {favorites.length === 0 ? (
                <div className="text-center py-8">
                  <Heart className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                  <p className="text-gray-500">Nenhum favorito salvo</p>
                  <p className="text-gray-400 text-sm">Adicione locais que você visita frequentemente</p>
                </div>
              ) : (
                favorites.map((favorite) => {
                  const IconComponent = iconMap[favorite.type]
                  return (
                    <motion.div
                      key={favorite.id}
                      className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors group"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className={`p-2 rounded-lg ${colorMap[favorite.type]}`}>
                        <IconComponent className="w-4 h-4" />
                      </div>
                      
                      <div 
                        className="flex-1 cursor-pointer"
                        onClick={() => handleLocationSelect(favorite, 'favorite')}
                      >
                        <h4 className="font-medium text-gray-900">{favorite.name}</h4>
                        <p className="text-sm text-gray-500 truncate">{favorite.address}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs text-gray-400">Usado {favorite.useCount}x</span>
                          <div className="flex items-center space-x-1">
                            {[...Array(Math.min(5, Math.floor(favorite.useCount / 2) + 1))].map((_, i) => (
                              <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                            ))}
                          </div>
                        </div>
                      </div>
                      
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                        <button
                          onClick={() => setEditingFavorite(favorite)}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        >
                          <Edit3 className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteFavorite(favorite.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </motion.div>
                  )
                })
              )}
            </motion.div>
          ) : (
            <motion.div
              key="recent"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-3"
            >
              {recentSearches.length === 0 ? (
                <div className="text-center py-8">
                  <Clock className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                  <p className="text-gray-500">Nenhuma busca recente</p>
                  <p className="text-gray-400 text-sm">Suas buscas aparecerão aqui</p>
                </div>
              ) : (
                recentSearches.map((search) => (
                  <motion.div
                    key={search.id}
                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                    onClick={() => handleLocationSelect(search, 'recent')}
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="p-2 bg-gray-100 rounded-lg">
                      <Navigation className="w-4 h-4 text-gray-600" />
                    </div>
                    
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{search.query}</h4>
                      <p className="text-sm text-gray-500 truncate">{search.result.place_name}</p>
                      <p className="text-xs text-gray-400">
                        {new Date(search.timestamp).toLocaleDateString('pt-BR', {
                          day: '2-digit',
                          month: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                    
                    {search.selected && (
                      <div className="p-1 bg-green-100 rounded">
                        <Star className="w-3 h-3 text-green-600 fill-current" />
                      </div>
                    )}
                  </motion.div>
                ))
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Add Favorite Modal */}
      <AnimatePresence>
        {showAddForm && (
          <AddFavoriteModal
            onSave={handleAddFavorite}
            onClose={() => setShowAddForm(false)}
          />
        )}
      </AnimatePresence>

      {/* Edit Favorite Modal */}
      <AnimatePresence>
        {editingFavorite && (
          <EditFavoriteModal
            favorite={editingFavorite}
            onSave={(updates) => handleEditFavorite(editingFavorite.id, updates)}
            onClose={() => setEditingFavorite(null)}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

// Add Favorite Modal Component
const AddFavoriteModal: React.FC<{
  onSave: (data: any) => void
  onClose: () => void
}> = ({ onSave, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    coordinates: [0, 0] as [number, number],
    type: 'custom' as FavoriteLocation['type'],
    icon: '📍'
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (formData.name && formData.address) {
      onSave(formData)
    }
  }

  return (
    <motion.div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-white rounded-xl p-6 w-full max-w-md mx-4"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Adicionar Favorito</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Ex: Casa, Trabalho, Academia"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Endereço</label>
            <input
              type="text"
              value={formData.address}
              onChange={(e) => setFormData({ ...formData, address: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Digite o endereço completo"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Tipo</label>
            <select
              value={formData.type}
              onChange={(e) => setFormData({ ...formData, type: e.target.value as FavoriteLocation['type'] })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="home">Casa</option>
              <option value="work">Trabalho</option>
              <option value="custom">Personalizado</option>
            </select>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Salvar
            </button>
          </div>
        </form>
      </motion.div>
    </motion.div>
  )
}

// Edit Favorite Modal Component
const EditFavoriteModal: React.FC<{
  favorite: FavoriteLocation
  onSave: (updates: Partial<FavoriteLocation>) => void
  onClose: () => void
}> = ({ favorite, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    name: favorite.name,
    address: favorite.address,
    type: favorite.type,
    icon: favorite.icon
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
  }

  return (
    <motion.div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-white rounded-xl p-6 w-full max-w-md mx-4"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Editar Favorito</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Endereço</label>
            <input
              type="text"
              value={formData.address}
              onChange={(e) => setFormData({ ...formData, address: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Tipo</label>
            <select
              value={formData.type}
              onChange={(e) => setFormData({ ...formData, type: e.target.value as FavoriteLocation['type'] })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="home">Casa</option>
              <option value="work">Trabalho</option>
              <option value="custom">Personalizado</option>
            </select>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Salvar
            </button>
          </div>
        </form>
      </motion.div>
    </motion.div>
  )
}

export default FavoritesPanel
