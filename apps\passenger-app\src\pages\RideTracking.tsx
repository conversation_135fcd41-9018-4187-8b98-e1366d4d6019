import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import {
  MapPin,
  Clock,
  Phone,
  MessageCircle,
  Star,
  Navigation,
  User,
  Car,
  AlertCircle,
  CheckCircle,
  X,
  Share
} from 'lucide-react'
import { ModernLayout, ModernCard } from '../components/ModernLayout'
import MapComponent from '../components/MapComponent'
import ChatComponent from '../components/ChatComponent'
import EmergencyButton from '../components/EmergencyButton'
import TripSharingComponent from '../components/TripSharingComponent'
import RealTimeETA from '../components/RealTimeETA'
import PaymentSystem from '../components/PaymentSystem'
import RideRatingSystem from '../components/RideRatingSystem'

// 🚗 RIDE TRACKING - DESIGN ORIGINAL MANTIDO
// Conversão Android nativa aplicada via wrapper adaptativo

interface RideStatus {
  status: 'waiting' | 'driver_coming' | 'arrived' | 'in_progress' | 'completed'
  message: string
  eta?: number
}

export const RideTracking: React.FC = () => {
  const navigate = useNavigate()
  const [rideStatus, setRideStatus] = useState<RideStatus>({
    status: 'driver_coming',
    message: 'O motorista está a caminho',
    eta: 3
  })
  const [isChatOpen, setIsChatOpen] = useState(false)
  const [showCancelModal, setShowCancelModal] = useState(false)
  const [showPayment, setShowPayment] = useState(false)
  const [showRating, setShowRating] = useState(false)
  const [currentLocation, setCurrentLocation] = useState<{lat: number, lng: number, address: string} | null>(null)

  // Dados simulados da corrida atual
  const rideId = 'ride-123'
  const passengerLocation = { lat: -23.5505, lng: -46.6333 }
  const destinationLocation = { lat: -23.5600, lng: -46.6400 }

  const driver = {
    id: 'driver-123',
    name: 'João Silva',
    rating: 4.8,
    photo: '/api/placeholder/60/60',
    vehicle: {
      model: 'Honda Civic',
      plate: 'ABC-1234',
      color: 'Prata'
    },
    phone: '+55 11 99999-9999'
  }

  const ride = {
    pickup: 'Rua das Flores, 123',
    destination: 'Shopping Center Norte',
    price: 18.90,
    distance: '8.5 km',
    estimatedTime: '25 min'
  }

  useEffect(() => {
    // Simulate ride status updates
    const statusUpdates = [
      { status: 'driver_coming', message: 'O motorista está a caminho', eta: 3 },
      { status: 'arrived', message: 'O motorista chegou!', eta: 0 },
      { status: 'in_progress', message: 'Corrida em andamento', eta: undefined },
      { status: 'completed', message: 'Corrida finalizada', eta: undefined }
    ]

    let currentIndex = 0
    const interval = setInterval(() => {
      if (currentIndex < statusUpdates.length - 1) {
        currentIndex++
        setRideStatus(statusUpdates[currentIndex] as RideStatus)

        // Quando a corrida for completada, mostrar pagamento
        if (statusUpdates[currentIndex].status === 'completed') {
          setTimeout(() => setShowPayment(true), 1000)
        }
      } else {
        clearInterval(interval)
      }
    }, 10000) // Update every 10 seconds for demo

    return () => clearInterval(interval)
  }, [navigate])

  const getStatusIcon = () => {
    switch (rideStatus.status) {
      case 'waiting':
      case 'driver_coming':
        return <Clock className="w-6 h-6 text-blue-500" />
      case 'arrived':
        return <CheckCircle className="w-6 h-6 text-green-500" />
      case 'in_progress':
        return <Navigation className="w-6 h-6 text-purple-500" />
      case 'completed':
        return <CheckCircle className="w-6 h-6 text-green-500" />
      default:
        return <Clock className="w-6 h-6 text-blue-500" />
    }
  }

  const getStatusColor = () => {
    switch (rideStatus.status) {
      case 'waiting':
      case 'driver_coming':
        return 'from-blue-500 to-blue-600'
      case 'arrived':
        return 'from-green-500 to-green-600'
      case 'in_progress':
        return 'from-purple-500 to-purple-600'
      case 'completed':
        return 'from-green-500 to-green-600'
      default:
        return 'from-blue-500 to-blue-600'
    }
  }

  const handleCancelRide = () => {
    setShowCancelModal(false)
    navigate('/dashboard')
  }

  const handleShareLocation = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Minha localização - MobiDrive',
        text: 'Estou em uma corrida. Acompanhe minha localização:',
        url: window.location.href
      })
    }
  }

  return (
    <ModernLayout
      title="Sua Corrida"
      subtitle={rideStatus.message}
      pageIcon="🚗"
      animatedSymbol="📍"
    >
      <div className="space-y-6">
        {/* Cancel Button */}
        <motion.div
          className="flex justify-end"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <motion.button
            className="p-2 text-red-500 hover:text-red-600 transition-colors bg-white/10 backdrop-blur-md rounded-xl"
            onClick={() => setShowCancelModal(true)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <X className="w-6 h-6" />
          </motion.button>
        </motion.div>
        {/* Real-time ETA Component */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <RealTimeETA
            rideId={rideId}
            driverId={driver.id}
            passengerLocation={passengerLocation}
            destinationLocation={destinationLocation}
            onETAUpdate={(eta) => {
              setRideStatus(prev => ({
                ...prev,
                eta: eta.pickupETA
              }))
            }}
          />
        </motion.div>

        {/* Status Card */}
        <motion.div
          className="glass-card p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="flex items-center space-x-4 mb-4">
            <motion.div
              className={`w-12 h-12 bg-gradient-to-r ${getStatusColor()} rounded-full flex items-center justify-center`}
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              {getStatusIcon()}
            </motion.div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">{rideStatus.message}</h3>
              {rideStatus.eta && (
                <p className="text-gray-600">Chegada em {rideStatus.eta} minutos</p>
              )}
            </div>
            {rideStatus.eta && (
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">{rideStatus.eta}</div>
                <div className="text-sm text-gray-500">min</div>
              </div>
            )}
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <motion.div
              className={`h-2 bg-gradient-to-r ${getStatusColor()} rounded-full`}
              initial={{ width: '25%' }}
              animate={{
                width: rideStatus.status === 'driver_coming' ? '25%' :
                       rideStatus.status === 'arrived' ? '50%' :
                       rideStatus.status === 'in_progress' ? '75%' :
                       rideStatus.status === 'completed' ? '100%' : '25%'
              }}
              transition={{ duration: 1 }}
            />
          </div>

          {/* Trip Details */}
          <div className="space-y-3 text-sm">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-gray-600">De: {ride.pickup}</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-gray-600">Para: {ride.destination}</span>
            </div>
          </div>
        </motion.div>

        {/* Driver Info */}
        <motion.div
          className="glass-card p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Seu Motorista</h3>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
              <User className="w-8 h-8 text-gray-600" />
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-900">{driver.name}</h4>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Star className="w-4 h-4 text-yellow-500" />
                <span>{driver.rating}</span>
                <span>•</span>
                <Car className="w-4 h-4" />
                <span>{driver.vehicle.model}</span>
                <span>•</span>
                <span>{driver.vehicle.plate}</span>
              </div>
            </div>
            <div className="flex space-x-2">
              <motion.button
                className="p-3 bg-green-500 text-white rounded-xl"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => window.open(`tel:${driver.phone}`)}
              >
                <Phone className="w-5 h-5" />
              </motion.button>
              <motion.button
                className="p-3 bg-blue-500 text-white rounded-xl"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsChatOpen(true)}
              >
                <MessageCircle className="w-5 h-5" />
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Map */}
        <motion.div
          className="glass-card p-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <MapComponent
            height="300px"
            showDrivers={true}
            interactive={false}
          />
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          className="flex justify-center space-x-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <TripSharingComponent
            rideId={rideId}
            origin={ride.pickup}
            destination={ride.destination}
            driverInfo={{
              name: driver.name,
              phone: driver.phone,
              vehicle: `${driver.vehicle.model} ${driver.vehicle.color}`,
              plate: driver.vehicle.plate,
              photo: driver.photo
            }}
            estimatedArrival={`${rideStatus.eta || 0} minutos`}
          />
        </motion.div>

        {/* Emergency Button */}
        <EmergencyButton
          rideId={rideId}
          driverInfo={{
            id: driver.id,
            name: driver.name,
            phone: driver.phone,
            vehicle: `${driver.vehicle.model} - ${driver.vehicle.plate}`
          }}
          currentLocation={currentLocation || {
            lat: passengerLocation.lat,
            lng: passengerLocation.lng,
            address: ride.pickup
          }}
        />

        {/* Chat Component */}
        <AnimatePresence>
          {isChatOpen && (
            <ChatComponent
              isOpen={isChatOpen}
              onClose={() => setIsChatOpen(false)}
              driverName={driver.name}
              driverPhoto={driver.photo}
            />
          )}
        </AnimatePresence>

        {/* Cancel Modal */}
        <AnimatePresence>
          {showCancelModal && (
            <motion.div
              className="fixed inset-0 z-50 flex items-center justify-center p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setShowCancelModal(false)} />
              <motion.div
                className="relative glass-card p-6 w-full max-w-sm"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
              >
                <h3 className="text-lg font-semibold text-white mb-4">Cancelar Corrida?</h3>
                <p className="text-white/80 mb-6">Tem certeza que deseja cancelar esta corrida?</p>
                <div className="flex space-x-3">
                  <motion.button
                    className="flex-1 bg-gray-500 text-white py-3 rounded-xl font-medium"
                    onClick={() => setShowCancelModal(false)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Não
                  </motion.button>
                  <motion.button
                    className="flex-1 bg-red-500 text-white py-3 rounded-xl font-medium"
                    onClick={handleCancelRide}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Sim, Cancelar
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Payment System */}
        <AnimatePresence>
          {showPayment && (
            <PaymentSystem
              rideId={rideId}
              amount={ride.price}
              onPaymentComplete={(paymentId) => {
                setShowPayment(false)
                setShowRating(true)
              }}
              onCancel={() => setShowPayment(false)}
            />
          )}
        </AnimatePresence>

        {/* Rating System */}
        <AnimatePresence>
          {showRating && (
            <RideRatingSystem
              rideId={rideId}
              driverInfo={{
                id: driver.id,
                name: driver.name,
                photo: driver.photo,
                vehicle: `${driver.vehicle.model} ${driver.vehicle.color}`
              }}
              onComplete={() => {
                setShowRating(false)
                navigate('/dashboard')
              }}
            />
          )}
        </AnimatePresence>
      </div>
    </ModernLayout>
  )
}

export default RideTracking
