-- Migration: Add driver location tracking tables
-- This migration adds support for real-time driver location tracking

-- Create driver_locations table for real-time location tracking
CREATE TABLE IF NOT EXISTS driver_locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    heading DECIMAL(5, 2), -- Direction in degrees (0-360)
    speed DECIMAL(5, 2), -- Speed in km/h
    accuracy DECIMAL(8, 2), -- GPS accuracy in meters
    is_available BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create driver_profiles table for driver-specific information
CREATE TABLE IF NOT EXISTS driver_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    vehicle_type VARCHAR(50) DEFAULT 'economy', -- economy, comfort, premium, moto
    vehicle_make VARCHAR(100),
    vehicle_model VARCHAR(100),
    vehicle_year INTEGER,
    vehicle_color VARCHAR(50),
    vehicle_plate VARCHAR(20),
    license_number VARCHAR(50),
    license_expiry DATE,
    rating DECIMAL(3, 2) DEFAULT 4.5,
    total_rides INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_driver_locations_user_id ON driver_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_driver_locations_active ON driver_locations(is_active, is_available);
CREATE INDEX IF NOT EXISTS idx_driver_locations_coords ON driver_locations(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_driver_locations_updated ON driver_locations(updated_at);

CREATE INDEX IF NOT EXISTS idx_driver_profiles_user_id ON driver_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_driver_profiles_active ON driver_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_driver_profiles_vehicle_type ON driver_profiles(vehicle_type);

-- Add spatial index for geospatial queries (PostGIS extension)
-- Note: This requires PostGIS extension to be enabled
-- CREATE INDEX IF NOT EXISTS idx_driver_locations_spatial ON driver_locations USING GIST (ST_Point(longitude, latitude));

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_driver_locations_updated_at 
    BEFORE UPDATE ON driver_locations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_driver_profiles_updated_at 
    BEFORE UPDATE ON driver_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_profiles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for driver_locations
CREATE POLICY "Drivers can view all active driver locations" ON driver_locations
    FOR SELECT USING (is_active = true);

CREATE POLICY "Drivers can insert their own location" ON driver_locations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Drivers can update their own location" ON driver_locations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Drivers can delete their own location" ON driver_locations
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for driver_profiles
CREATE POLICY "Anyone can view active driver profiles" ON driver_profiles
    FOR SELECT USING (is_active = true);

CREATE POLICY "Drivers can insert their own profile" ON driver_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Drivers can update their own profile" ON driver_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Drivers can delete their own profile" ON driver_profiles
    FOR DELETE USING (auth.uid() = user_id);

-- Create function to get nearby drivers
CREATE OR REPLACE FUNCTION get_nearby_drivers(
    user_lat DECIMAL,
    user_lng DECIMAL,
    radius_km DECIMAL DEFAULT 5
)
RETURNS TABLE (
    driver_id UUID,
    user_id UUID,
    latitude DECIMAL,
    longitude DECIMAL,
    heading DECIMAL,
    speed DECIMAL,
    accuracy DECIMAL,
    is_available BOOLEAN,
    vehicle_type VARCHAR,
    driver_name TEXT,
    driver_rating DECIMAL,
    vehicle_make VARCHAR,
    vehicle_model VARCHAR,
    vehicle_color VARCHAR,
    vehicle_plate VARCHAR,
    distance_km DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dl.id as driver_id,
        dl.user_id,
        dl.latitude,
        dl.longitude,
        dl.heading,
        dl.speed,
        dl.accuracy,
        dl.is_available,
        dp.vehicle_type,
        p.full_name as driver_name,
        dp.rating as driver_rating,
        dp.vehicle_make,
        dp.vehicle_model,
        dp.vehicle_color,
        dp.vehicle_plate,
        -- Calculate distance using Haversine formula
        (
            6371 * acos(
                cos(radians(user_lat)) * 
                cos(radians(dl.latitude)) * 
                cos(radians(dl.longitude) - radians(user_lng)) + 
                sin(radians(user_lat)) * 
                sin(radians(dl.latitude))
            )
        ) as distance_km
    FROM driver_locations dl
    JOIN driver_profiles dp ON dl.user_id = dp.user_id
    JOIN profiles p ON dl.user_id = p.id
    WHERE 
        dl.is_active = true 
        AND dl.is_available = true
        AND dp.is_active = true
        AND dl.updated_at > NOW() - INTERVAL '5 minutes' -- Only recent locations
        AND (
            6371 * acos(
                cos(radians(user_lat)) * 
                cos(radians(dl.latitude)) * 
                cos(radians(dl.longitude) - radians(user_lng)) + 
                sin(radians(user_lat)) * 
                sin(radians(dl.latitude))
            )
        ) <= radius_km
    ORDER BY distance_km ASC
    LIMIT 20;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update driver location
CREATE OR REPLACE FUNCTION update_driver_location(
    driver_lat DECIMAL,
    driver_lng DECIMAL,
    driver_heading DECIMAL DEFAULT NULL,
    driver_speed DECIMAL DEFAULT NULL,
    driver_accuracy DECIMAL DEFAULT NULL,
    available BOOLEAN DEFAULT true
)
RETURNS UUID AS $$
DECLARE
    location_id UUID;
BEGIN
    -- Insert or update driver location
    INSERT INTO driver_locations (
        user_id, latitude, longitude, heading, speed, accuracy, is_available, is_active
    ) VALUES (
        auth.uid(), driver_lat, driver_lng, driver_heading, driver_speed, driver_accuracy, available, true
    )
    ON CONFLICT (user_id) 
    DO UPDATE SET
        latitude = EXCLUDED.latitude,
        longitude = EXCLUDED.longitude,
        heading = EXCLUDED.heading,
        speed = EXCLUDED.speed,
        accuracy = EXCLUDED.accuracy,
        is_available = EXCLUDED.is_available,
        updated_at = NOW()
    RETURNING id INTO location_id;
    
    RETURN location_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON driver_locations TO authenticated;
GRANT ALL ON driver_profiles TO authenticated;
GRANT EXECUTE ON FUNCTION get_nearby_drivers TO authenticated;
GRANT EXECUTE ON FUNCTION update_driver_location TO authenticated;

-- Insert some sample driver data for testing
INSERT INTO driver_profiles (user_id, vehicle_type, vehicle_make, vehicle_model, vehicle_color, vehicle_plate, rating, is_verified)
SELECT 
    id,
    CASE 
        WHEN random() < 0.3 THEN 'economy'
        WHEN random() < 0.6 THEN 'comfort'
        WHEN random() < 0.8 THEN 'premium'
        ELSE 'moto'
    END,
    CASE 
        WHEN random() < 0.3 THEN 'Honda'
        WHEN random() < 0.6 THEN 'Toyota'
        ELSE 'Volkswagen'
    END,
    CASE 
        WHEN random() < 0.3 THEN 'Civic'
        WHEN random() < 0.6 THEN 'Corolla'
        ELSE 'Gol'
    END,
    CASE 
        WHEN random() < 0.2 THEN 'Branco'
        WHEN random() < 0.4 THEN 'Prata'
        WHEN random() < 0.6 THEN 'Preto'
        WHEN random() < 0.8 THEN 'Azul'
        ELSE 'Vermelho'
    END,
    'ABC-' || LPAD(floor(random() * 10000)::text, 4, '0'),
    4.0 + random() * 1.0, -- Rating between 4.0 and 5.0
    true
FROM auth.users 
WHERE email LIKE '%driver%' OR email LIKE '%motorista%'
ON CONFLICT (user_id) DO NOTHING;

-- Add some sample driver locations around São Paulo
INSERT INTO driver_locations (user_id, latitude, longitude, is_available, is_active)
SELECT 
    dp.user_id,
    -23.5505 + (random() - 0.5) * 0.1, -- São Paulo latitude with some variance
    -46.6333 + (random() - 0.5) * 0.1, -- São Paulo longitude with some variance
    random() > 0.2, -- 80% available
    true
FROM driver_profiles dp
ON CONFLICT (user_id) DO UPDATE SET
    latitude = EXCLUDED.latitude,
    longitude = EXCLUDED.longitude,
    is_available = EXCLUDED.is_available,
    updated_at = NOW();

COMMENT ON TABLE driver_locations IS 'Real-time location tracking for active drivers';
COMMENT ON TABLE driver_profiles IS 'Driver profile information including vehicle details';
COMMENT ON FUNCTION get_nearby_drivers IS 'Function to find drivers within a specified radius';
COMMENT ON FUNCTION update_driver_location IS 'Function to update driver location with conflict resolution';
