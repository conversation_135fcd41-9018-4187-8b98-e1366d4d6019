/* 🚫 NO-ZOOM CSS - DESABILITA ZOOM COMPLETAMENTE NAS PÁGINAS DO APP */

/* ===== CONFIGURAÇÕES GLOBAIS ANTI-ZOOM ===== */
.no-zoom-page {
  /* Desabilita zoom por touch */
  touch-action: manipulation !important;
  
  /* Desabilita seleção (exceto inputs) */
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  
  /* Desabilita highlight de tap */
  -webkit-tap-highlight-color: transparent !important;
  
  /* Desabilita overscroll */
  overscroll-behavior: none !important;
  
  /* Permite apenas scroll vertical */
  overflow-x: hidden !important;
  overflow-y: auto !important;
  
  /* Força dimensões fixas */
  width: 100% !important;
  height: 100% !important;
  
  /* Previne zoom por CSS */
  zoom: 1 !important;
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* ===== ELEMENTOS FILHOS - HERDAM CONFIGURAÇÕES ===== */
.no-zoom-page * {
  /* Desabilita zoom em todos os elementos */
  touch-action: manipulation !important;
  -webkit-user-select: none !important;
  user-select: none !important;
  
  /* Previne zoom por CSS */
  zoom: 1 !important;
  
  /* Desabilita highlight */
  -webkit-tap-highlight-color: transparent !important;
}

/* ===== EXCEÇÕES - ELEMENTOS QUE PODEM SER SELECIONADOS ===== */
.no-zoom-page input,
.no-zoom-page textarea,
.no-zoom-page [contenteditable],
.no-zoom-page [data-selectable="true"] {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
  touch-action: manipulation !important;
}

/* ===== EXCEÇÕES - MAPAS PODEM TER ZOOM ===== */
.no-zoom-page .mapbox-gl-canvas,
.no-zoom-page .mapbox,
.no-zoom-page .map-container,
.no-zoom-page .leaflet-container,
.no-zoom-page [data-allow-zoom="true"] {
  touch-action: auto !important;
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
  user-select: auto !important;
  
  /* Permite zoom nos mapas */
  zoom: auto !important;
  transform: none !important;
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -ms-transform: none !important;
  -o-transform: none !important;
}

/* ===== BOTÕES E LINKS - MANTÉM INTERATIVIDADE ===== */
.no-zoom-page button,
.no-zoom-page a,
.no-zoom-page [role="button"],
.no-zoom-page [data-clickable="true"] {
  touch-action: manipulation !important;
  -webkit-user-select: none !important;
  user-select: none !important;
  cursor: pointer !important;
}

/* ===== IMAGENS - DESABILITA ZOOM E SELEÇÃO ===== */
.no-zoom-page img {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  touch-action: manipulation !important;
  
  /* Previne drag de imagens */
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
  
  /* Previne context menu */
  -webkit-touch-callout: none !important;
}

/* ===== SCROLLBARS - MANTÉM FUNCIONALIDADE ===== */
.no-zoom-page::-webkit-scrollbar {
  width: 0px !important;
  background: transparent !important;
}

.no-zoom-page::-webkit-scrollbar-thumb {
  background: transparent !important;
}

/* ===== FORMULÁRIOS - CONFIGURAÇÕES ESPECIAIS ===== */
.no-zoom-page form {
  touch-action: manipulation !important;
}

.no-zoom-page input:focus,
.no-zoom-page textarea:focus,
.no-zoom-page [contenteditable]:focus {
  /* Previne zoom automático no iOS */
  font-size: 16px !important;
  transform: none !important;
  zoom: 1 !important;
}

/* ===== MOBILE ESPECÍFICO ===== */
@media screen and (max-width: 768px) {
  .no-zoom-page {
    /* Configurações específicas para mobile */
    -webkit-text-size-adjust: 100% !important;
    -ms-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
  }
  
  .no-zoom-page input,
  .no-zoom-page textarea {
    /* Previne zoom automático em inputs no iOS */
    font-size: 16px !important;
  }
}

/* ===== DESKTOP ESPECÍFICO ===== */
@media screen and (min-width: 769px) {
  .no-zoom-page {
    /* No desktop, mantém configurações mas permite seleção de texto */
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
  }
  
  .no-zoom-page * {
    -webkit-user-select: text !important;
    user-select: text !important;
  }
  
  .no-zoom-page img,
  .no-zoom-page button,
  .no-zoom-page [role="button"] {
    -webkit-user-select: none !important;
    user-select: none !important;
  }
}

/* ===== ANIMAÇÕES - MANTÉM PERFORMANCE ===== */
.no-zoom-page * {
  /* Otimiza animações */
  will-change: auto !important;
  backface-visibility: hidden !important;
}

/* ===== DEBUGGING - CLASSE PARA TESTAR ===== */
.no-zoom-debug {
  position: fixed !important;
  top: 10px !important;
  right: 10px !important;
  background: rgba(255, 0, 0, 0.8) !important;
  color: white !important;
  padding: 5px 10px !important;
  border-radius: 5px !important;
  font-size: 12px !important;
  z-index: 9999 !important;
  pointer-events: none !important;
}

/* ===== CLASSES UTILITÁRIAS ===== */
.allow-zoom {
  touch-action: auto !important;
  -webkit-user-select: auto !important;
  user-select: auto !important;
  zoom: auto !important;
  transform: none !important;
}

.allow-select {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

.no-select {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* ===== MAPBOX ESPECÍFICO ===== */
.mapbox-gl-canvas {
  touch-action: auto !important;
}

.mapbox-gl-map {
  touch-action: auto !important;
}

/* ===== LEAFLET ESPECÍFICO ===== */
.leaflet-container {
  touch-action: auto !important;
}

.leaflet-control-zoom {
  touch-action: auto !important;
}
