import { cacheService } from './CacheService'

export interface PaymentProvider {
  id: string
  name: string
  type: 'credit_card' | 'debit_card' | 'digital_wallet' | 'pix' | 'cash'
  icon: string
  fees: {
    fixed: number
    percentage: number
  }
  processing_time: string
  supported_currencies: string[]
  is_active: boolean
}

export interface PaymentIntent {
  id: string
  amount: number
  currency: string
  provider: string
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'cancelled'
  client_secret?: string
  metadata: Record<string, any>
}

export interface WeatherData {
  location: string
  temperature: number
  condition: string
  humidity: number
  wind_speed: number
  precipitation: number
  visibility: number
  uv_index: number
  feels_like: number
  forecast: {
    hour: number
    temperature: number
    condition: string
    precipitation_chance: number
  }[]
}

export interface TrafficData {
  route_id: string
  segments: {
    start: { lat: number; lng: number }
    end: { lat: number; lng: number }
    distance_km: number
    duration_minutes: number
    speed_kmh: number
    congestion_level: 'free' | 'light' | 'moderate' | 'heavy' | 'severe'
    incidents: string[]
  }[]
  total_duration: number
  total_distance: number
  alternative_routes: number
}

export interface SMSMessage {
  to: string
  message: string
  type: 'verification' | 'notification' | 'emergency' | 'marketing'
  priority: 'low' | 'normal' | 'high'
}

class ExternalAPIService {
  private readonly API_ENDPOINTS = {
    weather: 'https://api.openweathermap.org/data/2.5',
    traffic: 'https://api.mapbox.com/directions/v5',
    payment_stripe: 'https://api.stripe.com/v1',
    payment_mercadopago: 'https://api.mercadopago.com/v1',
    sms_twilio: 'https://api.twilio.com/2010-04-01',
    geocoding: 'https://api.mapbox.com/geocoding/v5',
    places: 'https://api.mapbox.com/search/v1'
  }

  private readonly API_KEYS = {
    weather: import.meta.env.VITE_OPENWEATHER_API_KEY,
    mapbox: import.meta.env.VITE_MAPBOX_TOKEN,
    stripe: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY,
    mercadopago: import.meta.env.VITE_MERCADOPAGO_PUBLIC_KEY,
    twilio: import.meta.env.VITE_TWILIO_AUTH_TOKEN
  }

  /**
   * Weather API Integration
   */
  async getWeatherData(lat: number, lng: number): Promise<WeatherData | null> {
    const cacheKey = `weather_${lat.toFixed(3)}_${lng.toFixed(3)}`
    
    // Check cache first (15 minutes TTL)
    const cached = cacheService.get<WeatherData>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const response = await fetch(
        `${this.API_ENDPOINTS.weather}/weather?lat=${lat}&lon=${lng}&appid=${this.API_KEYS.weather}&units=metric&lang=pt_br`
      )

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status}`)
      }

      const data = await response.json()

      // Get hourly forecast
      const forecastResponse = await fetch(
        `${this.API_ENDPOINTS.weather}/forecast?lat=${lat}&lon=${lng}&appid=${this.API_KEYS.weather}&units=metric&lang=pt_br`
      )

      let forecast: WeatherData['forecast'] = []
      if (forecastResponse.ok) {
        const forecastData = await forecastResponse.json()
        forecast = forecastData.list.slice(0, 12).map((item: any) => ({
          hour: new Date(item.dt * 1000).getHours(),
          temperature: Math.round(item.main.temp),
          condition: item.weather[0].main.toLowerCase(),
          precipitation_chance: item.pop * 100
        }))
      }

      const weatherData: WeatherData = {
        location: data.name,
        temperature: Math.round(data.main.temp),
        condition: data.weather[0].main.toLowerCase(),
        humidity: data.main.humidity,
        wind_speed: data.wind.speed,
        precipitation: data.rain?.['1h'] || 0,
        visibility: data.visibility / 1000, // Convert to km
        uv_index: 0, // Would need UV API
        feels_like: Math.round(data.main.feels_like),
        forecast
      }

      // Cache for 15 minutes
      cacheService.set(cacheKey, weatherData, 15)
      
      return weatherData

    } catch (error) {
      console.error('❌ Weather API error:', error)
      return null
    }
  }

  /**
   * Traffic API Integration
   */
  async getTrafficData(
    waypoints: Array<{ lat: number; lng: number }>,
    profile: 'driving' | 'driving-traffic' = 'driving-traffic'
  ): Promise<TrafficData | null> {
    const waypointsStr = waypoints.map(w => `${w.lng},${w.lat}`).join(';')
    const cacheKey = `traffic_${waypointsStr}_${profile}`
    
    // Check cache first (5 minutes TTL)
    const cached = cacheService.get<TrafficData>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const response = await fetch(
        `${this.API_ENDPOINTS.traffic}/mapbox/${profile}/${waypointsStr}?` +
        `access_token=${this.API_KEYS.mapbox}&` +
        `steps=true&geometries=geojson&annotations=speed,duration,congestion`
      )

      if (!response.ok) {
        throw new Error(`Traffic API error: ${response.status}`)
      }

      const data = await response.json()
      
      if (!data.routes || data.routes.length === 0) {
        return null
      }

      const route = data.routes[0]
      const segments = route.legs[0].steps.map((step: any) => ({
        start: { lat: step.maneuver.location[1], lng: step.maneuver.location[0] },
        end: { lat: step.maneuver.location[1], lng: step.maneuver.location[0] }, // Simplified
        distance_km: step.distance / 1000,
        duration_minutes: step.duration / 60,
        speed_kmh: step.distance > 0 ? (step.distance / 1000) / (step.duration / 3600) : 0,
        congestion_level: this.mapCongestionLevel(step.congestion || 'unknown'),
        incidents: [] // Would need incidents API
      }))

      const trafficData: TrafficData = {
        route_id: `route_${Date.now()}`,
        segments,
        total_duration: route.duration / 60,
        total_distance: route.distance / 1000,
        alternative_routes: data.routes.length - 1
      }

      // Cache for 5 minutes
      cacheService.set(cacheKey, trafficData, 5)
      
      return trafficData

    } catch (error) {
      console.error('❌ Traffic API error:', error)
      return null
    }
  }

  /**
   * Payment Processing
   */
  async createPaymentIntent(
    amount: number,
    currency: string = 'BRL',
    provider: 'stripe' | 'mercadopago' = 'stripe',
    metadata: Record<string, any> = {}
  ): Promise<PaymentIntent | null> {
    try {
      if (provider === 'stripe') {
        return await this.createStripePaymentIntent(amount, currency, metadata)
      } else if (provider === 'mercadopago') {
        return await this.createMercadoPagoPaymentIntent(amount, currency, metadata)
      }
      
      throw new Error(`Unsupported payment provider: ${provider}`)

    } catch (error) {
      console.error('❌ Payment intent creation error:', error)
      return null
    }
  }

  private async createStripePaymentIntent(
    amount: number,
    currency: string,
    metadata: Record<string, any>
  ): Promise<PaymentIntent> {
    const response = await fetch(`${this.API_ENDPOINTS.payment_stripe}/payment_intents`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.API_KEYS.stripe}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        amount: (amount * 100).toString(), // Stripe uses cents
        currency: currency.toLowerCase(),
        metadata: JSON.stringify(metadata)
      })
    })

    if (!response.ok) {
      throw new Error(`Stripe API error: ${response.status}`)
    }

    const data = await response.json()

    return {
      id: data.id,
      amount: data.amount / 100,
      currency: data.currency.toUpperCase(),
      provider: 'stripe',
      status: data.status,
      client_secret: data.client_secret,
      metadata: data.metadata
    }
  }

  private async createMercadoPagoPaymentIntent(
    amount: number,
    currency: string,
    metadata: Record<string, any>
  ): Promise<PaymentIntent> {
    const response = await fetch(`${this.API_ENDPOINTS.payment_mercadopago}/payments`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.API_KEYS.mercadopago}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        transaction_amount: amount,
        currency_id: currency,
        description: 'MobiDrive Ride Payment',
        metadata
      })
    })

    if (!response.ok) {
      throw new Error(`MercadoPago API error: ${response.status}`)
    }

    const data = await response.json()

    return {
      id: data.id.toString(),
      amount: data.transaction_amount,
      currency: data.currency_id,
      provider: 'mercadopago',
      status: this.mapMercadoPagoStatus(data.status),
      metadata: data.metadata
    }
  }

  /**
   * SMS Integration
   */
  async sendSMS(message: SMSMessage): Promise<boolean> {
    try {
      // In a real implementation, integrate with Twilio or similar
      console.log(`📱 Sending SMS to ${message.to}: ${message.message}`)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return true

    } catch (error) {
      console.error('❌ SMS sending error:', error)
      return false
    }
  }

  /**
   * Enhanced Geocoding
   */
  async geocodeAddress(address: string): Promise<any | null> {
    const cacheKey = `geocode_${address.toLowerCase()}`
    
    // Check cache first (1 hour TTL)
    const cached = cacheService.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const encodedAddress = encodeURIComponent(address)
      const response = await fetch(
        `${this.API_ENDPOINTS.geocoding}/mapbox.places/${encodedAddress}.json?` +
        `access_token=${this.API_KEYS.mapbox}&` +
        `country=BR&language=pt&limit=5`
      )

      if (!response.ok) {
        throw new Error(`Geocoding API error: ${response.status}`)
      }

      const data = await response.json()
      
      // Cache for 1 hour
      cacheService.set(cacheKey, data, 60)
      
      return data

    } catch (error) {
      console.error('❌ Geocoding error:', error)
      return null
    }
  }

  /**
   * Places Search
   */
  async searchPlaces(
    query: string,
    proximity?: { lat: number; lng: number },
    types?: string[]
  ): Promise<any | null> {
    try {
      let url = `${this.API_ENDPOINTS.places}/suggest/${encodeURIComponent(query)}?` +
                `access_token=${this.API_KEYS.mapbox}&` +
                `country=BR&language=pt&limit=10`

      if (proximity) {
        url += `&proximity=${proximity.lng},${proximity.lat}`
      }

      if (types && types.length > 0) {
        url += `&types=${types.join(',')}`
      }

      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`Places API error: ${response.status}`)
      }

      return await response.json()

    } catch (error) {
      console.error('❌ Places search error:', error)
      return null
    }
  }

  /**
   * Get available payment providers
   */
  getPaymentProviders(): PaymentProvider[] {
    return [
      {
        id: 'stripe_card',
        name: 'Cartão de Crédito/Débito',
        type: 'credit_card',
        icon: '💳',
        fees: { fixed: 0.39, percentage: 2.9 },
        processing_time: 'Instantâneo',
        supported_currencies: ['BRL', 'USD'],
        is_active: true
      },
      {
        id: 'pix',
        name: 'PIX',
        type: 'pix',
        icon: '🏦',
        fees: { fixed: 0, percentage: 0.99 },
        processing_time: 'Instantâneo',
        supported_currencies: ['BRL'],
        is_active: true
      },
      {
        id: 'mercadopago',
        name: 'Mercado Pago',
        type: 'digital_wallet',
        icon: '💰',
        fees: { fixed: 0, percentage: 3.99 },
        processing_time: 'Instantâneo',
        supported_currencies: ['BRL'],
        is_active: true
      },
      {
        id: 'cash',
        name: 'Dinheiro',
        type: 'cash',
        icon: '💵',
        fees: { fixed: 0, percentage: 0 },
        processing_time: 'Na entrega',
        supported_currencies: ['BRL'],
        is_active: true
      }
    ]
  }

  /**
   * Helper methods
   */
  private mapCongestionLevel(congestion: string): TrafficData['segments'][0]['congestion_level'] {
    const mapping = {
      'low': 'light',
      'moderate': 'moderate',
      'heavy': 'heavy',
      'severe': 'severe'
    } as const

    return mapping[congestion as keyof typeof mapping] || 'free'
  }

  private mapMercadoPagoStatus(status: string): PaymentIntent['status'] {
    const mapping = {
      'pending': 'pending',
      'approved': 'succeeded',
      'authorized': 'processing',
      'in_process': 'processing',
      'in_mediation': 'processing',
      'rejected': 'failed',
      'cancelled': 'cancelled',
      'refunded': 'cancelled',
      'charged_back': 'failed'
    } as const

    return mapping[status as keyof typeof mapping] || 'pending'
  }

  /**
   * Health check for external APIs
   */
  async healthCheck(): Promise<Record<string, boolean>> {
    const checks = {
      weather: false,
      traffic: false,
      payment_stripe: false,
      payment_mercadopago: false,
      geocoding: false
    }

    // Test weather API
    try {
      const response = await fetch(`${this.API_ENDPOINTS.weather}/weather?lat=-23.5505&lon=-46.6333&appid=${this.API_KEYS.weather}`)
      checks.weather = response.ok
    } catch {
      checks.weather = false
    }

    // Test geocoding API
    try {
      const response = await fetch(`${this.API_ENDPOINTS.geocoding}/mapbox.places/test.json?access_token=${this.API_KEYS.mapbox}`)
      checks.geocoding = response.ok || response.status === 404 // 404 is expected for invalid query
    } catch {
      checks.geocoding = false
    }

    // Add other API checks...

    return checks
  }
}

export const externalAPIService = new ExternalAPIService()
export default externalAPIService
