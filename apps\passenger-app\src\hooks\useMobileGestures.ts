import { useRef, useCallback, useEffect } from 'react';

// 📱 MOBILE GESTURES HOOK
// Simula gestos móveis para desktop: drag, momentum, bounce effects

interface MobileGesturesConfig {
  enableVerticalScroll?: boolean;
  enableHorizontalScroll?: boolean;
  momentumDecay?: number;
  bounceStiffness?: number;
  bounceDamping?: number;
  maxBounceDistance?: number;
}

interface GestureState {
  isDragging: boolean;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
  velocityX: number;
  velocityY: number;
  lastTime: number;
}

export const useMobileGestures = (config: MobileGesturesConfig = {}) => {
  const {
    enableVerticalScroll = true,
    enableHorizontalScroll = false,
    momentumDecay = 0.95,
    bounceStiffness = 0.3,
    bounceDamping = 0.8,
    maxBounceDistance = 50
  } = config;

  const containerRef = useRef<HTMLDivElement>(null);
  const gestureState = useRef<GestureState>({
    isDragging: false,
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
    velocityX: 0,
    velocityY: 0,
    lastTime: 0
  });

  const animationFrame = useRef<number>();
  const scrollPosition = useRef({ x: 0, y: 0 });

  // Calcula velocidade baseada no movimento
  const calculateVelocity = useCallback((deltaX: number, deltaY: number, deltaTime: number) => {
    if (deltaTime === 0) return { vx: 0, vy: 0 };
    return {
      vx: deltaX / deltaTime,
      vy: deltaY / deltaTime
    };
  }, []);

  // Aplica momentum scrolling
  const applyMomentum = useCallback(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const state = gestureState.current;

    // Aplica decay na velocidade
    state.velocityX *= momentumDecay;
    state.velocityY *= momentumDecay;

    // Atualiza posição do scroll
    if (enableHorizontalScroll) {
      scrollPosition.current.x += state.velocityX;
    }
    if (enableVerticalScroll) {
      scrollPosition.current.y += state.velocityY;
    }

    // Implementa bounce effects nos limites
    const maxScrollY = container.scrollHeight - container.clientHeight;
    const maxScrollX = container.scrollWidth - container.clientWidth;

    // Bounce vertical
    if (enableVerticalScroll) {
      if (scrollPosition.current.y < 0) {
        scrollPosition.current.y *= bounceDamping;
        state.velocityY *= -bounceStiffness;
      } else if (scrollPosition.current.y > maxScrollY) {
        const excess = scrollPosition.current.y - maxScrollY;
        scrollPosition.current.y = maxScrollY + (excess * bounceDamping);
        state.velocityY *= -bounceStiffness;
      }
    }

    // Bounce horizontal
    if (enableHorizontalScroll) {
      if (scrollPosition.current.x < 0) {
        scrollPosition.current.x *= bounceDamping;
        state.velocityX *= -bounceStiffness;
      } else if (scrollPosition.current.x > maxScrollX) {
        const excess = scrollPosition.current.x - maxScrollX;
        scrollPosition.current.x = maxScrollX + (excess * bounceDamping);
        state.velocityX *= -bounceStiffness;
      }
    }

    // Aplica o scroll
    container.scrollTo({
      left: Math.max(0, Math.min(maxScrollX, scrollPosition.current.x)),
      top: Math.max(0, Math.min(maxScrollY, scrollPosition.current.y)),
      behavior: 'auto'
    });

    // Continua animação se ainda há velocidade
    if (Math.abs(state.velocityX) > 0.1 || Math.abs(state.velocityY) > 0.1) {
      animationFrame.current = requestAnimationFrame(applyMomentum);
    }
  }, [enableVerticalScroll, enableHorizontalScroll, momentumDecay, bounceStiffness, bounceDamping]);

  // Handlers de mouse
  const handleMouseDown = useCallback((e: MouseEvent) => {
    if (!containerRef.current) return;

    const state = gestureState.current;
    state.isDragging = true;
    state.startX = e.clientX;
    state.startY = e.clientY;
    state.currentX = e.clientX;
    state.currentY = e.clientY;
    state.velocityX = 0;
    state.velocityY = 0;
    state.lastTime = Date.now();

    // Para animação de momentum anterior
    if (animationFrame.current) {
      cancelAnimationFrame(animationFrame.current);
    }

    // Atualiza posição inicial do scroll
    scrollPosition.current.x = containerRef.current.scrollLeft;
    scrollPosition.current.y = containerRef.current.scrollTop;

    // Adiciona cursor de dragging
    document.body.style.cursor = 'grabbing';
    document.body.style.userSelect = 'none';

    e.preventDefault();
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    const state = gestureState.current;
    if (!state.isDragging || !containerRef.current) return;

    const currentTime = Date.now();
    const deltaTime = currentTime - state.lastTime;
    
    const deltaX = e.clientX - state.currentX;
    const deltaY = e.clientY - state.currentY;

    // Calcula velocidade
    const { vx, vy } = calculateVelocity(deltaX, deltaY, deltaTime);
    state.velocityX = vx;
    state.velocityY = vy;

    // Atualiza posição
    if (enableHorizontalScroll) {
      scrollPosition.current.x -= deltaX;
    }
    if (enableVerticalScroll) {
      scrollPosition.current.y -= deltaY;
    }

    // Aplica scroll imediato
    const container = containerRef.current;
    const maxScrollY = container.scrollHeight - container.clientHeight;
    const maxScrollX = container.scrollWidth - container.clientWidth;

    container.scrollTo({
      left: Math.max(0, Math.min(maxScrollX, scrollPosition.current.x)),
      top: Math.max(0, Math.min(maxScrollY, scrollPosition.current.y)),
      behavior: 'auto'
    });

    state.currentX = e.clientX;
    state.currentY = e.clientY;
    state.lastTime = currentTime;

    e.preventDefault();
  }, [enableVerticalScroll, enableHorizontalScroll, calculateVelocity]);

  const handleMouseUp = useCallback(() => {
    const state = gestureState.current;
    if (!state.isDragging) return;

    state.isDragging = false;

    // Remove cursor de dragging
    document.body.style.cursor = '';
    document.body.style.userSelect = '';

    // Inicia momentum scrolling se há velocidade suficiente
    if (Math.abs(state.velocityX) > 1 || Math.abs(state.velocityY) > 1) {
      animationFrame.current = requestAnimationFrame(applyMomentum);
    }
  }, [applyMomentum]);

  // Setup e cleanup dos event listeners
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mouseleave', handleMouseUp);

    // Previne scroll padrão
    container.style.overflow = 'hidden';
    container.style.cursor = 'grab';

    return () => {
      container.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mouseleave', handleMouseUp);

      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
      }

      // Restaura cursor
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [handleMouseDown, handleMouseMove, handleMouseUp]);

  return {
    containerRef,
    isDragging: gestureState.current.isDragging
  };
};
