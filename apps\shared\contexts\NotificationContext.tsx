import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from '../auth';
import notificationService, { 
  Notification, 
  NotificationType, 
  NotificationOptions 
} from '../services/NotificationService';
import audioService, { NotificationSoundType } from '../services/AudioService';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  showNotification: (options: NotificationOptions) => void;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  deleteAllNotifications: () => Promise<void>;
  soundsEnabled: boolean;
  toggleSounds: () => void;
  playSound: (type: NotificationSoundType) => Promise<void>;
}

interface NotificationProviderProps {
  children: React.ReactNode;
  supabase: any;
  maxNotifications?: number;
  toastFunction?: (options: any) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
  supabase,
  maxNotifications = 50,
  toastFunction
}) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [soundsEnabled, setSoundsEnabled] = useState<boolean>(
    () => localStorage.getItem('mobidrive-notificationSoundsEnabled') !== 'false'
  );

  // Configurar serviços
  useEffect(() => {
    if (toastFunction) {
      notificationService.setToastFunction(toastFunction);
    }
    if (supabase) {
      notificationService.setSupabaseClient(supabase);
    }
  }, [toastFunction, supabase]);

  // Carregar notificações iniciais
  useEffect(() => {
    const loadNotifications = async () => {
      if (!user || !supabase) return;

      try {
        const { data, error } = await supabase
          .from('notifications')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(maxNotifications);

        if (error) {
          console.error('Erro ao carregar notificações:', error);
          return;
        }

        if (data) {
          setNotifications(data);
          setUnreadCount(data.filter((n: Notification) => !n.is_read).length);
        }
      } catch (error) {
        console.error('Erro ao carregar notificações:', error);
      }
    };

    loadNotifications();
  }, [user, supabase, maxNotifications]);

  // Inscrever-se para notificações em tempo real
  useEffect(() => {
    if (!user || !supabase) return;

    // Pré-carregar sons de notificação
    audioService.preloadAllNotificationSounds();

    // Inscrever-se para notificações em tempo real
    const { unsubscribe } = notificationService.subscribeToNotifications(
      user.id,
      (newNotification) => {
        // Adicionar nova notificação à lista
        setNotifications(prev => [newNotification, ...prev].slice(0, maxNotifications));
        setUnreadCount(prev => prev + 1);

        // Mostrar notificação na interface
        notificationService.showNotification({
          title: newNotification.title,
          message: newNotification.message,
          type: newNotification.type as NotificationType,
          metadata: newNotification.metadata
        });
      }
    );

    return () => {
      unsubscribe();
    };
  }, [user, supabase, maxNotifications]);

  // Mostrar notificação
  const showNotification = useCallback((options: NotificationOptions) => {
    notificationService.showNotification(options);
  }, []);

  // Marcar notificação como lida
  const markAsRead = useCallback(async (notificationId: string) => {
    if (!user || !supabase) return;

    try {
      const { error } = await supabase
        .from('notifications')
        .update({ 
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('id', notificationId);

      if (error) {
        console.error('Erro ao marcar notificação como lida:', error);
        return;
      }

      // Atualizar estado local
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId 
            ? { ...n, is_read: true, read_at: new Date().toISOString() } 
            : n
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Erro ao marcar notificação como lida:', error);
    }
  }, [user, supabase]);

  // Marcar todas as notificações como lidas
  const markAllAsRead = useCallback(async () => {
    if (!user || !supabase) return;

    try {
      const { error } = await supabase
        .from('notifications')
        .update({ 
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) {
        console.error('Erro ao marcar todas as notificações como lidas:', error);
        return;
      }

      // Atualizar estado local
      setNotifications(prev => 
        prev.map(n => ({ ...n, is_read: true, read_at: new Date().toISOString() }))
      );
      setUnreadCount(0);
    } catch (error) {
      console.error('Erro ao marcar todas as notificações como lidas:', error);
    }
  }, [user, supabase]);

  // Excluir notificação
  const deleteNotification = useCallback(async (notificationId: string) => {
    if (!user || !supabase) return;

    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) {
        console.error('Erro ao excluir notificação:', error);
        return;
      }

      // Atualizar estado local
      const deletedNotification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      
      if (deletedNotification && !deletedNotification.is_read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Erro ao excluir notificação:', error);
    }
  }, [user, supabase, notifications]);

  // Excluir todas as notificações
  const deleteAllNotifications = useCallback(async () => {
    if (!user || !supabase) return;

    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Erro ao excluir todas as notificações:', error);
        return;
      }

      // Atualizar estado local
      setNotifications([]);
      setUnreadCount(0);
    } catch (error) {
      console.error('Erro ao excluir todas as notificações:', error);
    }
  }, [user, supabase]);

  // Alternar sons de notificação
  const toggleSounds = useCallback(() => {
    const newState = !soundsEnabled;
    setSoundsEnabled(newState);
    localStorage.setItem('mobidrive-notificationSoundsEnabled', newState.toString());
    notificationService.setSoundsEnabled(newState);
  }, [soundsEnabled]);

  // Reproduzir som
  const playSound = useCallback(async (type: NotificationSoundType) => {
    if (soundsEnabled) {
      await audioService.playNotificationSound(type);
    }
  }, [soundsEnabled]);

  // Atualizar serviço quando o estado de som mudar
  useEffect(() => {
    notificationService.setSoundsEnabled(soundsEnabled);
  }, [soundsEnabled]);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        showNotification,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        deleteAllNotifications,
        soundsEnabled,
        toggleSounds,
        playSound
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export default NotificationProvider;
