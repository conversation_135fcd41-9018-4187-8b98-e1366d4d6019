// =====================================================
// TESTE DA CORREÇÃO DOS MÉTODOS DE PAGAMENTO
// Verifica se a duplicação foi corrigida
// =====================================================

import puppeteer from 'puppeteer'

async function testPaymentFix() {
  console.log('💳 TESTANDO CORREÇÃO DOS MÉTODOS DE PAGAMENTO')
  console.log('=' .repeat(50))

  let browser = null
  
  try {
    browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })

    const page = await browser.newPage()
    await page.setViewport({ width: 1280, height: 720 })

    let paymentMethodsCount = 0
    let duplicateErrors = 0

    // Monitorar console
    page.on('console', (msg) => {
      const text = msg.text()
      
      if (text.includes('Payment methods loaded:')) {
        console.log(`💳 ${text}`)
      } else if (text.includes('unique payment methods')) {
        console.log(`✅ ${text}`)
      } else if (text.includes('CONTAINER .device-wrapper-container NOT FOUND')) {
        // Ignorar - já corrigido
      }
    })

    console.log('\n🌐 Carregando página de login...')
    await page.goto('http://localhost:3000/login', { 
      waitUntil: 'domcontentloaded',
      timeout: 15000 
    })
    
    console.log('🔐 Fazendo login...')
    await page.type('input[type="email"]', '<EMAIL>')
    await page.type('input[type="password"]', 'Test123!')
    await page.click('button[type="submit"]')
    
    console.log('⏱️ Aguardando redirecionamento...')
    await page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 10000 })
    
    console.log('🗺️ Navegando para detalhes da corrida...')
    await page.goto('http://localhost:3000/ride-request/details', { 
      waitUntil: 'domcontentloaded',
      timeout: 10000 
    })
    
    console.log('⏱️ Aguardando carregamento dos métodos...')
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    // Contar métodos de pagamento na página
    const paymentButtons = await page.$$('button:has-text("Dinheiro")')
    paymentMethodsCount = paymentButtons.length
    
    // Verificar se há múltiplos métodos "Dinheiro"
    const allPaymentTexts = await page.$$eval('button', buttons => 
      buttons
        .filter(btn => btn.textContent.includes('Dinheiro'))
        .map(btn => btn.textContent.trim())
    )
    
    console.log('\n📊 RESULTADO DO TESTE:')
    console.log(`💳 Métodos "Dinheiro" encontrados: ${paymentMethodsCount}`)
    console.log(`📝 Textos dos métodos:`, allPaymentTexts)
    
    if (paymentMethodsCount === 1) {
      console.log('\n🎉 CORREÇÃO FUNCIONOU!')
      console.log('✅ Apenas 1 método "Dinheiro" encontrado')
      return true
    } else if (paymentMethodsCount > 1) {
      console.log('\n❌ PROBLEMA AINDA PERSISTE')
      console.log(`⚠️ ${paymentMethodsCount} métodos "Dinheiro" duplicados`)
      return false
    } else {
      console.log('\n⚠️ NENHUM MÉTODO ENCONTRADO')
      console.log('❓ Possível problema de carregamento')
      return false
    }

  } catch (error) {
    console.error('💥 Erro no teste:', error.message)
    return false
  } finally {
    if (browser) {
      await browser.close()
    }
  }
}

testPaymentFix()
  .then((success) => {
    console.log('\n🏁 TESTE DA CORREÇÃO CONCLUÍDO!')
    console.log(success ? '✅ SUCESSO' : '❌ FALHA')
    process.exit(success ? 0 : 1)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
