import { createClient } from '@supabase/supabase-js'

// Configuração do Supabase com fallbacks
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

// Validar se as variáveis estão definidas
if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('⚠️ Configuração do Supabase incompleta - usando modo offline:', {
    url: supabaseUrl,
    hasKey: !!supabaseAnonKey
  })
  // Não lançar erro para permitir que o app funcione sem Supabase
} else {
  console.log('✅ Supabase configurado:', {
    url: supabaseUrl,
    hasKey: !!supabaseAnonKey
  })
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    storageKey: 'mobidrive-passenger-auth',
    detectSessionInUrl: false,
  },
  realtime: {
    autoconnect: true, // ✅ HABILITADO: Realtime ativo para corridas
  },
  global: {
    headers: {
      'X-Client-Info': 'MobiDrive-Passenger/1.0.0',
    },
  },
})

// Serviço de localização em tempo real (simplificado para evitar stack overflow)
export class RealtimeLocationService {
  private isTracking = false
  private pollInterval: NodeJS.Timeout | null = null

  // Iniciar tracking de motoristas próximos (modo polling apenas)
  startDriverTracking(userLocation: { lat: number; lng: number }, callback: (drivers: any[]) => void) {
    try {
      console.log('🚗 Iniciando tracking de motoristas (modo polling)')

      // Parar tracking anterior se existir
      this.stopDriverTracking()

      this.isTracking = true

      // Buscar motoristas iniciais
      this.fetchNearbyDrivers(userLocation, callback)

      // Configurar polling a cada 15 segundos
      this.pollInterval = setInterval(() => {
        if (this.isTracking) {
          this.fetchNearbyDrivers(userLocation, callback)
        }
      }, 15000)

      console.log('✅ Tracking de motoristas ativo (polling mode)')

    } catch (error) {
      console.error('Erro ao iniciar tracking:', error)
      // Fallback: buscar motoristas uma vez
      this.fetchNearbyDrivers(userLocation, callback)
    }
  }

  // Parar tracking com cleanup seguro
  stopDriverTracking() {
    try {
      if (this.pollInterval) {
        clearInterval(this.pollInterval)
        this.pollInterval = null
      }
      this.isTracking = false
      console.log('🛑 Tracking de motoristas parado')
    } catch (error) {
      console.error('Erro ao parar tracking:', error)
      // Force cleanup
      this.pollInterval = null
      this.isTracking = false
    }
  }

  // Buscar motoristas próximos usando PostGIS
  private async fetchNearbyDrivers(userLocation: { lat: number; lng: number }, callback: (drivers: any[]) => void) {
    try {
      const { data: drivers, error } = await supabase
        .rpc('get_nearby_drivers', {
          user_lat: userLocation.lat,
          user_lng: userLocation.lng,
          radius_km: 10 // 10km de raio
        })

      if (error) {
        console.error('Erro ao buscar motoristas:', error)
        return
      }

      callback(drivers || [])
    } catch (error) {
      console.error('Erro na busca de motoristas:', error)
    }
  }

  // Atualizar localização do usuário
  async updateUserLocation(userId: string, location: { lat: number; lng: number }) {
    try {
      const { error } = await supabase
        .from('user_locations')
        .upsert({
          user_id: userId,
          latitude: location.lat,
          longitude: location.lng,
          updated_at: new Date().toISOString()
        })

      if (error) {
        console.error('Erro ao atualizar localização:', error)
      }
    } catch (error) {
      console.error('Erro ao atualizar localização:', error)
    }
  }
}

// Tabelas do banco
export const TABLES = {
  profiles: 'profiles',
  drivers: 'drivers',
  rides: 'rides',
  ride_requests: 'ride_requests',
  driver_locations: 'driver_locations',
  user_locations: 'user_locations', // Adicionada para tracking de passageiros
  payments: 'payments',
  payment_methods: 'payment_methods',
  chat_messages: 'chat_messages',
  notifications: 'notifications',
  app_settings: 'app_settings',
} as const

export default supabase
