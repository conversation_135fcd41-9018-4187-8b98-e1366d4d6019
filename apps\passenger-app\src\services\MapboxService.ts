import mapboxgl from 'mapbox-gl'
import { mapboxCacheService } from './MapboxCacheService'
import { mapboxAnalyticsService } from './MapboxAnalyticsService'
import { locationHistoryService } from './LocationHistoryService'

// Configuração robusta do token Mapbox
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN ||
                     (window as any).MAPBOX_ACCESS_TOKEN ||
                     (window as any).MAPBOX_TOKEN ||
                     'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'

console.log('🔧 MapboxService: Configurando token...')
console.log('📊 Token sources:', {
  'import.meta.env': !!import.meta.env.VITE_MAPBOX_ACCESS_TOKEN,
  'window.MAPBOX_ACCESS_TOKEN': !!(window as any).MAPBOX_ACCESS_TOKEN,
  'window.MAPBOX_TOKEN': !!(window as any).MAPBOX_TOKEN,
  'fallback': true
})

mapboxgl.accessToken = MAPBOX_TOKEN
console.log('✅ MapboxService: Token configurado:', MAPBOX_TOKEN.substring(0, 20) + '...')

// Mapbox API endpoints
const MAPBOX_API_BASE = 'https://api.mapbox.com'
const GEOCODING_API = `${MAPBOX_API_BASE}/geocoding/v5/mapbox.places`
const DIRECTIONS_API = `${MAPBOX_API_BASE}/directions/v5/mapbox/driving`
const MATRIX_API = `${MAPBOX_API_BASE}/directions-matrix/v1/mapbox/driving`
const ISOCHRONE_API = `${MAPBOX_API_BASE}/isochrone/v1/mapbox/driving`
const OPTIMIZATION_API = `${MAPBOX_API_BASE}/optimized-trips/v1/mapbox/driving`
const MAP_MATCHING_API = `${MAPBOX_API_BASE}/matching/v5/mapbox/driving`

// Error types
export class MapboxError extends Error {
  constructor(message: string, public code?: string, public details?: any) {
    super(message)
    this.name = 'MapboxError'
  }
}

// Rate limiting
class RateLimiter {
  private requests: number[] = []
  private readonly maxRequests = import.meta.env.DEV ? 5 : 30 // Very conservative in development
  private readonly timeWindow = 60000 // 1 minute
  private lastRequestTime = 0
  private readonly minInterval = 2000 // Minimum 2 seconds between requests (increased)

  canMakeRequest(): boolean {
    const now = Date.now()

    // Check minimum interval between requests
    if (now - this.lastRequestTime < this.minInterval) {
      return false
    }

    this.requests = this.requests.filter(time => now - time < this.timeWindow)
    return this.requests.length < this.maxRequests
  }

  recordRequest(): void {
    const now = Date.now()
    this.requests.push(now)
    this.lastRequestTime = now
  }

  getStats() {
    const now = Date.now()
    const recentRequests = this.requests.filter(time => now - time < this.timeWindow)
    return {
      currentRequests: recentRequests.length,
      maxRequests: this.maxRequests,
      remainingRequests: this.maxRequests - recentRequests.length,
      resetTime: recentRequests.length > 0 ? Math.max(...recentRequests) + this.timeWindow : now
    }
  }
}

const rateLimiter = new RateLimiter()

// Types
export interface SearchResult {
  id: string
  place_name: string
  center: [number, number]
  place_type: string[]
  properties: {
    address?: string
    category?: string
  }
  context?: Array<{
    id: string
    text: string
  }>
}

export interface RouteResult {
  distance: number
  duration: number
  geometry: {
    coordinates: [number, number][]
  }
  legs: Array<{
    distance: number
    duration: number
    steps: Array<{
      instruction: string
      distance: number
      duration: number
    }>
  }>
}

export interface RideEstimate {
  distance: number
  duration: number
  price: number
  route: RouteResult
  vehicleType?: string
}

export interface DriverLocation {
  id: string
  name: string
  coordinates: [number, number]
  eta: number
  rating: number
  vehicle: {
    model: string
    plate: string
    color: string
  }
}

export class MapboxService {
  private accessToken: string

  constructor() {
    this.accessToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || MAPBOX_TOKEN
    if (!this.accessToken) {
      console.error('❌ Mapbox token não encontrado!')
      console.error('import.meta.env.VITE_MAPBOX_ACCESS_TOKEN:', import.meta.env.VITE_MAPBOX_ACCESS_TOKEN)
      console.error('MAPBOX_TOKEN fallback:', MAPBOX_TOKEN)
      throw new Error('Mapbox access token is required')
    }
    console.log('✅ MapboxService inicializado com token:', this.accessToken.substring(0, 20) + '...')
  }

  /**
   * Search for places using Mapbox Geocoding API with enhanced proximity bias
   */
  async searchPlaces(query: string, options?: {
    proximity?: [number, number]
    bbox?: [number, number, number, number]
    types?: string[]
    limit?: number
    biasStrength?: 'weak' | 'medium' | 'strong'
  }): Promise<SearchResult[]> {
    if (!query.trim()) return []

    const startTime = Date.now()

    // Check cache first
    const cachedResults = mapboxCacheService.getCachedSearchResults(query, options)
    if (cachedResults) {
      const latency = Date.now() - startTime
      mapboxAnalyticsService.trackSearch(query, cachedResults, latency, true)
      locationHistoryService.addRecentSearch(query, cachedResults[0], false)
      return this.enhanceResultsWithDistance(cachedResults, options?.proximity)
    }

    // Rate limiting check
    if (!rateLimiter.canMakeRequest()) {
      mapboxAnalyticsService.trackError('RATE_LIMIT', 'Rate limit exceeded', { query })
      throw new MapboxError('Rate limit exceeded. Please wait before making more requests.', 'RATE_LIMIT')
    }

    const params = new URLSearchParams({
      access_token: this.accessToken,
      country: 'BR', // Use uppercase for better compatibility
      language: 'pt-BR',
      types: (options?.types || ['address', 'poi', 'place']).join(','),
      limit: (options?.limit || 15).toString(), // Increased limit for better filtering
      autocomplete: 'true',
      fuzzyMatch: 'true'
    })



    // Enhanced proximity bias
    if (options?.proximity) {
      params.append('proximity', options.proximity.join(','))

      // Add proximity bias strength
      const biasStrength = options.biasStrength || 'strong' // Default to strong for better UX
      switch (biasStrength) {
        case 'strong':
          // Create a smaller bounding box around the user location for stronger bias
          const radius = 0.02 // ~2km radius for very local results
          const bbox = [
            options.proximity[0] - radius,
            options.proximity[1] - radius,
            options.proximity[0] + radius,
            options.proximity[1] + radius
          ]
          params.append('bbox', bbox.join(','))
          break
        case 'medium':
          // Medium radius for city-wide results
          const mediumRadius = 0.05 // ~5km radius
          const mediumBbox = [
            options.proximity[0] - mediumRadius,
            options.proximity[1] - mediumRadius,
            options.proximity[0] + mediumRadius,
            options.proximity[1] + mediumRadius
          ]
          params.append('bbox', mediumBbox.join(','))
          break
        case 'weak':
          // Use only proximity without bbox
          break
      }
    }

    if (options?.bbox) {
      params.append('bbox', options.bbox.join(','))
    }

    if (options?.types) {
      params.append('types', options.types.join(','))
    }

    try {
      rateLimiter.recordRequest()

      const url = `${GEOCODING_API}/${encodeURIComponent(query)}.json?${params}`

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        signal: AbortSignal.timeout(10000) // 10 second timeout
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new MapboxError(
          `Geocoding API error: ${response.status}`,
          'API_ERROR',
          { status: response.status, ...errorData }
        )
      }

      const data = await response.json()

      if (!data.features) {
        throw new MapboxError('Invalid response format from Geocoding API', 'INVALID_RESPONSE', data)
      }

      // Filter and enhance results with distance calculation
      let results = data.features
        .filter((feature: any) => feature.center && feature.place_name)
        .map((feature: any) => ({
          id: feature.id || `${feature.center[0]}-${feature.center[1]}`,
          place_name: feature.place_name,
          center: feature.center,
          place_type: feature.place_type || [],
          properties: feature.properties || {},
          context: feature.context || [],
          text: feature.text || feature.place_name?.split(',')[0] || ''
        }))

      // Enhance with distance and sort by proximity if user location is available
      results = this.enhanceResultsWithDistance(results, options?.proximity)

      // Apply final limit after sorting
      results = results.slice(0, options?.limit || 8)

      // Cache results and track analytics
      const latency = Date.now() - startTime
      mapboxCacheService.cacheSearchResults(query, results, options)
      mapboxAnalyticsService.trackSearch(query, results, latency, false)

      // Add to recent searches if we have results
      if (results.length > 0) {
        locationHistoryService.addRecentSearch(query, results[0], false)
      }

      return results

    } catch (error) {
      const latency = Date.now() - startTime

      if (error instanceof MapboxError) {
        mapboxAnalyticsService.trackError(error.code || 'MAPBOX_ERROR', error.message, { query, latency })
        throw error
      }

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          mapboxAnalyticsService.trackError('TIMEOUT', 'Request timeout', { query, latency })
          throw new MapboxError('Request timeout', 'TIMEOUT')
        }
        mapboxAnalyticsService.trackError('NETWORK_ERROR', error.message, { query, latency })
        throw new MapboxError(`Search failed: ${error.message}`, 'NETWORK_ERROR', error)
      }

      mapboxAnalyticsService.trackError('UNKNOWN_ERROR', 'Unknown error occurred', { query, latency, error })
      throw new MapboxError('Unknown error occurred during search', 'UNKNOWN_ERROR', error)
    }
  }

  /**
   * Get enhanced directions between two points with traffic and optimization
   */
  async getDirections(
    origin: [number, number],
    destination: [number, number],
    options?: {
      alternatives?: boolean
      steps?: boolean
      geometries?: 'geojson' | 'polyline'
      profile?: 'driving' | 'walking' | 'cycling' | 'driving-traffic'
      avoidTolls?: boolean
      avoidFerries?: boolean
      avoidHighways?: boolean
    }
  ): Promise<RouteResult[]> {
    // Validate coordinates
    if (!this.isValidCoordinate(origin) || !this.isValidCoordinate(destination)) {
      throw new MapboxError('Invalid coordinates provided', 'INVALID_COORDINATES')
    }

    // Rate limiting check
    if (!rateLimiter.canMakeRequest()) {
      throw new MapboxError('Rate limit exceeded. Please wait before making more requests.', 'RATE_LIMIT')
    }

    const profile = options?.profile || 'driving-traffic' // Default to traffic-aware routing
    const params = new URLSearchParams({
      access_token: this.accessToken,
      alternatives: (options?.alternatives !== false).toString(),
      steps: (options?.steps !== false).toString(),
      geometries: options?.geometries || 'geojson',
      overview: 'full',
      language: 'pt-BR',
      annotations: 'distance,duration,speed,congestion', // Enhanced annotations
      continue_straight: 'true',
      waypoint_snapping: 'any'
    })

    // Add avoidance options
    const excludeOptions = []
    if (options?.avoidTolls) excludeOptions.push('toll')
    if (options?.avoidFerries) excludeOptions.push('ferry')
    if (options?.avoidHighways) excludeOptions.push('motorway')

    if (excludeOptions.length > 0) {
      params.append('exclude', excludeOptions.join(','))
    }

    const coordinates = `${origin.join(',')};${destination.join(',')}`
    const apiUrl = `${MAPBOX_API_BASE}/directions/v5/mapbox/${profile}`

    try {
      rateLimiter.recordRequest()

      const response = await fetch(`${apiUrl}/${coordinates}?${params}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        signal: AbortSignal.timeout(15000) // 15 second timeout
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        // Handle rate limiting specifically
        if (response.status === 429) {
          throw new MapboxError(
            'Rate limit exceeded. Please wait before making more requests.',
            'RATE_LIMIT',
            { status: response.status, ...errorData }
          )
        }

        throw new MapboxError(
          `Directions API error: ${response.status}`,
          'API_ERROR',
          { status: response.status, ...errorData }
        )
      }

      const data = await response.json()

      if (!data.routes) {
        throw new MapboxError('No routes found', 'NO_ROUTES', data)
      }

      // Validate and enhance route data
      return data.routes
        .filter((route: any) => route.geometry && route.distance && route.duration)
        .map((route: any) => ({
          distance: route.distance,
          duration: route.duration,
          geometry: route.geometry,
          legs: route.legs || [],
          weight: route.weight || route.duration,
          weight_name: route.weight_name || 'duration',
          steps: route.legs?.[0]?.steps || []
        }))

    } catch (error) {
      if (error instanceof MapboxError) {
        throw error
      }

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new MapboxError('Request timeout', 'TIMEOUT')
        }
        throw new MapboxError(`Directions failed: ${error.message}`, 'NETWORK_ERROR', error)
      }

      throw new MapboxError('Unknown error occurred getting directions', 'UNKNOWN_ERROR', error)
    }
  }

  /**
   * Enhance search results with distance calculation and proximity sorting
   */
  private enhanceResultsWithDistance(results: SearchResult[], userLocation?: [number, number]): SearchResult[] {
    if (!userLocation || !results.length) return results

    // Calculate distance for each result
    const resultsWithDistance = results.map(result => ({
      ...result,
      distance: this.calculateHaversineDistance(
        userLocation,
        [result.center[0], result.center[1]]
      )
    }))

    // Enhanced sorting: prioritize very close results (< 2km) heavily
    resultsWithDistance.sort((a, b) => {
      const distA = a.distance || 0
      const distB = b.distance || 0

      // If both are very close (< 2km), sort by distance
      if (distA < 2 && distB < 2) {
        return distA - distB
      }

      // If one is very close and other isn't, prioritize the close one
      if (distA < 2 && distB >= 2) return -1
      if (distB < 2 && distA >= 2) return 1

      // For distant results, sort by distance
      return distA - distB
    })

    // Filter out results that are too far (> 50km) unless there are very few results
    const filteredResults = resultsWithDistance.length > 3
      ? resultsWithDistance.filter(r => (r.distance || 0) < 50)
      : resultsWithDistance

    console.log('📍 Enhanced search results with distances:',
      filteredResults.slice(0, 5).map(r => ({
        name: r.place_name?.split(',')[0] || r.text,
        distance: `${(r.distance || 0).toFixed(2)}km`,
        priority: (r.distance || 0) < 2 ? 'HIGH' : 'NORMAL'
      }))
    )

    return filteredResults
  }

  /**
   * Validate coordinate pair
   */
  private isValidCoordinate(coord: [number, number]): boolean {
    return Array.isArray(coord) &&
           coord.length === 2 &&
           typeof coord[0] === 'number' &&
           typeof coord[1] === 'number' &&
           coord[0] >= -180 && coord[0] <= 180 &&
           coord[1] >= -90 && coord[1] <= 90
  }

  /**
   * Calculate ride estimate with price (improved)
   */
  async calculateRideEstimate(
    origin: [number, number],
    destination: [number, number],
    vehicleType: string = 'economy'
  ): Promise<RideEstimate | null> {
    // Create cache key for ride estimate
    const cacheKey = `ride_estimate_${origin.join(',')}_${destination.join(',')}_${vehicleType}`

    // Check cache first
    const cachedEstimate = mapboxCacheService.getCachedRideEstimate(origin, destination)
    if (cachedEstimate) {
      console.log('🎯 Using cached ride estimate')
      return cachedEstimate
    }

    try {
      // Add delay to prevent rate limiting
      await new Promise(resolve => setTimeout(resolve, 300))

      const routes = await this.getDirections(origin, destination, {
        profile: 'driving-traffic', // Use traffic-aware routing
        alternatives: false,
        steps: false
      })

      if (!routes.length) return null

      const route = routes[0]
      const distanceKm = route.distance / 1000
      const durationMin = route.duration / 60

      // Enhanced price calculation based on vehicle type
      const price = this.calculateDynamicPrice(distanceKm, durationMin, vehicleType)

      const estimate: RideEstimate = {
        distance: route.distance,
        duration: route.duration,
        price: price,
        route,
        vehicleType
      }

      // Cache the result for 10 minutes (shorter for traffic-aware routing)
      mapboxCacheService.cacheRideEstimate(origin, destination, estimate)

      console.log('✅ Ride estimate calculated:', {
        distance: `${distanceKm.toFixed(2)}km`,
        duration: `${durationMin.toFixed(1)}min`,
        price: `R$ ${price.toFixed(2)}`,
        vehicleType
      })

      return estimate
    } catch (error) {
      console.error('Error calculating ride estimate:', error)

      // If rate limited, return a mock estimate to keep UI stable
      if (error instanceof MapboxError && error.code === 'RATE_LIMIT') {
        console.log('🚫 Rate limited, returning mock estimate')
        return this.getMockRideEstimate(origin, destination, vehicleType)
      }

      return null
    }
  }

  /**
   * Calculate dynamic pricing based on vehicle type and demand
   */
  private calculateDynamicPrice(
    distanceKm: number,
    durationMin: number,
    vehicleType: string
  ): number {
    // Base pricing by vehicle type
    const pricingTiers = {
      moto: { base: 3.00, perKm: 1.50, perMin: 0.20, surge: 1.0 },
      economy: { base: 5.00, perKm: 2.00, perMin: 0.30, surge: 1.0 },
      comfort: { base: 8.00, perKm: 3.00, perMin: 0.40, surge: 1.1 },
      premium: { base: 12.00, perKm: 4.50, perMin: 0.60, surge: 1.2 }
    }

    const pricing = pricingTiers[vehicleType as keyof typeof pricingTiers] || pricingTiers.economy

    // Calculate base price
    let price = pricing.base + (distanceKm * pricing.perKm) + (durationMin * pricing.perMin)

    // Apply surge pricing during peak hours
    const hour = new Date().getHours()
    const isPeakHour = (hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19)

    if (isPeakHour) {
      price *= pricing.surge
    }

    // Minimum fare based on vehicle type
    const minimumFares = {
      moto: 4.00,
      economy: 6.00,
      comfort: 8.00,
      premium: 12.00
    }

    const minFare = minimumFares[vehicleType as keyof typeof minimumFares] || minimumFares.economy

    return Math.max(price, minFare)
  }

  /**
   * Get mock ride estimate when rate limited
   */
  private getMockRideEstimate(
    origin: [number, number],
    destination: [number, number],
    vehicleType: string = 'economy'
  ): RideEstimate {
    // Calculate approximate distance using Haversine formula
    const distance = this.calculateHaversineDistance(origin, destination)
    const duration = distance * 60 // Assume 1 km per minute in city traffic

    // Use dynamic pricing
    const price = this.calculateDynamicPrice(distance, duration / 60, vehicleType)

    console.log('🎭 Using mock estimate due to rate limiting:', {
      distance: `${distance.toFixed(2)}km`,
      duration: `${(duration / 60).toFixed(1)}min`,
      price: `R$ ${price.toFixed(2)}`,
      vehicleType
    })

    return {
      distance: distance * 1000, // Convert to meters
      duration: duration,
      price: price,
      route: {
        distance: distance * 1000,
        duration: duration,
        geometry: {
          coordinates: [origin, destination] // Simple straight line
        },
        legs: []
      },
      vehicleType
    }
  }

  /**
   * Calculate enhanced real-time ETA with multiple factors
   */
  async calculateRealTimeETA(
    origin: [number, number],
    destination: [number, number],
    options?: {
      vehicleType?: 'car' | 'motorcycle' | 'bicycle'
      timeOfDay?: 'peak' | 'off-peak' | 'night'
      weatherCondition?: 'clear' | 'rain' | 'heavy-rain'
    }
  ): Promise<{
    eta: number
    confidence: 'high' | 'medium' | 'low'
    factors: string[]
    alternativeETAs?: number[]
  } | null> {
    try {
      // Get traffic-aware routing with alternatives
      const routes = await this.getDirections(origin, destination, {
        profile: 'driving-traffic',
        alternatives: true
      })

      if (!routes.length) return null

      const mainRoute = routes[0]
      let eta = Math.round(mainRoute.duration / 60)
      const factors: string[] = []
      let confidence: 'high' | 'medium' | 'low' = 'high'

      // Apply vehicle type adjustments
      if (options?.vehicleType === 'motorcycle') {
        eta = Math.round(eta * 0.8) // Motorcycles are typically 20% faster
        factors.push('Motorcycle speed advantage')
      } else if (options?.vehicleType === 'bicycle') {
        eta = Math.round(eta * 2.5) // Bicycles are much slower
        factors.push('Bicycle routing')
        confidence = 'medium'
      }

      // Apply time of day adjustments
      if (options?.timeOfDay === 'peak') {
        eta = Math.round(eta * 1.3) // 30% longer during peak hours
        factors.push('Peak hour traffic')
        confidence = 'medium'
      } else if (options?.timeOfDay === 'night') {
        eta = Math.round(eta * 0.9) // 10% faster at night
        factors.push('Night time advantage')
      }

      // Apply weather adjustments
      if (options?.weatherCondition === 'rain') {
        eta = Math.round(eta * 1.15) // 15% longer in rain
        factors.push('Rain conditions')
        confidence = 'medium'
      } else if (options?.weatherCondition === 'heavy-rain') {
        eta = Math.round(eta * 1.4) // 40% longer in heavy rain
        factors.push('Heavy rain conditions')
        confidence = 'low'
      }

      // Get alternative ETAs
      const alternativeETAs = routes.slice(1, 3).map(route =>
        Math.round(route.duration / 60)
      )

      // Get traffic information for confidence adjustment
      const trafficInfo = await this.getTrafficInfo(origin, destination)
      if (trafficInfo) {
        if (trafficInfo.congestionLevel === 'heavy' || trafficInfo.congestionLevel === 'severe') {
          confidence = confidence === 'high' ? 'medium' : 'low'
          factors.push(`${trafficInfo.congestionLevel} traffic (+${trafficInfo.delayMinutes}min)`)
        }
      }

      return {
        eta: Math.max(eta, 1), // Minimum 1 minute
        confidence,
        factors,
        alternativeETAs: alternativeETAs.length > 0 ? alternativeETAs : undefined
      }

    } catch (error) {
      console.error('Error calculating enhanced real-time ETA:', error)

      // Enhanced fallback calculation
      const distance = this.calculateHaversineDistance(origin, destination)
      let fallbackETA = Math.max(Math.round((distance / 30) * 60) + 2, 3)

      // Apply basic adjustments to fallback
      if (options?.vehicleType === 'motorcycle') fallbackETA = Math.round(fallbackETA * 0.8)
      if (options?.vehicleType === 'bicycle') fallbackETA = Math.round(fallbackETA * 2.5)
      if (options?.timeOfDay === 'peak') fallbackETA = Math.round(fallbackETA * 1.3)
      if (options?.weatherCondition === 'rain') fallbackETA = Math.round(fallbackETA * 1.15)

      return {
        eta: fallbackETA,
        confidence: 'low',
        factors: ['Estimated (no real-time data)']
      }
    }
  }

  /**
   * Calculate distance between two points using Haversine formula
   */
  private calculateHaversineDistance(
    coord1: [number, number],
    coord2: [number, number]
  ): number {
    const R = 6371 // Earth's radius in kilometers
    const dLat = this.toRadians(coord2[1] - coord1[1])
    const dLon = this.toRadians(coord2[0] - coord1[0])
    const lat1 = this.toRadians(coord1[1])
    const lat2 = this.toRadians(coord2[1])

    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.sin(dLon/2) * Math.sin(dLon/2) * Math.cos(lat1) * Math.cos(lat2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))

    return R * c
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  /**
   * Get travel time matrix for multiple destinations
   */
  async getTravelTimeMatrix(
    origins: [number, number][],
    destinations: [number, number][]
  ): Promise<number[][]> {
    const originCoords = origins.map(coord => coord.join(',')).join(';')
    const destCoords = destinations.map(coord => coord.join(',')).join(';')

    const params = new URLSearchParams({
      access_token: this.accessToken,
      sources: Array.from({ length: origins.length }, (_, i) => i).join(';'),
      destinations: Array.from({ length: destinations.length }, (_, i) => i).join(';')
    })

    try {
      const response = await fetch(`${MATRIX_API}/${originCoords};${destCoords}?${params}`)
      const data = await response.json()

      return data.durations || []
    } catch (error) {
      console.error('Error getting travel time matrix:', error)
      return []
    }
  }

  /**
   * Get isochrone (reachable area within time limit)
   */
  async getIsochrone(
    center: [number, number],
    contours: number[] = [5, 10, 15] // minutes
  ): Promise<any> {
    const params = new URLSearchParams({
      access_token: this.accessToken,
      contours_minutes: contours.join(','),
      polygons: 'true',
      denoise: '1'
    })

    const coordinates = center.join(',')

    try {
      const response = await fetch(`${ISOCHRONE_API}/${coordinates}?${params}`)
      const data = await response.json()

      return data
    } catch (error) {
      console.error('Error getting isochrone:', error)
      return null
    }
  }

  /**
   * Optimize route for multiple waypoints (TSP solver)
   */
  async getOptimizedRoute(
    waypoints: [number, number][],
    options?: {
      source?: 'first' | 'any'
      destination?: 'last' | 'any'
      roundtrip?: boolean
      profile?: 'driving' | 'driving-traffic'
    }
  ): Promise<any> {
    if (waypoints.length < 2) {
      throw new MapboxError('At least 2 waypoints required for optimization', 'INVALID_INPUT')
    }

    const profile = options?.profile || 'driving-traffic'
    const coordinates = waypoints.map(wp => wp.join(',')).join(';')

    const params = new URLSearchParams({
      access_token: this.accessToken,
      source: options?.source || 'first',
      destination: options?.destination || 'last',
      roundtrip: (options?.roundtrip !== false).toString(),
      geometries: 'geojson',
      overview: 'full',
      steps: 'true',
      annotations: 'distance,duration,speed'
    })

    try {
      const response = await fetch(`${OPTIMIZATION_API}/${coordinates}?${params}`)

      if (!response.ok) {
        throw new MapboxError(`Optimization API error: ${response.status}`, 'API_ERROR')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Error getting optimized route:', error)
      throw error
    }
  }

  /**
   * Map matching - snap GPS traces to road network
   */
  async getMapMatching(
    coordinates: [number, number][],
    options?: {
      radiuses?: number[]
      timestamps?: number[]
      profile?: 'driving' | 'walking' | 'cycling'
    }
  ): Promise<any> {
    if (coordinates.length < 2) {
      throw new MapboxError('At least 2 coordinates required for map matching', 'INVALID_INPUT')
    }

    const profile = options?.profile || 'driving'
    const coordString = coordinates.map(coord => coord.join(',')).join(';')

    const params = new URLSearchParams({
      access_token: this.accessToken,
      geometries: 'geojson',
      overview: 'full',
      steps: 'false',
      annotations: 'distance,duration,speed'
    })

    if (options?.radiuses) {
      params.append('radiuses', options.radiuses.join(';'))
    }

    if (options?.timestamps) {
      params.append('timestamps', options.timestamps.join(';'))
    }

    try {
      const response = await fetch(`${MAP_MATCHING_API}/${coordString}?${params}`)

      if (!response.ok) {
        throw new MapboxError(`Map Matching API error: ${response.status}`, 'API_ERROR')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Error with map matching:', error)
      throw error
    }
  }

  /**
   * Get real-time traffic information for a route
   */
  async getTrafficInfo(
    origin: [number, number],
    destination: [number, number]
  ): Promise<{
    congestionLevel: 'low' | 'moderate' | 'heavy' | 'severe'
    delayMinutes: number
    alternativeRoutes: number
  } | null> {
    try {
      // Get both regular and traffic-aware routes
      const [regularRoute, trafficRoute] = await Promise.all([
        this.getDirections(origin, destination, { profile: 'driving', alternatives: false }),
        this.getDirections(origin, destination, { profile: 'driving-traffic', alternatives: true })
      ])

      if (!regularRoute.length || !trafficRoute.length) {
        return null
      }

      const regularDuration = regularRoute[0].duration
      const trafficDuration = trafficRoute[0].duration
      const delayMinutes = Math.max(0, Math.round((trafficDuration - regularDuration) / 60))

      let congestionLevel: 'low' | 'moderate' | 'heavy' | 'severe'
      if (delayMinutes < 2) congestionLevel = 'low'
      else if (delayMinutes < 5) congestionLevel = 'moderate'
      else if (delayMinutes < 10) congestionLevel = 'heavy'
      else congestionLevel = 'severe'

      return {
        congestionLevel,
        delayMinutes,
        alternativeRoutes: trafficRoute.length - 1
      }
    } catch (error) {
      console.error('Error getting traffic info:', error)
      return null
    }
  }

  /**
   * Find nearby drivers (mock implementation)
   */
  async findNearbyDrivers(
    userLocation: [number, number],
    radius: number = 5000 // meters
  ): Promise<DriverLocation[]> {
    // Mock data - in real app, this would come from your backend
    const mockDrivers: DriverLocation[] = [
      {
        id: '1',
        name: 'João Silva',
        coordinates: [userLocation[0] + 0.01, userLocation[1] + 0.005],
        eta: 3,
        rating: 4.8,
        vehicle: { model: 'Honda Civic', plate: 'ABC-1234', color: 'Prata' }
      },
      {
        id: '2',
        name: 'Maria Santos',
        coordinates: [userLocation[0] - 0.008, userLocation[1] - 0.003],
        eta: 5,
        rating: 4.9,
        vehicle: { model: 'Toyota Corolla', plate: 'XYZ-5678', color: 'Branco' }
      },
      {
        id: '3',
        name: 'Pedro Costa',
        coordinates: [userLocation[0] + 0.005, userLocation[1] - 0.008],
        eta: 7,
        rating: 4.7,
        vehicle: { model: 'Hyundai HB20', plate: 'DEF-9012', color: 'Azul' }
      }
    ]

    // Calculate actual ETAs using Matrix API
    try {
      const driverCoords = mockDrivers.map(d => d.coordinates)
      const matrix = await this.getTravelTimeMatrix(driverCoords, [userLocation])

      return mockDrivers.map((driver, index) => ({
        ...driver,
        eta: matrix[index] ? Math.round(matrix[index][0] / 60) : driver.eta
      }))
    } catch (error) {
      console.error('Error calculating driver ETAs:', error)
      return mockDrivers
    }
  }

  /**
   * Reverse geocoding - get address from coordinates
   */
  async reverseGeocode(coordinates: [number, number]): Promise<string> {
    const params = new URLSearchParams({
      access_token: this.accessToken,
      language: 'pt-BR',
      types: 'address,poi'
    })

    try {
      const response = await fetch(`${GEOCODING_API}/${coordinates.join(',')}.json?${params}`)
      const data = await response.json()

      return data.features?.[0]?.place_name || 'Endereço não encontrado'
    } catch (error) {
      console.error('Error reverse geocoding:', error)
      return 'Erro ao obter endereço'
    }
  }

  /**
   * Get rate limiter statistics
   */
  getRateLimitStats() {
    return rateLimiter.getStats()
  }

  /**
   * Get nearby drivers with enhanced real-time analysis
   */
  async getNearbyDrivers(
    userLocation: [number, number],
    options?: {
      radius?: number // km
      maxDrivers?: number
      vehicleTypes?: string[]
      includeAnalytics?: boolean
    }
  ): Promise<{
    drivers: any[]
    analytics?: {
      averageETA: number
      driverDensity: number
      coverageArea: number
      demandLevel: 'low' | 'medium' | 'high'
    }
  }> {
    const radius = options?.radius || 5 // 5km default radius
    const maxDrivers = options?.maxDrivers || 10

    // Enhanced mock data with more realistic distribution
    const mockDrivers = [
      {
        id: '1',
        name: 'João Silva',
        coordinates: [userLocation[0] + 0.002, userLocation[1] + 0.003] as [number, number],
        eta: 3,
        rating: 4.8,
        vehicle: { model: 'Toyota Corolla', plate: 'ABC-1234', color: 'Branco', type: 'standard' },
        status: 'available',
        lastUpdate: Date.now()
      },
      {
        id: '2',
        name: 'Maria Santos',
        coordinates: [userLocation[0] - 0.001, userLocation[1] + 0.002] as [number, number],
        eta: 5,
        rating: 4.9,
        vehicle: { model: 'Honda Civic', plate: 'XYZ-5678', color: 'Prata', type: 'premium' },
        status: 'available',
        lastUpdate: Date.now()
      },
      {
        id: '3',
        name: 'Pedro Costa',
        coordinates: [userLocation[0] + 0.005, userLocation[1] - 0.008] as [number, number],
        eta: 7,
        rating: 4.7,
        vehicle: { model: 'Hyundai HB20', plate: 'DEF-9012', color: 'Azul', type: 'standard' },
        status: 'available',
        lastUpdate: Date.now()
      },
      {
        id: '4',
        name: 'Ana Oliveira',
        coordinates: [userLocation[0] - 0.003, userLocation[1] - 0.001] as [number, number],
        eta: 4,
        rating: 4.6,
        vehicle: { model: 'Nissan March', plate: 'GHI-3456', color: 'Vermelho', type: 'economy' },
        status: 'available',
        lastUpdate: Date.now()
      }
    ]

    // Filter by vehicle type if specified
    let filteredDrivers = mockDrivers
    if (options?.vehicleTypes?.length) {
      filteredDrivers = mockDrivers.filter(driver =>
        options.vehicleTypes!.includes(driver.vehicle.type)
      )
    }

    // Filter by radius
    filteredDrivers = filteredDrivers.filter(driver => {
      const distance = this.calculateHaversineDistance(userLocation, driver.coordinates)
      return distance <= radius
    })

    // Limit number of drivers
    filteredDrivers = filteredDrivers.slice(0, maxDrivers)

    // Calculate enhanced ETAs using Matrix API
    try {
      const driverCoords = filteredDrivers.map(d => d.coordinates)
      const matrix = await this.getTravelTimeMatrix(driverCoords, [userLocation])

      const enhancedDrivers = filteredDrivers.map((driver, index) => ({
        ...driver,
        eta: matrix[index] ? Math.round(matrix[index][0] / 60) : driver.eta,
        distance: this.calculateHaversineDistance(userLocation, driver.coordinates)
      }))

      // Sort by ETA
      enhancedDrivers.sort((a, b) => a.eta - b.eta)

      let analytics
      if (options?.includeAnalytics) {
        const etas = enhancedDrivers.map(d => d.eta)
        const averageETA = etas.reduce((sum, eta) => sum + eta, 0) / etas.length

        // Calculate driver density (drivers per km²)
        const areaKm2 = Math.PI * radius * radius
        const driverDensity = enhancedDrivers.length / areaKm2

        // Determine demand level based on average ETA and density
        let demandLevel: 'low' | 'medium' | 'high'
        if (averageETA < 4 && driverDensity > 0.5) demandLevel = 'low'
        else if (averageETA < 8 && driverDensity > 0.2) demandLevel = 'medium'
        else demandLevel = 'high'

        analytics = {
          averageETA: Math.round(averageETA),
          driverDensity: Math.round(driverDensity * 100) / 100,
          coverageArea: Math.round(areaKm2 * 100) / 100,
          demandLevel
        }
      }

      return {
        drivers: enhancedDrivers,
        analytics
      }
    } catch (error) {
      console.error('Error calculating enhanced driver data:', error)
      return {
        drivers: filteredDrivers.map(driver => ({
          ...driver,
          distance: this.calculateHaversineDistance(userLocation, driver.coordinates)
        }))
      }
    }
  }

  /**
   * Get service health status
   */
  async getHealthStatus() {
    try {
      const rateLimitStats = this.getRateLimitStats()
      const canMakeRequest = rateLimiter.canMakeRequest()

      return {
        status: canMakeRequest ? 'healthy' : 'rate_limited',
        rateLimiting: rateLimitStats,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    }
  }
}

export const mapboxService = new MapboxService()
export default mapboxService
