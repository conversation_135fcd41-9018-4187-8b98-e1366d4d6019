// Service Worker avançado para passenger-app
const CACHE_NAME = 'passenger-app-v1.0.1';
const STATIC_CACHE = 'passenger-app-static-v2';
const DYNAMIC_CACHE = 'passenger-app-dynamic-v2';

// Assets para cache estático
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  '/favicon.png',
  '/favicon.svg',
  '/apple-touch-icon.png'
];

// Assets para cache dinâmico
const DYNAMIC_ASSETS = [
  '/api/',
  'https://api.mapbox.com/',
  'https://supabase.co/'
];

// Install event - cache assets estáticos
self.addEventListener('install', event => {
  console.log('[SW] Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('[SW] Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('[SW] Static assets cached');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('[SW] Error caching static assets:', error);
      })
  );
});

// Activate event - limpar caches antigos
self.addEventListener('activate', event => {
  console.log('[SW] Activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Claiming clients');
        return self.clients.claim();
      })
  );
});

// Fetch event - estratégia simplificada para evitar erros
self.addEventListener('fetch', event => {
  const { request } = event;

  // Ignorar requests para extensões do navegador e outros protocolos
  if (!request.url.startsWith('http')) {
    return;
  }

  // Ignorar requests para recursos que podem causar problemas
  const url = new URL(request.url);
  const skipPatterns = [
    'chrome-extension://',
    'moz-extension://',
    'safari-extension://',
    'ms-browser-extension://',
    'data:',
    'blob:',
    'filesystem:'
  ];

  if (skipPatterns.some(pattern => request.url.startsWith(pattern))) {
    return;
  }

  // Estratégia simplificada: tentar buscar, se falhar, ignorar
  event.respondWith(
    fetch(request)
      .then(response => {
        // Se a resposta for bem-sucedida, tentar fazer cache
        if (response.status === 200 && response.type === 'basic') {
          try {
            const responseClone = response.clone();
            caches.open(DYNAMIC_CACHE)
              .then(cache => {
                cache.put(request, responseClone);
              })
              .catch(() => {
                // Ignorar erros de cache silenciosamente
              });
          } catch (error) {
            // Ignorar erros de clonagem
          }
        }
        return response;
      })
      .catch(error => {
        // Em caso de erro, tentar buscar no cache
        return caches.match(request)
          .then(cachedResponse => {
            if (cachedResponse) {
              return cachedResponse;
            }

            // Se for uma página, retornar index.html
            if (request.destination === 'document') {
              return caches.match('/index.html');
            }

            // Para outros recursos, deixar o navegador lidar
            throw error;
          })
          .catch(() => {
            // Se tudo falhar, retornar resposta vazia
            return new Response('', {
              status: 404,
              statusText: 'Not Found'
            });
          });
      })
  );
});

// Background sync para dados offline
self.addEventListener('sync', event => {
  console.log('[SW] Background sync:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Sincronizar dados pendentes
      syncPendingData()
    );
  }
});

// Push notifications
self.addEventListener('push', event => {
  console.log('[SW] Push received');
  
  const options = {
    body: event.data ? event.data.text() : 'Nova notificação do MobiDrive',
    icon: '/favicon-192x192.png',
    badge: '/favicon-96x96.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Ver detalhes',
        icon: '/icons/checkmark.png'
      },
      {
        action: 'close',
        title: 'Fechar',
        icon: '/icons/xmark.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('MobiDrive', options)
  );
});

// Notification click
self.addEventListener('notificationclick', event => {
  console.log('[SW] Notification click received.');
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Função para sincronizar dados pendentes
async function syncPendingData() {
  try {
    // Implementar lógica de sincronização
    console.log('[SW] Syncing pending data...');
    
    // Exemplo: enviar dados armazenados localmente
    const pendingData = await getPendingData();
    
    for (const data of pendingData) {
      await sendDataToServer(data);
    }
    
    console.log('[SW] Data sync completed');
  } catch (error) {
    console.error('[SW] Error syncing data:', error);
  }
}

async function getPendingData() {
  // Implementar busca de dados pendentes
  return [];
}

async function sendDataToServer(data) {
  // Implementar envio para servidor
  return fetch('/api/sync', {
    method: 'POST',
    body: JSON.stringify(data),
    headers: {
      'Content-Type': 'application/json'
    }
  });
}