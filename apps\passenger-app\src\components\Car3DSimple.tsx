import React, { useRef, useState, useEffect } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Environment } from '@react-three/drei'
import * as THREE from 'three'

// Componente do carro 3D simplificado com rotação baseada em scroll e toque
const SimpleCar: React.FC<{ manualRotation: [number, number, number] }> = ({ manualRotation }) => {
  const carRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (carRef.current) {
      // Rotação baseada no scroll da página
      const scrollY = window.scrollY || 0
      const maxScroll = Math.max(document.body.scrollHeight - window.innerHeight, 1)
      const scrollProgress = Math.min(scrollY / maxScroll, 1)

      // Combinar rotação do scroll + rotação manual + rotação automática lenta
      carRef.current.rotation.x = manualRotation[0] + Math.sin(scrollProgress * Math.PI) * 0.1
      carRef.current.rotation.y = manualRotation[1] + scrollProgress * Math.PI * 4 + state.clock.elapsedTime * 0.05
      carRef.current.rotation.z = manualRotation[2]
    }
  })

  return (
    <group ref={carRef} position={[0, 0, 0]}>
      {/* Corpo principal do carro */}
      <mesh position={[0, 0.5, 0]} castShadow receiveShadow>
        <boxGeometry args={[4, 1, 2]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* Teto do carro */}
      <mesh position={[0, 1.2, 0]} castShadow>
        <boxGeometry args={[3, 0.8, 1.8]} />
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>

      {/* Para-brisa frontal */}
      <mesh position={[1.3, 1.2, 0]} rotation={[0, 0, -0.2]} castShadow>
        <boxGeometry args={[0.8, 0.7, 1.6]} />
        <meshStandardMaterial
          color="#87CEEB"
          transparent
          opacity={0.3}
        />
      </mesh>

      {/* Rodas */}
      <group position={[1.3, -0.2, 1.2]}>
        <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
          <cylinderGeometry args={[0.4, 0.4, 0.3]} />
          <meshStandardMaterial color="#2a2a2a" metalness={0.7} roughness={0.3} />
        </mesh>
      </group>

      <group position={[1.3, -0.2, -1.2]}>
        <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
          <cylinderGeometry args={[0.4, 0.4, 0.3]} />
          <meshStandardMaterial color="#2a2a2a" metalness={0.7} roughness={0.3} />
        </mesh>
      </group>

      <group position={[-1.3, -0.2, 1.2]}>
        <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
          <cylinderGeometry args={[0.4, 0.4, 0.3]} />
          <meshStandardMaterial color="#2a2a2a" metalness={0.7} roughness={0.3} />
        </mesh>
      </group>

      <group position={[-1.3, -0.2, -1.2]}>
        <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
          <cylinderGeometry args={[0.4, 0.4, 0.3]} />
          <meshStandardMaterial color="#2a2a2a" metalness={0.7} roughness={0.3} />
        </mesh>
      </group>

      {/* Faróis dianteiros */}
      <mesh position={[2.1, 0.3, 0.7]} castShadow>
        <sphereGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.3}
        />
      </mesh>
      <mesh position={[2.1, 0.3, -0.7]} castShadow>
        <sphereGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.3}
        />
      </mesh>
    </group>
  )
}

// Componente principal simplificado
interface Car3DSimpleProps {
  className?: string
}

export const Car3DSimple: React.FC<Car3DSimpleProps> = ({ className = "" }) => {
  const [rotation, setRotation] = useState<[number, number, number]>([0, 0, 0])
  const [isDragging, setIsDragging] = useState(false)
  const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 })

  // Handlers para mouse
  const handleMouseDown = (event: React.MouseEvent) => {
    event.preventDefault()
    setIsDragging(true)
    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isDragging) return
    event.preventDefault()

    const deltaX = event.clientX - lastPosition.x
    const deltaY = event.clientY - lastPosition.y

    setRotation(prev => [
      prev[0] + deltaY * 0.005,
      prev[1] + deltaX * 0.005,
      prev[2]
    ])

    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Handlers para touch (mobile)
  const handleTouchStart = (event: React.TouchEvent) => {
    event.preventDefault()
    const touch = event.touches[0]
    setIsDragging(true)
    setLastPosition({ x: touch.clientX, y: touch.clientY })
  }

  const handleTouchMove = (event: React.TouchEvent) => {
    if (!isDragging) return
    event.preventDefault()

    const touch = event.touches[0]
    const deltaX = touch.clientX - lastPosition.x
    const deltaY = touch.clientY - lastPosition.y

    setRotation(prev => [
      prev[0] + deltaY * 0.005,
      prev[1] + deltaX * 0.005,
      prev[2]
    ])

    setLastPosition({ x: touch.clientX, y: touch.clientY })
  }

  const handleTouchEnd = () => {
    setIsDragging(false)
  }

  // Event listeners globais para melhor experiência
  useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false)
    const handleGlobalTouchEnd = () => setIsDragging(false)

    const handleGlobalMouseMove = (event: MouseEvent) => {
      if (!isDragging) return

      const deltaX = event.clientX - lastPosition.x
      const deltaY = event.clientY - lastPosition.y

      setRotation(prev => [
        prev[0] + deltaY * 0.005,
        prev[1] + deltaX * 0.005,
        prev[2]
      ])

      setLastPosition({ x: event.clientX, y: event.clientY })
    }

    const handleGlobalTouchMove = (event: TouchEvent) => {
      if (!isDragging) return
      event.preventDefault()

      const touch = event.touches[0]
      const deltaX = touch.clientX - lastPosition.x
      const deltaY = touch.clientY - lastPosition.y

      setRotation(prev => [
        prev[0] + deltaY * 0.005,
        prev[1] + deltaX * 0.005,
        prev[2]
      ])

      setLastPosition({ x: touch.clientX, y: touch.clientY })
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)
      document.addEventListener('touchmove', handleGlobalTouchMove, { passive: false })
      document.addEventListener('touchend', handleGlobalTouchEnd)
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
      document.removeEventListener('touchmove', handleGlobalTouchMove)
      document.removeEventListener('touchend', handleGlobalTouchEnd)
    }
  }, [isDragging, lastPosition])

  return (
    <div
      className={`w-full h-full ${className} select-none`}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
        pointerEvents: 'auto',
        cursor: isDragging ? 'grabbing' : 'grab',
        touchAction: 'none'
      }}
    >
      <Canvas
        shadows
        camera={{ position: [4, 2, 3], fov: 50 }}
        style={{
          background: 'transparent',
          width: '100%',
          height: '100%'
        }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]}
      >
        {/* Câmera fixa - sem controles manuais */}

        {/* Iluminação */}
        <ambientLight intensity={0.7} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1}
          castShadow
          shadow-mapSize-width={1024}
          shadow-mapSize-height={1024}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />
        <pointLight position={[-5, 5, -5]} intensity={0.3} />
        <pointLight position={[5, 5, 5]} intensity={0.3} />

        {/* Ambiente */}
        <Environment preset="city" />

        {/* Carro */}
        <SimpleCar manualRotation={rotation} />

        {/* Chão */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -0.8, 0]} receiveShadow>
          <planeGeometry args={[20, 20]} />
          <meshStandardMaterial
            color="#1a1a1a"
            transparent
            opacity={0.15}
          />
        </mesh>
      </Canvas>
    </div>
  )
}

export default Car3DSimple
