// Tipos base compartilhados
export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
}

export interface Ride {
  id: string;
  passenger_id: string;
  driver_id?: string;
  pickup_location: Location;
  destination_location: Location;
  status: RideStatus;
  fare: number;
  created_at: string;
  updated_at: string;
}

export type RideStatus = 
  | 'pending'
  | 'accepted'
  | 'in_progress'
  | 'completed'
  | 'cancelled';

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}


// Tipos específicos do app do passageiro
export interface PassengerProfile extends User {
  rating: number;
  total_rides: number;
  preferred_payment_method?: string;
}

export interface RideRequest {
  pickup_location: Location;
  destination_location: Location;
  passenger_notes?: string;
  estimated_fare?: number;
}

export interface Driver {
  id: string;
  name: string;
  rating: number;
  vehicle_info: VehicleInfo;
  current_location?: Location;
  eta?: number;
}

export interface VehicleInfo {
  make: string;
  model: string;
  year: number;
  color: string;
  license_plate: string;
}

export interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'debit_card' | 'pix' | 'cash';
  last_four?: string;
  is_default: boolean;
}
