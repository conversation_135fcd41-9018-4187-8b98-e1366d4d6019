import { supabase } from '../lib/supabase'

export interface TrafficData {
  segment_id: string
  average_speed: number
  congestion_level: 'low' | 'medium' | 'high' | 'severe'
  timestamp: string
  sample_size: number
}

export interface ETAPrediction {
  estimated_minutes: number
  confidence: number
  factors: {
    distance_km: number
    base_travel_time: number
    traffic_delay: number
    weather_delay: number
    time_of_day_factor: number
    historical_factor: number
  }
  alternative_routes?: {
    route_id: string
    eta_minutes: number
    traffic_level: string
  }[]
}

export interface RouteSegment {
  start_lat: number
  start_lng: number
  end_lat: number
  end_lng: number
  distance_km: number
  typical_speed: number
  current_speed?: number
}

class ETAPredictionService {
  private readonly EARTH_RADIUS_KM = 6371
  private readonly BASE_CITY_SPEED = 25 // km/h average city speed
  private trafficCache = new Map<string, TrafficData>()
  private historicalDataCache = new Map<string, number>()

  /**
   * Predict ETA with high accuracy using multiple data sources
   */
  async predictETA(
    fromLat: number,
    fromLng: number,
    toLat: number,
    toLng: number,
    vehicleType: string = 'economy',
    currentTime?: Date
  ): Promise<ETAPrediction> {
    const time = currentTime || new Date()
    console.log(`🕐 Predicting ETA from [${fromLat}, ${fromLng}] to [${toLat}, ${toLng}]`)

    // Calculate base distance
    const distanceKm = this.calculateDistance(fromLat, fromLng, toLat, toLng)
    
    // Get route segments for detailed analysis
    const segments = await this.getRouteSegments(fromLat, fromLng, toLat, toLng)
    
    // Calculate base travel time
    const baseTravelTime = (distanceKm / this.BASE_CITY_SPEED) * 60 // minutes
    
    // Get traffic data
    const trafficDelay = await this.calculateTrafficDelay(segments, time)
    
    // Get weather impact
    const weatherDelay = await this.calculateWeatherDelay(distanceKm, time)
    
    // Time of day factor
    const timeOfDayFactor = this.calculateTimeOfDayFactor(time)
    
    // Historical data factor
    const historicalFactor = await this.getHistoricalFactor(
      fromLat, fromLng, toLat, toLng, time, vehicleType
    )
    
    // Calculate final ETA
    let estimatedMinutes = baseTravelTime
    estimatedMinutes += trafficDelay
    estimatedMinutes += weatherDelay
    estimatedMinutes *= timeOfDayFactor
    estimatedMinutes *= historicalFactor
    
    // Add vehicle-specific adjustments
    estimatedMinutes *= this.getVehicleSpeedFactor(vehicleType)
    
    // Add buffer for pickup/dropoff
    estimatedMinutes += this.getPickupBuffer(vehicleType)
    
    // Calculate confidence based on data quality
    const confidence = this.calculateConfidence(segments, trafficDelay, historicalFactor)
    
    // Get alternative routes
    const alternativeRoutes = await this.getAlternativeRoutes(
      fromLat, fromLng, toLat, toLng, estimatedMinutes
    )

    const prediction: ETAPrediction = {
      estimated_minutes: Math.round(estimatedMinutes),
      confidence,
      factors: {
        distance_km: distanceKm,
        base_travel_time: baseTravelTime,
        traffic_delay: trafficDelay,
        weather_delay: weatherDelay,
        time_of_day_factor: timeOfDayFactor,
        historical_factor: historicalFactor
      },
      alternative_routes: alternativeRoutes
    }

    console.log(`⏱️ ETA Prediction:`, {
      eta: `${prediction.estimated_minutes}min`,
      confidence: `${(confidence * 100).toFixed(1)}%`,
      distance: `${distanceKm.toFixed(1)}km`
    })

    return prediction
  }

  /**
   * Get route segments for detailed analysis
   */
  private async getRouteSegments(
    fromLat: number, fromLng: number, 
    toLat: number, toLng: number
  ): Promise<RouteSegment[]> {
    try {
      // Try to get route from Mapbox Directions API
      const response = await fetch(
        `https://api.mapbox.com/directions/v5/mapbox/driving/${fromLng},${fromLat};${toLng},${toLat}?` +
        `access_token=${import.meta.env.VITE_MAPBOX_TOKEN}&` +
        `steps=true&geometries=geojson&annotations=speed,duration`
      )

      if (!response.ok) {
        throw new Error('Failed to get route data')
      }

      const data = await response.json()
      
      if (data.routes && data.routes[0] && data.routes[0].legs) {
        const steps = data.routes[0].legs[0].steps
        
        return steps.map((step: any, index: number) => ({
          start_lat: step.maneuver.location[1],
          start_lng: step.maneuver.location[0],
          end_lat: index < steps.length - 1 ? steps[index + 1].maneuver.location[1] : toLat,
          end_lng: index < steps.length - 1 ? steps[index + 1].maneuver.location[0] : toLng,
          distance_km: step.distance / 1000,
          typical_speed: step.duration > 0 ? (step.distance / 1000) / (step.duration / 3600) : this.BASE_CITY_SPEED
        }))
      }
    } catch (error) {
      console.warn('Failed to get detailed route segments:', error)
    }

    // Fallback: create simple segment
    return [{
      start_lat: fromLat,
      start_lng: fromLng,
      end_lat: toLat,
      end_lng: toLng,
      distance_km: this.calculateDistance(fromLat, fromLng, toLat, toLng),
      typical_speed: this.BASE_CITY_SPEED
    }]
  }

  /**
   * Calculate traffic delay based on current conditions
   */
  private async calculateTrafficDelay(segments: RouteSegment[], time: Date): Promise<number> {
    let totalDelay = 0

    for (const segment of segments) {
      const segmentId = this.getSegmentId(segment)
      
      // Try to get real-time traffic data
      let trafficData = this.trafficCache.get(segmentId)
      
      if (!trafficData || this.isTrafficDataStale(trafficData)) {
        trafficData = await this.fetchTrafficData(segment, time)
        if (trafficData) {
          this.trafficCache.set(segmentId, trafficData)
        }
      }

      if (trafficData) {
        // Calculate delay based on speed difference
        const expectedTime = (segment.distance_km / segment.typical_speed) * 60
        const actualTime = (segment.distance_km / trafficData.average_speed) * 60
        const segmentDelay = Math.max(0, actualTime - expectedTime)
        
        totalDelay += segmentDelay
      } else {
        // Fallback: estimate based on time of day
        const timeBasedDelay = this.estimateTrafficDelayByTime(segment, time)
        totalDelay += timeBasedDelay
      }
    }

    return totalDelay
  }

  /**
   * Fetch real-time traffic data
   */
  private async fetchTrafficData(segment: RouteSegment, time: Date): Promise<TrafficData | null> {
    try {
      // In a real implementation, this would call a traffic API
      // For now, simulate based on time and location
      
      const hour = time.getHours()
      const isRushHour = (hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19)
      const isWeekend = time.getDay() === 0 || time.getDay() === 6
      
      let congestionLevel: TrafficData['congestion_level'] = 'low'
      let speedReduction = 1.0

      if (isRushHour && !isWeekend) {
        congestionLevel = 'high'
        speedReduction = 0.6
      } else if (hour >= 10 && hour <= 16 && !isWeekend) {
        congestionLevel = 'medium'
        speedReduction = 0.8
      } else if (isWeekend && hour >= 14 && hour <= 18) {
        congestionLevel = 'medium'
        speedReduction = 0.75
      }

      return {
        segment_id: this.getSegmentId(segment),
        average_speed: segment.typical_speed * speedReduction,
        congestion_level,
        timestamp: time.toISOString(),
        sample_size: 100 // Simulated
      }
    } catch (error) {
      console.warn('Failed to fetch traffic data:', error)
      return null
    }
  }

  /**
   * Calculate weather delay
   */
  private async calculateWeatherDelay(distanceKm: number, time: Date): Promise<number> {
    try {
      // In a real implementation, integrate with weather API
      // For now, simulate weather conditions
      
      const hour = time.getHours()
      const random = Math.random()
      
      // Simulate rain probability
      if ((hour >= 16 && hour <= 19) && random < 0.3) {
        // Rain during rush hour
        return distanceKm * 0.5 // 0.5 minutes delay per km
      }
      
      // Simulate extreme weather
      if (random < 0.05) {
        return distanceKm * 1.0 // 1 minute delay per km
      }
      
      return 0
    } catch (error) {
      console.warn('Failed to get weather data:', error)
      return 0
    }
  }

  /**
   * Calculate time of day factor
   */
  private calculateTimeOfDayFactor(time: Date): number {
    const hour = time.getHours()
    const dayOfWeek = time.getDay()
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6

    // Rush hour factors
    if (!isWeekend && ((hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19))) {
      return 1.4 // 40% slower during rush hour
    }

    // Late night factor
    if (hour >= 23 || hour <= 5) {
      return 0.8 // 20% faster at night
    }

    // Weekend evening
    if (isWeekend && hour >= 20 && hour <= 23) {
      return 1.2 // 20% slower on weekend evenings
    }

    return 1.0 // Normal speed
  }

  /**
   * Get historical factor based on past trips
   */
  private async getHistoricalFactor(
    fromLat: number, fromLng: number,
    toLat: number, toLng: number,
    time: Date, vehicleType: string
  ): Promise<number> {
    const routeKey = this.getRouteKey(fromLat, fromLng, toLat, toLng, time.getHours(), vehicleType)
    
    // Check cache first
    if (this.historicalDataCache.has(routeKey)) {
      return this.historicalDataCache.get(routeKey)!
    }

    try {
      // Query historical trip data
      const { data, error } = await supabase
        .from('completed_rides')
        .select('actual_duration, estimated_duration')
        .gte('pickup_lat', fromLat - 0.01)
        .lte('pickup_lat', fromLat + 0.01)
        .gte('pickup_lng', fromLng - 0.01)
        .lte('pickup_lng', fromLng + 0.01)
        .gte('destination_lat', toLat - 0.01)
        .lte('destination_lat', toLat + 0.01)
        .gte('destination_lng', toLng - 0.01)
        .lte('destination_lng', toLng + 0.01)
        .eq('vehicle_type', vehicleType)
        .gte('completed_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days
        .limit(20)

      if (error || !data || data.length === 0) {
        return 1.0 // Default factor
      }

      // Calculate average accuracy factor
      const accuracyFactors = data
        .filter(ride => ride.actual_duration && ride.estimated_duration)
        .map(ride => ride.actual_duration / ride.estimated_duration)

      if (accuracyFactors.length === 0) {
        return 1.0
      }

      const averageFactor = accuracyFactors.reduce((sum, factor) => sum + factor, 0) / accuracyFactors.length
      
      // Cache the result
      this.historicalDataCache.set(routeKey, averageFactor)
      
      return Math.max(0.5, Math.min(2.0, averageFactor)) // Clamp between 0.5x and 2.0x
    } catch (error) {
      console.warn('Failed to get historical factor:', error)
      return 1.0
    }
  }

  /**
   * Get vehicle speed factor
   */
  private getVehicleSpeedFactor(vehicleType: string): number {
    const factors = {
      moto: 0.8,     // Motorcycles are faster in traffic
      economy: 1.0,   // Baseline
      comfort: 1.05,  // Slightly slower (more careful driving)
      premium: 1.1    // Slower (luxury, careful driving)
    }

    return factors[vehicleType as keyof typeof factors] || 1.0
  }

  /**
   * Get pickup buffer time
   */
  private getPickupBuffer(vehicleType: string): number {
    const buffers = {
      moto: 1,      // 1 minute
      economy: 2,   // 2 minutes
      comfort: 3,   // 3 minutes
      premium: 4    // 4 minutes (more time for luxury service)
    }

    return buffers[vehicleType as keyof typeof buffers] || 2
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(
    segments: RouteSegment[],
    trafficDelay: number,
    historicalFactor: number
  ): number {
    let confidence = 0.8 // Base confidence

    // Reduce confidence if we have limited traffic data
    if (segments.length === 1) {
      confidence -= 0.2
    }

    // Reduce confidence for high traffic delay (uncertainty)
    if (trafficDelay > 10) {
      confidence -= 0.1
    }

    // Adjust based on historical accuracy
    if (Math.abs(historicalFactor - 1.0) > 0.3) {
      confidence -= 0.1
    }

    return Math.max(0.3, Math.min(1.0, confidence))
  }

  /**
   * Get alternative routes
   */
  private async getAlternativeRoutes(
    fromLat: number, fromLng: number,
    toLat: number, toLng: number,
    primaryETA: number
  ): Promise<ETAPrediction['alternative_routes']> {
    try {
      // In a real implementation, this would query multiple route options
      // For now, simulate some alternatives
      
      const alternatives = [
        {
          route_id: 'highway',
          eta_minutes: Math.round(primaryETA * 0.9),
          traffic_level: 'medium'
        },
        {
          route_id: 'scenic',
          eta_minutes: Math.round(primaryETA * 1.2),
          traffic_level: 'low'
        }
      ]

      return alternatives.filter(alt => Math.abs(alt.eta_minutes - primaryETA) > 2)
    } catch (error) {
      console.warn('Failed to get alternative routes:', error)
      return []
    }
  }

  /**
   * Utility functions
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const dLat = this.toRadians(lat2 - lat1)
    const dLon = this.toRadians(lon2 - lon1)
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return this.EARTH_RADIUS_KM * c
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  private getSegmentId(segment: RouteSegment): string {
    return `${segment.start_lat.toFixed(4)},${segment.start_lng.toFixed(4)}-${segment.end_lat.toFixed(4)},${segment.end_lng.toFixed(4)}`
  }

  private getRouteKey(fromLat: number, fromLng: number, toLat: number, toLng: number, hour: number, vehicleType: string): string {
    return `${fromLat.toFixed(3)},${fromLng.toFixed(3)}-${toLat.toFixed(3)},${toLng.toFixed(3)}-${hour}-${vehicleType}`
  }

  private isTrafficDataStale(data: TrafficData): boolean {
    const age = Date.now() - new Date(data.timestamp).getTime()
    return age > 5 * 60 * 1000 // 5 minutes
  }

  private estimateTrafficDelayByTime(segment: RouteSegment, time: Date): number {
    const hour = time.getHours()
    const isRushHour = (hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19)
    const isWeekend = time.getDay() === 0 || time.getDay() === 6

    if (isRushHour && !isWeekend) {
      return segment.distance_km * 2 // 2 minutes delay per km during rush hour
    } else if (!isWeekend && hour >= 10 && hour <= 16) {
      return segment.distance_km * 0.5 // 0.5 minutes delay per km during day
    }

    return 0
  }

  /**
   * Clear caches
   */
  clearCache(): void {
    this.trafficCache.clear()
    this.historicalDataCache.clear()
    console.log('🧹 ETA prediction caches cleared')
  }
}

export const etaPredictionService = new ETAPredictionService()
export default etaPredictionService
