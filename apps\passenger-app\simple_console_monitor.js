// =====================================================
// MONITOR SIMPLES DO CONSOLE DO NAVEGADOR
// Versão simplificada e robusta
// =====================================================

import puppeteer from 'puppeteer'

async function monitorConsole(url, duration = 30000) {
  console.log('🎯 INICIANDO MONITOR DO CONSOLE')
  console.log('=' .repeat(50))
  console.log(`🌐 URL: ${url}`)
  console.log(`⏱️ Duração: ${duration / 1000}s`)
  console.log('=' .repeat(50))

  let browser = null
  let page = null
  
  const logs = []
  const errors = []
  const warnings = []
  const networkIssues = []
  
  try {
    // Iniciar browser
    console.log('🚀 Iniciando navegador...')
    browser = await puppeteer.launch({
      headless: false, // Mostrar navegador
      devtools: true,  // Abrir DevTools
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage'
      ]
    })

    page = await browser.newPage()
    
    // Configurar viewport
    await page.setViewport({ width: 1280, height: 720 })
    
    console.log('✅ Navegador iniciado')
    
    // Configurar listeners ANTES de navegar
    console.log('🔧 Configurando listeners...')
    
    page.on('console', (msg) => {
      const logEntry = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString()
      }
      
      logs.push(logEntry)
      
      const time = new Date().toLocaleTimeString()
      
      switch (msg.type()) {
        case 'error':
          errors.push(logEntry)
          console.log(`[${time}] ❌ CONSOLE ERROR: ${msg.text()}`)
          break
        case 'warning':
          warnings.push(logEntry)
          console.log(`[${time}] ⚠️ CONSOLE WARNING: ${msg.text()}`)
          break
        case 'log':
          console.log(`[${time}] 📝 CONSOLE LOG: ${msg.text()}`)
          break
        case 'info':
          console.log(`[${time}] ℹ️ CONSOLE INFO: ${msg.text()}`)
          break
        default:
          console.log(`[${time}] 🔍 CONSOLE ${msg.type().toUpperCase()}: ${msg.text()}`)
      }
    })

    page.on('pageerror', (error) => {
      const errorEntry = {
        type: 'pageerror',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }
      
      errors.push(errorEntry)
      const time = new Date().toLocaleTimeString()
      console.log(`[${time}] 💥 PAGE ERROR: ${error.message}`)
    })

    page.on('requestfailed', (request) => {
      const failedRequest = {
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText,
        timestamp: new Date().toISOString()
      }
      
      networkIssues.push(failedRequest)
      const time = new Date().toLocaleTimeString()
      console.log(`[${time}] 🌐 REQUEST FAILED: ${request.method()} ${request.url()}`)
      console.log(`[${time}] 📋 Reason: ${request.failure()?.errorText}`)
    })

    page.on('response', (response) => {
      if (response.status() >= 400) {
        const errorResponse = {
          url: response.url(),
          status: response.status(),
          statusText: response.statusText(),
          timestamp: new Date().toISOString()
        }
        
        networkIssues.push(errorResponse)
        const time = new Date().toLocaleTimeString()
        console.log(`[${time}] 🔴 HTTP ${response.status()}: ${response.url()}`)
      }
    })

    console.log('✅ Listeners configurados')
    
    // Navegar para a página
    console.log(`\n🌐 Navegando para: ${url}`)
    await page.goto(url, { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    })
    
    console.log('✅ Página carregada')
    console.log('\n📊 MONITORANDO CONSOLE EM TEMPO REAL...')
    console.log('=' .repeat(50))
    
    // Aguardar o tempo especificado
    await page.waitForTimeout(duration)
    
    // Gerar relatório final
    console.log('\n' + '=' .repeat(50))
    console.log('📊 RELATÓRIO FINAL DO CONSOLE')
    console.log('=' .repeat(50))
    
    console.log(`📝 Total de logs: ${logs.length}`)
    console.log(`❌ Total de erros: ${errors.length}`)
    console.log(`⚠️ Total de warnings: ${warnings.length}`)
    console.log(`🌐 Total de problemas de rede: ${networkIssues.length}`)
    
    if (errors.length > 0) {
      console.log('\n❌ ERROS ENCONTRADOS:')
      errors.forEach((error, index) => {
        console.log(`  ${index + 1}. [${error.type}] ${error.text || error.message}`)
      })
    }
    
    if (warnings.length > 0) {
      console.log('\n⚠️ WARNINGS ENCONTRADOS:')
      warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning.text}`)
      })
    }
    
    if (networkIssues.length > 0) {
      console.log('\n🌐 PROBLEMAS DE REDE:')
      networkIssues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue.status || 'FAILED'} ${issue.url}`)
      })
    }
    
    // Análise final
    console.log('\n🎯 ANÁLISE:')
    if (errors.length === 0 && networkIssues.length === 0) {
      console.log('🎉 EXCELENTE! Nenhum erro crítico encontrado!')
    } else if (errors.length > 0) {
      console.log('⚠️ Foram encontrados erros que precisam ser corrigidos.')
    } else if (networkIssues.length > 0) {
      console.log('⚠️ Foram encontrados problemas de rede.')
    }
    
    return {
      logs,
      errors,
      warnings,
      networkIssues,
      summary: {
        totalLogs: logs.length,
        totalErrors: errors.length,
        totalWarnings: warnings.length,
        totalNetworkIssues: networkIssues.length
      }
    }
    
  } catch (error) {
    console.error('💥 Erro durante monitoramento:', error.message)
    throw error
  } finally {
    if (browser) {
      console.log('\n🔒 Fechando navegador...')
      await browser.close()
      console.log('✅ Navegador fechado')
    }
  }
}

// Função para testar múltiplas páginas
async function testMultiplePages() {
  const pages = [
    'http://localhost:3000',
    'http://localhost:3000/ride-request/map',
    'http://localhost:3000/ride-request/details'
  ]
  
  console.log('🎯 TESTANDO MÚLTIPLAS PÁGINAS')
  console.log('=' .repeat(50))
  
  const results = []
  
  for (let i = 0; i < pages.length; i++) {
    const url = pages[i]
    console.log(`\n📄 TESTE ${i + 1}/${pages.length}: ${url}`)
    console.log('-' .repeat(30))
    
    try {
      const result = await monitorConsole(url, 10000) // 10 segundos por página
      results.push({ url, ...result })
      
      console.log(`✅ Teste ${i + 1} concluído`)
      
      // Pausa entre testes
      if (i < pages.length - 1) {
        console.log('\n⏸️ Pausa de 2 segundos...')
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
      
    } catch (error) {
      console.error(`❌ Erro no teste ${i + 1}:`, error.message)
      results.push({ url, error: error.message })
    }
  }
  
  // Relatório geral
  console.log('\n' + '=' .repeat(60))
  console.log('📊 RELATÓRIO GERAL DE TODAS AS PÁGINAS')
  console.log('=' .repeat(60))
  
  let totalErrors = 0
  let totalWarnings = 0
  let totalNetworkIssues = 0
  
  results.forEach((result, index) => {
    console.log(`\n📄 Página ${index + 1}: ${result.url}`)
    if (result.error) {
      console.log(`  💥 Erro: ${result.error}`)
    } else {
      console.log(`  📝 Logs: ${result.summary.totalLogs}`)
      console.log(`  ❌ Erros: ${result.summary.totalErrors}`)
      console.log(`  ⚠️ Warnings: ${result.summary.totalWarnings}`)
      console.log(`  🌐 Problemas de rede: ${result.summary.totalNetworkIssues}`)
      
      totalErrors += result.summary.totalErrors
      totalWarnings += result.summary.totalWarnings
      totalNetworkIssues += result.summary.totalNetworkIssues
    }
  })
  
  console.log('\n🎯 TOTAIS GERAIS:')
  console.log(`❌ Total de erros: ${totalErrors}`)
  console.log(`⚠️ Total de warnings: ${totalWarnings}`)
  console.log(`🌐 Total de problemas de rede: ${totalNetworkIssues}`)
  
  if (totalErrors === 0) {
    console.log('\n🎉 SUCESSO! Nenhum erro encontrado em todas as páginas!')
  } else {
    console.log('\n⚠️ Foram encontrados erros que precisam ser corrigidos.')
  }
  
  return results
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const url = process.argv[2]
  const duration = parseInt(process.argv[3]) || 30000
  
  if (url) {
    console.log(`🧪 Testando URL específica: ${url}`)
    monitorConsole(url, duration)
      .then(() => {
        console.log('\n🏁 Monitoramento concluído!')
        process.exit(0)
      })
      .catch((error) => {
        console.error('💥 Erro fatal:', error)
        process.exit(1)
      })
  } else {
    console.log('🧪 Testando todas as páginas da aplicação')
    testMultiplePages()
      .then(() => {
        console.log('\n🏁 Todos os testes concluídos!')
        process.exit(0)
      })
      .catch((error) => {
        console.error('💥 Erro fatal:', error)
        process.exit(1)
      })
  }
}
