-- =====================================================
-- TABELAS PARA SISTEMA DE ESPERA DE MOTORISTA
-- Execute no Supabase SQL Editor
-- =====================================================

-- Extensão para cálculos geográficos
CREATE EXTENSION IF NOT EXISTS postgis;

-- Tabela de motoristas
CREATE TABLE IF NOT EXISTS drivers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20) NOT NULL,
  rating DECIMAL(3,2) DEFAULT 5.0,
  total_rides INTEGER DEFAULT 0,
  vehicle_model VARCHAR(100) NOT NULL,
  vehicle_color VARCHAR(50) NOT NULL,
  vehicle_plate VARCHAR(20) NOT NULL,
  vehicle_year INTEGER NOT NULL,
  photo_url TEXT,
  status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('available', 'busy', 'offline')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de corridas
CREATE TABLE IF NOT EXISTS rides (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  passenger_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  driver_id UUID REFERENCES drivers(id) ON DELETE SET NULL,
  status VARCHAR(20) DEFAULT 'requested' CHECK (status IN ('requested', 'assigned', 'en_route', 'in_progress', 'completed', 'cancelled')),
  
  -- Origem
  origin_latitude DECIMAL(10, 8) NOT NULL,
  origin_longitude DECIMAL(11, 8) NOT NULL,
  origin_address TEXT NOT NULL,
  
  -- Destino
  destination_latitude DECIMAL(10, 8) NOT NULL,
  destination_longitude DECIMAL(11, 8) NOT NULL,
  destination_address TEXT NOT NULL,
  
  -- Estimativas
  estimated_duration INTEGER NOT NULL, -- em segundos
  estimated_distance INTEGER NOT NULL, -- em metros
  estimated_price DECIMAL(10,2) NOT NULL,
  actual_price DECIMAL(10,2),
  
  -- Timestamps
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  assigned_at TIMESTAMP WITH TIME ZONE,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  cancelled_at TIMESTAMP WITH TIME ZONE,
  
  -- Outros
  cancellation_reason TEXT,
  payment_method VARCHAR(50) DEFAULT 'cash',
  notes TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de localização dos motoristas
CREATE TABLE IF NOT EXISTS driver_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE,
  ride_id UUID REFERENCES rides(id) ON DELETE SET NULL,
  
  -- Localização
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  bearing INTEGER DEFAULT 0, -- direção em graus (0-360)
  speed DECIMAL(5,2) DEFAULT 0, -- velocidade em km/h
  
  -- Status e ETA
  status VARCHAR(20) DEFAULT 'en_route' CHECK (status IN ('searching', 'assigned', 'en_route', 'arrived')),
  eta INTEGER, -- tempo estimado de chegada em minutos
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_drivers_status ON drivers(status);
CREATE INDEX IF NOT EXISTS idx_drivers_location ON drivers USING GIST(ST_Point(0, 0)); -- Placeholder para localização
CREATE INDEX IF NOT EXISTS idx_rides_passenger ON rides(passenger_id);
CREATE INDEX IF NOT EXISTS idx_rides_driver ON rides(driver_id);
CREATE INDEX IF NOT EXISTS idx_rides_status ON rides(status);
CREATE INDEX IF NOT EXISTS idx_driver_locations_driver ON driver_locations(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_locations_ride ON driver_locations(ride_id);
CREATE INDEX IF NOT EXISTS idx_driver_locations_location ON driver_locations USING GIST(ST_Point(longitude, latitude));

-- Função para encontrar motoristas próximos
CREATE OR REPLACE FUNCTION find_nearby_drivers(
  user_lat DECIMAL,
  user_lng DECIMAL,
  radius_km DECIMAL DEFAULT 5
)
RETURNS TABLE (
  driver_id UUID,
  driver_name VARCHAR,
  latitude DECIMAL,
  longitude DECIMAL,
  distance_km DECIMAL,
  vehicle_model VARCHAR,
  vehicle_color VARCHAR,
  vehicle_plate VARCHAR,
  rating DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id as driver_id,
    d.name as driver_name,
    dl.latitude,
    dl.longitude,
    ST_Distance(
      ST_Point(user_lng, user_lat)::geography,
      ST_Point(dl.longitude, dl.latitude)::geography
    ) / 1000 as distance_km,
    d.vehicle_model,
    d.vehicle_color,
    d.vehicle_plate,
    d.rating
  FROM drivers d
  JOIN driver_locations dl ON d.id = dl.driver_id
  WHERE 
    d.status = 'available'
    AND ST_DWithin(
      ST_Point(user_lng, user_lat)::geography,
      ST_Point(dl.longitude, dl.latitude)::geography,
      radius_km * 1000
    )
  ORDER BY distance_km
  LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- Função para atualizar timestamp automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers para atualizar updated_at
CREATE TRIGGER update_drivers_updated_at
  BEFORE UPDATE ON drivers
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rides_updated_at
  BEFORE UPDATE ON rides
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_driver_locations_updated_at
  BEFORE UPDATE ON driver_locations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Políticas RLS (Row Level Security)
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE rides ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;

-- Política para motoristas - podem ver e editar seus próprios dados
CREATE POLICY "Drivers can view and edit own data" ON drivers
  FOR ALL USING (auth.uid() = user_id);

-- Política para corridas - passageiros podem ver suas corridas
CREATE POLICY "Passengers can view own rides" ON rides
  FOR SELECT USING (auth.uid() = passenger_id);

-- Política para corridas - motoristas podem ver corridas atribuídas
CREATE POLICY "Drivers can view assigned rides" ON rides
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM drivers WHERE id = rides.driver_id
    )
  );

-- Política para localização de motoristas - público pode ver (para busca)
CREATE POLICY "Public can view driver locations" ON driver_locations
  FOR SELECT USING (true);

-- Política para localização de motoristas - apenas o próprio motorista pode atualizar
CREATE POLICY "Drivers can update own location" ON driver_locations
  FOR ALL USING (
    auth.uid() IN (
      SELECT user_id FROM drivers WHERE id = driver_locations.driver_id
    )
  );

-- Inserir dados de exemplo (opcional)
INSERT INTO drivers (user_id, name, email, phone, vehicle_model, vehicle_color, vehicle_plate, vehicle_year, status) VALUES
  (gen_random_uuid(), 'João Silva', '<EMAIL>', '+55 11 99999-1111', 'Honda Civic', 'Branco', 'ABC-1234', 2020, 'available'),
  (gen_random_uuid(), 'Maria Santos', '<EMAIL>', '+55 11 99999-2222', 'Toyota Corolla', 'Prata', 'DEF-5678', 2021, 'available'),
  (gen_random_uuid(), 'Pedro Costa', '<EMAIL>', '+55 11 99999-3333', 'Volkswagen Polo', 'Azul', 'GHI-9012', 2019, 'available')
ON CONFLICT (email) DO NOTHING;

-- Inserir localizações de exemplo para os motoristas
INSERT INTO driver_locations (driver_id, latitude, longitude, status) 
SELECT 
  d.id,
  -23.5505 + (random() - 0.5) * 0.1, -- São Paulo com variação
  -46.6333 + (random() - 0.5) * 0.1,
  'available'
FROM drivers d
WHERE NOT EXISTS (
  SELECT 1 FROM driver_locations dl WHERE dl.driver_id = d.id
);

-- Comentários para documentação
COMMENT ON TABLE drivers IS 'Tabela de motoristas cadastrados no sistema';
COMMENT ON TABLE rides IS 'Tabela de corridas solicitadas e realizadas';
COMMENT ON TABLE driver_locations IS 'Tabela de localização em tempo real dos motoristas';
COMMENT ON FUNCTION find_nearby_drivers IS 'Função para encontrar motoristas próximos a uma localização';

-- Verificar se as tabelas foram criadas
SELECT 
  table_name,
  table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('drivers', 'rides', 'driver_locations')
ORDER BY table_name;
