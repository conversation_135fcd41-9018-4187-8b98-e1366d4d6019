import { useEffect } from 'react'

export const useNoZoom = (): void => {
  useEffect(() => {
    // Configurar viewport para impedir zoom
    let metaViewport = document.querySelector('meta[name="viewport"]')
    if (!metaViewport) {
      metaViewport = document.createElement('meta')
      metaViewport.setAttribute('name', 'viewport')
      document.head.appendChild(metaViewport)
    }
    metaViewport.setAttribute('content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )

    // Impedir gestos de zoom
    const preventZoom = (e: TouchEvent) => {
      if (e.touches.length > 1) {
        e.preventDefault()
      }
    }

    const preventDoubleTouch = (e: TouchEvent) => {
      const now = Date.now()
      if (now - lastTouch <= 300) {
        e.preventDefault()
      }
      lastTouch = now
    }

    let lastTouch = 0

    document.addEventListener('touchstart', preventZoom, { passive: false })
    document.addEventListener('touchstart', preventDoubleTouch, { passive: false })

    return () => {
      document.removeEventListener('touchstart', preventZoom)
      document.removeEventListener('touchstart', preventDoubleTouch)
    }
  }, [])
}
