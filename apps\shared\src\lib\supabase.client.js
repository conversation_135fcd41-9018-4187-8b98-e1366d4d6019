/**
 * Cliente Supabase centralizado para todos os aplicativos MobiDrive
 *
 * Este módulo fornece uma instância única do cliente Supabase que pode ser
 * importada e usada por qualquer aplicativo no monorepo.
 */

import { createClient } from '@supabase/supabase-js';
import { SUPABASE_CONFIG, SUPABASE_URL, SUPABASE_ANON_KEY, TABLES } from '../config/supabase.config.js';
import SupabaseCache from './supabase-cache.js';
import SupabaseOfflineManager from './supabase-offline.js';
import SupabaseHealthMonitor from './supabase-health.js';

// Detectar se estamos no navegador ou no servidor
const isBrowser = typeof window !== 'undefined';

// Criar cliente Supabase com configurações otimizadas
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_CONFIG);

// Adicionar mecanismo de fallback para URLs antigas
if (isBrowser) {
  // URLs antigas que podem estar em cache
  const OLD_URLS = [
    'irpvrtugseftaelabktu.supabase.co'
  ];

  // Interceptar fetch para corrigir URLs antigas
  const originalFetch = window.fetch;
  window.fetch = function(resource, options) {
    if (typeof resource === 'string') {
      // Verificar se a URL contém alguma URL antiga
      for (const oldUrl of OLD_URLS) {
        if (resource.includes(oldUrl)) {
          // Substituir pela URL atual
          const newResource = resource.replace(oldUrl, SUPABASE_URL.replace('https://', ''));
          console.warn(`[Supabase] URL antiga detectada e corrigida: ${resource} -> ${newResource}`);
          resource = newResource;
          break;
        }
      }
    }

    // Chamar o fetch original
    return originalFetch.call(this, resource, options);
  };

  // Interceptar XMLHttpRequest para corrigir URLs antigas
  const originalOpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
    if (typeof url === 'string') {
      // Verificar se a URL contém alguma URL antiga
      for (const oldUrl of OLD_URLS) {
        if (url.includes(oldUrl)) {
          // Substituir pela URL atual
          const newUrl = url.replace(oldUrl, SUPABASE_URL.replace('https://', ''));
          console.warn(`[Supabase] URL antiga detectada e corrigida em XMLHttpRequest: ${url} -> ${newUrl}`);
          url = newUrl;
          break;
        }
      }
    }

    // Chamar o método original
    return originalOpen.call(this, method, url, async, user, password);
  };
}

// Criar instâncias dos módulos auxiliares
const offlineManager = new SupabaseOfflineManager(supabase, {
  debug: process.env.NODE_ENV === 'development'
});

const healthMonitor = new SupabaseHealthMonitor(supabase, {
  debug: process.env.NODE_ENV === 'development'
});

// Adicionar utilitários ao cliente Supabase
supabase.tables = TABLES;
supabase.cache = SupabaseCache;
supabase.offline = offlineManager;
supabase.health = healthMonitor;

// Adicionar método para verificar a conexão
supabase.checkConnection = async () => {
  try {
    const { data, error } = await supabase
      .from(TABLES.APP_SETTINGS)
      .select('key, value')
      .limit(1);

    return { connected: !error, data, error };
  } catch (error) {
    return { connected: false, error };
  }
};

// Adicionar método para limpar o cache
supabase.clearCache = () => {
  if (isBrowser) {
    // Limpar cache de autenticação
    const authKey = `sb-${SUPABASE_URL.replace('https://', '')}-auth-token`;
    localStorage.removeItem(authKey);

    // Limpar outros caches relacionados ao Supabase
    for (const key in localStorage) {
      if (key.startsWith('sb-') || key.includes('supabase') || key.includes('mobidrive')) {
        localStorage.removeItem(key);
      }
    }

    // Limpar cache do SupabaseCache
    SupabaseCache.clear();

    return true;
  }

  return false;
};

// Adicionar método para operações com cache
supabase.withCache = (table) => {
  const originalFrom = supabase.from(table);

  return {
    ...originalFrom,
    select: (columns) => {
      const originalSelect = originalFrom.select(columns);

      // Adicionar método para buscar com cache
      originalSelect.withCache = (ttl) => {
        return {
          ...originalSelect,
          eq: (column, value) => {
            const originalEq = originalSelect.eq(column, value);

            // Sobrescrever método then para usar cache
            const originalThen = originalEq.then.bind(originalEq);
            originalEq.then = (callback) => {
              // Gerar chave de cache
              const cacheKey = SupabaseCache.generateKey(table, {
                select: columns,
                eq: { column, value }
              });

              // Verificar cache
              const cachedData = SupabaseCache.get(cacheKey);
              if (cachedData) {
                return Promise.resolve(callback(cachedData));
              }

              // Se não estiver em cache, fazer requisição
              return originalThen((result) => {
                // Armazenar em cache se não houver erro
                if (!result.error) {
                  SupabaseCache.set(cacheKey, result, ttl);
                }

                return callback(result);
              });
            };

            return originalEq;
          }
        };
      };

      return originalSelect;
    }
  };
};

// Exportar a instância única
export default supabase;

// Exportar tabelas e configurações para conveniência
export { TABLES, SUPABASE_URL, SUPABASE_ANON_KEY };
