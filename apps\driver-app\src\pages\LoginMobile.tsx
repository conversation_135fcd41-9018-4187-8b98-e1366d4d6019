import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Navigate, Link } from 'react-router-dom'
import { Mail, Lock, Eye, EyeOff, ArrowRight, Car } from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { GradientBackground } from '../components/GradientBackground'
import { useNoZoom } from '../hooks/useNoZoom'

import '../styles/no-zoom.css'

// 📱 LOGIN MOBILE DRIVER - DESIGN FIEL AO ORIGINAL COM TEMA VERDE

interface LoginMobileProps {
  onLogin?: (email: string, password: string) => void
}

export const LoginMobile: React.FC<LoginMobileProps> = ({ onLogin }) => {
  const { signIn, loading, error, user } = useAuth()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  // 🚫 DESABILITA ZOOM COMPLETAMENTE + CONFIGURAÇÕES ANDROID NATIVAS
  useNoZoom()

  // Configurações Android nativas (mantendo design original)
  useEffect(() => {
    // Meta viewport para Android nativo
    let metaViewport = document.querySelector('meta[name="viewport"]')
    if (!metaViewport) {
      metaViewport = document.createElement('meta')
      metaViewport.setAttribute('name', 'viewport')
      document.head.appendChild(metaViewport)
    }
    metaViewport.setAttribute('content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )

    // Configurações de overflow para Android
    document.body.style.margin = '0'
    document.body.style.padding = '0'
    document.body.style.overflow = 'hidden'
    document.documentElement.style.margin = '0'
    document.documentElement.style.padding = '0'
    document.documentElement.style.overflow = 'hidden'
  }, [])

  // Redirect se já logado
  if (user) {
    return <Navigate to="/dashboard" replace />
  }

  // Submit do formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (onLogin) {
      onLogin(email, password)
    } else {
      await signIn(email, password)
    }
  }

  // Animações simples e limpas (mantendo originais)
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">
      {/* Background Gradient Verde (TEMA DRIVER) */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-emerald-600 opacity-70"></div>

      {/* Overlay muito sutil para legibilidade */}
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Conteúdo Principal */}
      <div className="relative z-10 flex flex-col h-full min-h-screen">

        {/* Header com Logo (TEMA DRIVER) */}
        <motion.div
          className="pt-8 pb-6 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10">
            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <Car className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-green-400">DRIVE</span>
              </h1>
              <p className="text-xs text-white/70">Painel do Motorista</p>
            </div>
          </div>
        </motion.div>

        {/* Formulário Central */}
        <div className="flex-1 flex items-center justify-center px-4">
          <motion.div
            className="w-full max-w-xs"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
              variants={itemVariants}
            >
              {/* Título do Formulário */}
              <motion.div
                className="text-center mb-6"
                variants={itemVariants}
              >
                <h2 className="text-xl font-semibold text-white mb-2">
                  Bem-vindo, Motorista
                </h2>
                <p className="text-white/70 text-sm">
                  Entre na sua conta para começar a dirigir
                </p>
              </motion.div>

              {/* Formulário */}
              <motion.form
                onSubmit={handleSubmit}
                className="space-y-4"
                variants={itemVariants}
              >
                {/* Campo Email */}
                <motion.div variants={itemVariants}>
                  <label htmlFor="email" className="block text-sm font-medium text-white/90 mb-2">
                    Email
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      autoComplete="email"
                      className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </motion.div>

                {/* Campo Senha */}
                <motion.div variants={itemVariants}>
                  <label htmlFor="password" className="block text-sm font-medium text-white/90 mb-2">
                    Senha
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                    <input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      autoComplete="current-password"
                      className="w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                      placeholder="••••••••"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                    >
                      {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </motion.div>

                {/* Mensagem de Erro */}
                <AnimatePresence>
                  {error && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="bg-red-500/20 border border-red-400/30 rounded-xl p-3"
                    >
                      <p className="text-red-200 text-sm">{error}</p>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Botão de Submit */}
                <motion.button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {loading ? (
                    <motion.div
                      className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                  ) : (
                    <>
                      <span>Entrar</span>
                      <ArrowRight className="w-4 h-4" />
                    </>
                  )}
                </motion.button>
              </motion.form>

              {/* Link de Cadastro */}
              <motion.div
                className="mt-6 text-center"
                variants={itemVariants}
              >
                <p className="text-white/70 text-sm">
                  Não tem uma conta?{' '}
                  <Link
                    to="/register"
                    className="text-green-400 font-medium hover:text-green-300 transition-colors"
                  >
                    Cadastre-se
                  </Link>
                </p>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>

        {/* Footer Simples */}
        <motion.div
          className="pb-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <p className="text-white/50 text-xs">
            © 2024 MobiDrive Driver. Todos os direitos reservados.
          </p>
        </motion.div>
      </div>
    </div>
  )
}

export default LoginMobile
