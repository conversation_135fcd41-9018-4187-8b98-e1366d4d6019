// =====================================================
// DEBUG DO SCHEMA SUPABASE
// Verifica campos da tabela ride_requests
// =====================================================

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

const supabase = createClient(supabaseUrl, supabaseKey)

async function debugSupabaseSchema() {
  console.log('🔍 INVESTIGANDO SCHEMA DO SUPABASE')
  console.log('=' .repeat(50))

  try {
    // 1. Verificar se a tabela ride_requests existe
    console.log('\n📋 Testando acesso à tabela ride_requests...')
    const { data: testData, error: testError } = await supabase
      .from('ride_requests')
      .select('*')
      .limit(1)

    if (testError) {
      console.error('❌ Erro ao acessar ride_requests:', testError)
      
      // Tentar outras variações de nome
      console.log('\n🔍 Tentando outras variações de nome...')
      
      const variations = ['rides', 'ride_request', 'requests', 'trip_requests']
      
      for (const tableName of variations) {
        try {
          const { data, error } = await supabase
            .from(tableName)
            .select('*')
            .limit(1)
          
          if (!error) {
            console.log(`✅ Tabela encontrada: ${tableName}`)
            console.log('📊 Dados de exemplo:', data)
          }
        } catch (e) {
          console.log(`❌ ${tableName}: não existe`)
        }
      }
    } else {
      console.log('✅ Tabela ride_requests acessível')
      console.log('📊 Dados de exemplo:', testData)
    }

    // 2. Testar inserção simples
    console.log('\n🧪 Testando inserção simples...')
    
    const testInsert = {
      user_id: '9ad5afad-8d2d-423e-b6e9-15e3e5a6ddc3', // ID do usuário atual
      origin_address: 'Teste Origin',
      destination_address: 'Teste Destination',
      status: 'pending'
    }
    
    const { data: insertData, error: insertError } = await supabase
      .from('ride_requests')
      .insert(testInsert)
      .select('id')
      .single()
    
    if (insertError) {
      console.error('❌ Erro na inserção:', insertError)
      console.log('📝 Detalhes do erro:', {
        code: insertError.code,
        message: insertError.message,
        details: insertError.details,
        hint: insertError.hint
      })
    } else {
      console.log('✅ Inserção bem-sucedida:', insertData)
      
      // Limpar teste
      await supabase
        .from('ride_requests')
        .delete()
        .eq('id', insertData.id)
      
      console.log('🧹 Registro de teste removido')
    }

    // 3. Verificar campos disponíveis
    console.log('\n📋 Verificando campos disponíveis...')
    
    const { data: fieldsData, error: fieldsError } = await supabase
      .from('ride_requests')
      .select('*')
      .limit(1)
    
    if (!fieldsError && fieldsData && fieldsData.length > 0) {
      console.log('📊 Campos disponíveis:', Object.keys(fieldsData[0]))
    }

    // 4. Testar campos específicos
    console.log('\n🔍 Testando campos específicos...')
    
    const fieldsToTest = [
      'pickup_lat', 'pickup_lng',
      'destination_lat', 'destination_lng',
      'origin_lat', 'origin_lng',
      'pickup_latitude', 'pickup_longitude',
      'distance', 'duration',
      'estimated_price', 'vehicle_type',
      'payment_method'
    ]
    
    for (const field of fieldsToTest) {
      try {
        const { error } = await supabase
          .from('ride_requests')
          .select(field)
          .limit(1)
        
        if (!error) {
          console.log(`✅ Campo existe: ${field}`)
        }
      } catch (e) {
        console.log(`❌ Campo não existe: ${field}`)
      }
    }

  } catch (error) {
    console.error('💥 Erro geral:', error)
  }
}

debugSupabaseSchema()
  .then(() => {
    console.log('\n🏁 DEBUG DO SCHEMA CONCLUÍDO!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
