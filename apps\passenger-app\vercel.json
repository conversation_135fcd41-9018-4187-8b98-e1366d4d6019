{"version": 2, "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)\\.js", "headers": [{"key": "Content-Type", "value": "application/javascript"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/(.*)\\.css", "headers": [{"key": "Content-Type", "value": "text/css"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/manifest.json", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "no-referrer-when-downgrade"}]}]}