// =====================================================
// SOLICITAÇÃO SIMPLES DE CORRIDA
// Versão otimizada para Rua Boa Vista, Ibotirama
// =====================================================

import puppeteer from 'puppeteer'

async function requestRideToIbotirama() {
  console.log('🚗 SOLICITAÇÃO DE CORRIDA PARA IBOTIRAMA')
  console.log('=' .repeat(50))
  console.log('📍 Destino: Rua Boa Vista, Ibotirama')
  console.log('🚗 Veículo: Carro normal')
  console.log('💰 Pagamento: Dinheiro')
  console.log('=' .repeat(50))

  let browser = null
  
  try {
    // Iniciar navegador com configurações otimizadas
    console.log('\n🚀 Iniciando navegador...')
    browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--no-first-run'
      ]
    })

    const page = await browser.newPage()
    await page.setViewport({ width: 1280, height: 720 })

    // Monitorar console
    page.on('console', (msg) => {
      const time = new Date().toLocaleTimeString()
      if (msg.text().includes('✅') || msg.text().includes('SUCCESS')) {
        console.log(`[${time}] 🟢 ${msg.text()}`)
      } else if (msg.text().includes('❌') || msg.text().includes('ERROR')) {
        console.log(`[${time}] 🔴 ${msg.text()}`)
      }
    })

    // ETAPA 1: LOGIN
    console.log('\n🔐 FAZENDO LOGIN...')
    
    try {
      await page.goto('http://localhost:3000/login', { 
        waitUntil: 'domcontentloaded',
        timeout: 15000 
      })
      
      console.log('✅ Página de login carregada')
      await new Promise(resolve => setTimeout(resolve, 3000))

      // Preencher credenciais
      const emailField = await page.$('input[type="email"]')
      const passwordField = await page.$('input[type="password"]')

      if (emailField && passwordField) {
        console.log('📝 Fazendo login...')
        
        await emailField.type('<EMAIL>', { delay: 50 })
        await passwordField.type('test123456', { delay: 50 })
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const submitButton = await page.$('button[type="submit"]')
        if (submitButton) {
          await submitButton.click()
          await new Promise(resolve => setTimeout(resolve, 5000))
          
          const currentUrl = page.url()
          if (!currentUrl.includes('/login')) {
            console.log('✅ Login realizado com sucesso!')
          } else {
            console.log('❌ Login falhou')
            return
          }
        }
      }
    } catch (error) {
      console.log('❌ Erro no login:', error.message)
      return
    }

    // ETAPA 2: NAVEGAR PARA SELEÇÃO DE MAPA
    console.log('\n🗺️ SELECIONANDO DESTINO...')
    
    try {
      await page.goto('http://localhost:3000/ride-request/map', { 
        waitUntil: 'domcontentloaded',
        timeout: 15000 
      })
      
      console.log('✅ Página de mapa carregada')
      await new Promise(resolve => setTimeout(resolve, 8000))
      
      // Tentar inserir destino via JavaScript diretamente
      console.log('📍 Inserindo destino: Rua Boa Vista, Ibotirama')
      
      await page.evaluate(() => {
        // Tentar encontrar campo de busca e preencher
        const searchInputs = document.querySelectorAll('input[type="text"], input[type="search"], input[placeholder*="destino"], input[placeholder*="onde"]')
        
        for (const input of searchInputs) {
          if (input.placeholder.toLowerCase().includes('destino') || 
              input.placeholder.toLowerCase().includes('onde') ||
              input.className.toLowerCase().includes('search')) {
            input.value = 'Rua Boa Vista, Ibotirama'
            input.dispatchEvent(new Event('input', { bubbles: true }))
            input.dispatchEvent(new Event('change', { bubbles: true }))
            console.log('✅ Destino inserido via JavaScript')
            break
          }
        }
      })
      
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Procurar e clicar em botão de confirmação
      try {
        const confirmButtons = await page.$$eval('button', buttons =>
          buttons.filter(btn =>
            btn.textContent.includes('Confirmar') ||
            btn.textContent.includes('Continuar')
          )
        )

        if (confirmButtons.length > 0) {
          console.log('🖱️ Confirmando destino...')
          await page.evaluate(() => {
            const buttons = document.querySelectorAll('button')
            for (const btn of buttons) {
              if (btn.textContent.includes('Confirmar') || btn.textContent.includes('Continuar')) {
                btn.click()
                break
              }
            }
          })
          await new Promise(resolve => setTimeout(resolve, 3000))
        }
      } catch (e) {
        console.log('⚠️ Botão de confirmação não encontrado')
      }
      
    } catch (error) {
      console.log('⚠️ Erro na seleção do mapa:', error.message)
    }

    // ETAPA 3: DETALHES DA VIAGEM
    console.log('\n🚗 CONFIGURANDO VIAGEM...')
    
    try {
      await page.goto('http://localhost:3000/ride-request/details', { 
        waitUntil: 'domcontentloaded',
        timeout: 15000 
      })
      
      console.log('✅ Página de detalhes carregada')
      await new Promise(resolve => setTimeout(resolve, 10000))
      
      // Selecionar carro normal via JavaScript
      console.log('🚗 Selecionando carro normal...')
      
      await page.evaluate(() => {
        // Procurar botões de veículo
        const vehicleButtons = document.querySelectorAll('button, div[role="button"]')
        
        for (const button of vehicleButtons) {
          const text = button.textContent?.toLowerCase() || ''
          if (text.includes('normal') || text.includes('carro') || text.includes('econômico')) {
            button.click()
            console.log('✅ Carro normal selecionado')
            break
          }
        }
      })
      
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Selecionar pagamento em dinheiro
      console.log('💰 Selecionando pagamento em dinheiro...')
      
      await page.evaluate(() => {
        // Procurar opções de pagamento
        const paymentButtons = document.querySelectorAll('button, div[role="button"], label')
        
        for (const button of paymentButtons) {
          const text = button.textContent?.toLowerCase() || ''
          if (text.includes('dinheiro') || text.includes('cash') || text.includes('espécie')) {
            button.click()
            console.log('✅ Pagamento em dinheiro selecionado')
            break
          }
        }
      })
      
      await new Promise(resolve => setTimeout(resolve, 2000))
      
    } catch (error) {
      console.log('⚠️ Erro nos detalhes:', error.message)
    }

    // ETAPA 4: SOLICITAR CORRIDA
    console.log('\n🚀 SOLICITANDO CORRIDA...')
    
    try {
      // Procurar botão de solicitar via JavaScript
      const requestButtonFound = await page.evaluate(() => {
        const buttons = document.querySelectorAll('button')

        for (const button of buttons) {
          const text = button.textContent?.toLowerCase() || ''
          if (text.includes('solicitar') || text.includes('confirmar') || text.includes('pedir')) {
            return true
          }
        }
        return false
      })

      if (requestButtonFound) {
        console.log('🎯 SOLICITANDO CORRIDA PARA RUA BOA VISTA, IBOTIRAMA!')
        console.log('📋 Detalhes:')
        console.log('   📍 Destino: Rua Boa Vista, Ibotirama')
        console.log('   🚗 Veículo: Carro normal')
        console.log('   💰 Pagamento: Dinheiro')

        // Clicar no botão via JavaScript
        await page.evaluate(() => {
          const buttons = document.querySelectorAll('button')

          for (const button of buttons) {
            const text = button.textContent?.toLowerCase() || ''
            if (text.includes('solicitar') || text.includes('confirmar') || text.includes('pedir')) {
              button.click()
              break
            }
          }
        })

        await new Promise(resolve => setTimeout(resolve, 8000))
        
        console.log('\n🎉 CORRIDA SOLICITADA COM SUCESSO!')
        console.log('✅ Aguardando motorista aceitar')
        console.log('📱 Acompanhe o status na aplicação')
        
        // Verificar se foi redirecionado
        const finalUrl = page.url()
        console.log(`🌐 URL atual: ${finalUrl}`)
        
        return true
        
      } else {
        console.log('❌ Botão de solicitação não encontrado')
        
        // Tentar via JavaScript como último recurso
        console.log('🔄 Tentando solicitar via JavaScript...')
        
        const jsResult = await page.evaluate(() => {
          const allButtons = document.querySelectorAll('button')
          
          for (const button of allButtons) {
            const text = button.textContent?.toLowerCase() || ''
            if (text.includes('solicitar') || text.includes('confirmar') || text.includes('pedir')) {
              button.click()
              return true
            }
          }
          
          return false
        })
        
        if (jsResult) {
          console.log('✅ Corrida solicitada via JavaScript!')
          await new Promise(resolve => setTimeout(resolve, 5000))
          return true
        } else {
          console.log('❌ Não foi possível solicitar a corrida')
          return false
        }
      }
      
    } catch (error) {
      console.log('❌ Erro ao solicitar corrida:', error.message)
      return false
    }

  } catch (error) {
    console.error('💥 Erro geral:', error.message)
    return false
  } finally {
    if (browser) {
      console.log('\n🔒 Mantendo navegador aberto para acompanhamento...')
      console.log('💡 Você pode fechar manualmente quando quiser')
      // Não fechar para permitir acompanhamento
    }
  }
}

// Executar solicitação
requestRideToIbotirama()
  .then((success) => {
    if (success) {
      console.log('\n🏁 SOLICITAÇÃO CONCLUÍDA COM SUCESSO!')
      console.log('🚗 Corrida para Rua Boa Vista, Ibotirama solicitada!')
    } else {
      console.log('\n⚠️ Houve problemas na solicitação')
      console.log('💡 Verifique a aplicação manualmente')
    }
    
    process.exit(success ? 0 : 1)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
