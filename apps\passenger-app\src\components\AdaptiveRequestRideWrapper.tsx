import React, { useEffect } from 'react'
import { RequestRideMobile } from '../pages/RequestRideMobile'
import RequestRide from '../pages/RequestRide'
import { DeviceWrapper } from './DeviceWrapper'
import { useDeviceDetection, shouldUseMobileNativeUI, shouldUseDesktopMockup, logDeviceInfo } from '../utils/deviceDetector'

// 🔄 WRAPPER ADAPTATIVO DE REQUEST RIDE
// Escolhe automaticamente entre UI mobile nativa e mockup desktop
// Baseado na detecção real do dispositivo
// MANTÉM DESIGN ORIGINAL + CONVERSÃO ANDROID NATIVA

export const AdaptiveRequestRideWrapper: React.FC = () => {
  const deviceInfo = useDeviceDetection()

  // Log informações do dispositivo para debug
  useEffect(() => {
    logDeviceInfo()
  }, [])

  // Decide qual UI usar baseado no dispositivo
  const useMobileNativeUI = shouldUseMobileNativeUI()
  const useDesktopMockup = shouldUseDesktopMockup()

  // Debug removido para console limpo

  // MOBILE NATIVO: Usa React Native Elements diretamente
  if (useMobileNativeUI) {
    return (
      <div style={{ 
        width: '100vw', 
        height: '100vh', 
        overflow: 'hidden',
        backgroundColor: '#f5f5f5'
      }}>
        <RequestRideMobile />
      </div>
    )
  }

  // DESKTOP MOCKUP: Usa o sistema de mockup iPhone
  if (useDesktopMockup) {
    return (
      <DeviceWrapper>
        <RequestRide />
      </DeviceWrapper>
    )
  }

  // FALLBACK: Usa mobile nativo como padrão
  return (
    <div style={{ 
      width: '100vw', 
      height: '100vh', 
      overflow: 'hidden',
      backgroundColor: '#f5f5f5'
    }}>
      <RequestRideMobile />
    </div>
  )
}

export default AdaptiveRequestRideWrapper
