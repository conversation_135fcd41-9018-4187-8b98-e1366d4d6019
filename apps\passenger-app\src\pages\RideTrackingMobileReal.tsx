import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Navigate, useParams, useLocation } from 'react-router-dom'
import { 
  MapPin, 
  ArrowLeft, 
  User, 
  Star, 
  Phone, 
  MessageCircle, 
  Navigation, 
  CheckCircle,
  Clock,
  X,
  AlertTriangle
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { GradientBackground } from '../components/GradientBackground'
import { useNoZoom } from '../hooks/useNoZoom'
import { ChatMobile } from '../components/ChatMobile'
import { rideService, RideRequest } from '../services/RideService'
import { simpleNotificationService } from '../services/SimpleNotificationService'
import { analyticsService } from '../services/AnalyticsService'
import '../styles/no-zoom.css'

export const RideTrackingMobileReal: React.FC = () => {
  const { user } = useAuth()
  const { rideId } = useParams<{ rideId: string }>()
  const location = useLocation()
  const [rideData, setRideData] = useState<RideRequest | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [chatOpen, setChatOpen] = useState(false)
  const [rideSubscription, setRideSubscription] = useState<any>(null)

  useNoZoom()

  // Redirect se não logado
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // Carregar dados da corrida
  useEffect(() => {
    if (rideId) {
      loadRideData()
      subscribeToRideUpdates()
    }

    return () => {
      if (rideSubscription) {
        rideSubscription.unsubscribe()
      }
    }
  }, [rideId])

  // Registrar visualização da página
  useEffect(() => {
    analyticsService.trackPageView('ride_tracking', {
      ride_id: rideId,
      has_driver: !!rideData?.driver_id
    })
  }, [rideId, rideData])

  const loadRideData = async () => {
    if (!rideId) return

    setLoading(true)
    setError(null)

    try {
      const data = await rideService.getRideStatus(rideId)
      if (data) {
        setRideData(data)
        
        // Registrar evento de tracking
        analyticsService.trackRideEvent('tracking_viewed', {
          ride_id: rideId,
          status: data.status,
          has_driver: !!data.driver_id
        })
      } else {
        setError('Corrida não encontrada')
      }
    } catch (error) {
      console.error('Erro ao carregar corrida:', error)
      setError('Erro ao carregar dados da corrida')
    } finally {
      setLoading(false)
    }
  }

  const subscribeToRideUpdates = () => {
    if (!rideId) return

    const subscription = rideService.subscribeToRideUpdates(rideId, (updatedRide) => {
      setRideData(updatedRide)
      
      // Mostrar notificação para mudanças de status importantes
      if (updatedRide.status === 'accepted' && rideData?.status === 'pending') {
        simpleNotificationService.notifyRideAccepted(
          user!.id,
          rideId,
          'Motorista'
        )
      } else if (updatedRide.status === 'in_progress' && rideData?.status === 'accepted') {
        simpleNotificationService.notifyRideStarted(user!.id, rideId)
      } else if (updatedRide.status === 'completed' && rideData?.status === 'in_progress') {
        simpleNotificationService.notifyRideCompleted(
          user!.id,
          rideId,
          updatedRide.final_price || updatedRide.estimated_price
        )
      }
    })

    setRideSubscription(subscription)
  }

  const cancelRide = async () => {
    if (!rideId || !rideData) return

    if (confirm('Tem certeza que deseja cancelar a corrida?')) {
      const success = await rideService.cancelRide(rideId, 'Cancelado pelo usuário')
      
      if (success) {
        analyticsService.trackRideEvent('cancelled', {
          ride_id: rideId,
          status: rideData.status,
          reason: 'user_cancelled'
        })
        
        window.location.href = '/dashboard'
      } else {
        alert('Erro ao cancelar corrida. Tente novamente.')
      }
    }
  }

  const openChat = () => {
    setChatOpen(true)
    analyticsService.trackUserAction('chat_opened', 'ride_tracking', {
      ride_id: rideId
    })
  }

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return { 
          icon: '🔍', 
          title: 'Procurando motorista...', 
          color: 'from-blue-500 to-blue-600',
          description: 'Aguarde enquanto encontramos um motorista próximo'
        }
      case 'accepted':
        return { 
          icon: '✅', 
          title: 'Motorista encontrado!', 
          color: 'from-green-500 to-green-600',
          description: 'O motorista está a caminho do local de partida'
        }
      case 'in_progress':
        return { 
          icon: '🛣️', 
          title: 'Viagem em andamento', 
          color: 'from-purple-500 to-purple-600',
          description: 'Tenha uma boa viagem!'
        }
      case 'completed':
        return { 
          icon: '🎉', 
          title: 'Viagem concluída!', 
          color: 'from-green-500 to-green-600',
          description: 'Obrigado por usar o MobiDrive!'
        }
      case 'cancelled':
        return { 
          icon: '❌', 
          title: 'Corrida cancelada', 
          color: 'from-red-500 to-red-600',
          description: 'A corrida foi cancelada'
        }
      default:
        return { 
          icon: '⏳', 
          title: 'Carregando...', 
          color: 'from-gray-500 to-gray-600',
          description: 'Aguarde...'
        }
    }
  }

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price)
  }

  if (loading) {
    return (
      <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">
        <GradientBackground variant="static" opacity={0.7} />
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 flex flex-col h-full min-h-screen items-center justify-center px-4">
          <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center">
            <motion.div
              className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4"
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Clock className="w-8 h-8 text-white" />
            </motion.div>
            <h2 className="text-xl font-bold text-white mb-2">Carregando corrida...</h2>
            <p className="text-white/70">Aguarde um momento</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !rideData) {
    return (
      <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">
        <GradientBackground variant="static" opacity={0.7} />
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 flex flex-col h-full min-h-screen items-center justify-center px-4">
          <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center">
            <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-white mb-2">Erro</h2>
            <p className="text-white/70 mb-4">{error || 'Corrida não encontrada'}</p>
            <motion.button
              onClick={() => window.location.href = '/dashboard'}
              className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-xl font-semibold"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Voltar ao Dashboard
            </motion.button>
          </div>
        </div>
      </div>
    )
  }

  const statusInfo = getStatusInfo(rideData.status)

  return (
    <>
      <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">
        <GradientBackground variant="static" opacity={0.7} />
        <div className="absolute inset-0 bg-black/20"></div>

        <div className="relative z-10 flex flex-col h-full min-h-screen">
          {/* Header */}
          <motion.div
            className="pt-8 pb-6 text-center"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="flex items-center justify-between px-4 mb-4">
              <motion.button
                onClick={() => window.location.href = '/dashboard'}
                className="p-2 text-white/80 hover:text-white transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ArrowLeft className="w-6 h-6" />
              </motion.button>
              
              <div className="inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                  <img src="/icons/icon-48x48.png" alt="MobiDrive" className="w-6 h-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white">
                    MOBI<span className="text-blue-400">DRIVE</span>
                  </h1>
                  <p className="text-xs text-white/70">Acompanhar Corrida</p>
                </div>
              </div>

              <div className="w-10 h-10"></div> {/* Spacer */}
            </div>
          </motion.div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto px-4 space-y-6">
            {/* Status da Corrida */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
            >
              <div className="text-center">
                <motion.div
                  className={`w-16 h-16 bg-gradient-to-r ${statusInfo.color} rounded-full flex items-center justify-center mx-auto mb-4`}
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <span className="text-2xl text-white">{statusInfo.icon}</span>
                </motion.div>
                <h2 className="text-xl font-bold text-white mb-2">{statusInfo.title}</h2>
                <p className="text-white/80 text-sm">{statusInfo.description}</p>
                <p className="text-white/60 text-xs mt-2">
                  Criada em {formatTime(rideData.created_at!)}
                </p>
              </div>
            </motion.div>

            {/* Informações do Motorista */}
            {rideData.driver_id && rideData.status !== 'pending' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
              >
                <h3 className="text-lg font-semibold text-white mb-4">👤 Seu Motorista</h3>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                    <User className="w-8 h-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-white">Motorista</h4>
                    <div className="flex items-center space-x-2 text-sm text-white/70">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span>4.8</span>
                      <span>•</span>
                      <span>Honda Civic</span>
                    </div>
                    <p className="text-sm text-white/60">Prata • ABC-1234</p>
                  </div>
                  <div className="flex space-x-2">
                    <motion.button
                      className="p-3 bg-green-500 text-white rounded-xl"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Phone className="w-5 h-5" />
                    </motion.button>
                    <motion.button
                      onClick={openChat}
                      className="p-3 bg-blue-500 text-white rounded-xl"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <MessageCircle className="w-5 h-5" />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Detalhes da Viagem */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
            >
              <h3 className="text-lg font-semibold text-white mb-4">📍 Detalhes da Viagem</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-white">Origem</p>
                    <p className="text-sm text-white/70">{rideData.origin_address}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-3 h-3 bg-red-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-white">Destino</p>
                    <p className="text-sm text-white/70">{rideData.destination_address}</p>
                  </div>
                </div>
                <div className="border-t border-white/10 pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-white/70">Valor da corrida:</span>
                    <span className="text-lg font-bold text-white">
                      {formatPrice(rideData.final_price || rideData.estimated_price)}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Ações */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
            >
              {rideData.status === 'completed' ? (
                <div className="space-y-3">
                  <motion.button
                    onClick={() => window.location.href = '/dashboard'}
                    className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <CheckCircle className="w-5 h-5" />
                    <span>Finalizar e Avaliar</span>
                  </motion.button>
                  <motion.button
                    onClick={() => window.location.href = '/request-ride'}
                    className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-4 rounded-xl font-semibold"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Nova Corrida
                  </motion.button>
                </div>
              ) : rideData.status !== 'cancelled' ? (
                <motion.button
                  onClick={cancelRide}
                  className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-xl font-semibold"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Cancelar Corrida
                </motion.button>
              ) : (
                <motion.button
                  onClick={() => window.location.href = '/dashboard'}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-4 rounded-xl font-semibold"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Voltar ao Dashboard
                </motion.button>
              )}
            </motion.div>
          </div>

          {/* Footer */}
          <motion.div
            className="pb-4 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <p className="text-white/50 text-xs">
              © 2024 MobiDrive. Todos os direitos reservados.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Chat Modal */}
      <AnimatePresence>
        {chatOpen && rideId && (
          <ChatMobile
            rideId={rideId}
            isOpen={chatOpen}
            onClose={() => setChatOpen(false)}
          />
        )}
      </AnimatePresence>
    </>
  )
}

export default RideTrackingMobileReal
