import React, { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  MapPin, ArrowRight, Zap,
  Navigation, Clock, CreditCard, Heart,
  Target, Route, Car, Shield, Sparkles
} from 'lucide-react'
import mapboxgl from 'mapbox-gl'
import { mapboxToken } from '../mapbox-config'
import { useAuth } from '../contexts/AuthContextSimple'
import { supabase } from '../lib/supabase'
import { useMapboxSearch } from '../hooks/useMapboxSearch'
import { useRealtimeDrivers } from '../hooks/useRealtimeDrivers'
import { driverMatchingService } from '../services/DriverMatchingService'
import { dynamicPricingService } from '../services/DynamicPricingService'
import { etaPredictionService } from '../services/ETAPredictionService'
import { analyticsService } from '../services/AnalyticsService'
import { safetyService } from '../services/SafetyService'
import { rewardsService } from '../services/RewardsService'
import { externalAPIService } from '../services/ExternalAPIService'
import RideMatchingStatus from '../components/RideMatchingStatus'
import SafetyDashboard from '../components/SafetyDashboard'
import RewardsCenter from '../components/RewardsCenter'
// 🚗 ADVANCED RIDE REQUEST - 2025 REDESIGN
// Implementing cutting-edge Mapbox features with glassmorphism design

// Configure Mapbox
mapboxgl.accessToken = mapboxToken

// Types and Interfaces
interface Location {
  coordinates: [number, number]
  address: string
  place_name: string
  center: [number, number]
}

interface VehicleOption {
  id: string
  name: string
  icon: string
  description: string
  basePrice: number
  pricePerKm: number
  eta: number
  available: boolean
  surge?: number
  features: string[]
}

interface RouteOption {
  id: string
  name: string
  duration: number
  distance: number
  traffic: 'light' | 'moderate' | 'heavy'
  price: number
  geometry: any
  waypoints?: Location[]
  priceEstimate?: any // Full price breakdown from dynamic pricing
}

interface PaymentMethod {
  id: string
  type: 'credit_card' | 'debit_card' | 'pix' | 'cash'
  name: string
  last_four?: string
  brand?: string
  is_default: boolean
}
// Vehicle Options Data
const VEHICLE_OPTIONS: VehicleOption[] = [
  {
    id: 'economy',
    name: 'MobiEconomy',
    icon: '🚗',
    description: 'Opção econômica e confiável',
    basePrice: 5.00,
    pricePerKm: 1.20,
    eta: 5,
    available: true,
    features: ['4 lugares', 'Ar condicionado', 'Música']
  },
  {
    id: 'comfort',
    name: 'MobiComfort',
    icon: '🚙',
    description: 'Mais espaço e conforto',
    basePrice: 8.00,
    pricePerKm: 1.80,
    eta: 7,
    available: true,
    features: ['4 lugares', 'Ar condicionado', 'Wi-Fi', 'Carregador']
  },
  {
    id: 'premium',
    name: 'MobiPremium',
    icon: '🏎️',
    description: 'Experiência premium',
    basePrice: 15.00,
    pricePerKm: 3.00,
    eta: 10,
    available: false,
    features: ['4 lugares', 'Couro', 'Wi-Fi', 'Água', 'Música premium']
  },
  {
    id: 'moto',
    name: 'MobiMoto',
    icon: '🏍️',
    description: 'Rápido e econômico',
    basePrice: 3.00,
    pricePerKm: 0.80,
    eta: 3,
    available: true,
    surge: 1.2,
    features: ['1 pessoa', 'Capacete', 'Entrega rápida']
  }
]

// Animation Variants (used in JSX)
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
}

// Main Component
export const RequestRide: React.FC = () => {
  const { user } = useAuth()
  const navigate = useNavigate()
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)

  // State Management
  const [currentStep, setCurrentStep] = useState<'location' | 'vehicle' | 'payment' | 'confirmation'>('location')
  const [pickupLocation, setPickupLocation] = useState<Location | null>(null)
  const [destinationLocation, setDestinationLocation] = useState<Location | null>(null)
  const [selectedVehicle, setSelectedVehicle] = useState<VehicleOption | null>(null)
  const [selectedPayment, setSelectedPayment] = useState<PaymentMethod | null>(null)
  const [routeOptions, setRouteOptions] = useState<RouteOption[]>([])
  const [selectedRoute, setSelectedRoute] = useState<RouteOption | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null)
  const [isSearchingDriver, setIsSearchingDriver] = useState(false)
  const [matchedDriver, setMatchedDriver] = useState<any>(null)
  const [searchStartTime, setSearchStartTime] = useState<Date | null>(null)
  const [estimatedWaitTime, setEstimatedWaitTime] = useState(0)
  const [showSafetyDashboard, setShowSafetyDashboard] = useState(false)
  const [showRewardsCenter, setShowRewardsCenter] = useState(false)
  const [currentRideId, setCurrentRideId] = useState<string | null>(null)
  const [weatherData, setWeatherData] = useState<any>(null)
  const [userPoints, setUserPoints] = useState(0)

  // Use Mapbox Search Hook for better proximity-based search
  const {
    searchQuery,
    searchResults,
    isSearching,
    searchError,
    searchPlaces,
    clearSearch
  } = useMapboxSearch({ userLocation: userLocation || undefined })

  // Use Realtime Drivers Hook for live driver tracking
  const {
    drivers: activeDrivers,
    nearbyDrivers,
    isConnected: driversConnected,
    activeDriversCount,
    error: driversError
  } = useRealtimeDrivers({
    userLocation: userLocation || undefined,
    radiusKm: 10,
    autoConnect: true
  })

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current || map.current) return

    // Track page view and performance
    analyticsService.trackPageView('/request-ride')
    analyticsService.trackAppPerformance()

    // Set user for analytics
    if (user?.id) {
      analyticsService.setUser(user.id)

      // Initialize safety service
      safetyService.initialize(user.id).catch(console.error)

      // Load user rewards
      loadUserRewards()

      // Load weather data
      loadWeatherData()
    }

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/streets-v12',
      center: [-46.6333, -23.5505], // São Paulo
      zoom: 12,
      attributionControl: false,
      antialias: true
    })

    map.current.on('load', () => {
      // Add traffic layer
      map.current!.addSource('mapbox-traffic', {
        type: 'vector',
        url: 'mapbox://mapbox.mapbox-traffic-v1'
      })

      map.current!.addLayer({
        id: 'traffic',
        type: 'line',
        source: 'mapbox-traffic',
        'source-layer': 'traffic',
        paint: {
          'line-width': 2,
          'line-color': [
            'case',
            ['==', ['get', 'congestion'], 'low'], '#00ff00',
            ['==', ['get', 'congestion'], 'moderate'], '#ffff00',
            ['==', ['get', 'congestion'], 'heavy'], '#ff0000',
            '#000000'
          ]
        }
      })
    })

    // Get user location
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const coords: [number, number] = [position.coords.longitude, position.coords.latitude]
        console.log('🎯 User location obtained:', coords, 'Accuracy:', position.coords.accuracy + 'm')
        setUserLocation(coords)
        map.current?.setCenter(coords)
        map.current?.setZoom(14)
      },
      (error) => {
        console.warn('Could not get user location:', error)
        // Default to São Paulo center
        const defaultCoords: [number, number] = [-46.6333, -23.5505]
        console.log('🎯 Using default location (São Paulo):', defaultCoords)
        setUserLocation(defaultCoords)
        map.current?.setCenter(defaultCoords)
        map.current?.setZoom(12)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    )

    return () => {
      map.current?.remove()
    }
  }, [])

  // Load user rewards
  const loadUserRewards = async () => {
    try {
      if (!user?.id) return
      const rewards = await rewardsService.getUserRewards(user.id)
      setUserPoints(rewards.total_points)
    } catch (error) {
      console.error('Error loading user rewards:', error)
    }
  }

  // Load weather data
  const loadWeatherData = async () => {
    try {
      if (!userLocation) return
      const weather = await externalAPIService.getWeatherData(userLocation[1], userLocation[0])
      setWeatherData(weather)
    } catch (error) {
      console.error('Error loading weather data:', error)
    }
  }

  // Award points for actions
  const awardPoints = async (points: number, reason: string, metadata?: any) => {
    try {
      if (!user?.id) return
      await rewardsService.awardPoints(user.id, points, reason, metadata)
      await loadUserRewards() // Refresh points
    } catch (error) {
      console.error('Error awarding points:', error)
    }
  }

  // Handle search input changes
  const handleSearchInput = useCallback((query: string) => {
    searchPlaces(query)
  }, [searchPlaces])

  // Calculate route function
  const calculateRoute = useCallback(async (pickup: Location, destination: Location) => {
    if (!pickup || !destination) return

    setIsLoading(true)
    try {
      const response = await fetch(
        `https://api.mapbox.com/directions/v5/mapbox/driving/${pickup.coordinates.join(',')};${destination.coordinates.join(',')}?` +
        `access_token=${mapboxToken}&` +
        `geometries=geojson&` +
        `overview=full&` +
        `steps=true&` +
        `alternatives=true&` +
        `annotations=traffic`
      )

      const data = await response.json()

      if (data.routes && data.routes.length > 0) {
        const routes: RouteOption[] = await Promise.all(
          data.routes.map(async (route: any, index: number) => {
            const distanceKm = Math.round(route.distance / 1000 * 100) / 100
            const durationMin = Math.round(route.duration / 60)

            // Calculate dynamic price for economy vehicle (default)
            const priceEstimate = await dynamicPricingService.calculatePrice(
              'economy',
              distanceKm,
              durationMin,
              pickup.coordinates[1],
              pickup.coordinates[0],
              destination.coordinates[1],
              destination.coordinates[0]
            )

            return {
              id: `route-${index}`,
              name: index === 0 ? 'Rota Recomendada' : `Alternativa ${index}`,
              duration: durationMin,
              distance: distanceKm,
              traffic: route.duration_traffic > route.duration * 1.2 ? 'heavy' :
                      route.duration_traffic > route.duration * 1.1 ? 'moderate' : 'light',
              price: priceEstimate.finalPrice,
              geometry: route.geometry,
              priceEstimate // Store full price details
            }
          })
        )

        setRouteOptions(routes)
        setSelectedRoute(routes[0])

        // Update map with route
        if (map.current && routes[0]) {
          // Add route source
          if (map.current.getSource('route')) {
            (map.current.getSource('route') as mapboxgl.GeoJSONSource).setData(routes[0].geometry)
          } else {
            map.current.addSource('route', {
              type: 'geojson',
              data: routes[0].geometry
            })

            map.current.addLayer({
              id: 'route',
              type: 'line',
              source: 'route',
              layout: {
                'line-join': 'round',
                'line-cap': 'round'
              },
              paint: {
                'line-color': '#3b82f6',
                'line-width': 4,
                'line-opacity': 0.8
              }
            })
          }

          // Fit bounds
          const bounds = new mapboxgl.LngLatBounds()
          bounds.extend(pickup.coordinates)
          bounds.extend(destination.coordinates)
          map.current.fitBounds(bounds, { padding: 50 })
        }
      }
    } catch (error) {
      console.error('Route calculation error:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Add markers to map including active drivers
  const addMarkers = useCallback(() => {
    if (!map.current) return

    // Clear existing markers
    const existingMarkers = document.querySelectorAll('.custom-marker')
    existingMarkers.forEach(marker => marker.remove())

    // Add pickup marker
    if (pickupLocation) {
      const pickupEl = document.createElement('div')
      pickupEl.className = 'custom-marker pickup-marker'
      pickupEl.innerHTML = `
        <div style="
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
          border: 3px solid white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 12px rgba(34, 197, 94, 0.4);
          font-size: 18px;
          cursor: pointer;
          z-index: 1000;
        ">📍</div>
      `

      new mapboxgl.Marker(pickupEl)
        .setLngLat(pickupLocation.coordinates)
        .addTo(map.current)
    }

    // Add destination marker
    if (destinationLocation) {
      const destEl = document.createElement('div')
      destEl.className = 'custom-marker destination-marker'
      destEl.innerHTML = `
        <div style="
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
          border: 3px solid white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
          font-size: 18px;
          cursor: pointer;
          z-index: 1000;
        ">🎯</div>
      `

      new mapboxgl.Marker(destEl)
        .setLngLat(destinationLocation.coordinates)
        .addTo(map.current)
    }

    // Add active driver markers
    nearbyDrivers.forEach((driver, index) => {
      const driverEl = document.createElement('div')
      driverEl.className = 'custom-marker driver-marker'

      // Different icons for different vehicle types
      const vehicleIcon = driver.vehicle_type === 'moto' ? '🏍️' :
                         driver.vehicle_type === 'premium' ? '🚗' :
                         driver.vehicle_type === 'comfort' ? '🚙' : '🚕'

      driverEl.innerHTML = `
        <div style="
          width: 32px;
          height: 32px;
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          border: 2px solid white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
          font-size: 14px;
          cursor: pointer;
          z-index: 500;
          animation: pulse 2s infinite;
        ">${vehicleIcon}</div>
      `

      const driverMarker = new mapboxgl.Marker(driverEl)
        .setLngLat([driver.longitude, driver.latitude])
        .addTo(map.current!)

      // Add popup with driver info
      const popup = new mapboxgl.Popup({
        offset: 25,
        closeButton: false,
        closeOnClick: false
      }).setHTML(`
        <div style="padding: 8px; font-size: 12px;">
          <div style="font-weight: bold; color: #1f2937;">${driver.driver_name}</div>
          <div style="color: #6b7280;">⭐ ${driver.driver_rating.toFixed(1)}</div>
          <div style="color: #6b7280;">${driver.vehicle_info.color} ${driver.vehicle_info.make} ${driver.vehicle_info.model}</div>
          <div style="color: #3b82f6; font-size: 10px;">📍 ${(driver.distance || 0).toFixed(1)}km de distância</div>
        </div>
      `)

      driverEl.addEventListener('mouseenter', () => {
        driverMarker.setPopup(popup).togglePopup()
      })

      driverEl.addEventListener('mouseleave', () => {
        popup.remove()
      })
    })

    console.log(`🗺️ Added ${nearbyDrivers.length} driver markers to map`)
  }, [pickupLocation, destinationLocation, nearbyDrivers])

  // Update markers when locations change
  useEffect(() => {
    addMarkers()
  }, [addMarkers])

  // Calculate route when both locations are set
  useEffect(() => {
    if (pickupLocation && destinationLocation) {
      calculateRoute(pickupLocation, destinationLocation)
    }
  }, [pickupLocation, destinationLocation, calculateRoute])

  // Handle location selection
  const handleLocationSelect = (location: any, type: 'pickup' | 'destination') => {
    const newLocation: Location = {
      coordinates: location.center,
      address: location.place_name,
      place_name: location.place_name,
      center: location.center
    }

    if (type === 'pickup') {
      setPickupLocation(newLocation)
    } else {
      setDestinationLocation(newLocation)
    }

    clearSearch()
  }

  // Handle vehicle selection with dynamic pricing
  const handleVehicleSelect = async (vehicle: VehicleOption) => {
    if (!vehicle.available) return

    setSelectedVehicle(vehicle)
    setIsLoading(true)

    try {
      // Recalculate prices for selected vehicle type
      if (routeOptions.length > 0 && pickupLocation && destinationLocation) {
        const updatedRoutes = await Promise.all(
          routeOptions.map(async (route) => {
            const priceEstimate = await dynamicPricingService.calculatePrice(
              vehicle.id,
              route.distance,
              route.duration,
              pickupLocation.coordinates[1],
              pickupLocation.coordinates[0],
              destinationLocation.coordinates[1],
              destinationLocation.coordinates[0]
            )

            return {
              ...route,
              price: priceEstimate.finalPrice,
              priceEstimate
            }
          })
        )

        setRouteOptions(updatedRoutes)

        const updatedSelectedRoute = updatedRoutes.find(r => r.id === selectedRoute?.id)
        if (updatedSelectedRoute) {
          setSelectedRoute(updatedSelectedRoute)
        }
      }

      setCurrentStep('payment')
    } catch (error) {
      console.error('Error updating prices for vehicle:', error)
      // Fallback to simple calculation
      if (routeOptions.length > 0 && selectedRoute) {
        const updatedRoutes = routeOptions.map(route => ({
          ...route,
          price: vehicle.basePrice + (route.distance * vehicle.pricePerKm) * (vehicle.surge || 1)
        }))
        setRouteOptions(updatedRoutes)

        const updatedSelectedRoute = updatedRoutes.find(r => r.id === selectedRoute.id)
        if (updatedSelectedRoute) {
          setSelectedRoute(updatedSelectedRoute)
        }
      }
      setCurrentStep('payment')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle ride confirmation
  const handleConfirmRide = async () => {
    if (!user || !pickupLocation || !destinationLocation || !selectedVehicle || !selectedRoute) {
      return
    }

    setIsLoading(true)
    setIsSearchingDriver(true)
    setSearchStartTime(new Date())

    // Track ride request
    analyticsService.trackRideRequest(pickupLocation.address, destinationLocation.address, selectedVehicle.id)

    try {
      // 1. Criar corrida no banco de dados
      const { data, error } = await supabase
        .from('ride_requests')
        .insert({
          user_id: user.id,
          origin_address: pickupLocation.address,
          origin_coords: `POINT(${pickupLocation.coordinates[0]} ${pickupLocation.coordinates[1]})`,
          destination_address: destinationLocation.address,
          destination_coords: `POINT(${destinationLocation.coordinates[0]} ${destinationLocation.coordinates[1]})`,
          distance: selectedRoute.distance,
          duration: selectedRoute.duration,
          estimated_price: selectedRoute.price,
          vehicle_type: selectedVehicle.id,
          status: 'pending'
        })
        .select()
        .single()

      if (error) throw error

      console.log('🚗 Corrida criada com sucesso:', data.id)

      // 2. Find best drivers using intelligent matching
      const matchingCriteria = driverMatchingService.getDefaultCriteria(selectedVehicle.id)
      const bestDrivers = await driverMatchingService.findBestDrivers(
        pickupLocation.coordinates[1],
        pickupLocation.coordinates[0],
        matchingCriteria,
        nearbyDrivers
      )

      console.log(`🎯 Found ${bestDrivers.length} suitable drivers`)

      if (bestDrivers.length === 0) {
        throw new Error('Nenhum motorista disponível no momento. Tente novamente em alguns minutos.')
      }

      // 3. Send ride request to best drivers
      const expiresAt = new Date(Date.now() + 30000) // 30 segundos para responder

      const rideRequest = {
        id: data.id,
        passenger_id: user.id,
        pickup_lat: pickupLocation.coordinates[1],
        pickup_lng: pickupLocation.coordinates[0],
        destination_lat: destinationLocation.coordinates[1],
        destination_lng: destinationLocation.coordinates[0],
        vehicle_type: selectedVehicle.id,
        estimated_price: selectedRoute.price,
        estimated_distance: selectedRoute.distance,
        estimated_duration: selectedRoute.duration,
        created_at: new Date().toISOString(),
        expires_at: expiresAt.toISOString()
      }

      const notifiedDrivers = await driverMatchingService.sendRideRequestToDrivers(
        rideRequest,
        bestDrivers,
        3 // Send to top 3 drivers
      )

      console.log(`✅ Notificação enviada para ${notifiedDrivers.length} motoristas`)

      // 3. Set current ride ID and initialize safety
      setCurrentRideId(data.id)

      // 4. Award points for ride request
      await awardPoints(10, 'Solicitação de corrida', {
        ride_id: data.id,
        vehicle_type: selectedVehicle.id,
        distance: selectedRoute.distance
      })

      // 5. Setup safety monitoring
      if (selectedRoute.duration) {
        safetyService.setupCheckInReminders(data.id, selectedRoute.duration)
      }

      // 6. Navigate to ride tracking
      navigate('/ride-tracking', {
        state: {
          rideId: data.id,
          pickup: pickupLocation,
          destination: destinationLocation,
          vehicle: selectedVehicle,
          route: selectedRoute
        }
      })
    } catch (error) {
      console.error('❌ Erro ao solicitar corrida:', error)
      alert('Erro ao solicitar corrida. Tente novamente.')
    } finally {
      setIsLoading(false)
    }
  }

  // Render JSX
  return (
    <>
      {/* Add CSS for driver marker animations */}
      <style>{`
        @keyframes pulse {
          0%, 100% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.1); opacity: 0.8; }
        }
        .driver-marker:hover {
          transform: scale(1.2) !important;
          transition: transform 0.2s ease-in-out;
        }
      `}</style>

      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[url('/api/placeholder/1920/1080')] bg-cover bg-center opacity-5" />
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10" />

      {/* Ride Matching Status */}
      <RideMatchingStatus
        isSearching={isSearchingDriver}
        driversFound={nearbyDrivers.length}
        estimatedWaitTime={estimatedWaitTime}
        matchedDriver={matchedDriver}
        onCancel={() => {
          setIsSearchingDriver(false)
          setMatchedDriver(null)
          setSearchStartTime(null)
          analyticsService.track('ride_search_cancelled', {
            search_duration: searchStartTime ? Date.now() - searchStartTime.getTime() : 0
          })
        }}
      />

      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative z-10 p-6 pb-0"
      >
        <div className="flex items-center justify-between mb-6">
          <motion.button
            onClick={() => navigate('/dashboard')}
            className="p-3 rounded-2xl bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <ArrowRight className="w-6 h-6 rotate-180" />
          </motion.button>

          <div className="text-center">
            <h1 className="text-2xl font-bold text-white mb-1">Solicitar Corrida</h1>
            <p className="text-white/70 text-sm">Para onde vamos hoje?</p>
            {weatherData && (
              <div className="flex items-center justify-center space-x-2 mt-2">
                <span className="text-white/60 text-xs">
                  {weatherData.temperature}°C • {weatherData.condition}
                </span>
              </div>
            )}
            {/* Debug info */}
            {userLocation && (
              <div className="text-white/50 text-xs mt-1">
                📍 {userLocation[1].toFixed(4)}, {userLocation[0].toFixed(4)}
                <button
                  onClick={() => {
                    console.log('🧪 Testing search with user location:', userLocation)
                    searchPlaces('rua boa vista')
                  }}
                  className="ml-2 px-2 py-1 bg-blue-500/20 rounded text-xs"
                >
                  Test Search
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {/* Safety Button */}
            <motion.button
              onClick={() => setShowSafetyDashboard(true)}
              className="p-3 rounded-2xl bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              🛡️
            </motion.button>

            {/* Rewards Button */}
            <motion.button
              onClick={() => setShowRewardsCenter(true)}
              className="p-3 rounded-2xl bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 transition-all duration-300 relative"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              🏆
              {userPoints > 0 && (
                <span className="absolute -top-1 -right-1 bg-yellow-500 text-yellow-900 text-xs px-1.5 py-0.5 rounded-full font-bold">
                  {userPoints}
                </span>
              )}
            </motion.button>

            {/* Sparkles Animation */}
            <motion.div
              className="p-3 rounded-2xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-md border border-white/20"
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
            >
              <Sparkles className="w-6 h-6 text-white" />
            </motion.div>
          </div>
        </div>

        {/* Step Indicator */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white/10 backdrop-blur-md rounded-3xl p-4 border border-white/20 mb-6"
        >
          <div className="flex items-center justify-between">
            {[
              { key: 'location', icon: MapPin, label: 'Local' },
              { key: 'vehicle', icon: Car, label: 'Veículo' },
              { key: 'payment', icon: CreditCard, label: 'Pagamento' },
              { key: 'confirmation', icon: Shield, label: 'Confirmar' }
            ].map((step, index) => {
              const isActive = currentStep === step.key
              const isCompleted = ['location', 'vehicle', 'payment', 'confirmation'].indexOf(currentStep) > index

              return (
                <div key={step.key} className="flex items-center">
                  <motion.div
                    className={`w-12 h-12 rounded-2xl flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                      isActive
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg shadow-blue-500/25'
                        : isCompleted
                        ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white'
                        : 'bg-white/10 text-white/50'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    animate={isActive ? { scale: [1, 1.05, 1] } : {}}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <step.icon className="w-5 h-5" />
                  </motion.div>
                  {index < 3 && (
                    <div className={`w-8 h-1 mx-2 rounded-full transition-all duration-300 ${
                      isCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' : 'bg-white/20'
                    }`} />
                  )}
                </div>
              )
            })}
          </div>
        </motion.div>
      </motion.div>

      {/* Main Content */}
      <div className="relative z-10 px-6 pb-6">
        <AnimatePresence mode="wait">
          {/* Location Step */}
          {currentStep === 'location' && (
            <motion.div
              key="location"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-4"
            >
              {/* Search Inputs */}
              <div className="space-y-3">
                {/* Pickup Input */}
                <motion.div
                  className="bg-white/10 backdrop-blur-md rounded-3xl p-4 border border-white/20"
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                      <Navigation className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <label className="text-white/70 text-xs font-medium">Origem</label>
                      <input
                        type="text"
                        placeholder="Sua localização atual"
                        value={pickupLocation?.address || searchQuery}
                        onChange={(e) => {
                          handleSearchInput(e.target.value)
                        }}
                        className="w-full bg-transparent text-white placeholder-white/50 border-none outline-none text-sm font-medium mt-1"
                      />
                    </div>
                    <motion.button
                      onClick={() => {
                        if (userLocation) {
                          // Reverse geocode current location
                          fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${userLocation.join(',')}.json?access_token=${mapboxToken}`)
                            .then(res => res.json())
                            .then(data => {
                              if (data.features?.[0]) {
                                handleLocationSelect(data.features[0], 'pickup')
                              }
                            })
                        }
                      }}
                      className="p-2 rounded-xl bg-white/10 hover:bg-white/20 transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Target className="w-4 h-4 text-white" />
                    </motion.button>
                  </div>
                </motion.div>

                {/* Destination Input */}
                <motion.div
                  className="bg-white/10 backdrop-blur-md rounded-3xl p-4 border border-white/20"
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-2xl bg-gradient-to-r from-red-500 to-pink-500 flex items-center justify-center">
                      <MapPin className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <label className="text-white/70 text-xs font-medium">Destino</label>
                      <input
                        type="text"
                        placeholder="Para onde você quer ir?"
                        value={destinationLocation?.address || searchQuery}
                        onChange={(e) => {
                          handleSearchInput(e.target.value)
                        }}
                        className="w-full bg-transparent text-white placeholder-white/50 border-none outline-none text-sm font-medium mt-1"
                      />
                    </div>
                    <motion.button
                      className="p-2 rounded-xl bg-white/10 hover:bg-white/20 transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Heart className="w-4 h-4 text-white" />
                    </motion.button>
                  </div>
                </motion.div>
              </div>

              {/* Search Results */}
              {searchResults.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white/10 backdrop-blur-md rounded-3xl border border-white/20 overflow-hidden"
                >
                  {searchResults.map((result, index) => (
                    <motion.button
                      key={index}
                      onClick={() => handleLocationSelect(result, destinationLocation ? 'pickup' : 'destination')}
                      className="w-full p-4 text-left hover:bg-white/10 transition-all duration-300 border-b border-white/10 last:border-b-0"
                      whileHover={{ x: 5 }}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-xl bg-white/10 flex items-center justify-center">
                          <MapPin className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <p className="text-white font-medium text-sm">{result.text}</p>
                          <p className="text-white/60 text-xs">{result.place_name}</p>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </motion.div>
              )}

              {/* Interactive Map */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-white/10 backdrop-blur-md rounded-3xl border border-white/20 overflow-hidden"
              >
                <div className="p-4 border-b border-white/20">
                  <div className="flex items-center justify-between">
                    <h3 className="text-white font-semibold">Mapa Interativo</h3>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse" />
                        <span className="text-white/70 text-xs">Tráfego em tempo real</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${driversConnected ? 'bg-blue-500 animate-pulse' : 'bg-gray-500'}`} />
                        <span className="text-white/70 text-xs">
                          {nearbyDrivers.length} motoristas próximos
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  ref={mapContainer}
                  className="h-80 w-full"
                  style={{ minHeight: '320px' }}
                />
              </motion.div>

              {/* Continue Button */}
              {pickupLocation && destinationLocation && (
                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  onClick={() => setCurrentStep('vehicle')}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold py-4 px-6 rounded-3xl shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center justify-center space-x-2">
                    <span>Escolher Veículo</span>
                    <ArrowRight className="w-5 h-5" />
                  </div>
                </motion.button>
              )}
            </motion.div>
          )}

          {/* Vehicle Selection Step */}
          {currentStep === 'vehicle' && (
            <motion.div
              key="vehicle"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-4"
            >
              {/* Route Summary */}
              {selectedRoute && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white/10 backdrop-blur-md rounded-3xl p-4 border border-white/20"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                        <Route className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="text-white font-semibold">{selectedRoute.name}</p>
                        <p className="text-white/70 text-sm">{selectedRoute.distance} km • {selectedRoute.duration} min</p>
                      </div>
                    </div>
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                      selectedRoute.traffic === 'light' ? 'bg-green-500/20 text-green-400' :
                      selectedRoute.traffic === 'moderate' ? 'bg-yellow-500/20 text-yellow-400' :
                      'bg-red-500/20 text-red-400'
                    }`}>
                      {selectedRoute.traffic === 'light' ? 'Trânsito livre' :
                       selectedRoute.traffic === 'moderate' ? 'Trânsito moderado' :
                       'Trânsito intenso'}
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Vehicle Options */}
              <div className="space-y-3">
                <h3 className="text-white font-semibold text-lg mb-2">Escolha seu veículo</h3>
                {VEHICLE_OPTIONS.map((vehicle, index) => {
                  const price = selectedRoute ? vehicle.basePrice + (selectedRoute.distance * vehicle.pricePerKm) * (vehicle.surge || 1) : 0
                  const isSelected = selectedVehicle?.id === vehicle.id

                  return (
                    <motion.button
                      key={vehicle.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => handleVehicleSelect(vehicle)}
                      disabled={!vehicle.available}
                      className={`w-full p-4 rounded-3xl border transition-all duration-300 ${
                        isSelected
                          ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-500/50 shadow-lg shadow-blue-500/25'
                          : vehicle.available
                          ? 'bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/15 hover:border-white/30'
                          : 'bg-white/5 border-white/10 opacity-50'
                      }`}
                      whileHover={vehicle.available ? { scale: 1.02 } : {}}
                      whileTap={vehicle.available ? { scale: 0.98 } : {}}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="text-3xl">{vehicle.icon}</div>
                          <div className="text-left">
                            <div className="flex items-center space-x-2">
                              <h4 className="text-white font-semibold">{vehicle.name}</h4>
                              {vehicle.surge && (
                                <span className="px-2 py-1 bg-orange-500/20 text-orange-400 text-xs rounded-full">
                                  {vehicle.surge}x
                                </span>
                              )}
                            </div>
                            <p className="text-white/70 text-sm">{vehicle.description}</p>
                            <div className="flex items-center space-x-2 mt-1">
                              <Clock className="w-3 h-3 text-white/50" />
                              <span className="text-white/50 text-xs">{vehicle.eta} min</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-white font-bold text-lg">
                            R$ {price.toFixed(2)}
                          </p>
                          {selectedRoute?.priceEstimate?.surge > 1.2 && (
                            <div className="flex items-center space-x-1">
                              <span className="text-orange-400 text-xs">🔥</span>
                              <span className="text-orange-400 text-xs">
                                {selectedRoute.priceEstimate.surge.toFixed(1)}x
                              </span>
                            </div>
                          )}
                          {!vehicle.available && (
                            <p className="text-red-400 text-xs">Indisponível</p>
                          )}
                        </div>
                      </div>

                      {/* Features */}
                      <div className="flex flex-wrap gap-2 mt-3">
                        {vehicle.features.map((feature, idx) => (
                          <span
                            key={idx}
                            className="px-2 py-1 bg-white/10 text-white/70 text-xs rounded-full"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </motion.button>
                  )
                })}
              </div>
            </motion.div>
          )}

          {/* Payment Step */}
          {currentStep === 'payment' && (
            <motion.div
              key="payment"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-4"
            >
              {/* Trip Summary */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white/10 backdrop-blur-md rounded-3xl p-4 border border-white/20"
              >
                <h3 className="text-white font-semibold mb-3">Resumo da Viagem</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Veículo</span>
                    <span className="text-white font-medium">{selectedVehicle?.name}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Distância</span>
                    <span className="text-white font-medium">{selectedRoute?.distance} km</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Tempo estimado</span>
                    <span className="text-white font-medium">{selectedRoute?.duration} min</span>
                  </div>
                  <div className="border-t border-white/20 pt-2 mt-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white font-semibold">Total</span>
                      <span className="text-white font-bold text-lg">
                        R$ {selectedRoute?.price.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Price Breakdown */}
              {selectedRoute?.priceEstimate && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white/10 backdrop-blur-md rounded-3xl p-4 border border-white/20"
                >
                  <h3 className="text-white font-semibold mb-3">Detalhamento do Preço</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Tarifa base</span>
                      <span className="text-white">R$ {selectedRoute.priceEstimate.breakdown.baseFare.toFixed(2)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Distância</span>
                      <span className="text-white">R$ {selectedRoute.priceEstimate.breakdown.distanceCost.toFixed(2)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Tempo</span>
                      <span className="text-white">R$ {selectedRoute.priceEstimate.breakdown.timeCost.toFixed(2)}</span>
                    </div>

                    {selectedRoute.priceEstimate.surge > 1.1 && (
                      <>
                        <div className="flex items-center justify-between">
                          <span className="text-orange-400">Tarifa dinâmica ({selectedRoute.priceEstimate.surge.toFixed(1)}x)</span>
                          <span className="text-orange-400">+R$ {selectedRoute.priceEstimate.breakdown.surgeAmount.toFixed(2)}</span>
                        </div>

                        <div className="bg-orange-500/10 rounded-2xl p-3 mt-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-orange-400 text-sm">🔥</span>
                            <span className="text-orange-400 text-sm font-medium">Preço dinâmico ativo</span>
                          </div>
                          <div className="space-y-1">
                            {selectedRoute.priceEstimate.explanation.map((reason: string, index: number) => (
                              <p key={index} className="text-orange-300 text-xs">{reason}</p>
                            ))}
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </motion.div>
              )}

              {/* Payment Methods */}
              <div className="space-y-3">
                <h3 className="text-white font-semibold">Método de Pagamento</h3>
                {[
                  { id: 'pix', name: 'PIX', icon: '💳', description: 'Pagamento instantâneo' },
                  { id: 'credit', name: 'Cartão de Crédito', icon: '💳', description: '**** 1234' },
                  { id: 'cash', name: 'Dinheiro', icon: '💵', description: 'Pagar no final da viagem' }
                ].map((method, index) => (
                  <motion.button
                    key={method.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    onClick={() => setSelectedPayment(method as any)}
                    className={`w-full p-4 rounded-3xl border transition-all duration-300 ${
                      selectedPayment?.id === method.id
                        ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-500/50'
                        : 'bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/15'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="text-2xl">{method.icon}</div>
                      <div className="flex-1 text-left">
                        <p className="text-white font-medium">{method.name}</p>
                        <p className="text-white/70 text-sm">{method.description}</p>
                      </div>
                      {selectedPayment?.id === method.id && (
                        <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                          <div className="w-2 h-2 rounded-full bg-white" />
                        </div>
                      )}
                    </div>
                  </motion.button>
                ))}
              </div>

              {/* Confirm Button */}
              {selectedPayment && (
                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  onClick={handleConfirmRide}
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-4 px-6 rounded-3xl shadow-lg shadow-green-500/25 hover:shadow-green-500/40 transition-all duration-300 disabled:opacity-50"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center justify-center space-x-2">
                    {isLoading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        <span>Solicitando...</span>
                      </>
                    ) : (
                      <>
                        <span>Confirmar Corrida</span>
                        <Zap className="w-5 h-5" />
                      </>
                    )}
                  </div>
                </motion.button>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      </div>

      {/* Safety Dashboard */}
      <SafetyDashboard
        isVisible={showSafetyDashboard}
        onClose={() => setShowSafetyDashboard(false)}
        currentRideId={currentRideId}
      />

      {/* Rewards Center */}
      <RewardsCenter
        isVisible={showRewardsCenter}
        onClose={() => setShowRewardsCenter(false)}
      />
    </>
  )
}

export default RequestRide
