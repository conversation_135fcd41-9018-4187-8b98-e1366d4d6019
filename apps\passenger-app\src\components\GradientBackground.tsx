import React from 'react'

// 🎨 BACKGROUND COM GRADIENTE ANIMADO
// Componente para gerenciar diferentes GIFs de fundo

interface GradientBackgroundProps {
  variant?: 'subtle' | 'particles' | 'minimal' | 'elegant' | 'static'
  className?: string
  opacity?: number
  brightness?: number
  contrast?: number
}

// URLs dos GIFs disponíveis - MAIS SUTIS E ELEGANTES
const GRADIENT_GIFS = {
  subtle: 'https://media.giphy.com/media/l0HlKlRMBvdzAFYYM/giphy.gif',    // Muito sutil
  particles: 'https://media.giphy.com/media/xT0xepIsg7kK2ZZO0g/giphy.gif', // Partículas sutis
  minimal: 'https://media.giphy.com/media/MtEbLYV2Rp1Ha/giphy.gif',        // Minimalista
  elegant: 'https://media.giphy.com/media/ddGvtRqYF75uDPzJb4/giphy.gif',   // Elegante
  static: ''  // Gradiente CSS estático
}

export const GradientBackground: React.FC<GradientBackgroundProps> = ({
  variant = 'subtle',
  className = '',
  opacity = 0.6,
  brightness = 0.5,
  contrast = 1.1
}) => {
  const gifUrl = GRADIENT_GIFS[variant]

  // Se for static, usar apenas CSS
  if (variant === 'static') {
    return (
      <div
        className={`absolute inset-0 ${className}`}
        style={{
          background: `
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.05) 0%, transparent 50%),
            linear-gradient(135deg, #000000 0%, #0a0a0a 25%, #111111 50%, #0a0a0a 75%, #000000 100%)
          `,
          opacity: opacity
        }}
      />
    )
  }

  return (
    <div className={`absolute inset-0 flex items-center justify-center ${className}`}>
      <img
        src={gifUrl}
        alt="Gradient Background"
        className="w-full h-full object-cover"
        style={{
          filter: `brightness(${brightness}) contrast(${contrast}) saturate(0.8)`,
          opacity: opacity
        }}
        loading="lazy"
        onError={(e) => {
          // Fallback para gradiente CSS muito sutil se o GIF falhar
          const target = e.target as HTMLImageElement
          target.style.display = 'none'
          const parent = target.parentElement
          if (parent) {
            parent.style.background = `
              radial-gradient(circle at 30% 70%, rgba(120, 119, 198, 0.08) 0%, transparent 50%),
              linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%)
            `
          }
        }}
      />
    </div>
  )
}

// Componente com seletor de variantes para testes
export const GradientBackgroundSelector: React.FC = () => {
  const [variant, setVariant] = React.useState<'subtle' | 'particles' | 'minimal' | 'elegant' | 'static'>('static')

  return (
    <div className="relative w-full h-full">
      {/* Background */}
      <GradientBackground variant={variant} />

      {/* Seletor de variantes */}
      <div className="absolute top-4 right-4 z-50">
        <div className="bg-black/60 backdrop-blur-md rounded-lg p-3 space-y-2">
          <div className="text-white text-xs font-medium mb-2">🎨 Background Sutil:</div>

          <button
            onClick={() => setVariant('static')}
            className={`block w-full text-left px-3 py-1 rounded text-xs transition-colors ${
              variant === 'static'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            ⚫ CSS Estático
          </button>

          <button
            onClick={() => setVariant('subtle')}
            className={`block w-full text-left px-3 py-1 rounded text-xs transition-colors ${
              variant === 'subtle'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            🌫️ Muito Sutil
          </button>

          <button
            onClick={() => setVariant('particles')}
            className={`block w-full text-left px-3 py-1 rounded text-xs transition-colors ${
              variant === 'particles'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            ✨ Partículas
          </button>

          <button
            onClick={() => setVariant('minimal')}
            className={`block w-full text-left px-3 py-1 rounded text-xs transition-colors ${
              variant === 'minimal'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            ⚫ Minimalista
          </button>

          <button
            onClick={() => setVariant('elegant')}
            className={`block w-full text-left px-3 py-1 rounded text-xs transition-colors ${
              variant === 'elegant'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            💫 Elegante
          </button>
        </div>
      </div>
    </div>
  )
}

export default GradientBackground
