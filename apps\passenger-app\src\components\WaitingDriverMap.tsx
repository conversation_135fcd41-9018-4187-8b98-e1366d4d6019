import React, { useRef, useEffect, useState, useCallback } from 'react'
import mapboxgl from 'mapbox-gl'
import { motion } from 'framer-motion'
import { MapPin, Navigation, Car, Clock, AlertCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'
import 'mapbox-gl/dist/mapbox-gl.css'

// Configure Mapbox token
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'
mapboxgl.accessToken = MAPBOX_TOKEN

interface Driver {
  id: string
  name: string
  latitude: number
  longitude: number
  bearing: number
  speed: number
  eta: number
  vehicle: {
    model: string
    color: string
    plate: string
  }
  status: 'searching' | 'assigned' | 'en_route' | 'arrived'
}

interface WaitingDriverMapProps {
  rideId?: string
  userLocation: [number, number]
  destination: [number, number]
  onDriverUpdate?: (driver: Driver | null) => void
  className?: string
}

export const WaitingDriverMap: React.FC<WaitingDriverMapProps> = ({
  rideId,
  userLocation,
  destination,
  onDriverUpdate,
  className = ''
}) => {
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const driverMarker = useRef<mapboxgl.Marker | null>(null)
  const userMarker = useRef<mapboxgl.Marker | null>(null)
  const destinationMarker = useRef<mapboxgl.Marker | null>(null)

  const [driver, setDriver] = useState<Driver | null>(null)
  const [isMapLoaded, setIsMapLoaded] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [driverRoute, setDriverRoute] = useState<any>(null)
  const [isTrackingDriver, setIsTrackingDriver] = useState(false)

  // Refs for tracking intervals
  const driverUpdateInterval = useRef<NodeJS.Timeout | null>(null)
  const routeUpdateInterval = useRef<NodeJS.Timeout | null>(null)

  console.log('🗺️ WaitingDriverMap props:', { rideId, userLocation, destination })

  // Utility functions for geospatial calculations
  const calculateDistance = useCallback((point1: [number, number], point2: [number, number]): number => {
    const R = 6371 // Earth's radius in kilometers
    const dLat = (point2[1] - point1[1]) * Math.PI / 180
    const dLon = (point2[0] - point1[0]) * Math.PI / 180
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1[1] * Math.PI / 180) * Math.cos(point2[1] * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }, [])

  const calculateBearing = useCallback((point1: [number, number], point2: [number, number]): number => {
    const dLon = (point2[0] - point1[0]) * Math.PI / 180
    const lat1 = point1[1] * Math.PI / 180
    const lat2 = point2[1] * Math.PI / 180
    const y = Math.sin(dLon) * Math.cos(lat2)
    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLon)
    const bearing = Math.atan2(y, x) * 180 / Math.PI
    return (bearing + 360) % 360
  }, [])

  const interpolatePosition = useCallback((
    start: [number, number],
    end: [number, number],
    progress: number
  ): [number, number] => {
    const lat = start[1] + (end[1] - start[1]) * progress
    const lng = start[0] + (end[0] - start[0]) * progress
    return [lng, lat]
  }, [])

  // Initialize map
  const initializeMap = useCallback(() => {
    if (!mapContainer.current || map.current) return

    try {
      console.log('🗺️ Initializing waiting driver map...')
      
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/dark-v11',
        center: userLocation,
        zoom: 14,
        attributionControl: false,
        antialias: true
      })

      map.current.on('load', () => {
        console.log('✅ Waiting driver map loaded')
        setIsMapLoaded(true)
        addMarkersAndRoute()
      })

      map.current.on('error', (e) => {
        console.error('❌ Map error:', e)
        setError('Erro ao carregar o mapa')
      })

    } catch (error) {
      console.error('❌ Failed to initialize map:', error)
      setError('Falha ao inicializar o mapa')
    }
  }, [userLocation])

  // Add markers and route
  const addMarkersAndRoute = useCallback(() => {
    if (!map.current || !isMapLoaded) return

    console.log('📍 Adding markers and route...')

    // Add user location marker
    const userEl = document.createElement('div')
    userEl.className = 'user-marker'
    userEl.innerHTML = `
      <div class="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-lg animate-pulse"></div>
    `

    userMarker.current = new mapboxgl.Marker(userEl)
      .setLngLat(userLocation)
      .addTo(map.current)

    // Add destination marker
    const destEl = document.createElement('div')
    destEl.className = 'destination-marker'
    destEl.innerHTML = `
      <div class="w-6 h-6 bg-red-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
        <div class="w-2 h-2 bg-white rounded-full"></div>
      </div>
    `

    destinationMarker.current = new mapboxgl.Marker(destEl)
      .setLngLat(destination)
      .addTo(map.current)

    // Add route between user and destination
    addRoute(userLocation, destination)

    // Fit map to show both points
    const bounds = new mapboxgl.LngLatBounds()
    bounds.extend(userLocation)
    bounds.extend(destination)

    map.current.fitBounds(bounds, {
      padding: 50,
      duration: 1000
    })

  }, [userLocation, destination, isMapLoaded])

  // Add route line
  const addRoute = useCallback(async (start: [number, number], end: [number, number]) => {
    if (!map.current) return

    try {
      const response = await fetch(
        `https://api.mapbox.com/directions/v5/mapbox/driving/${start[0]},${start[1]};${end[0]},${end[1]}?geometries=geojson&overview=full&access_token=${MAPBOX_TOKEN}`
      )
      
      const data = await response.json()

      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0]

        // Remove existing route
        if (map.current.getSource('route')) {
          map.current.removeLayer('route')
          map.current.removeSource('route')
        }

        // Add route source and layer
        map.current.addSource('route', {
          type: 'geojson',
          data: {
            type: 'Feature',
            properties: {},
            geometry: route.geometry
          }
        })

        map.current.addLayer({
          id: 'route',
          type: 'line',
          source: 'route',
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': '#3b82f6',
            'line-width': 4,
            'line-opacity': 0.8
          }
        })
      }
    } catch (error) {
      console.error('❌ Failed to add route:', error)
    }
  }, [])

  // Update driver marker with smooth animation
  const updateDriverMarker = useCallback((driverData: Driver, animate: boolean = true) => {
    if (!map.current || !isMapLoaded) return

    const driverLocation: [number, number] = [driverData.longitude, driverData.latitude]
    console.log('🚗 Updating driver marker:', { location: driverLocation, bearing: driverData.bearing, animate })

    if (driverMarker.current) {
      // Smooth animation to new position
      if (animate) {
        const currentLngLat = driverMarker.current.getLngLat()
        const startPos: [number, number] = [currentLngLat.lng, currentLngLat.lat]
        const endPos = driverLocation

        let progress = 0
        const animationDuration = 2000 // 2 seconds
        const frameRate = 60
        const totalFrames = (animationDuration / 1000) * frameRate
        const increment = 1 / totalFrames

        const animateMarker = () => {
          progress += increment
          if (progress >= 1) {
            progress = 1
          }

          const currentPos = interpolatePosition(startPos, endPos, progress)
          driverMarker.current?.setLngLat(currentPos)

          // Update marker rotation based on bearing
          const markerElement = driverMarker.current?.getElement()
          if (markerElement) {
            const carIcon = markerElement.querySelector('.driver-car-icon')
            if (carIcon) {
              (carIcon as HTMLElement).style.transform = `rotate(${driverData.bearing}deg)`
            }
          }

          if (progress < 1) {
            requestAnimationFrame(animateMarker)
          }
        }

        requestAnimationFrame(animateMarker)
      } else {
        // Instant update
        driverMarker.current.setLngLat(driverLocation)
      }
    } else {
      // Create new driver marker with direction indicator
      const driverEl = document.createElement('div')
      driverEl.className = 'driver-marker'
      driverEl.innerHTML = `
        <div class="relative">
          <div class="w-10 h-10 bg-green-500 rounded-full border-3 border-white shadow-lg flex items-center justify-center">
            <div class="driver-car-icon w-6 h-6 text-white transition-transform duration-300" style="transform: rotate(${driverData.bearing}deg)">
              <svg fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
              </svg>
            </div>
          </div>
          <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <div class="absolute top-0 left-0 w-10 h-10 bg-green-400 rounded-full animate-ping opacity-20"></div>
        </div>
      `

      driverMarker.current = new mapboxgl.Marker(driverEl)
        .setLngLat(driverLocation)
        .addTo(map.current)
    }

    // Update route from driver to user
    if (driverData.status === 'en_route') {
      updateDriverRoute(driverLocation, userLocation)
    }

  }, [userLocation, isMapLoaded, interpolatePosition])

  // Update driver route dynamically
  const updateDriverRoute = useCallback(async (driverLocation: [number, number], userLocation: [number, number]) => {
    if (!map.current || !isMapLoaded) return

    try {
      console.log('🛣️ Updating driver route from:', driverLocation, 'to:', userLocation)

      const response = await fetch(
        `https://api.mapbox.com/directions/v5/mapbox/driving/${driverLocation[0]},${driverLocation[1]};${userLocation[0]},${userLocation[1]}?geometries=geojson&overview=full&access_token=${MAPBOX_TOKEN}`
      )

      const data = await response.json()

      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0]
        setDriverRoute(route)

        // Remove existing driver route
        if (map.current.getSource('driver-route')) {
          map.current.removeLayer('driver-route')
          map.current.removeSource('driver-route')
        }

        // Add new driver route
        map.current.addSource('driver-route', {
          type: 'geojson',
          data: {
            type: 'Feature',
            properties: {},
            geometry: route.geometry
          }
        })

        map.current.addLayer({
          id: 'driver-route',
          type: 'line',
          source: 'driver-route',
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': '#10b981', // Green for driver route
            'line-width': 5,
            'line-opacity': 0.8
          }
        })

        // Calculate and update ETA
        const durationMinutes = Math.ceil(route.duration / 60)
        const distanceKm = (route.distance / 1000).toFixed(1)

        console.log(`🕐 Updated ETA: ${durationMinutes} min, Distance: ${distanceKm} km`)

        // Update driver data with new ETA
        if (driver) {
          const updatedDriver = {
            ...driver,
            eta: durationMinutes,
            location: driverLocation
          }
          setDriver(updatedDriver)
          onDriverUpdate?.(updatedDriver)
        }

        // Fit map to show driver route
        const coordinates = route.geometry.coordinates
        const bounds = coordinates.reduce((bounds: any, coord: any) => {
          return bounds.extend(coord)
        }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]))

        map.current.fitBounds(bounds, {
          padding: 60,
          duration: 1000
        })

      } else {
        console.warn('⚠️ No driver route found')
      }
    } catch (error) {
      console.error('❌ Failed to update driver route:', error)
    }
  }, [isMapLoaded, driver, onDriverUpdate])

  // Start real-time driver tracking
  const startDriverTracking = useCallback((initialDriver: Driver) => {
    if (isTrackingDriver) return

    console.log('🎯 Starting real-time driver tracking')
    setIsTrackingDriver(true)
    setDriver(initialDriver)

    let currentLocation = initialDriver.location
    let targetLocation = userLocation
    let stepIndex = 0
    let routeSteps: [number, number][] = []

    // Generate route steps for simulation
    const generateRouteSteps = async () => {
      try {
        const response = await fetch(
          `https://api.mapbox.com/directions/v5/mapbox/driving/${currentLocation[0]},${currentLocation[1]};${targetLocation[0]},${targetLocation[1]}?geometries=geojson&overview=full&steps=true&access_token=${MAPBOX_TOKEN}`
        )

        const data = await response.json()

        if (data.routes && data.routes.length > 0) {
          const route = data.routes[0]
          const coordinates = route.geometry.coordinates

          // Create steps along the route (every 10th coordinate for smoother movement)
          routeSteps = coordinates.filter((_: any, index: number) => index % 10 === 0)
          console.log(`🗺️ Generated ${routeSteps.length} route steps`)
        }
      } catch (error) {
        console.error('❌ Failed to generate route steps:', error)
        // Fallback to direct line
        routeSteps = [currentLocation, targetLocation]
      }
    }

    // Initialize route steps
    generateRouteSteps().then(() => {
      // Update driver position every 3 seconds
      driverUpdateInterval.current = setInterval(() => {
        if (stepIndex < routeSteps.length - 1) {
          stepIndex++
          const newLocation = routeSteps[stepIndex]
          const bearing = calculateBearing(currentLocation, newLocation)

          const updatedDriver: Driver = {
            ...initialDriver,
            location: newLocation,
            latitude: newLocation[1],
            longitude: newLocation[0],
            bearing: bearing,
            speed: 25 + Math.random() * 15, // 25-40 km/h
            eta: Math.max(1, Math.ceil((routeSteps.length - stepIndex) / 3)) // Rough ETA calculation
          }

          console.log(`🚗 Driver moving to step ${stepIndex}/${routeSteps.length}:`, newLocation)

          setDriver(updatedDriver)
          updateDriverMarker(updatedDriver, true)
          onDriverUpdate?.(updatedDriver)

          currentLocation = newLocation

          // Update route every few steps
          if (stepIndex % 3 === 0) {
            updateDriverRoute(newLocation, userLocation)
          }

          // Driver arrived
          if (stepIndex >= routeSteps.length - 1) {
            console.log('🎯 Driver arrived!')
            const arrivedDriver = { ...updatedDriver, status: 'arrived' as const, eta: 0 }
            setDriver(arrivedDriver)
            onDriverUpdate?.(arrivedDriver)
            stopDriverTracking()
          }
        }
      }, 3000) // Update every 3 seconds
    })

  }, [isTrackingDriver, userLocation, calculateBearing, updateDriverMarker, updateDriverRoute, onDriverUpdate])

  // Stop driver tracking
  const stopDriverTracking = useCallback(() => {
    console.log('🛑 Stopping driver tracking')
    setIsTrackingDriver(false)

    if (driverUpdateInterval.current) {
      clearInterval(driverUpdateInterval.current)
      driverUpdateInterval.current = null
    }

    if (routeUpdateInterval.current) {
      clearInterval(routeUpdateInterval.current)
      routeUpdateInterval.current = null
    }
  }, [])

  // Subscribe to driver location updates via Supabase
  useEffect(() => {
    if (!rideId) return

    console.log('🔄 Subscribing to driver updates for ride:', rideId)

    const subscription = supabase
      .channel(`driver-location-${rideId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'driver_locations',
          filter: `ride_id=eq.${rideId}`
        },
        (payload) => {
          console.log('📡 Driver location update:', payload)
          
          if (payload.new) {
            const driverData = payload.new as any
            const driver: Driver = {
              id: driverData.driver_id,
              name: driverData.driver_name || 'Motorista',
              latitude: driverData.latitude,
              longitude: driverData.longitude,
              bearing: driverData.bearing || 0,
              speed: driverData.speed || 0,
              eta: driverData.eta || 5,
              vehicle: {
                model: driverData.vehicle_model || 'Veículo',
                color: driverData.vehicle_color || 'Branco',
                plate: driverData.vehicle_plate || 'ABC-1234'
              },
              status: driverData.status || 'en_route'
            }

            setDriver(driver)
            updateDriverMarker(driver)
            onDriverUpdate?.(driver)
          }
        }
      )
      .subscribe()

    return () => {
      console.log('🔌 Unsubscribing from driver updates')
      subscription.unsubscribe()
    }
  }, [rideId, updateDriverMarker, onDriverUpdate])

  // Initialize map on mount
  useEffect(() => {
    initializeMap()

    return () => {
      if (map.current) {
        map.current.remove()
        map.current = null
      }
    }
  }, [initializeMap])

  // Mock driver data for testing with real-time tracking
  useEffect(() => {
    if (!driver && !rideId && !isTrackingDriver) {
      // Simulate driver assignment after 3 seconds
      const timer = setTimeout(() => {
        // Start driver at a location 2-3km away from user
        const offsetLat = (Math.random() - 0.5) * 0.03 // ~1.5km radius
        const offsetLng = (Math.random() - 0.5) * 0.03

        const mockDriver: Driver = {
          id: 'mock-driver-1',
          name: 'João Silva',
          latitude: userLocation[1] + offsetLat,
          longitude: userLocation[0] + offsetLng,
          location: [userLocation[0] + offsetLng, userLocation[1] + offsetLat],
          bearing: calculateBearing([userLocation[0] + offsetLng, userLocation[1] + offsetLat], userLocation),
          speed: 30,
          eta: 8,
          vehicle: {
            model: 'Honda Civic',
            color: 'Branco',
            plate: 'ABC-1234'
          },
          status: 'en_route'
        }

        console.log('🚗 Mock driver assigned, starting real-time tracking')
        startDriverTracking(mockDriver)
        onDriverUpdate?.(mockDriver)
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [driver, rideId, userLocation, isTrackingDriver, calculateBearing, startDriverTracking, onDriverUpdate])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopDriverTracking()
    }
  }, [stopDriverTracking])

  if (error) {
    return (
      <div className={`bg-red-500/10 border border-red-500/20 rounded-lg p-4 ${className}`}>
        <div className="flex items-center space-x-2 text-red-400">
          <AlertCircle className="w-5 h-5" />
          <span className="text-sm">{error}</span>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative overflow-hidden rounded-lg ${className}`}>
      <div ref={mapContainer} className="w-full h-full min-h-[300px]" />
      
      {/* Driver info overlay */}
      {driver && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute bottom-4 left-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg p-3 text-white"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                <Car className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="font-semibold text-sm">{driver.name}</p>
                <p className="text-xs text-gray-300">{driver.vehicle.model} • {driver.vehicle.plate}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-1 text-blue-400">
                <Clock className="w-4 h-4" />
                <span className="text-sm font-semibold">{driver.eta} min</span>
              </div>
              <p className="text-xs text-gray-300">ETA</p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Loading overlay */}
      {!isMapLoaded && (
        <div className="absolute inset-0 bg-gray-900/50 flex items-center justify-center">
          <div className="text-white text-center">
            <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-sm">Carregando mapa...</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default WaitingDriverMap
