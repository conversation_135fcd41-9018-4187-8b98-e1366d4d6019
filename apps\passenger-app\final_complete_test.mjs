// =====================================================
// TESTE FINAL COMPLETO COM CREDENCIAIS VÁLIDAS
// Testa todo o fluxo da aplicação com usuário real
// =====================================================

import puppeteer from 'puppeteer'

async function runCompleteTest() {
  console.log('🎯 TESTE FINAL COMPLETO DA APLICAÇÃO')
  console.log('=' .repeat(60))
  console.log('🔑 Usando credenciais válidas: <EMAIL>')
  console.log('=' .repeat(60))

  let browser = null
  
  try {
    // Iniciar navegador
    console.log('\n🚀 Iniciando navegador...')
    browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--start-maximized']
    })

    const page = await browser.newPage()
    await page.setViewport({ width: 1280, height: 720 })

    // Monitorar console
    const logs = []
    const errors = []

    page.on('console', (msg) => {
      const logEntry = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toLocaleTimeString()
      }
      
      logs.push(logEntry)
      
      if (msg.type() === 'error') {
        errors.push(logEntry)
        console.log(`[${logEntry.timestamp}] ❌ ${msg.text()}`)
      } else if (msg.text().includes('✅') || msg.text().includes('🎉')) {
        console.log(`[${logEntry.timestamp}] 🟢 ${msg.text()}`)
      } else if (msg.text().includes('❌') || msg.text().includes('ERROR')) {
        console.log(`[${logEntry.timestamp}] 🔴 ${msg.text()}`)
      }
    })

    page.on('pageerror', (error) => {
      errors.push({
        type: 'pageerror',
        message: error.message,
        timestamp: new Date().toLocaleTimeString()
      })
      console.log(`[${new Date().toLocaleTimeString()}] 💥 PAGE ERROR: ${error.message}`)
    })

    // ETAPA 1: LOGIN
    console.log('\n🔐 ETAPA 1: FAZENDO LOGIN')
    console.log('-' .repeat(40))
    
    await page.goto('http://localhost:3000/login', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    })
    
    console.log('✅ Página de login carregada')
    await new Promise(resolve => setTimeout(resolve, 3000))

    // Preencher credenciais
    const emailField = await page.$('input[type="email"]')
    const passwordField = await page.$('input[type="password"]')

    if (emailField && passwordField) {
      console.log('📝 Preenchendo credenciais...')
      
      await emailField.click({ clickCount: 3 })
      await emailField.type('<EMAIL>', { delay: 50 })
      
      await passwordField.click({ clickCount: 3 })
      await passwordField.type('test123456', { delay: 50 })
      
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const submitButton = await page.$('button[type="submit"]')
      if (submitButton) {
        console.log('🖱️ Clicando em login...')
        await submitButton.click()
        
        await new Promise(resolve => setTimeout(resolve, 5000))
        
        const currentUrl = page.url()
        if (!currentUrl.includes('/login')) {
          console.log('✅ LOGIN REALIZADO COM SUCESSO!')
          console.log(`🌐 Redirecionado para: ${currentUrl}`)
        } else {
          console.log('❌ Login falhou')
          return
        }
      }
    }

    // ETAPA 2: TESTAR NAVEGAÇÃO
    console.log('\n🗺️ ETAPA 2: TESTANDO NAVEGAÇÃO')
    console.log('-' .repeat(40))
    
    // Ir para seleção de mapa
    console.log('🌐 Navegando para seleção de mapa...')
    await page.goto('http://localhost:3000/ride-request/map', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    })
    
    console.log('✅ Página de mapa carregada')
    await new Promise(resolve => setTimeout(resolve, 8000))
    
    // Verificar se mapa carregou
    const mapContainer = await page.$('.mapboxgl-map, #map, [class*="map"]')
    if (mapContainer) {
      console.log('✅ Mapa detectado na página')
    } else {
      console.log('⚠️ Mapa não detectado')
    }

    // ETAPA 3: TESTAR DETALHES DA VIAGEM
    console.log('\n🚗 ETAPA 3: TESTANDO DETALHES DA VIAGEM')
    console.log('-' .repeat(40))
    
    await page.goto('http://localhost:3000/ride-request/details', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    })
    
    console.log('✅ Página de detalhes carregada')
    await new Promise(resolve => setTimeout(resolve, 10000))
    
    // Verificar elementos da página
    const priceElements = await page.$$('[class*="price"], [class*="valor"], .currency')
    const paymentMethods = await page.$$('[class*="payment"], [class*="pagamento"]')
    const vehicleOptions = await page.$$('[class*="vehicle"], [class*="veiculo"], button')
    
    console.log(`💰 Elementos de preço encontrados: ${priceElements.length}`)
    console.log(`💳 Métodos de pagamento encontrados: ${paymentMethods.length}`)
    console.log(`🚗 Opções de veículo encontradas: ${vehicleOptions.length}`)

    // ETAPA 4: TESTAR DASHBOARD
    console.log('\n📊 ETAPA 4: TESTANDO DASHBOARD')
    console.log('-' .repeat(40))
    
    await page.goto('http://localhost:3000/dashboard', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    })
    
    console.log('✅ Dashboard carregado')
    await new Promise(resolve => setTimeout(resolve, 5000))

    // RELATÓRIO FINAL
    console.log('\n' + '=' .repeat(60))
    console.log('📊 RELATÓRIO FINAL DO TESTE COMPLETO')
    console.log('=' .repeat(60))
    
    console.log(`📝 Total de logs capturados: ${logs.length}`)
    console.log(`❌ Total de erros: ${errors.length}`)
    
    // Analisar logs importantes
    const successLogs = logs.filter(log => 
      log.text.includes('✅') || 
      log.text.includes('SUCCESS') || 
      log.text.includes('configurado') ||
      log.text.includes('inicializado')
    )
    
    const errorLogs = logs.filter(log => 
      log.text.includes('❌') || 
      log.text.includes('ERROR') || 
      log.text.includes('Failed')
    )
    
    console.log(`🟢 Logs de sucesso: ${successLogs.length}`)
    console.log(`🔴 Logs de erro: ${errorLogs.length}`)
    
    if (successLogs.length > 0) {
      console.log('\n✅ PRINCIPAIS SUCESSOS:')
      successLogs.slice(0, 5).forEach((log, i) => {
        console.log(`  ${i + 1}. ${log.text}`)
      })
    }
    
    if (errorLogs.length > 0) {
      console.log('\n❌ PRINCIPAIS ERROS:')
      errorLogs.slice(0, 5).forEach((log, i) => {
        console.log(`  ${i + 1}. ${log.text}`)
      })
    }
    
    // Classificação final
    let status = 'EXCELENTE'
    if (errors.length > 5) {
      status = 'PROBLEMÁTICO'
    } else if (errors.length > 2) {
      status = 'ATENÇÃO'
    } else if (errors.length > 0) {
      status = 'BOM'
    }
    
    console.log(`\n🏥 STATUS GERAL DA APLICAÇÃO: ${status}`)
    
    if (errors.length === 0) {
      console.log('\n🎉 PARABÉNS! APLICAÇÃO FUNCIONANDO PERFEITAMENTE!')
      console.log('✅ Todos os testes passaram sem erros críticos!')
    } else {
      console.log(`\n⚠️ Encontrados ${errors.length} problemas que precisam atenção`)
    }
    
    console.log('\n🔑 CREDENCIAIS TESTADAS E FUNCIONAIS:')
    console.log('   📧 Email: <EMAIL>')
    console.log('   🔒 Senha: test123456')
    
    return {
      totalLogs: logs.length,
      totalErrors: errors.length,
      successLogs: successLogs.length,
      errorLogs: errorLogs.length,
      status
    }

  } catch (error) {
    console.error('💥 Erro durante teste:', error.message)
    throw error
  } finally {
    if (browser) {
      console.log('\n🔒 Fechando navegador...')
      await browser.close()
    }
  }
}

// Executar teste
runCompleteTest()
  .then((results) => {
    console.log('\n🏁 TESTE COMPLETO CONCLUÍDO!')
    console.log(`📊 Resumo: ${results.totalLogs} logs, ${results.totalErrors} erros, status: ${results.status}`)
    process.exit(results.totalErrors > 0 ? 1 : 0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
