import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  Play,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  RefreshCw,
  Activity,
  Database,
  Wifi,
  Bell,
  BarChart3,
  Smartphone,
  Settings
} from 'lucide-react';
import { GradientBackground } from '../components/GradientBackground';
import { useNoZoom } from '../hooks/useNoZoom';
import { integrationTestService, IntegrationTestReport, TestResult } from '../services/IntegrationTestService';
import { systemHealthService, SystemStatus } from '../services/SystemHealthService';
import { analyticsService } from '../services/AnalyticsService';
import '../styles/no-zoom.css';

export const SystemTestsMobile: React.FC = () => {
  const [testReport, setTestReport] = useState<IntegrationTestReport | null>(null);
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [running, setRunning] = useState(false);
  const [selectedTest, setSelectedTest] = useState<TestResult | null>(null);

  useNoZoom();

  useEffect(() => {
    // Carregar status inicial do sistema
    loadSystemStatus();
    
    // Registrar visualização da página
    analyticsService.trackPageView('system_tests', {
      user_type: 'admin'
    });
  }, []);

  const loadSystemStatus = async () => {
    const status = systemHealthService.getSystemStatus();
    setSystemStatus(status);
  };

  const runIntegrationTests = async () => {
    setRunning(true);
    setTestReport(null);
    
    try {
      analyticsService.trackUserAction('integration_tests_started', 'system_tests');
      
      const report = await integrationTestService.runFullIntegrationTest();
      setTestReport(report);
      
      // Atualizar status do sistema após os testes
      await loadSystemStatus();
      
      analyticsService.trackUserAction('integration_tests_completed', 'system_tests', {
        total_tests: report.totalTests,
        passed: report.passed,
        failed: report.failed,
        warnings: report.warnings,
        overall_status: report.overallStatus
      });
      
    } catch (error) {
      console.error('Erro ao executar testes:', error);
      analyticsService.trackError('integration_tests_error', error);
    } finally {
      setRunning(false);
    }
  };

  const runSystemHealthCheck = async () => {
    setRunning(true);
    
    try {
      const status = await systemHealthService.fullHealthCheck();
      setSystemStatus(status);
      
      analyticsService.trackUserAction('health_check_completed', 'system_tests', {
        overall_status: status.overall,
        components_count: status.components.length,
        critical_issues: status.criticalIssues.length
      });
      
    } catch (error) {
      console.error('Erro na verificação de saúde:', error);
    } finally {
      setRunning(false);
    }
  };

  const getStatusIcon = (status: 'passed' | 'failed' | 'warning') => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getHealthIcon = (component: string) => {
    const iconMap = {
      database: Database,
      auth: Settings,
      realtime: Wifi,
      mapbox: Activity,
      notifications: Bell,
      analytics: BarChart3,
      pwa: Smartphone,
      cache: RefreshCw
    };
    
    const IconComponent = iconMap[component as keyof typeof iconMap] || Activity;
    return <IconComponent className="w-5 h-5" />;
  };

  const getHealthColor = (status: 'healthy' | 'degraded' | 'down') => {
    switch (status) {
      case 'healthy':
        return 'from-green-500 to-green-600';
      case 'degraded':
        return 'from-yellow-500 to-yellow-600';
      case 'down':
        return 'from-red-500 to-red-600';
    }
  };

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">
      <GradientBackground variant="static" opacity={0.7} />
      <div className="absolute inset-0 bg-black/20"></div>

      <div className="relative z-10 flex flex-col h-full min-h-screen">
        {/* Header */}
        <motion.div
          className="pt-8 pb-6"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="flex items-center justify-between px-4 mb-4">
            <motion.button
              onClick={() => window.location.href = '/dashboard'}
              className="p-2 text-white/80 hover:text-white transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowLeft className="w-6 h-6" />
            </motion.button>
            
            <div className="text-center">
              <h1 className="text-2xl font-bold text-white">
                🧪 Testes do Sistema
              </h1>
              <p className="text-xs text-white/70">Diagnóstico e Integração</p>
            </div>

            <div className="w-10 h-10"></div>
          </div>
        </motion.div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto px-4 space-y-6">
          {/* Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
          >
            <h3 className="text-lg font-semibold text-white mb-4">🚀 Ações</h3>
            <div className="space-y-3">
              <motion.button
                onClick={runIntegrationTests}
                disabled={running}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2 disabled:opacity-50"
                whileHover={{ scale: running ? 1 : 1.02 }}
                whileTap={{ scale: running ? 1 : 0.98 }}
              >
                {running ? (
                  <>
                    <RefreshCw className="w-5 h-5 animate-spin" />
                    <span>Executando Testes...</span>
                  </>
                ) : (
                  <>
                    <Play className="w-5 h-5" />
                    <span>Executar Testes de Integração</span>
                  </>
                )}
              </motion.button>

              <motion.button
                onClick={runSystemHealthCheck}
                disabled={running}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2 disabled:opacity-50"
                whileHover={{ scale: running ? 1 : 1.02 }}
                whileTap={{ scale: running ? 1 : 0.98 }}
              >
                {running ? (
                  <>
                    <RefreshCw className="w-5 h-5 animate-spin" />
                    <span>Verificando Saúde...</span>
                  </>
                ) : (
                  <>
                    <Activity className="w-5 h-5" />
                    <span>Verificar Saúde do Sistema</span>
                  </>
                )}
              </motion.button>
            </div>
          </motion.div>

          {/* System Status */}
          {systemStatus && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
            >
              <h3 className="text-lg font-semibold text-white mb-4">💊 Saúde do Sistema</h3>
              
              <div className="mb-4">
                <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full bg-gradient-to-r ${getHealthColor(systemStatus.overall)}`}>
                  <span className="text-white font-medium text-sm">
                    {systemStatus.overall === 'healthy' ? '✅ Saudável' :
                     systemStatus.overall === 'degraded' ? '⚠️ Degradado' : '❌ Crítico'}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                {systemStatus.components.map((component) => (
                  <div
                    key={component.component}
                    className={`p-3 rounded-xl bg-gradient-to-r ${getHealthColor(component.status)} bg-opacity-20 border border-white/10`}
                  >
                    <div className="flex items-center space-x-2 mb-1">
                      {getHealthIcon(component.component)}
                      <span className="text-white font-medium text-sm capitalize">
                        {component.component}
                      </span>
                    </div>
                    <div className="text-xs text-white/70">
                      {component.responseTime}ms
                    </div>
                  </div>
                ))}
              </div>

              {systemStatus.criticalIssues.length > 0 && (
                <div className="mt-4 p-3 bg-red-500/20 rounded-xl border border-red-500/30">
                  <h4 className="text-red-200 font-medium text-sm mb-2">⚠️ Problemas Críticos:</h4>
                  {systemStatus.criticalIssues.map((issue, index) => (
                    <p key={index} className="text-red-200 text-xs">• {issue}</p>
                  ))}
                </div>
              )}
            </motion.div>
          )}

          {/* Test Results */}
          {testReport && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
            >
              <h3 className="text-lg font-semibold text-white mb-4">📊 Resultados dos Testes</h3>
              
              <div className="grid grid-cols-3 gap-3 mb-4">
                <div className="text-center p-3 bg-green-500/20 rounded-xl border border-green-500/30">
                  <div className="text-2xl font-bold text-green-400">{testReport.passed}</div>
                  <div className="text-xs text-green-200">Passou</div>
                </div>
                <div className="text-center p-3 bg-red-500/20 rounded-xl border border-red-500/30">
                  <div className="text-2xl font-bold text-red-400">{testReport.failed}</div>
                  <div className="text-xs text-red-200">Falhou</div>
                </div>
                <div className="text-center p-3 bg-yellow-500/20 rounded-xl border border-yellow-500/30">
                  <div className="text-2xl font-bold text-yellow-400">{testReport.warnings}</div>
                  <div className="text-xs text-yellow-200">Avisos</div>
                </div>
              </div>

              <div className="space-y-2">
                {testReport.tests.map((test, index) => (
                  <motion.div
                    key={index}
                    onClick={() => setSelectedTest(test)}
                    className="flex items-center justify-between p-3 bg-white/5 rounded-xl border border-white/10 cursor-pointer hover:bg-white/10 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(test.status)}
                      <div>
                        <div className="text-white font-medium text-sm">{test.testName}</div>
                        <div className="text-white/60 text-xs">{test.message}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 text-white/50 text-xs">
                      <Clock className="w-3 h-3" />
                      <span>{test.duration}ms</span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </div>

        {/* Footer */}
        <motion.div
          className="pb-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <p className="text-white/50 text-xs">
            © 2024 MobiDrive. Sistema de Diagnóstico.
          </p>
        </motion.div>
      </div>

      {/* Test Details Modal */}
      <AnimatePresence>
        {selectedTest && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4"
            onClick={() => setSelectedTest(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-black/80 backdrop-blur-md rounded-2xl p-6 border border-white/10 max-w-sm w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Detalhes do Teste</h3>
                <button
                  onClick={() => setSelectedTest(null)}
                  className="text-white/60 hover:text-white"
                >
                  ×
                </button>
              </div>
              
              <div className="space-y-3">
                <div>
                  <div className="text-sm text-white/70">Nome:</div>
                  <div className="text-white font-medium">{selectedTest.testName}</div>
                </div>
                
                <div>
                  <div className="text-sm text-white/70">Status:</div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(selectedTest.status)}
                    <span className="text-white capitalize">{selectedTest.status}</span>
                  </div>
                </div>
                
                <div>
                  <div className="text-sm text-white/70">Mensagem:</div>
                  <div className="text-white">{selectedTest.message}</div>
                </div>
                
                <div>
                  <div className="text-sm text-white/70">Duração:</div>
                  <div className="text-white">{selectedTest.duration}ms</div>
                </div>
                
                {selectedTest.details && (
                  <div>
                    <div className="text-sm text-white/70">Detalhes:</div>
                    <div className="text-white/80 text-xs bg-white/5 p-2 rounded">
                      <pre>{JSON.stringify(selectedTest.details, null, 2)}</pre>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SystemTestsMobile;
