import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Clock, MapPin, Navigation, AlertCircle, TrendingUp, TrendingDown } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface RealTimeETAProps {
  rideId: string
  driverId: string
  passengerLocation: {
    lat: number
    lng: number
  }
  destinationLocation: {
    lat: number
    lng: number
  }
  onETAUpdate?: (eta: ETAData) => void
}

interface ETAData {
  pickupETA: number // minutos
  tripDuration: number // minutos
  totalTime: number // minutos
  distance: number // km
  driverDistance: number // km até o passageiro
  confidence: number // 0-100%
  lastUpdated: Date
  trafficCondition: 'light' | 'moderate' | 'heavy'
  alternativeRoutes?: number
}

interface DriverLocation {
  lat: number
  lng: number
  heading: number
  speed: number
  timestamp: Date
}

export const RealTimeETA: React.FC<RealTimeETAProps> = ({
  rideId,
  driverId,
  passengerLocation,
  destinationLocation,
  onETAUpdate
}) => {
  const [etaData, setETAData] = useState<ETAData | null>(null)
  const [driverLocation, setDriverLocation] = useState<DriverLocation | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [etaHistory, setETAHistory] = useState<number[]>([])

  // Calcular ETA usando Mapbox Directions API
  const calculateETA = useCallback(async (driverLat: number, driverLng: number) => {
    try {
      const mapboxToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN
      if (!mapboxToken) {
        throw new Error('Token do Mapbox não configurado')
      }

      // Calcular rota do motorista até o passageiro
      const pickupResponse = await fetch(
        `https://api.mapbox.com/directions/v5/mapbox/driving/${driverLng},${driverLat};${passengerLocation.lng},${passengerLocation.lat}?access_token=${mapboxToken}&overview=simplified&annotations=duration,distance,speed`
      )

      if (!pickupResponse.ok) {
        throw new Error('Erro ao calcular rota de pickup')
      }

      const pickupData = await pickupResponse.json()
      const pickupRoute = pickupData.routes[0]

      // Calcular rota do passageiro até o destino
      const tripResponse = await fetch(
        `https://api.mapbox.com/directions/v5/mapbox/driving/${passengerLocation.lng},${passengerLocation.lat};${destinationLocation.lng},${destinationLocation.lat}?access_token=${mapboxToken}&overview=simplified&annotations=duration,distance,speed`
      )

      if (!tripResponse.ok) {
        throw new Error('Erro ao calcular rota da viagem')
      }

      const tripData = await tripResponse.json()
      const tripRoute = tripData.routes[0]

      // Calcular condições de trânsito baseado na velocidade média
      const averageSpeed = (pickupRoute.distance / pickupRoute.duration) * 3.6 // km/h
      let trafficCondition: 'light' | 'moderate' | 'heavy' = 'light'
      
      if (averageSpeed < 20) trafficCondition = 'heavy'
      else if (averageSpeed < 40) trafficCondition = 'moderate'

      // Ajustar ETA baseado no histórico e condições
      const basePickupETA = Math.round(pickupRoute.duration / 60) // minutos
      const baseTripDuration = Math.round(tripRoute.duration / 60) // minutos
      
      // Aplicar fatores de correção baseados no trânsito
      const trafficMultiplier = trafficCondition === 'heavy' ? 1.3 : 
                               trafficCondition === 'moderate' ? 1.15 : 1.0

      const adjustedPickupETA = Math.round(basePickupETA * trafficMultiplier)
      const adjustedTripDuration = Math.round(baseTripDuration * trafficMultiplier)

      // Calcular confiança baseada na consistência do histórico
      const confidence = calculateConfidence(adjustedPickupETA)

      const newETAData: ETAData = {
        pickupETA: adjustedPickupETA,
        tripDuration: adjustedTripDuration,
        totalTime: adjustedPickupETA + adjustedTripDuration,
        distance: Math.round((tripRoute.distance / 1000) * 10) / 10, // km
        driverDistance: Math.round((pickupRoute.distance / 1000) * 10) / 10, // km
        confidence,
        lastUpdated: new Date(),
        trafficCondition,
        alternativeRoutes: pickupData.routes.length
      }

      setETAData(newETAData)
      setETAHistory(prev => [...prev.slice(-9), adjustedPickupETA]) // Manter últimos 10
      onETAUpdate?.(newETAData)

      // Salvar no banco para histórico
      await saveETAToDatabase(newETAData)

      return newETAData
    } catch (error) {
      console.error('Erro ao calcular ETA:', error)
      setError('Erro ao calcular tempo de chegada')
      return null
    }
  }, [passengerLocation, destinationLocation, onETAUpdate])

  // Calcular confiança baseada na variação do histórico
  const calculateConfidence = (currentETA: number): number => {
    if (etaHistory.length < 3) return 85 // Confiança inicial

    const variance = etaHistory.reduce((acc, eta) => {
      return acc + Math.pow(eta - currentETA, 2)
    }, 0) / etaHistory.length

    const standardDeviation = Math.sqrt(variance)
    
    // Quanto menor a variação, maior a confiança
    if (standardDeviation < 1) return 95
    if (standardDeviation < 2) return 90
    if (standardDeviation < 3) return 85
    if (standardDeviation < 5) return 80
    return 75
  }

  // Salvar ETA no banco para análise
  const saveETAToDatabase = async (eta: ETAData) => {
    try {
      await supabase
        .from('eta_history')
        .insert({
          ride_id: rideId,
          driver_id: driverId,
          pickup_eta: eta.pickupETA,
          trip_duration: eta.tripDuration,
          total_time: eta.totalTime,
          distance: eta.distance,
          driver_distance: eta.driverDistance,
          confidence: eta.confidence,
          traffic_condition: eta.trafficCondition,
          driver_location: driverLocation ? `POINT(${driverLocation.lng} ${driverLocation.lat})` : null,
          created_at: new Date().toISOString()
        })
    } catch (error) {
      console.error('Erro ao salvar ETA:', error)
    }
  }

  // Monitorar localização do motorista em tempo real (desabilitado)
  useEffect(() => {
    if (!driverId) return

    console.log('⚠️ Realtime driver tracking desabilitado para estabilidade');

    // Implementar polling como alternativa
    const pollInterval = setInterval(async () => {
      try {
        // Buscar localização atual do motorista
        const { data: location } = await supabase
          .from('driver_locations')
          .select('*')
          .eq('user_id', driverId)
          .single()

        if (location) {
          const newDriverLocation: DriverLocation = {
            lat: location.location.coordinates[1],
            lng: location.location.coordinates[0],
            heading: location.heading || 0,
            speed: location.speed || 0,
            timestamp: new Date(location.updated_at)
          }

          setDriverLocation(newDriverLocation)

          // Recalcular ETA com nova localização
          await calculateETA(newDriverLocation.lat, newDriverLocation.lng)
        }
      } catch (error) {
        console.error('Erro ao buscar localização do motorista:', error)
      }
    }, 10000) // Poll a cada 10 segundos

    return () => {
      clearInterval(pollInterval)
    }
  }, [driverId, calculateETA])

  // Buscar localização inicial do motorista
  useEffect(() => {
    const fetchInitialLocation = async () => {
      try {
        setIsLoading(true)
        
        const { data: location, error } = await supabase
          .from('driver_locations')
          .select('*')
          .eq('user_id', driverId)
          .eq('is_active', true)
          .single()

        if (error) {
          throw error
        }

        if (location) {
          const initialLocation: DriverLocation = {
            lat: location.location.coordinates[1],
            lng: location.location.coordinates[0],
            heading: location.heading || 0,
            speed: location.speed || 0,
            timestamp: new Date(location.updated_at)
          }

          setDriverLocation(initialLocation)
          await calculateETA(initialLocation.lat, initialLocation.lng)
        }
      } catch (error) {
        console.error('Erro ao buscar localização inicial:', error)
        setError('Erro ao localizar motorista')
      } finally {
        setIsLoading(false)
      }
    }

    fetchInitialLocation()
  }, [driverId, calculateETA])

  // Atualizar ETA periodicamente
  useEffect(() => {
    if (!driverLocation) return

    const interval = setInterval(() => {
      calculateETA(driverLocation.lat, driverLocation.lng)
    }, 30000) // A cada 30 segundos

    return () => clearInterval(interval)
  }, [driverLocation, calculateETA])

  const getETATrend = () => {
    if (etaHistory.length < 2) return null
    
    const current = etaHistory[etaHistory.length - 1]
    const previous = etaHistory[etaHistory.length - 2]
    
    if (current < previous) return 'improving'
    if (current > previous) return 'worsening'
    return 'stable'
  }

  const getTrafficColor = (condition: string) => {
    switch (condition) {
      case 'light': return 'text-green-600 bg-green-100'
      case 'moderate': return 'text-yellow-600 bg-yellow-100'
      case 'heavy': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-2xl p-4 shadow-lg">
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          <span className="text-gray-600">Calculando tempo de chegada...</span>
        </div>
      </div>
    )
  }

  if (error || !etaData) {
    return (
      <div className="bg-white rounded-2xl p-4 shadow-lg">
        <div className="flex items-center space-x-3 text-red-600">
          <AlertCircle className="w-6 h-6" />
          <span>{error || 'Erro ao calcular ETA'}</span>
        </div>
      </div>
    )
  }

  const trend = getETATrend()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-2xl p-6 shadow-lg"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Clock className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-gray-900">Tempo de Chegada</h3>
        </div>
        
        <div className="flex items-center space-x-2">
          {trend === 'improving' && <TrendingDown className="w-4 h-4 text-green-500" />}
          {trend === 'worsening' && <TrendingUp className="w-4 h-4 text-red-500" />}
          
          <span className={`text-xs px-2 py-1 rounded-full ${getTrafficColor(etaData.trafficCondition)}`}>
            {etaData.trafficCondition === 'light' && 'Trânsito Leve'}
            {etaData.trafficCondition === 'moderate' && 'Trânsito Moderado'}
            {etaData.trafficCondition === 'heavy' && 'Trânsito Intenso'}
          </span>
        </div>
      </div>

      {/* Main ETA */}
      <div className="text-center mb-4">
        <div className="text-3xl font-bold text-gray-900 mb-1">
          {etaData.pickupETA} min
        </div>
        <p className="text-gray-600">até o motorista chegar</p>
        
        <div className="mt-2 text-sm text-gray-500">
          + {etaData.tripDuration} min de viagem = {etaData.totalTime} min total
        </div>
      </div>

      {/* Details */}
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div className="flex items-center space-x-2">
          <MapPin className="w-4 h-4 text-gray-400" />
          <span className="text-gray-600">
            {etaData.driverDistance} km de distância
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <Navigation className="w-4 h-4 text-gray-400" />
          <span className="text-gray-600">
            {etaData.confidence}% de confiança
          </span>
        </div>
      </div>

      {/* Last Updated */}
      <div className="mt-4 pt-4 border-t border-gray-100 text-xs text-gray-500 text-center">
        Atualizado há {Math.round((Date.now() - etaData.lastUpdated.getTime()) / 1000)}s
      </div>
    </motion.div>
  )
}

export default RealTimeETA
