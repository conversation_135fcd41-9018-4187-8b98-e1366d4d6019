// =====================================================
// TESTE COM USUÁRIO EXISTENTE
// Testa login com usuários que já existem no banco
// =====================================================

import puppeteer from 'puppeteer'

class ExistingUserLoginTest {
  constructor() {
    this.browser = null
    this.page = null
    this.testUsers = [
      { email: '<EMAIL>', password: '123456', name: '<PERSON><PERSON><PERSON>' },
      { email: '<EMAIL>', password: 'admin123', name: 'Admin' },
      { email: '<EMAIL>', password: '123456', name: 'Admin (senha alt)' },
      { email: '<EMAIL>', password: 'password', name: '<PERSON><PERSON><PERSON> (senha alt)' }
    ]
  }

  async init() {
    console.log('🧪 TESTE COM USUÁRIOS EXISTENTES')
    console.log('=' .repeat(50))
    
    this.browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })
    
    this.page = await this.browser.newPage()
    await this.page.setViewport({ width: 1280, height: 720 })
    
    this.setupMonitoring()
    console.log('✅ Monitor configurado')
  }

  setupMonitoring() {
    this.page.on('console', (msg) => {
      const time = new Date().toLocaleTimeString()
      
      if (msg.type() === 'error') {
        console.log(`[${time}] ❌ ${msg.text()}`)
      } else if (msg.text().includes('login') || msg.text().includes('auth') || msg.text().includes('✅')) {
        console.log(`[${time}] 🎯 ${msg.text()}`)
      }
    })

    this.page.on('response', (response) => {
      if (response.url().includes('auth') && response.status() >= 400) {
        console.log(`🔴 AUTH ERROR: ${response.status()} ${response.url()}`)
        
        response.text().then(body => {
          console.log(`📄 Error body: ${body}`)
        }).catch(() => {})
      } else if (response.url().includes('auth') && response.status() === 200) {
        console.log(`🟢 AUTH SUCCESS: ${response.status()} ${response.url()}`)
      }
    })
  }

  async testLogin(email, password, userName) {
    console.log(`\n🔐 TESTANDO: ${userName}`)
    console.log(`📧 Email: ${email}`)
    console.log(`🔒 Senha: ${password}`)
    console.log('-' .repeat(40))
    
    try {
      // Navegar para login
      await this.page.goto('http://localhost:3000/login', { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })
      
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Preencher campos
      const emailField = await this.page.$('input[type="email"]')
      const passwordField = await this.page.$('input[type="password"]')
      
      if (emailField && passwordField) {
        // Limpar e preencher email
        await emailField.click({ clickCount: 3 })
        await emailField.type(email, { delay: 50 })
        
        // Limpar e preencher senha
        await passwordField.click({ clickCount: 3 })
        await passwordField.type(password, { delay: 50 })
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Submeter
        const submitButton = await this.page.$('button[type="submit"]')
        if (submitButton) {
          console.log('🖱️ Clicando em submit...')
          await submitButton.click()
          
          // Aguardar resposta
          await new Promise(resolve => setTimeout(resolve, 5000))
          
          // Verificar se foi redirecionado
          const currentUrl = this.page.url()
          const isLoggedIn = !currentUrl.includes('/login')
          
          if (isLoggedIn) {
            console.log('✅ LOGIN SUCESSO!')
            console.log(`🌐 Redirecionado para: ${currentUrl}`)
            
            // Fazer logout para próximo teste
            await this.logout()
            
            return true
          } else {
            console.log('❌ Login falhou - ainda na página de login')
            return false
          }
        } else {
          console.log('❌ Botão de submit não encontrado')
          return false
        }
      } else {
        console.log('❌ Campos de login não encontrados')
        return false
      }
      
    } catch (error) {
      console.log(`❌ Erro durante teste: ${error.message}`)
      return false
    }
  }

  async logout() {
    try {
      // Tentar fazer logout via JavaScript
      await this.page.evaluate(() => {
        if (window.supabase) {
          return window.supabase.auth.signOut()
        }
      })
      
      await new Promise(resolve => setTimeout(resolve, 2000))
      console.log('🚪 Logout realizado')
    } catch (error) {
      console.log('⚠️ Erro no logout:', error.message)
    }
  }

  async testAllUsers() {
    console.log(`\n🎯 TESTANDO ${this.testUsers.length} COMBINAÇÕES DE USUÁRIO/SENHA`)
    console.log('=' .repeat(60))
    
    const results = []
    
    for (let i = 0; i < this.testUsers.length; i++) {
      const user = this.testUsers[i]
      
      const success = await this.testLogin(user.email, user.password, user.name)
      
      results.push({
        ...user,
        success
      })
      
      if (success) {
        console.log(`🎉 CREDENCIAIS VÁLIDAS ENCONTRADAS!`)
        console.log(`📧 Email: ${user.email}`)
        console.log(`🔒 Senha: ${user.password}`)
        break // Parar no primeiro sucesso
      }
      
      // Pausa entre testes
      if (i < this.testUsers.length - 1) {
        console.log('\n⏸️ Pausa entre testes...')
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }
    
    return results
  }

  async generateReport(results) {
    console.log('\n' + '=' .repeat(60))
    console.log('📊 RELATÓRIO DE TESTE DE CREDENCIAIS')
    console.log('=' .repeat(60))
    
    const successful = results.filter(r => r.success)
    const failed = results.filter(r => !r.success)
    
    console.log(`✅ Credenciais válidas: ${successful.length}`)
    console.log(`❌ Credenciais inválidas: ${failed.length}`)
    
    if (successful.length > 0) {
      console.log('\n🎉 CREDENCIAIS FUNCIONAIS:')
      successful.forEach((user, i) => {
        console.log(`  ${i + 1}. ${user.email} / ${user.password}`)
      })
      
      console.log('\n💡 USE ESTAS CREDENCIAIS PARA TESTES!')
    } else {
      console.log('\n⚠️ NENHUMA CREDENCIAL FUNCIONAL ENCONTRADA')
      console.log('💡 Pode ser necessário:')
      console.log('   - Verificar configurações do Supabase')
      console.log('   - Criar novos usuários')
      console.log('   - Verificar políticas RLS')
    }
    
    return successful.length > 0
  }

  async close() {
    if (this.browser) {
      await this.browser.close()
      console.log('\n🔒 Navegador fechado')
    }
  }

  async run() {
    try {
      await this.init()
      const results = await this.testAllUsers()
      const hasValidCredentials = await this.generateReport(results)
      
      return { results, hasValidCredentials }
      
    } finally {
      await this.close()
    }
  }
}

// Executar teste
const tester = new ExistingUserLoginTest()

tester.run()
  .then(({ results, hasValidCredentials }) => {
    console.log('\n🏁 TESTE DE CREDENCIAIS CONCLUÍDO!')
    
    if (hasValidCredentials) {
      console.log('🎉 Credenciais válidas encontradas!')
      process.exit(0)
    } else {
      console.log('⚠️ Nenhuma credencial válida encontrada')
      process.exit(1)
    }
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
