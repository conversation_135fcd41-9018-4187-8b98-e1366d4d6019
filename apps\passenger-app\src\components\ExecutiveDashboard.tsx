import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  TrendingUp, TrendingDown, Users, Car, DollarSign, Star, 
  MapPin, Clock, Shield, Zap, Activity, BarChart3, PieChart,
  Globe, Target, Award, Rocket
} from 'lucide-react'
import { supabase } from '../lib/supabase'
import { analyticsService } from '../services/AnalyticsService'

interface ExecutiveMetrics {
  totalUsers: number
  activeRides: number
  totalRevenue: number
  averageRating: number
  completionRate: number
  responseTime: number
  userGrowth: number
  revenueGrowth: number
  marketShare: number
  customerSatisfaction: number
}

interface RealtimeStats {
  ridesPerMinute: number
  activeDrivers: number
  avgWaitTime: number
  peakHours: string[]
  topCities: Array<{ name: string; rides: number }>
  emergencyEvents: number
}

export const ExecutiveDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<ExecutiveMetrics>({
    totalUsers: 0,
    activeRides: 0,
    totalRevenue: 0,
    averageRating: 0,
    completionRate: 0,
    responseTime: 0,
    userGrowth: 0,
    revenueGrowth: 0,
    marketShare: 0,
    customerSatisfaction: 0
  })

  const [realtimeStats, setRealtimeStats] = useState<RealtimeStats>({
    ridesPerMinute: 0,
    activeDrivers: 0,
    avgWaitTime: 0,
    peakHours: [],
    topCities: [],
    emergencyEvents: 0
  })

  const [isLoading, setIsLoading] = useState(true)
  const [selectedTimeframe, setSelectedTimeframe] = useState<'24h' | '7d' | '30d' | '1y'>('24h')

  // Carregar métricas executivas
  useEffect(() => {
    loadExecutiveMetrics()
    loadRealtimeStats()

    // Atualizar a cada 30 segundos
    const interval = setInterval(() => {
      loadExecutiveMetrics()
      loadRealtimeStats()
    }, 30000)

    return () => clearInterval(interval)
  }, [selectedTimeframe])

  const loadExecutiveMetrics = async () => {
    try {
      setIsLoading(true)

      // Simular dados executivos (em produção, viria de queries complexas)
      const mockMetrics: ExecutiveMetrics = {
        totalUsers: 1247893,
        activeRides: 15847,
        totalRevenue: 89456723.50,
        averageRating: 4.87,
        completionRate: 98.3,
        responseTime: 2.4,
        userGrowth: 23.7,
        revenueGrowth: 156.8,
        marketShare: 34.2,
        customerSatisfaction: 96.1
      }

      // Adicionar variação baseada no timeframe
      const variation = Math.random() * 0.1 - 0.05 // ±5%
      Object.keys(mockMetrics).forEach(key => {
        if (typeof mockMetrics[key as keyof ExecutiveMetrics] === 'number') {
          mockMetrics[key as keyof ExecutiveMetrics] *= (1 + variation)
        }
      })

      setMetrics(mockMetrics)

      // Analytics
      analyticsService.track('executive_dashboard_viewed', {
        timeframe: selectedTimeframe,
        total_users: mockMetrics.totalUsers,
        active_rides: mockMetrics.activeRides
      })

    } catch (error) {
      console.error('Erro ao carregar métricas executivas:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadRealtimeStats = async () => {
    try {
      // Simular dados em tempo real
      const mockStats: RealtimeStats = {
        ridesPerMinute: Math.floor(Math.random() * 50) + 20,
        activeDrivers: Math.floor(Math.random() * 5000) + 15000,
        avgWaitTime: Math.random() * 3 + 1.5,
        peakHours: ['08:00-09:00', '18:00-19:00', '22:00-23:00'],
        topCities: [
          { name: 'São Paulo', rides: 8947 },
          { name: 'Rio de Janeiro', rides: 5632 },
          { name: 'Belo Horizonte', rides: 3421 },
          { name: 'Brasília', rides: 2876 },
          { name: 'Salvador', rides: 2543 }
        ],
        emergencyEvents: Math.floor(Math.random() * 3)
      }

      setRealtimeStats(mockStats)
    } catch (error) {
      console.error('Erro ao carregar stats em tempo real:', error)
    }
  }

  // Componente de métrica
  const MetricCard: React.FC<{
    title: string
    value: string | number
    change?: number
    icon: React.ReactNode
    color: string
    format?: 'number' | 'currency' | 'percentage' | 'rating'
  }> = ({ title, value, change, icon, color, format = 'number' }) => {
    const formatValue = (val: string | number) => {
      if (typeof val === 'string') return val
      
      switch (format) {
        case 'currency':
          return `R$ ${val.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
        case 'percentage':
          return `${val.toFixed(1)}%`
        case 'rating':
          return `${val.toFixed(2)} ⭐`
        default:
          return val.toLocaleString('pt-BR')
      }
    }

    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
      >
        <div className="flex items-center justify-between mb-4">
          <div className={`w-12 h-12 ${color} rounded-xl flex items-center justify-center`}>
            {icon}
          </div>
          {change !== undefined && (
            <div className={`flex items-center space-x-1 ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
              <span className="text-sm font-medium">{Math.abs(change).toFixed(1)}%</span>
            </div>
          )}
        </div>
        
        <div>
          <h3 className="text-2xl font-bold text-gray-900 mb-1">
            {formatValue(value)}
          </h3>
          <p className="text-gray-600 text-sm">{title}</p>
        </div>
      </motion.div>
    )
  }

  // Componente de gráfico simples
  const SimpleChart: React.FC<{
    title: string
    data: Array<{ label: string; value: number }>
    color: string
  }> = ({ title, data, color }) => (
    <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center justify-between">
            <span className="text-gray-600 text-sm">{item.label}</span>
            <div className="flex items-center space-x-2">
              <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${(item.value / Math.max(...data.map(d => d.value))) * 100}%` }}
                  transition={{ duration: 1, delay: index * 0.1 }}
                  className={`h-full ${color}`}
                />
              </div>
              <span className="text-sm font-medium text-gray-900 w-12 text-right">
                {item.value.toLocaleString()}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      {/* Header */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              MobiDrive Executive Dashboard
            </h1>
            <p className="text-gray-600 text-lg">
              Monitoramento em tempo real • Última atualização: {new Date().toLocaleTimeString()}
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Timeframe selector */}
            <div className="flex bg-white rounded-xl p-1 shadow-lg">
              {(['24h', '7d', '30d', '1y'] as const).map((timeframe) => (
                <button
                  key={timeframe}
                  onClick={() => setSelectedTimeframe(timeframe)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedTimeframe === timeframe
                      ? 'bg-blue-500 text-white'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {timeframe}
                </button>
              ))}
            </div>
            
            {/* Status indicator */}
            <div className="flex items-center space-x-2 bg-green-100 text-green-700 px-4 py-2 rounded-xl">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium">Sistema Operacional</span>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Métricas principais */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
      >
        <MetricCard
          title="Usuários Totais"
          value={metrics.totalUsers}
          change={metrics.userGrowth}
          icon={<Users className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-blue-500 to-blue-600"
        />
        
        <MetricCard
          title="Corridas Ativas"
          value={metrics.activeRides}
          icon={<Car className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-green-500 to-green-600"
        />
        
        <MetricCard
          title="Receita Total"
          value={metrics.totalRevenue}
          change={metrics.revenueGrowth}
          icon={<DollarSign className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-purple-500 to-purple-600"
          format="currency"
        />
        
        <MetricCard
          title="Avaliação Média"
          value={metrics.averageRating}
          icon={<Star className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-yellow-500 to-yellow-600"
          format="rating"
        />
      </motion.div>

      {/* Métricas operacionais */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
      >
        <MetricCard
          title="Taxa de Conclusão"
          value={metrics.completionRate}
          icon={<Target className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-emerald-500 to-emerald-600"
          format="percentage"
        />
        
        <MetricCard
          title="Tempo de Resposta"
          value={`${metrics.responseTime.toFixed(1)}min`}
          icon={<Clock className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-orange-500 to-orange-600"
        />
        
        <MetricCard
          title="Market Share"
          value={metrics.marketShare}
          icon={<Globe className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-indigo-500 to-indigo-600"
          format="percentage"
        />
        
        <MetricCard
          title="Satisfação do Cliente"
          value={metrics.customerSatisfaction}
          icon={<Award className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-pink-500 to-pink-600"
          format="percentage"
        />
      </motion.div>

      {/* Stats em tempo real */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8"
      >
        {/* Tempo real */}
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <Activity className="w-5 h-5 text-blue-500" />
            <span>Tempo Real</span>
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Corridas/min</span>
              <span className="text-2xl font-bold text-blue-600">{realtimeStats.ridesPerMinute}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Motoristas ativos</span>
              <span className="text-2xl font-bold text-green-600">{realtimeStats.activeDrivers.toLocaleString()}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Tempo médio espera</span>
              <span className="text-2xl font-bold text-orange-600">{realtimeStats.avgWaitTime.toFixed(1)}min</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Emergências ativas</span>
              <span className={`text-2xl font-bold ${realtimeStats.emergencyEvents > 0 ? 'text-red-600' : 'text-gray-400'}`}>
                {realtimeStats.emergencyEvents}
              </span>
            </div>
          </div>
        </div>

        {/* Top cidades */}
        <SimpleChart
          title="Top Cidades"
          data={realtimeStats.topCities.map(city => ({ label: city.name, value: city.rides }))}
          color="bg-gradient-to-r from-blue-500 to-purple-500"
        />

        {/* Horários de pico */}
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <Clock className="w-5 h-5 text-purple-500" />
            <span>Horários de Pico</span>
          </h3>
          <div className="space-y-3">
            {realtimeStats.peakHours.map((hour, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-purple-500 rounded-full" />
                <span className="text-gray-700 font-medium">{hour}</span>
              </div>
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl">
            <div className="flex items-center space-x-2 mb-2">
              <Rocket className="w-5 h-5 text-purple-600" />
              <span className="font-semibold text-purple-900">Projeção</span>
            </div>
            <p className="text-sm text-purple-700">
              Crescimento de 23.7% nas próximas 4 semanas baseado em tendências atuais
            </p>
          </div>
        </div>
      </motion.div>

      {/* Footer com insights */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 text-white"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold mb-2">🚀</div>
            <h3 className="font-semibold mb-1">Crescimento Acelerado</h3>
            <p className="text-blue-100 text-sm">156.8% de crescimento em receita</p>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold mb-2">🏆</div>
            <h3 className="font-semibold mb-1">Liderança de Mercado</h3>
            <p className="text-blue-100 text-sm">34.2% de market share conquistado</p>
          </div>
          
          <div className="text-center">
            <div className="text-3xl font-bold mb-2">⭐</div>
            <h3 className="font-semibold mb-1">Excelência Operacional</h3>
            <p className="text-blue-100 text-sm">4.87/5.0 de satisfação do cliente</p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default ExecutiveDashboard
