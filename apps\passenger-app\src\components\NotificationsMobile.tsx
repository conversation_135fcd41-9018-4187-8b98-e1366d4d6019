import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  <PERSON>,
  X,
  Check,
  Check<PERSON><PERSON><PERSON>,
  Clock,
  Car,
  CreditCard,
  MessageCircle,
  AlertTriangle,
  Gift,
  Settings,
  Trash2,
  MarkAsRead
} from 'lucide-react';
import { simpleNotificationService, SimpleNotification } from '../services/SimpleNotificationService';
import { useAuth } from '../contexts/AuthContextSimple';

interface NotificationsMobileProps {
  isOpen: boolean;
  onClose: () => void;
}

export const NotificationsMobile: React.FC<NotificationsMobileProps> = ({ isOpen, onClose }) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<SimpleNotification[]>([]);
  const [loading, setLoading] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [filter, setFilter] = useState<'all' | 'unread' | 'ride' | 'payment'>('all');

  useEffect(() => {
    if (isOpen) {
      loadNotifications();
      loadUnreadCount();
    }
  }, [isOpen, filter]);

  useEffect(() => {
    // Subscrever a novas notificações
    const subscription = simpleNotificationService.subscribeToNotifications((notification) => {
      setNotifications(prev => [notification, ...prev]);
      setUnreadCount(prev => prev + 1);
    });

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, []);

  const loadNotifications = async () => {
    setLoading(true);
    try {
      const data = await simpleNotificationService.getUserNotifications(50);
      let filteredData = data;

      switch (filter) {
        case 'unread':
          filteredData = data.filter(n => !n.read_at);
          break;
        case 'ride':
          filteredData = data.filter(n => 
            ['ride_request', 'ride_accepted', 'ride_started', 'ride_completed'].includes(n.type)
          );
          break;
        case 'payment':
          filteredData = data.filter(n => n.type === 'payment');
          break;
      }

      setNotifications(filteredData);
    } catch (error) {
      console.error('Erro ao carregar notificações:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    try {
      const count = await simpleNotificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Erro ao carregar contagem:', error);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await simpleNotificationService.markAsRead(notificationId);
      setNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, read_at: new Date().toISOString() } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Erro ao marcar como lida:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      await simpleNotificationService.markAllAsRead();
      setNotifications(prev =>
        prev.map(n => ({ ...n, read_at: n.read_at || new Date().toISOString() }))
      );
      setUnreadCount(0);
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error);
    }
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      await simpleNotificationService.deleteNotification(notificationId);
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
    } catch (error) {
      console.error('Erro ao deletar notificação:', error);
    }
  };

  const getNotificationIcon = (type: SimpleNotification['type']) => {
    const iconMap = {
      ride_request: Car,
      ride_accepted: Car,
      ride_started: Car,
      ride_completed: Check,
      payment: CreditCard,
      chat_message: MessageCircle,
      system: AlertTriangle
    };

    const IconComponent = iconMap[type] || Bell;
    return <IconComponent className="w-5 h-5" />;
  };

  const getNotificationColor = (type: SimpleNotification['type'], priority: SimpleNotification['priority']) => {
    if (priority === 'urgent') return 'from-red-500 to-red-600';
    if (priority === 'high') return 'from-orange-500 to-orange-600';
    
    const colorMap = {
      ride_request: 'from-blue-500 to-blue-600',
      ride_accepted: 'from-green-500 to-green-600',
      ride_started: 'from-purple-500 to-purple-600',
      ride_completed: 'from-green-500 to-green-600',
      payment: 'from-yellow-500 to-yellow-600',
      chat_message: 'from-blue-500 to-blue-600',
      system: 'from-gray-500 to-gray-600'
    };

    return colorMap[type] || 'from-gray-500 to-gray-600';
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Agora';
    if (diffMins < 60) return `${diffMins}min`;
    if (diffHours < 24) return `${diffHours}h`;
    if (diffDays < 7) return `${diffDays}d`;
    
    return date.toLocaleDateString('pt-BR');
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0, x: '100%' }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: '100%' }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className="fixed inset-0 z-50 bg-black flex flex-col"
    >
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-md border-b border-white/10 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.button
              onClick={onClose}
              className="p-2 text-white/80 hover:text-white transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <X className="w-5 h-5" />
            </motion.button>
            
            <div>
              <h3 className="text-white font-semibold text-lg">Notificações</h3>
              {unreadCount > 0 && (
                <p className="text-white/60 text-sm">{unreadCount} não lidas</p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <motion.button
                onClick={markAllAsRead}
                className="p-2 text-white/80 hover:text-white transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <CheckCheck className="w-5 h-5" />
              </motion.button>
            )}
            <motion.button
              className="p-2 text-white/80 hover:text-white transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Settings className="w-5 h-5" />
            </motion.button>
          </div>
        </div>

        {/* Filtros */}
        <div className="flex space-x-2 mt-4">
          {[
            { key: 'all', label: 'Todas' },
            { key: 'unread', label: 'Não lidas' },
            { key: 'ride', label: 'Corridas' },
            { key: 'payment', label: 'Pagamentos' }
          ].map((filterOption) => (
            <motion.button
              key={filterOption.key}
              onClick={() => setFilter(filterOption.key as any)}
              className={`px-3 py-1 rounded-full text-sm transition-all ${
                filter === filterOption.key
                  ? 'bg-blue-500 text-white'
                  : 'bg-white/10 text-white/70 hover:bg-white/20'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {filterOption.label}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Lista de Notificações */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div className="text-white/60">Carregando notificações...</div>
          </div>
        ) : notifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-white/60">
            <Bell className="w-16 h-16 mb-4 opacity-50" />
            <p className="text-lg font-medium">Nenhuma notificação</p>
            <p className="text-sm">Você está em dia!</p>
          </div>
        ) : (
          <div className="p-4 space-y-3">
            <AnimatePresence>
              {notifications.map((notification, index) => (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className={`relative overflow-hidden rounded-xl border border-white/10 ${
                    notification.read_at ? 'bg-white/5' : 'bg-white/10'
                  }`}
                >
                  {/* Indicador de prioridade */}
                  {!notification.read_at && (
                    <div className={`absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b ${getNotificationColor(notification.type, notification.priority)}`} />
                  )}

                  <div className="p-4">
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-xl bg-gradient-to-r ${getNotificationColor(notification.type, notification.priority)}`}>
                        {getNotificationIcon(notification.type)}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="text-white font-medium text-sm">
                              {notification.title}
                            </h4>
                            <p className="text-white/70 text-sm mt-1 line-clamp-2">
                              {notification.message}
                            </p>
                          </div>

                          <div className="flex items-center space-x-2 ml-2">
                            <span className="text-white/50 text-xs flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              {formatTime(notification.created_at)}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between mt-3">
                          <div className="flex items-center space-x-2">
                            {!notification.read_at && (
                              <motion.button
                                onClick={() => markAsRead(notification.id)}
                                className="p-1 text-white/60 hover:text-white transition-colors"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <Check className="w-4 h-4" />
                              </motion.button>
                            )}
                            
                            <motion.button
                              onClick={() => deleteNotification(notification.id)}
                              className="p-1 text-white/60 hover:text-red-400 transition-colors"
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                            >
                              <Trash2 className="w-4 h-4" />
                            </motion.button>
                          </div>

                          {notification.read_at && (
                            <div className="text-white/40">
                              <CheckCheck className="w-4 h-4" />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default NotificationsMobile;
