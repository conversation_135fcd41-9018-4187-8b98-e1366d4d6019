import React, { useState } from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContextSimple'
import { ModernLayout, ModernCard, ModernInput, ModernButton } from '../components/ModernLayout'

// 📝 PÁGINA DE REGISTRO - DESIGN ORIGINAL MANTIDO
// Conversão Android nativa aplicada via wrapper adaptativo

export const Register: React.FC = () => {
  const { signUp, loading, error, user } = useAuth()
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    full_name: '',
    phone: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [passwordErrors, setPasswordErrors] = useState<string[]>([])
  const [isSuccess, setIsSuccess] = useState(false)

  // Se o usuário j<PERSON> está logado, redirecionar para dashboard
  if (user && !loading) {
    return <Navigate to="/dashboard" replace />
  }

  const validatePassword = (password: string): string[] => {
    const errors: string[] = []

    if (password.length < 8) {
      errors.push('Mínimo 8 caracteres')
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Uma letra maiúscula')
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Uma letra minúscula')
    }
    if (!/\d/.test(password)) {
      errors.push('Um número')
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Um caractere especial')
    }

    return errors
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    if (field === 'password') {
      setPasswordErrors(validatePassword(value))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (passwordErrors.length > 0) {
      return
    }

    if (formData.password !== formData.confirmPassword) {
      return
    }

    try {
      await signUp(formData.email, formData.password, {
        full_name: formData.full_name,
        phone: formData.phone
      })
      setIsSuccess(true)
    } catch (err) {
      console.error('Erro no cadastro:', err)
    }
  }

  // Página de sucesso (MANTENDO DESIGN ORIGINAL)
  if (isSuccess) {
    return (
      <ModernLayout
        title="Sucesso!"
        subtitle="Conta criada com sucesso"
        pageIcon="✅"
        animatedSymbol="🎉"
      >
        <ModernCard className="text-center">
          <div style={{ fontSize: '64px', marginBottom: '16px' }}>✅</div>
          <h2 style={{ fontSize: '24px', fontWeight: '700', marginBottom: '12px', color: '#1f2937' }}>
            Conta Criada!
          </h2>
          <p style={{ color: '#6b7280', marginBottom: '24px', lineHeight: '1.5' }}>
            Verifique seu email para confirmar sua conta e fazer login.
          </p>
          <ModernButton
            onClick={() => window.location.href = '/login'}
            icon="🚀"
            variant="success"
          >
            Ir para Login
          </ModernButton>
        </ModernCard>
      </ModernLayout>
    )
  }

  // Layout principal (MANTENDO DESIGN ORIGINAL)
  return (
    <ModernLayout
      title="MobiDrive"
      subtitle="Criar Nova Conta"
      pageIcon="👤"
      animatedSymbol="✨"
    >
      <ModernCard title="Cadastro" icon="📝">
        <form onSubmit={handleSubmit}>

          <ModernInput
            label="Nome Completo"
            icon="👤"
            placeholder="Seu nome completo"
            value={formData.full_name}
            onChange={(e) => handleInputChange('full_name', e.target.value)}
          />

          <ModernInput
            label="Email"
            icon="📧"
            type="email"
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
          />

          <ModernInput
            label="Telefone"
            icon="📱"
            type="tel"
            placeholder="(11) 99999-9999"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
          />

          <div className="modern-input-group">
            <label className="modern-input-label">Senha</label>
            <div className="modern-input-container">
              <span className="modern-input-icon">🔒</span>
              <input
                type={showPassword ? "text" : "password"}
                placeholder="••••••••"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="modern-input"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '20px',
                  cursor: 'pointer',
                  padding: '8px',
                  borderRadius: '8px',
                  opacity: 0.7
                }}
              >
                {showPassword ? "🙈" : "👁️"}
              </button>
            </div>
            {formData.password && (
              <div style={{ marginTop: '8px', fontSize: '12px' }}>
                {passwordErrors.map((error, index) => (
                  <p key={index} style={{ color: '#ef4444', margin: '2px 0' }}>• {error}</p>
                ))}
                {passwordErrors.length === 0 && (
                  <p style={{ color: '#10b981', margin: '2px 0' }}>✓ Senha válida</p>
                )}
              </div>
            )}
          </div>

          <div className="modern-input-group">
            <label className="modern-input-label">Confirmar Senha</label>
            <div className="modern-input-container">
              <span className="modern-input-icon">🔒</span>
              <input
                type={showConfirmPassword ? "text" : "password"}
                placeholder="••••••••"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                className="modern-input"
                required
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '20px',
                  cursor: 'pointer',
                  padding: '8px',
                  borderRadius: '8px',
                  opacity: 0.7
                }}
              >
                {showConfirmPassword ? "🙈" : "👁️"}
              </button>
            </div>
            {formData.confirmPassword && (
              <div style={{ marginTop: '8px', fontSize: '12px' }}>
                {formData.password === formData.confirmPassword ? (
                  <p style={{ color: '#10b981', margin: '2px 0' }}>✓ Senhas coincidem</p>
                ) : (
                  <p style={{ color: '#ef4444', margin: '2px 0' }}>• Senhas não coincidem</p>
                )}
              </div>
            )}
          </div>

          {error && (
            <div style={{
              background: 'rgba(239, 68, 68, 0.1)',
              border: '1px solid rgba(239, 68, 68, 0.3)',
              borderRadius: '12px',
              padding: '12px',
              marginBottom: '16px'
            }}>
              <p style={{ color: '#ef4444', fontSize: '14px', margin: 0 }}>{error}</p>
            </div>
          )}

          <ModernButton
            loading={loading}
            disabled={passwordErrors.length > 0 || formData.password !== formData.confirmPassword}
            icon="🚀"
            variant="primary"
          >
            Criar Conta
          </ModernButton>
        </form>
      </ModernCard>

      <ModernCard glass className="text-center">
        <p style={{ color: 'rgba(255, 255, 255, 0.9)', margin: 0 }}>
          Já tem uma conta?{' '}
          <a
            href="/login"
            style={{
              color: '#fff',
              fontWeight: '700',
              textDecoration: 'none',
              textShadow: '0 1px 2px rgba(0,0,0,0.2)'
            }}
          >
            Faça login
          </a>
        </p>
      </ModernCard>
    </ModernLayout>
  )
}

export default Register
