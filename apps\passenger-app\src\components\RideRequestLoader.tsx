import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, MapPin, Users, CheckCircle, Clock } from 'lucide-react'

interface RideRequestLoaderProps {
  isVisible: boolean
  onComplete?: () => void
}

interface LoadingStep {
  id: string
  title: string
  description: string
  icon: React.ComponentType<any>
  duration: number
  color: string
}

const loadingSteps: LoadingStep[] = [
  {
    id: 'processing',
    title: 'Processando solicitação',
    description: 'Criando sua corrida no sistema...',
    icon: Clock,
    duration: 1000,
    color: 'text-blue-500'
  },
  {
    id: 'searching',
    title: 'Buscando motoristas',
    description: 'Procurando motoristas próximos...',
    icon: Search,
    duration: 2000,
    color: 'text-yellow-500'
  },
  {
    id: 'notifying',
    title: 'Notificando motoristas',
    description: 'Enviando notificações para motoristas disponíveis...',
    icon: Users,
    duration: 1500,
    color: 'text-purple-500'
  },
  {
    id: 'matching',
    title: 'Aguardando aceite',
    description: 'Aguardando um motorista aceitar sua corrida...',
    icon: MapPin,
    duration: 2000,
    color: 'text-orange-500'
  },
  {
    id: 'completed',
    title: 'Motorista encontrado!',
    description: 'Conectando você com seu motorista...',
    icon: CheckCircle,
    duration: 500,
    color: 'text-green-500'
  }
]

export const RideRequestLoader: React.FC<RideRequestLoaderProps> = ({
  isVisible,
  onComplete
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    if (!isVisible) {
      setCurrentStepIndex(0)
      setProgress(0)
      return
    }

    let stepTimer: NodeJS.Timeout
    let progressTimer: NodeJS.Timeout

    const runStep = (stepIndex: number) => {
      if (stepIndex >= loadingSteps.length) {
        onComplete?.()
        return
      }

      const step = loadingSteps[stepIndex]
      setCurrentStepIndex(stepIndex)
      setProgress(0)

      // Animar progresso da etapa atual
      const progressIncrement = 100 / (step.duration / 50)
      let currentProgress = 0

      progressTimer = setInterval(() => {
        currentProgress += progressIncrement
        if (currentProgress >= 100) {
          currentProgress = 100
          clearInterval(progressTimer)
        }
        setProgress(currentProgress)
      }, 50)

      // Ir para próxima etapa
      stepTimer = setTimeout(() => {
        clearInterval(progressTimer)
        runStep(stepIndex + 1)
      }, step.duration)
    }

    runStep(0)

    return () => {
      clearTimeout(stepTimer)
      clearInterval(progressTimer)
    }
  }, [isVisible, onComplete])

  if (!isVisible) return null

  const currentStep = loadingSteps[currentStepIndex]

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      >
        <motion.div
          className="glass-card p-8 max-w-md w-full text-center"
          initial={{ y: 50 }}
          animate={{ y: 0 }}
        >
          {/* Ícone animado */}
          <motion.div
            className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
            animate={{ 
              rotate: currentStep.id === 'searching' ? 360 : 0,
              scale: currentStep.id === 'completed' ? [1, 1.2, 1] : 1
            }}
            transition={{ 
              rotate: { duration: 2, repeat: currentStep.id === 'searching' ? Infinity : 0 },
              scale: { duration: 0.5 }
            }}
          >
            <currentStep.icon className={`w-10 h-10 text-white`} />
          </motion.div>

          {/* Título e descrição */}
          <motion.h3
            key={currentStep.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-xl font-bold text-gray-900 mb-2"
          >
            {currentStep.title}
          </motion.h3>

          <motion.p
            key={`${currentStep.id}-desc`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-gray-600 mb-6"
          >
            {currentStep.description}
          </motion.p>

          {/* Barra de progresso */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <motion.div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.1 }}
            />
          </div>

          {/* Indicadores de etapas */}
          <div className="flex justify-center space-x-2">
            {loadingSteps.map((step, index) => (
              <div
                key={step.id}
                className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                  index < currentStepIndex
                    ? 'bg-green-500'
                    : index === currentStepIndex
                    ? 'bg-blue-500'
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          {/* Estatísticas em tempo real */}
          <motion.div
            className="mt-6 grid grid-cols-3 gap-4 text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
          >
            <div className="text-center">
              <div className="font-semibold text-gray-900">
                {Math.floor(Math.random() * 15) + 5}
              </div>
              <div className="text-gray-500">Motoristas próximos</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-gray-900">
                {Math.floor(Math.random() * 3) + 2} min
              </div>
              <div className="text-gray-500">Tempo estimado</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-gray-900">
                {Math.floor(Math.random() * 5) + 1} km
              </div>
              <div className="text-gray-500">Distância média</div>
            </div>
          </motion.div>

          {/* Dica para o usuário */}
          {currentStepIndex >= 2 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-4 p-3 bg-blue-50 rounded-lg"
            >
              <p className="text-sm text-blue-700">
                💡 <strong>Dica:</strong> Motoristas próximos estão sendo notificados. 
                Aguarde alguns segundos para o aceite.
              </p>
            </motion.div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
