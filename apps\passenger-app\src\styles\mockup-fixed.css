/* 📱 MOCKUP ZOOM 100% FIXO - APENAS ESTRUTURA DO IPHONE */

.mobile-mockup-container {
  /* FORÇA ZOOM FIXO - MÁXIMA PRIORIDADE */
  zoom: 1 !important;
  transform: scale(1) !important;
  transform-origin: center center !important;

  /* Posição fixa para não ser afetada por scroll/zoom */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;

  /* Z-index máximo para ficar acima de tudo */
  z-index: 9999 !important;

  /* Previne zoom do browser - TODOS OS PREFIXOS */
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;

  /* Previne zoom touch em mobile */
  touch-action: manipulation !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;

  /* Força escala 1 em todos os contextos */
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;

  /* Previne transformações CSS */
  will-change: auto !important;
  backface-visibility: visible !important;
  perspective: none !important;
}

.mobile-mockup-screen {
  /* Dimensões fixas do iPhone */
  width: 375px !important;
  height: 812px !important;

  /* Zoom fixo */
  zoom: 1 !important;
  transform: scale(1) !important;
  transform-origin: center center !important;

  /* Previne redimensionamento */
  min-width: 375px !important;
  max-width: 375px !important;
  min-height: 812px !important;
  max-height: 812px !important;

  /* Overflow controlado */
  overflow: hidden !important;

  /* Previne zoom do conteúdo */
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

.mobile-mockup-content {
  /* Conteúdo dentro do mockup */
  width: 100% !important;
  height: 100% !important;
  zoom: 1 !important;
  transform: scale(1) !important;

  /* Previne zoom do conteúdo interno */
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* FORÇA ZOOM FIXO APENAS NOS ELEMENTOS ESTRUTURAIS DO IPHONE */
/* NÃO aplica no conteúdo do app (.mobile-mockup-content *) */

/* Container do iPhone e tela */
.mobile-mockup-container > div,
.mobile-mockup-screen {
  zoom: 1 !important;
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}

/* Elementos estruturais do iPhone - ESPECÍFICOS */
.mobile-mockup-container > div > div,  /* Corpo do iPhone */
.mobile-mockup-container > div > div > div,  /* Moldura interna */
.mobile-mockup-screen > div:not(.mobile-mockup-content),  /* Notch, Status bar, Home indicator */
.mobile-mockup-container .absolute:not(.mobile-mockup-content):not(.mobile-mockup-content *) {  /* Botões laterais, badges */
  zoom: 1 !important;
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}

/* Elementos específicos do iPhone - MUITO ESPECÍFICO */
.mobile-mockup-container .absolute[class*="top-0"],     /* Notch e Status bar */
.mobile-mockup-container .absolute[class*="bottom-"],   /* Home indicator e badges */
.mobile-mockup-container .absolute[class*="left-0"],    /* Botões volume */
.mobile-mockup-container .absolute[class*="right-0"],   /* Botão power */
.mobile-mockup-container .absolute[class*="bottom-8"] { /* Badges informativos */
  zoom: 1 !important;
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}

/* ELEMENTOS ESPECÍFICOS DO IPHONE - NÃO DO CONTEÚDO DO APP */
/* Aplica zoom fixo apenas nos elementos estruturais do iPhone */

/* Notch e seus elementos */
.mobile-mockup-container .absolute[class*="top-0"] *,
.mobile-mockup-container .absolute[class*="z-50"] *,
.mobile-mockup-container .absolute[class*="z-40"] * {
  zoom: 1 !important;
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}

/* Botões físicos do iPhone */
.mobile-mockup-container .absolute[class*="left-0"][class*="top-"],
.mobile-mockup-container .absolute[class*="right-0"][class*="top-"] {
  zoom: 1 !important;
  width: 4px !important;
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}

/* Badges informativos */
.mobile-mockup-container .absolute[class*="bottom-8"] *,
.mobile-mockup-container .absolute[class*="bottom-8"] {
  zoom: 1 !important;
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
}

/* Previne zoom em media queries */
@media screen and (min-width: 1px) and (max-width: 9999px) {
  .mobile-mockup-container {
    zoom: 1 !important;
    transform: scale(1) !important;
  }

  .mobile-mockup-screen {
    width: 375px !important;
    height: 812px !important;
    zoom: 1 !important;
    transform: scale(1) !important;
  }
}

/* Previne zoom em diferentes resoluções */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
  .mobile-mockup-container,
  .mobile-mockup-screen {
    zoom: 1 !important;
    -webkit-transform: scale(1) !important;
  }
}

/* ELEMENTOS REACT/FRAMER MOTION - ZOOM FIXO */
.mobile-mockup-container [data-framer-component],
.mobile-mockup-container [data-framer-name],
.mobile-mockup-container .motion-div,
.mobile-mockup-container .motion-button,
.mobile-mockup-container .motion-img,
.mobile-mockup-container .motion-span,
.mobile-mockup-container [style*="transform"],
.mobile-mockup-container [style*="scale"],
.mobile-mockup-container [style*="zoom"] {
  zoom: 1 !important;
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
}

/* FORÇA ZOOM 100% NO VIEWPORT */
.mobile-mockup-container {
  /* Meta viewport override */
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;

  /* Força zoom 1 em qualquer contexto */
  contain: layout style paint !important;
  isolation: isolate !important;
}

/* Previne zoom por gestos */
.mobile-mockup-container {
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;

  /* Previne pinch zoom */
  touch-action: pan-x pan-y !important;
}

/* Força dimensões em qualquer zoom do browser */
@supports (zoom: 1) {
  .mobile-mockup-screen {
    zoom: 1 !important;
  }
}

@supports not (zoom: 1) {
  .mobile-mockup-screen {
    transform: scale(1) !important;
  }
}
