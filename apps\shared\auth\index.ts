/**
 * MobiDrive Shared Authentication Module
 *
 * This module exports all authentication-related components and utilities
 * for use across all MobiDrive apps.
 */

// Export authentication context and hook
export {
  AuthProvider,
  useAuth,
  type AuthProviderProps,
  type AuthContextType,
  type AuthState,
  type Profile
} from './AuthContext';

// Export login form component
export {
  LoginForm,
  type LoginFormProps
} from './LoginForm';

// Export login page component
export {
  LoginPage,
  type LoginPageProps
} from './LoginPage';

// Export protected route component
export {
  ProtectedRoute,
  type ProtectedRouteProps
} from './ProtectedRoute';

// Default export for convenience
import { AuthProvider as AuthProviderComponent } from './AuthContext';
import { useAuth as useAuthHook } from './AuthContext';
import { LoginForm as LoginFormComponent } from './LoginForm';
import { LoginPage as LoginPageComponent } from './LoginPage';
import { ProtectedRoute as ProtectedRouteComponent } from './ProtectedRoute';

export default {
  AuthProvider: AuthProviderComponent,
  useAuth: useAuthHook,
  LoginForm: LoginFormComponent,
  LoginPage: LoginPageComponent,
  ProtectedRoute: ProtectedRouteComponent
};
