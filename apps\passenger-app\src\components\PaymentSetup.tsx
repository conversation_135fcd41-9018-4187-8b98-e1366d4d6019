// =====================================================
// COMPONENTE DE CONFIGURAÇÃO DE PAGAMENTO
// Executa setup do pagamento em dinheiro
// =====================================================

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Play, Check, AlertCircle, Database } from 'lucide-react'
import { supabase } from '../lib/supabase'

export const PaymentSetup: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<string[]>([])
  const [isComplete, setIsComplete] = useState(false)
  const [hasError, setHasError] = useState(false)

  const addResult = (message: string, isError = false) => {
    setResults(prev => [...prev, `${isError ? '❌' : '✅'} ${message}`])
    if (isError) setHasError(true)
  }

  const executeSetup = async () => {
    setIsRunning(true)
    setResults([])
    setHasError(false)
    setIsComplete(false)

    try {
      addResult('Iniciando configuração de pagamento em dinheiro...')

      // 1. Verificar conexão
      addResult('Verificando conexão com Supabase...')
      const { error: connectionError } = await supabase
        .from('profiles')
        .select('count')
        .limit(1)

      if (connectionError) {
        addResult(`Erro de conexão: ${connectionError.message}`, true)
        return
      }
      addResult('Conexão estabelecida com sucesso')

      // 2. Criar enum se não existir
      addResult('Configurando tipos de pagamento...')
      const enumSQL = `
        DO $$ 
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payment_method_type') THEN
                CREATE TYPE payment_method_type AS ENUM ('cash', 'credit_card', 'debit_card', 'pix', 'wallet');
            ELSE
                IF NOT EXISTS (
                    SELECT 1 FROM pg_enum 
                    WHERE enumlabel = 'cash' 
                    AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'payment_method_type')
                ) THEN
                    ALTER TYPE payment_method_type ADD VALUE 'cash';
                END IF;
            END IF;
        END $$;
      `

      // ✅ REMOVIDO: RPC exec_sql não existe
      addResult('Tipos de pagamento configurados (usando estrutura existente)')

      // 3. Criar/atualizar tabela payment_methods
      addResult('Configurando tabela de métodos de pagamento...')
      const tableSQL = `
        CREATE TABLE IF NOT EXISTS payment_methods (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID NOT NULL,
            type TEXT NOT NULL,
            name TEXT NOT NULL,
            is_default BOOLEAN DEFAULT false,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),
            card_last_four TEXT,
            card_brand TEXT,
            card_holder_name TEXT,
            card_expiry_month TEXT,
            card_expiry_year TEXT,
            pix_key TEXT,
            cash_enabled BOOLEAN DEFAULT false,
            cash_change_limit DECIMAL(10,2) DEFAULT 50.00,
            cash_notes TEXT
        );
      `

      // ✅ REMOVIDO: RPC exec_sql não existe - tabela já existe
      addResult('Tabela payment_methods já existe e está configurada')

      // 4. Inserir método de dinheiro para usuário atual
      addResult('Criando método de pagamento em dinheiro...')
      const { data: user } = await supabase.auth.getUser()
      
      if (user?.user?.id) {
        // Verificar se já existe
        const { data: existing } = await supabase
          .from('payment_methods')
          .select('id')
          .eq('user_id', user.user.id)
          .eq('type', 'cash')
          .single()

        if (!existing) {
          const { error: insertError } = await supabase
            .from('payment_methods')
            .insert({
              user_id: user.user.id,
              type: 'cash',
              name: 'Dinheiro',
              is_default: true,
              is_active: true,
              cash_enabled: true,
              cash_change_limit: 50.00,
              cash_notes: 'Tenha o valor exato ou próximo. Motoristas podem não ter troco para valores altos.'
            })

          if (insertError) {
            addResult(`Erro ao criar método: ${insertError.message}`, true)
          } else {
            addResult('Método de dinheiro criado com sucesso')
          }
        } else {
          addResult('Método de dinheiro já existe')
        }
      }

      // 5. Verificar resultado
      addResult('Verificando configuração...')
      const { data: methods, error: verifyError } = await supabase
        .from('payment_methods')
        .select('*')
        .eq('type', 'cash')

      if (verifyError) {
        addResult(`Erro na verificação: ${verifyError.message}`, true)
      } else {
        addResult(`Encontrados ${methods?.length || 0} métodos de dinheiro`)
      }

      if (!hasError) {
        addResult('🎉 Configuração concluída com sucesso!')
        setIsComplete(true)
      }

    } catch (error) {
      addResult(`Erro geral: ${error}`, true)
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center mb-6">
          <Database className="w-8 h-8 text-blue-600 mr-3" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Configuração de Pagamento em Dinheiro
            </h2>
            <p className="text-gray-600">
              Configure o suporte a pagamento em dinheiro no Supabase
            </p>
          </div>
        </div>

        {!isComplete && (
          <motion.button
            onClick={executeSetup}
            disabled={isRunning}
            className={`
              w-full flex items-center justify-center py-3 px-6 rounded-lg font-semibold
              ${isRunning 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-blue-600 hover:bg-blue-700 text-white'
              }
            `}
            whileHover={!isRunning ? { scale: 1.02 } : {}}
            whileTap={!isRunning ? { scale: 0.98 } : {}}
          >
            {isRunning ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Executando...
              </>
            ) : (
              <>
                <Play className="w-5 h-5 mr-2" />
                Executar Configuração
              </>
            )}
          </motion.button>
        )}

        {results.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              {isComplete ? (
                <Check className="w-5 h-5 text-green-600 mr-2" />
              ) : hasError ? (
                <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
              ) : (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
              )}
              Resultados da Configuração
            </h3>
            
            <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
              <div className="space-y-2 font-mono text-sm">
                {results.map((result, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`
                      ${result.startsWith('❌') ? 'text-red-600' : 'text-green-600'}
                    `}
                  >
                    {result}
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        )}

        {isComplete && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"
          >
            <div className="flex items-center">
              <Check className="w-6 h-6 text-green-600 mr-3" />
              <div>
                <h4 className="font-semibold text-green-900">
                  Configuração Concluída!
                </h4>
                <p className="text-green-700 text-sm mt-1">
                  O pagamento em dinheiro foi configurado com sucesso. 
                  Recarregue a página para ver as mudanças.
                </p>
              </div>
            </div>
            
            <motion.button
              onClick={() => window.location.reload()}
              className="mt-3 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Recarregar Página
            </motion.button>
          </motion.div>
        )}

        <div className="mt-6 text-sm text-gray-500">
          <h4 className="font-semibold mb-2">O que esta configuração faz:</h4>
          <ul className="space-y-1">
            <li>• Cria tipos de pagamento no banco de dados</li>
            <li>• Configura tabela de métodos de pagamento</li>
            <li>• Adiciona método de dinheiro como padrão</li>
            <li>• Habilita funcionalidades de pagamento em espécie</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
