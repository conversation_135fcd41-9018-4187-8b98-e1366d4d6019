import { useState, useEffect, useCallback, useRef } from 'react'
import { mapboxService, SearchResult, RideEstimate, DriverLocation, MapboxError } from '../services/MapboxService'

// Re-export MapboxError for convenience
export { MapboxError }

interface UseMapboxSearchOptions {
  userLocation?: [number, number]
  debounceMs?: number
  minQueryLength?: number
}

interface SearchState {
  query: string
  results: SearchResult[]
  isLoading: boolean
  error: string | null
  selectedResult: SearchResult | null
}

interface RideState {
  origin: SearchResult | null
  destination: SearchResult | null
  estimate: RideEstimate | null
  nearbyDrivers: DriverLocation[]
  isCalculating: boolean
}

export const useMapboxSearch = (options: UseMapboxSearchOptions = {}) => {
  const {
    userLocation,
    debounceMs = 2500, // Increased to 2.5 seconds for maximum rate limiting protection
    minQueryLength = 4  // Increased to 4 characters to reduce API calls
  } = options

  // Search state
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    results: [],
    isLoading: false,
    error: null,
    selectedResult: null
  })

  // Ride state
  const [rideState, setRideState] = useState<RideState>({
    origin: null,
    destination: null,
    estimate: null,
    nearbyDrivers: [],
    isCalculating: false
  })

  // Refs for debouncing
  const debounceRef = useRef<NodeJS.Timeout>()
  const abortControllerRef = useRef<AbortController>()

  /**
   * Search for places with debouncing
   */
  const searchPlaces = useCallback(async (query: string) => {
    // Clear previous timeout
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    setSearchState(prev => ({
      ...prev,
      query,
      error: null
    }))

    if (query.length < minQueryLength) {
      setSearchState(prev => ({
        ...prev,
        results: [],
        isLoading: false
      }))
      return
    }

    // Debounce the search
    debounceRef.current = setTimeout(async () => {
      setSearchState(prev => ({ ...prev, isLoading: true }))

      try {
        // Create new abort controller
        abortControllerRef.current = new AbortController()

        const results = await mapboxService.searchPlaces(query, {
          proximity: userLocation,
          types: ['address', 'poi', 'place'],
          limit: 8
        })

        setSearchState(prev => ({
          ...prev,
          results,
          isLoading: false
        }))
      } catch (error) {
        if (error instanceof MapboxError) {
          let errorMessage = 'Erro ao buscar locais'

          switch (error.code) {
            case 'RATE_LIMIT':
              errorMessage = 'Muitas buscas. Aguarde um momento.'
              break
            case 'TIMEOUT':
              errorMessage = 'Busca demorou muito. Tente novamente.'
              break
            case 'NETWORK_ERROR':
              errorMessage = 'Erro de conexão. Verifique sua internet.'
              break
            case 'API_ERROR':
              errorMessage = 'Erro no serviço de busca.'
              break
            default:
              errorMessage = error.message
          }

          setSearchState(prev => ({
            ...prev,
            error: errorMessage,
            isLoading: false,
            results: []
          }))
        } else if (error instanceof Error && error.name !== 'AbortError') {
          setSearchState(prev => ({
            ...prev,
            error: 'Erro inesperado ao buscar locais',
            isLoading: false,
            results: []
          }))
        }
      }
    }, debounceMs)
  }, [userLocation, debounceMs, minQueryLength])

  /**
   * Select a search result
   */
  const selectResult = useCallback((result: SearchResult) => {
    setSearchState(prev => ({
      ...prev,
      selectedResult: result,
      query: result.place_name,
      results: []
    }))
  }, [])

  /**
   * Set origin location
   */
  const setOrigin = useCallback((location: SearchResult | [number, number]) => {
    if (Array.isArray(location)) {
      // Convert coordinates to SearchResult
      mapboxService.reverseGeocode(location).then(address => {
        const result: SearchResult = {
          id: 'current-location',
          place_name: address,
          center: location,
          place_type: ['address'],
          properties: {}
        }
        setRideState(prev => ({ ...prev, origin: result }))
      })
    } else {
      setRideState(prev => ({ ...prev, origin: location }))
    }
  }, [])

  /**
   * Set destination location
   */
  const setDestination = useCallback((location: SearchResult) => {
    setRideState(prev => ({ ...prev, destination: location }))
  }, [])

  /**
   * Calculate ride estimate
   */
  const calculateRideEstimate = useCallback(async () => {
    if (!rideState.origin || !rideState.destination) return

    setRideState(prev => ({ ...prev, isCalculating: true }))

    try {
      const estimate = await mapboxService.calculateRideEstimate(
        rideState.origin.center,
        rideState.destination.center
      )

      setRideState(prev => ({
        ...prev,
        estimate,
        isCalculating: false
      }))
    } catch (error) {
      console.error('Error calculating ride estimate:', error)
      setRideState(prev => ({ ...prev, isCalculating: false }))
    }
  }, [rideState.origin, rideState.destination])

  /**
   * Find nearby drivers
   */
  const findNearbyDrivers = useCallback(async () => {
    if (!userLocation) return

    try {
      const drivers = await mapboxService.findNearbyDrivers(userLocation)
      setRideState(prev => ({ ...prev, nearbyDrivers: drivers }))
    } catch (error) {
      console.error('Error finding nearby drivers:', error)
    }
  }, [userLocation])

  /**
   * Clear search
   */
  const clearSearch = useCallback(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    setSearchState({
      query: '',
      results: [],
      isLoading: false,
      error: null,
      selectedResult: null
    })
  }, [])

  /**
   * Clear ride data
   */
  const clearRide = useCallback(() => {
    setRideState({
      origin: null,
      destination: null,
      estimate: null,
      nearbyDrivers: [],
      isCalculating: false
    })
  }, [])

  /**
   * Get current location
   */
  const getCurrentLocation = useCallback((): Promise<[number, number]> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation not supported'))
        return
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve([position.coords.longitude, position.coords.latitude])
        },
        (error) => {
          reject(error)
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      )
    })
  }, [])

  // Auto-calculate estimate when origin and destination are set (with throttling)
  useEffect(() => {
    if (rideState.origin && rideState.destination && !rideState.isCalculating) {
      // Add a longer delay to prevent rate limiting
      const timer = setTimeout(() => {
        calculateRideEstimate()
      }, 3000) // Increased to 3 seconds delay

      return () => clearTimeout(timer)
    }
  }, [rideState.origin, rideState.destination, calculateRideEstimate, rideState.isCalculating])

  // Find nearby drivers when user location changes
  useEffect(() => {
    if (userLocation) {
      findNearbyDrivers()
    }
  }, [userLocation, findNearbyDrivers])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    // Search state
    searchQuery: searchState.query,
    searchResults: searchState.results,
    isSearching: searchState.isLoading,
    searchError: searchState.error,
    selectedResult: searchState.selectedResult,

    // Ride state
    origin: rideState.origin,
    destination: rideState.destination,
    rideEstimate: rideState.estimate,
    nearbyDrivers: rideState.nearbyDrivers,
    isCalculatingRide: rideState.isCalculating,

    // Actions
    searchPlaces,
    selectResult,
    setOrigin,
    setDestination,
    calculateRideEstimate,
    findNearbyDrivers,
    clearSearch,
    clearRide,
    getCurrentLocation
  }
}
