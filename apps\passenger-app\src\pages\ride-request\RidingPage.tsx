import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Navigate, useNavigate } from 'react-router-dom'
import { 
  MapPin,
  Star,
  Phone,
  MessageCircle,
  Shield,
  Share2,
  Navigation,
  Clock,
  Route,
  CheckCircle
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContextSimple'
import { useNoZoom } from '../../hooks/useNoZoom'

export const RidingPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  
  useNoZoom()

  // Redirect if not logged in
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // State management
  const [rideData, setRideData] = useState<any>(null)
  const [progress, setProgress] = useState(0)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [distanceTraveled, setDistanceTraveled] = useState(0)
  const [isNearDestination, setIsNearDestination] = useState(false)

  // Load ride data from sessionStorage
  useEffect(() => {
    const savedRideData = sessionStorage.getItem('rideData')
    
    if (savedRideData) {
      setRideData(JSON.parse(savedRideData))
    } else {
      // If no ride data, redirect back to map
      navigate('/ride-request/map')
    }
  }, [navigate])

  // Simulate ride progress
  useEffect(() => {
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + 1
        
        // Update distance and time based on progress
        setDistanceTraveled((newProgress / 100) * 5) // 5km total
        setTimeElapsed(Math.floor((newProgress / 100) * 15 * 60)) // 15 minutes total
        
        // Check if near destination (90% progress)
        if (newProgress >= 90) {
          setIsNearDestination(true)
        }
        
        // Complete ride at 100%
        if (newProgress >= 100) {
          clearInterval(progressInterval)
          return 100
        }
        
        return newProgress
      })
    }, 3000) // Update every 3 seconds for demo

    return () => clearInterval(progressInterval)
  }, [])

  // Event handlers
  const handleCall = useCallback(() => {
    if (rideData?.driver?.phone) {
      window.open(`tel:${rideData.driver.phone}`)
    }
  }, [rideData])

  const handleMessage = useCallback(() => {
    if (rideData?.driver?.phone) {
      window.open(`sms:${rideData.driver.phone}`)
    }
  }, [rideData])

  const handleEmergency = useCallback(() => {
    // In a real app, this would contact emergency services
    alert('Emergência ativada! Contatos de emergência foram notificados.')
  }, [])

  const handleShare = useCallback(() => {
    if (navigator.share) {
      navigator.share({
        title: 'Minha corrida MobiDrive',
        text: `Estou em uma corrida para ${rideData?.destination?.place_name}`,
        url: window.location.href
      })
    } else {
      // Fallback for browsers that don't support Web Share API
      const shareText = `Estou em uma corrida MobiDrive para ${rideData?.destination?.place_name}`
      navigator.clipboard.writeText(shareText)
      alert('Link da corrida copiado!')
    }
  }, [rideData])

  const handleFinishRide = useCallback(() => {
    // Store final ride data for rating page
    const finalRideData = {
      ...rideData,
      endTime: new Date().toISOString(),
      finalDistance: 5.0,
      finalDuration: 15,
      finalPrice: rideData?.price || 0
    }
    sessionStorage.setItem('completedRide', JSON.stringify(finalRideData))
    navigate('/ride-request/rating')
  }, [rideData, navigate])

  // Format time helper
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-black/40"></div>
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

      <div className="relative z-10 flex flex-col h-full min-h-screen">
        {/* Header */}
        <motion.div
          className="pt-8 pb-6 px-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1 text-center">
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-sm text-white/70">Corrida em andamento</p>
            </div>

            {/* Emergency Button */}
            <motion.button
              onClick={handleEmergency}
              className="p-2 rounded-xl bg-red-500/20 backdrop-blur-sm border border-red-500/30 text-red-400"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Shield className="w-5 h-5" />
            </motion.button>
          </div>
        </motion.div>

        {/* Content */}
        <div className="flex-1 px-4 pb-8">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {/* Driver Info */}
            {rideData?.driver && (
              <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20 shadow-2xl">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-lg">
                    {rideData.driver.photo}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-bold text-white">{rideData.driver.name}</h3>
                    <div className="flex items-center space-x-1">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      <span className="text-white/70 text-sm">{rideData.driver.rating}</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleCall}
                      className="p-2 bg-green-500/20 border border-green-500/30 text-green-400 rounded-lg hover:bg-green-500/30 transition-colors"
                    >
                      <Phone className="w-4 h-4" />
                    </button>
                    <button
                      onClick={handleMessage}
                      className="p-2 bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors"
                    >
                      <MessageCircle className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Route Map */}
            <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl overflow-hidden">
              <div className="h-64 bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center relative">
                <div className="text-center text-white/70">
                  <Route className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Rota em tempo real</p>
                </div>

                {/* Mock route progress */}
                <div className="absolute inset-4">
                  {/* Route line */}
                  <svg className="w-full h-full">
                    <path
                      d="M 20 200 Q 100 50 200 100 T 300 180"
                      stroke="#3b82f6"
                      strokeWidth="3"
                      fill="none"
                      strokeDasharray="5,5"
                    />
                  </svg>
                  
                  {/* Current position */}
                  <motion.div
                    animate={{
                      x: `${progress * 2.8}%`,
                      y: `${Math.sin(progress * 0.1) * 20 + 160}px`
                    }}
                    transition={{ duration: 0.5 }}
                    className="absolute w-4 h-4 bg-blue-500 rounded-full shadow-lg"
                    style={{ left: '20px', top: '0px' }}
                  />
                  
                  {/* Destination marker */}
                  <div className="absolute right-4 bottom-8 w-4 h-4 bg-red-500 rounded-full shadow-lg" />
                </div>
              </div>
            </motion.div>

            {/* Trip Progress */}
            <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
              <h3 className="text-lg font-bold text-white mb-4">Progresso da Viagem</h3>
              
              {/* Progress Bar */}
              <div className="w-full bg-white/20 rounded-full h-2 mb-4">
                <motion.div
                  className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <Navigation className="w-5 h-5 text-blue-400 mx-auto mb-1" />
                  <p className="text-white/70 text-xs">Distância</p>
                  <p className="text-white font-bold">{distanceTraveled.toFixed(1)} km</p>
                </div>
                <div>
                  <Clock className="w-5 h-5 text-blue-400 mx-auto mb-1" />
                  <p className="text-white/70 text-xs">Tempo</p>
                  <p className="text-white font-bold">{formatTime(timeElapsed)}</p>
                </div>
                <div>
                  <Route className="w-5 h-5 text-blue-400 mx-auto mb-1" />
                  <p className="text-white/70 text-xs">Progresso</p>
                  <p className="text-white font-bold">{progress}%</p>
                </div>
              </div>
            </motion.div>

            {/* Destination Info */}
            {rideData?.destination && (
              <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20 shadow-2xl">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                    <MapPin className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-white/70 text-sm">Destino</p>
                    <p className="text-white font-medium">{rideData.destination.place_name}</p>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Action Buttons */}
            <motion.div variants={itemVariants} className="flex space-x-3">
              <button
                onClick={handleShare}
                className="flex-1 bg-blue-500/20 border border-blue-500/30 text-blue-400 py-3 px-4 rounded-xl flex items-center justify-center space-x-2 hover:bg-blue-500/30 transition-colors"
              >
                <Share2 className="w-4 h-4" />
                <span>Compartilhar</span>
              </button>
              
              {isNearDestination && (
                <motion.button
                  onClick={handleFinishRide}
                  className="flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white py-3 px-4 rounded-xl flex items-center justify-center space-x-2 transition-all"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                >
                  <CheckCircle className="w-4 h-4" />
                  <span>Finalizar</span>
                </motion.button>
              )}
            </motion.div>

            {/* Trip Summary */}
            {rideData && (
              <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20 shadow-2xl">
                <h3 className="text-lg font-bold text-white mb-3">Resumo</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/70">Veículo:</span>
                    <span className="text-white">{rideData.vehicle?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Pagamento:</span>
                    <span className="text-white">{rideData.payment?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Valor:</span>
                    <span className="text-white font-bold">R$ {rideData.price?.toFixed(2)}</span>
                  </div>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default RidingPage
