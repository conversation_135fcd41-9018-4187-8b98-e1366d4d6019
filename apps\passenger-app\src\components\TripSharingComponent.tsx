import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Share2, Users, Copy, Check, MessageCircle, Phone, MapPin, Clock, X } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContextSimple'

interface TripSharingProps {
  rideId: string
  origin: string
  destination: string
  driverInfo: {
    name: string
    phone: string
    vehicle: string
    plate: string
    photo?: string
  }
  estimatedArrival: string
}

interface SharedContact {
  id: string
  name: string
  phone: string
  email?: string
  relationship: string
  is_emergency_contact: boolean
}

export const TripSharingComponent: React.FC<TripSharingProps> = ({
  rideId,
  origin,
  destination,
  driverInfo,
  estimatedArrival
}) => {
  const { user } = useAuth()
  const [isOpen, setIsOpen] = useState(false)
  const [shareLink, setShareLink] = useState('')
  const [copied, setCopied] = useState(false)
  const [emergencyContacts, setEmergencyContacts] = useState<SharedContact[]>([])
  const [selectedContacts, setSelectedContacts] = useState<string[]>([])
  const [isSharing, setIsSharing] = useState(false)

  useEffect(() => {
    if (isOpen) {
      generateShareLink()
      loadEmergencyContacts()
    }
  }, [isOpen, rideId])

  const generateShareLink = async () => {
    try {
      // Criar link de compartilhamento público
      const shareData = {
        ride_id: rideId,
        user_id: user?.id,
        origin,
        destination,
        driver_info: driverInfo,
        estimated_arrival: estimatedArrival,
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 horas
      }

      const { data: shareRecord, error } = await supabase
        .from('trip_shares')
        .insert(shareData)
        .select()
        .single()

      if (error) {
        console.error('Erro ao criar link de compartilhamento:', error)
        return
      }

      const link = `${window.location.origin}/shared-trip/${shareRecord.id}`
      setShareLink(link)
    } catch (error) {
      console.error('Erro ao gerar link:', error)
    }
  }

  const loadEmergencyContacts = async () => {
    try {
      const { data: contacts, error } = await supabase
        .from('emergency_contacts')
        .select('*')
        .eq('user_id', user?.id)
        .eq('is_active', true)

      if (error) {
        console.error('Erro ao carregar contatos:', error)
        return
      }

      setEmergencyContacts(contacts || [])
    } catch (error) {
      console.error('Erro ao carregar contatos:', error)
    }
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Erro ao copiar:', error)
    }
  }

  const shareWithContacts = async () => {
    if (selectedContacts.length === 0) return

    setIsSharing(true)
    try {
      // Criar notificações para contatos selecionados
      const notifications = selectedContacts.map(contactId => {
        const contact = emergencyContacts.find(c => c.id === contactId)
        return {
          user_id: contactId,
          title: 'Viagem Compartilhada',
          message: `${user?.email} está compartilhando uma viagem com você`,
          type: 'trip_share',
          metadata: {
            ride_id: rideId,
            share_link: shareLink,
            contact_info: contact,
            trip_info: {
              origin,
              destination,
              driver: driverInfo,
              estimated_arrival: estimatedArrival
            }
          }
        }
      })

      const { error } = await supabase
        .from('notifications')
        .insert(notifications)

      if (error) {
        console.error('Erro ao enviar notificações:', error)
        return
      }

      // Enviar SMS se disponível
      await sendSMSNotifications()

      console.log('✅ Viagem compartilhada com', selectedContacts.length, 'contatos')
    } catch (error) {
      console.error('Erro ao compartilhar:', error)
    } finally {
      setIsSharing(false)
    }
  }

  const sendSMSNotifications = async () => {
    try {
      const selectedContactsData = emergencyContacts.filter(c =>
        selectedContacts.includes(c.id) && c.phone
      )

      for (const contact of selectedContactsData) {
        const message = `🚗 ${user?.email} está em uma viagem e compartilhou com você.
Origem: ${origin}
Destino: ${destination}
Motorista: ${driverInfo.name} - ${driverInfo.vehicle}
Chegada prevista: ${estimatedArrival}
Acompanhe em: ${shareLink}`

        // TODO: Integrar com serviço de SMS (Twilio, etc.)
        console.log('SMS para', contact.phone, ':', message)
      }
    } catch (error) {
      console.error('Erro ao enviar SMS:', error)
    }
  }

  const toggleContactSelection = (contactId: string) => {
    setSelectedContacts(prev =>
      prev.includes(contactId)
        ? prev.filter(id => id !== contactId)
        : [...prev, contactId]
    )
  }

  const shareViaWhatsApp = () => {
    const message = `🚗 Estou em uma viagem e quero compartilhar com você:

📍 Origem: ${origin}
🎯 Destino: ${destination}
👨‍💼 Motorista: ${driverInfo.name}
🚙 Veículo: ${driverInfo.vehicle} - ${driverInfo.plate}
⏰ Chegada prevista: ${estimatedArrival}

Acompanhe minha viagem: ${shareLink}`

    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  return (
    <>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsOpen(true)}
        className="bg-blue-500 hover:bg-blue-600 text-white rounded-full p-3 shadow-lg"
      >
        <Share2 className="w-5 h-5" />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/50 flex items-end sm:items-center justify-center p-4"
            onClick={() => setIsOpen(false)}
          >
            <motion.div
              initial={{ y: 300, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: 300, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-t-3xl sm:rounded-2xl w-full max-w-md max-h-[80vh] overflow-y-auto"
            >
              {/* Header */}
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-gray-900">Compartilhar Viagem</h3>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-2 hover:bg-gray-100 rounded-full"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                <p className="text-gray-600 mt-1">
                  Permita que seus contatos acompanhem sua viagem em tempo real
                </p>
              </div>

              {/* Trip Info */}
              <div className="p-6 border-b border-gray-100">
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm text-gray-500">Origem</p>
                      <p className="font-medium">{origin}</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm text-gray-500">Destino</p>
                      <p className="font-medium">{destination}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 pt-2 border-t border-gray-100">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      Chegada prevista: {estimatedArrival}
                    </span>
                  </div>
                </div>
              </div>

              {/* Share Link */}
              <div className="p-6 border-b border-gray-100">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Link de Compartilhamento
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={shareLink}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm"
                  />
                  <button
                    onClick={copyToClipboard}
                    className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg flex items-center space-x-1"
                  >
                    {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              {/* Emergency Contacts */}
              {emergencyContacts.length > 0 && (
                <div className="p-6 border-b border-gray-100">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Contatos de Emergência
                  </label>
                  <div className="space-y-2">
                    {emergencyContacts.map((contact) => (
                      <label
                        key={contact.id}
                        className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={selectedContacts.includes(contact.id)}
                          onChange={() => toggleContactSelection(contact.id)}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{contact.name}</p>
                          <p className="text-sm text-gray-500">{contact.relationship}</p>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="p-6 space-y-3">
                <button
                  onClick={shareViaWhatsApp}
                  className="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-xl flex items-center justify-center space-x-2"
                >
                  <MessageCircle className="w-5 h-5" />
                  <span>Compartilhar via WhatsApp</span>
                </button>

                {selectedContacts.length > 0 && (
                  <button
                    onClick={shareWithContacts}
                    disabled={isSharing}
                    className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white font-semibold py-3 px-4 rounded-xl flex items-center justify-center space-x-2"
                  >
                    <Users className="w-5 h-5" />
                    <span>
                      {isSharing ? 'Compartilhando...' : `Compartilhar com ${selectedContacts.length} contatos`}
                    </span>
                  </button>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

export default TripSharingComponent
