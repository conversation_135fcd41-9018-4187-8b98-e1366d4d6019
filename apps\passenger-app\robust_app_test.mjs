// =====================================================
// TESTE ROBUSTO DA APLICAÇÃO
// Verifica conectividade e testa funcionalidades principais
// =====================================================

import puppeteer from 'puppeteer'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'
const MAPBOX_TOKEN = 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'

class RobustAppTester {
  constructor() {
    this.supabase = createClient(supabaseUrl, supabaseAnonKey)
    this.browser = null
    this.page = null
    this.testResults = {
      supabase: false,
      mapbox: false,
      server: false,
      login: false,
      navigation: false,
      errors: [],
      logs: []
    }
  }

  async testSupabaseConnection() {
    console.log('\n🧪 TESTE 1: CONEXÃO SUPABASE')
    console.log('-' .repeat(40))
    
    try {
      const { data, error } = await this.supabase
        .from('profiles')
        .select('count')
        .limit(1)
      
      if (!error) {
        console.log('✅ Supabase conectado e funcionando')
        this.testResults.supabase = true
        return true
      } else {
        console.log('❌ Erro no Supabase:', error.message)
        this.testResults.errors.push(`Supabase: ${error.message}`)
        return false
      }
    } catch (error) {
      console.log('❌ Erro na conexão Supabase:', error.message)
      this.testResults.errors.push(`Supabase connection: ${error.message}`)
      return false
    }
  }

  async testMapboxToken() {
    console.log('\n🧪 TESTE 2: TOKEN MAPBOX')
    console.log('-' .repeat(40))
    
    try {
      const response = await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/São Paulo.json?access_token=${MAPBOX_TOKEN}`)
      
      if (response.ok) {
        const data = await response.json()
        if (data.features && data.features.length > 0) {
          console.log('✅ Token Mapbox válido e funcionando')
          console.log(`📍 Encontrados ${data.features.length} resultados para São Paulo`)
          this.testResults.mapbox = true
          return true
        } else {
          console.log('⚠️ Token válido mas sem resultados')
          return false
        }
      } else {
        console.log('❌ Token Mapbox inválido:', response.status)
        this.testResults.errors.push(`Mapbox token: HTTP ${response.status}`)
        return false
      }
    } catch (error) {
      console.log('❌ Erro ao testar Mapbox:', error.message)
      this.testResults.errors.push(`Mapbox: ${error.message}`)
      return false
    }
  }

  async testServerConnectivity() {
    console.log('\n🧪 TESTE 3: CONECTIVIDADE DO SERVIDOR')
    console.log('-' .repeat(40))
    
    const urls = [
      'http://localhost:3000',
      'http://127.0.0.1:3000'
    ]
    
    for (const url of urls) {
      try {
        console.log(`🔍 Testando ${url}...`)
        const response = await fetch(url, { 
          method: 'HEAD',
          signal: AbortSignal.timeout(5000)
        })
        
        if (response.ok) {
          console.log(`✅ Servidor respondendo em ${url}`)
          this.testResults.server = true
          return url
        } else {
          console.log(`⚠️ ${url} retornou ${response.status}`)
        }
      } catch (error) {
        console.log(`❌ ${url} não acessível: ${error.message}`)
      }
    }
    
    console.log('❌ Nenhum servidor encontrado')
    this.testResults.errors.push('Server not accessible')
    return null
  }

  async testLoginWithBrowser(serverUrl) {
    console.log('\n🧪 TESTE 4: LOGIN NO NAVEGADOR')
    console.log('-' .repeat(40))
    
    try {
      console.log('🚀 Iniciando navegador...')
      this.browser = await puppeteer.launch({
        headless: false,
        devtools: false,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
        timeout: 10000
      })

      this.page = await this.browser.newPage()
      await this.page.setViewport({ width: 1280, height: 720 })

      // Monitorar console
      this.page.on('console', (msg) => {
        const logEntry = `[${msg.type()}] ${msg.text()}`
        this.testResults.logs.push(logEntry)
        
        if (msg.type() === 'error') {
          console.log(`❌ Console: ${msg.text()}`)
          this.testResults.errors.push(`Console: ${msg.text()}`)
        } else if (msg.text().includes('✅') || msg.text().includes('SUCCESS')) {
          console.log(`🟢 Console: ${msg.text()}`)
        }
      })

      console.log(`🌐 Navegando para ${serverUrl}/login...`)
      
      // Tentar navegar com timeout menor
      await this.page.goto(`${serverUrl}/login`, { 
        waitUntil: 'domcontentloaded',
        timeout: 15000 
      })
      
      console.log('✅ Página de login carregada')
      await new Promise(resolve => setTimeout(resolve, 3000))

      // Verificar se campos existem
      const emailField = await this.page.$('input[type="email"]')
      const passwordField = await this.page.$('input[type="password"]')

      if (emailField && passwordField) {
        console.log('📝 Campos de login encontrados')
        
        // Preencher credenciais
        await emailField.type('<EMAIL>', { delay: 50 })
        await passwordField.type('test123456', { delay: 50 })
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const submitButton = await this.page.$('button[type="submit"]')
        if (submitButton) {
          console.log('🖱️ Fazendo login...')
          await submitButton.click()
          
          // Aguardar resposta
          await new Promise(resolve => setTimeout(resolve, 5000))
          
          const currentUrl = this.page.url()
          if (!currentUrl.includes('/login')) {
            console.log('✅ Login realizado com sucesso!')
            console.log(`🌐 Redirecionado para: ${currentUrl}`)
            this.testResults.login = true
            return true
          } else {
            console.log('❌ Login falhou - ainda na página de login')
            return false
          }
        } else {
          console.log('❌ Botão de submit não encontrado')
          return false
        }
      } else {
        console.log('❌ Campos de login não encontrados')
        return false
      }
      
    } catch (error) {
      console.log('❌ Erro no teste de login:', error.message)
      this.testResults.errors.push(`Browser login: ${error.message}`)
      return false
    }
  }

  async testNavigation(serverUrl) {
    console.log('\n🧪 TESTE 5: NAVEGAÇÃO ENTRE PÁGINAS')
    console.log('-' .repeat(40))
    
    if (!this.page) {
      console.log('❌ Navegador não disponível')
      return false
    }
    
    const pages = [
      { url: '/dashboard', name: 'Dashboard' },
      { url: '/ride-request/map', name: 'Seleção de Mapa' },
      { url: '/ride-request/details', name: 'Detalhes da Viagem' }
    ]
    
    let successCount = 0
    
    for (const pageInfo of pages) {
      try {
        console.log(`🌐 Testando ${pageInfo.name}...`)
        
        await this.page.goto(`${serverUrl}${pageInfo.url}`, { 
          waitUntil: 'domcontentloaded',
          timeout: 10000 
        })
        
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        const title = await this.page.title()
        console.log(`✅ ${pageInfo.name} carregada: "${title}"`)
        successCount++
        
      } catch (error) {
        console.log(`❌ Erro em ${pageInfo.name}: ${error.message}`)
        this.testResults.errors.push(`Navigation ${pageInfo.name}: ${error.message}`)
      }
    }
    
    const success = successCount === pages.length
    this.testResults.navigation = success
    
    console.log(`📊 Navegação: ${successCount}/${pages.length} páginas OK`)
    return success
  }

  async generateFinalReport() {
    console.log('\n' + '=' .repeat(60))
    console.log('📊 RELATÓRIO FINAL DO TESTE ROBUSTO')
    console.log('=' .repeat(60))
    
    const tests = [
      { name: 'Supabase', result: this.testResults.supabase },
      { name: 'Mapbox', result: this.testResults.mapbox },
      { name: 'Servidor', result: this.testResults.server },
      { name: 'Login', result: this.testResults.login },
      { name: 'Navegação', result: this.testResults.navigation }
    ]
    
    let passedTests = 0
    
    console.log('\n🧪 RESULTADOS DOS TESTES:')
    tests.forEach((test, i) => {
      const status = test.result ? '✅' : '❌'
      console.log(`  ${i + 1}. ${test.name}: ${status}`)
      if (test.result) passedTests++
    })
    
    const successRate = (passedTests / tests.length * 100).toFixed(1)
    console.log(`\n📊 Taxa de sucesso: ${passedTests}/${tests.length} (${successRate}%)`)
    
    if (this.testResults.errors.length > 0) {
      console.log('\n❌ ERROS ENCONTRADOS:')
      this.testResults.errors.forEach((error, i) => {
        console.log(`  ${i + 1}. ${error}`)
      })
    }
    
    if (this.testResults.logs.length > 0) {
      const importantLogs = this.testResults.logs.filter(log => 
        log.includes('✅') || log.includes('❌') || log.includes('ERROR')
      )
      
      if (importantLogs.length > 0) {
        console.log('\n📝 LOGS IMPORTANTES:')
        importantLogs.slice(0, 5).forEach((log, i) => {
          console.log(`  ${i + 1}. ${log}`)
        })
      }
    }
    
    // Classificação final
    let status = 'EXCELENTE'
    if (passedTests < 3) {
      status = 'CRÍTICO'
    } else if (passedTests < 4) {
      status = 'PROBLEMÁTICO'
    } else if (passedTests < 5) {
      status = 'BOM'
    }
    
    console.log(`\n🏥 STATUS GERAL: ${status}`)
    
    if (passedTests === tests.length) {
      console.log('\n🎉 TODOS OS TESTES PASSARAM!')
      console.log('✅ Aplicação totalmente funcional!')
    } else {
      console.log(`\n⚠️ ${tests.length - passedTests} teste(s) falharam`)
      console.log('💡 Verifique os erros acima para correção')
    }
    
    return {
      passedTests,
      totalTests: tests.length,
      successRate: parseFloat(successRate),
      status,
      errors: this.testResults.errors
    }
  }

  async cleanup() {
    if (this.browser) {
      console.log('\n🔒 Fechando navegador...')
      await this.browser.close()
    }
  }

  async runAllTests() {
    console.log('🎯 INICIANDO TESTE ROBUSTO DA APLICAÇÃO')
    console.log('=' .repeat(60))
    
    try {
      // Testes de infraestrutura
      await this.testSupabaseConnection()
      await this.testMapboxToken()
      const serverUrl = await this.testServerConnectivity()
      
      // Testes de interface (só se servidor estiver disponível)
      if (serverUrl) {
        await this.testLoginWithBrowser(serverUrl)
        await this.testNavigation(serverUrl)
      } else {
        console.log('\n⚠️ Pulando testes de interface (servidor indisponível)')
      }
      
      // Relatório final
      const report = await this.generateFinalReport()
      
      return report
      
    } finally {
      await this.cleanup()
    }
  }
}

// Executar teste
const tester = new RobustAppTester()

tester.runAllTests()
  .then((report) => {
    console.log('\n🏁 TESTE ROBUSTO CONCLUÍDO!')
    process.exit(report.passedTests === report.totalTests ? 0 : 1)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
