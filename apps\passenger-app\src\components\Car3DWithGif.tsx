import React, { useRef, useState, useEffect } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Environment } from '@react-three/drei'
import * as THREE from 'three'

// Componente que combina o carro 3D com o GIF de fundo
const Car3DWithGif: React.FC<{ className?: string }> = ({ className = "" }) => {
  const [rotation, setRotation] = useState<[number, number, number]>([0, 0, 0])
  const [isDragging, setIsDragging] = useState(false)
  const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 })
  const [showGif, setShowGif] = useState(false)

  // Componente do carro 3D
  const ModernCar: React.FC<{ manualRotation: [number, number, number] }> = ({ manualRotation }) => {
    const carRef = useRef<THREE.Group>(null)

    useFrame((state) => {
      if (carRef.current) {
        const scrollY = window.scrollY || 0
        const maxScroll = Math.max(document.body.scrollHeight - window.innerHeight, 1)
        const scrollProgress = Math.min(scrollY / maxScroll, 1)

        carRef.current.rotation.x = manualRotation[0] + Math.sin(scrollProgress * Math.PI) * 0.1
        carRef.current.rotation.y = manualRotation[1] + scrollProgress * Math.PI * 2 + state.clock.elapsedTime * 0.02
        carRef.current.rotation.z = manualRotation[2]
        
        // Movimento sutil para cima e para baixo
        carRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
      }
    })

    return (
      <group ref={carRef} position={[0, 0, 0]} scale={[1.2, 1.2, 1.2]}>
        {/* Corpo principal do carro */}
        <mesh position={[0, 0.5, 0]} castShadow receiveShadow>
          <boxGeometry args={[4.5, 1.2, 2.2]} />
          <meshStandardMaterial
            color="#0a0a0a"
            metalness={0.95}
            roughness={0.05}
            envMapIntensity={1.5}
          />
        </mesh>

        {/* Teto do carro */}
        <mesh position={[0.2, 1.4, 0]} castShadow>
          <boxGeometry args={[3.2, 0.9, 2]} />
          <meshStandardMaterial
            color="#1a1a1a"
            metalness={0.9}
            roughness={0.1}
          />
        </mesh>

        {/* Para-brisa frontal */}
        <mesh position={[1.5, 1.4, 0]} rotation={[0, 0, -0.3]} castShadow>
          <boxGeometry args={[0.9, 0.8, 1.8]} />
          <meshStandardMaterial
            color="#87CEEB"
            transparent
            opacity={0.2}
            metalness={0.1}
            roughness={0.1}
          />
        </mesh>

        {/* Rodas */}
        {[
          [1.6, -0.3, 1.3],
          [1.6, -0.3, -1.3],
          [-1.6, -0.3, 1.3],
          [-1.6, -0.3, -1.3]
        ].map((position, index) => (
          <group key={index} position={position as [number, number, number]}>
            <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
              <cylinderGeometry args={[0.5, 0.5, 0.4]} />
              <meshStandardMaterial color="#1a1a1a" metalness={0.1} roughness={0.9} />
            </mesh>
            <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0.15]} castShadow>
              <cylinderGeometry args={[0.35, 0.35, 0.1]} />
              <meshStandardMaterial color="#c0c0c0" metalness={0.9} roughness={0.1} />
            </mesh>
          </group>
        ))}

        {/* Faróis */}
        <mesh position={[2.3, 0.4, 0.8]} castShadow>
          <sphereGeometry args={[0.2]} />
          <meshStandardMaterial
            color="#ffffff"
            emissive="#ffffff"
            emissiveIntensity={0.5}
          />
        </mesh>
        <mesh position={[2.3, 0.4, -0.8]} castShadow>
          <sphereGeometry args={[0.2]} />
          <meshStandardMaterial
            color="#ffffff"
            emissive="#ffffff"
            emissiveIntensity={0.5}
          />
        </mesh>
      </group>
    )
  }

  // Handlers para interação
  const handleMouseDown = (event: React.MouseEvent) => {
    event.preventDefault()
    setIsDragging(true)
    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isDragging) return
    event.preventDefault()

    const deltaX = event.clientX - lastPosition.x
    const deltaY = event.clientY - lastPosition.y

    setRotation(prev => [
      prev[0] + deltaY * 0.005,
      prev[1] + deltaX * 0.005,
      prev[2]
    ])

    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Event listeners globais
  useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false)
    const handleGlobalMouseMove = (event: MouseEvent) => {
      if (!isDragging) return

      const deltaX = event.clientX - lastPosition.x
      const deltaY = event.clientY - lastPosition.y

      setRotation(prev => [
        prev[0] + deltaY * 0.005,
        prev[1] + deltaX * 0.005,
        prev[2]
      ])

      setLastPosition({ x: event.clientX, y: event.clientY })
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }
  }, [isDragging, lastPosition])

  // Alternar entre 3D e GIF
  useEffect(() => {
    const interval = setInterval(() => {
      setShowGif(prev => !prev)
    }, 10000) // Alternar a cada 10 segundos

    return () => clearInterval(interval)
  }, [])

  return (
    <div
      className={`w-full h-full ${className} select-none relative`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
        pointerEvents: 'auto',
        cursor: isDragging ? 'grabbing' : 'grab',
        touchAction: 'none'
      }}
    >
      {/* GIF de fundo */}
      {showGif && (
        <div 
          className="absolute inset-0 flex items-center justify-center"
          style={{
            background: 'linear-gradient(135deg, rgba(0,0,0,0.8), rgba(0,0,0,0.6))',
            zIndex: 2
          }}
        >
          <img
            src="/gif/NEW.gif"
            alt="Carro 3D Animado"
            className="max-w-full max-h-full object-contain opacity-80"
            style={{
              filter: 'brightness(1.2) contrast(1.1)',
              mixBlendMode: 'screen'
            }}
          />
        </div>
      )}

      {/* Canvas 3D */}
      <div
        className={`absolute inset-0 transition-opacity duration-1000 ${showGif ? 'opacity-30' : 'opacity-100'}`}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
      >
        <Canvas
          shadows
          camera={{ position: [6, 4, 6], fov: 50 }}
          style={{
            background: 'transparent',
            width: '100%',
            height: '100%'
          }}
          gl={{
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
          }}
          dpr={[1, 2]}
        >
          {/* Iluminação */}
          <ambientLight intensity={0.4} />
          <directionalLight
            position={[10, 10, 5]}
            intensity={1.2}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
          />
          <pointLight position={[-5, 5, -5]} intensity={0.4} />
          <pointLight position={[5, 5, 5]} intensity={0.4} />

          {/* Ambiente */}
          <Environment preset="city" />

          {/* Carro */}
          <ModernCar manualRotation={rotation} />

          {/* Chão reflexivo */}
          <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1, 0]} receiveShadow>
            <planeGeometry args={[30, 30]} />
            <meshStandardMaterial
              color="#1a1a1a"
              transparent
              opacity={0.2}
              metalness={0.8}
              roughness={0.2}
            />
          </mesh>
        </Canvas>
      </div>

      {/* Indicador de modo */}
      <div className="absolute bottom-4 right-4 text-white/50 text-xs bg-black/30 px-2 py-1 rounded">
        {showGif ? 'Modo GIF' : 'Modo 3D'}
      </div>
    </div>
  )
}

export default Car3DWithGif
