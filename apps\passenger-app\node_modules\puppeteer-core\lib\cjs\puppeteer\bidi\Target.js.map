{"version": 3, "file": "Target.js", "sourceRoot": "", "sources": ["../../../../src/bidi/Target.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,gDAAoD;AACpD,mDAAyD;AAMzD,uCAAmC;AAGnC;;GAEG;AACH,MAAa,iBAAkB,SAAQ,kBAAM;IAC3C,QAAQ,CAAc;IAEtB,YAAY,OAAoB;QAC9B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAEQ,MAAM;QACb,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IACQ,GAAG;QACV,OAAO,EAAE,CAAC;IACZ,CAAC;IACQ,gBAAgB;QACvB,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IACQ,IAAI;QACX,OAAO,sBAAU,CAAC,OAAO,CAAC;IAC5B,CAAC;IACQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IACQ,cAAc;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;IAC/C,CAAC;IACQ,MAAM;QACb,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;CACF;AA7BD,8CA6BC;AAED;;GAEG;AACH,MAAa,cAAe,SAAQ,kBAAM;IACxC,KAAK,CAAW;IAEhB,YAAY,IAAc;QACxB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;IAEQ,KAAK,CAAC,IAAI;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IACQ,KAAK,CAAC,MAAM;QACnB,OAAO,kBAAQ,CAAC,IAAI,CAClB,IAAI,CAAC,cAAc,EAAE,EACrB,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,eAAe,CACvC,CAAC;IACJ,CAAC;IACQ,GAAG;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAC1B,CAAC;IACQ,gBAAgB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;IACvC,CAAC;IACQ,IAAI;QACX,OAAO,sBAAU,CAAC,IAAI,CAAC;IACzB,CAAC;IACQ,OAAO;QACd,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;IACQ,cAAc;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;IACrC,CAAC;IACQ,MAAM;QACb,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;CACF;AAnCD,wCAmCC;AAED;;GAEG;AACH,MAAa,eAAgB,SAAQ,kBAAM;IACzC,MAAM,CAAY;IAClB,KAAK,CAAuB;IAE5B,YAAY,KAAgB;QAC1B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAEQ,KAAK,CAAC,IAAI;QACjB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,GAAG,kBAAQ,CAAC,IAAI,CACxB,IAAI,CAAC,cAAc,EAAE,EACrB,IAAI,CAAC,MAAM,CAAC,eAAe,CAC5B,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IACQ,KAAK,CAAC,MAAM;QACnB,OAAO,kBAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IAC3E,CAAC;IACQ,GAAG;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IAC3B,CAAC;IACQ,gBAAgB;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;IACxC,CAAC;IACQ,IAAI;QACX,OAAO,sBAAU,CAAC,IAAI,CAAC;IACzB,CAAC;IACQ,OAAO;QACd,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;IACQ,cAAc;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,cAAc,EAAE,CAAC;IAC7C,CAAC;IACQ,MAAM;QACb,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;CACF;AAvCD,0CAuCC;AAED;;GAEG;AACH,MAAa,gBAAiB,SAAQ,kBAAM;IAC1C,OAAO,CAAgB;IAEvB,YAAY,MAAqB;QAC/B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAEQ,KAAK,CAAC,IAAI;QACjB,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IACQ,KAAK,CAAC,MAAM;QACnB,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IACQ,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IAC5B,CAAC;IACQ,gBAAgB;QACvB,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IACQ,IAAI;QACX,OAAO,sBAAU,CAAC,KAAK,CAAC;IAC1B,CAAC;IACQ,OAAO;QACd,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;IACQ,cAAc;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,cAAc,EAAE,CAAC;IACpD,CAAC;IACQ,MAAM;QACb,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;CACF;AAhCD,4CAgCC"}