import { useEffect, useState } from 'react'

interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  memoryUsage: number
  connectionType: string
  isSlowConnection: boolean
}

/**
 * Hook para monitorar performance da aplicação
 */
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    connectionType: 'unknown',
    isSlowConnection: false
  })

  useEffect(() => {
    const measurePerformance = () => {
      try {
        // Tempo de carregamento
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        const loadTime = navigation.loadEventEnd - navigation.fetchStart

        // Tempo de renderização
        const renderTime = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart

        // Uso de memória (se disponível)
        let memoryUsage = 0
        if ('memory' in performance) {
          const memory = (performance as any).memory
          memoryUsage = memory.usedJSHeapSize / 1024 / 1024 // MB
        }

        // Tipo de conexão
        let connectionType = 'unknown'
        let isSlowConnection = false
        
        if ('connection' in navigator) {
          const connection = (navigator as any).connection
          connectionType = connection.effectiveType || 'unknown'
          isSlowConnection = ['slow-2g', '2g'].includes(connectionType)
        }

        setMetrics({
          loadTime: Math.round(loadTime),
          renderTime: Math.round(renderTime),
          memoryUsage: Math.round(memoryUsage * 100) / 100,
          connectionType,
          isSlowConnection
        })

        // Log das métricas para debug
        console.log('📊 Performance Metrics:', {
          loadTime: `${Math.round(loadTime)}ms`,
          renderTime: `${Math.round(renderTime)}ms`,
          memoryUsage: `${Math.round(memoryUsage * 100) / 100}MB`,
          connectionType,
          isSlowConnection
        })

      } catch (error) {
        console.warn('Erro ao medir performance:', error)
      }
    }

    // Aguardar o carregamento completo
    if (document.readyState === 'complete') {
      measurePerformance()
    } else {
      window.addEventListener('load', measurePerformance)
      return () => window.removeEventListener('load', measurePerformance)
    }
  }, [])

  return metrics
}

/**
 * Hook para monitorar performance de componentes específicos
 */
export const useComponentPerformance = (componentName: string) => {
  const [renderCount, setRenderCount] = useState(0)
  const [lastRenderTime, setLastRenderTime] = useState(0)

  useEffect(() => {
    const startTime = performance.now()
    setRenderCount(prev => prev + 1)

    // Medir tempo de renderização
    const measureRender = () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime
      setLastRenderTime(renderTime)

      // Log apenas se for lento (> 16ms para 60fps)
      if (renderTime > 16) {
        console.warn(`⚠️ Renderização lenta em ${componentName}: ${renderTime.toFixed(2)}ms`)
      }
    }

    // Usar requestAnimationFrame para medir após a renderização
    const rafId = requestAnimationFrame(measureRender)
    return () => cancelAnimationFrame(rafId)
  })

  return {
    renderCount,
    lastRenderTime: Math.round(lastRenderTime * 100) / 100,
    isSlowRender: lastRenderTime > 16
  }
}

/**
 * Hook para detectar dispositivos com performance limitada
 */
export const useDeviceCapabilities = () => {
  const [capabilities, setCapabilities] = useState({
    isLowEnd: false,
    cores: navigator.hardwareConcurrency || 1,
    memory: 0,
    connectionSpeed: 'unknown'
  })

  useEffect(() => {
    const detectCapabilities = () => {
      const cores = navigator.hardwareConcurrency || 1
      let memory = 0
      let isLowEnd = false

      // Detectar memória (se disponível)
      if ('deviceMemory' in navigator) {
        memory = (navigator as any).deviceMemory
        isLowEnd = memory <= 2 // Dispositivos com 2GB ou menos
      }

      // Detectar por outros indicadores
      if (cores <= 2) {
        isLowEnd = true
      }

      // Detectar conexão
      let connectionSpeed = 'unknown'
      if ('connection' in navigator) {
        const connection = (navigator as any).connection
        connectionSpeed = connection.effectiveType || 'unknown'
        
        if (['slow-2g', '2g'].includes(connectionSpeed)) {
          isLowEnd = true
        }
      }

      setCapabilities({
        isLowEnd,
        cores,
        memory,
        connectionSpeed
      })

      // Aplicar otimizações para dispositivos limitados
      if (isLowEnd) {
        console.log('📱 Dispositivo com performance limitada detectado - aplicando otimizações')
        document.documentElement.classList.add('low-end-device')
      }
    }

    detectCapabilities()
  }, [])

  return capabilities
}

export default usePerformanceMonitor
