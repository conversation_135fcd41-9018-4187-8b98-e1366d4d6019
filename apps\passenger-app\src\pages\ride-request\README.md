# 🚗 Fluxo de Solicitação de Corrida - 5 Páginas Separadas

Este diretório contém as 5 páginas do fluxo completo de solicitação de corrida do MobiDrive, separadas em arquivos individuais para facilitar a manutenção e desenvolvimento.

## 📁 Estrutura das Páginas

### 1️⃣ **MapSelectionPage.tsx** - Seleção de Destino
**Rota:** `/ride-request/map`

**Funcionalidades:**
- ✅ Mapa Mapbox full-screen com tema escuro
- ✅ Caixa de pesquisa integrada no mapa
- ✅ Pesquisa por texto e voz
- ✅ Resultados ordenados por distância
- ✅ Marcador arrastável para seleção precisa
- ✅ Projeção de rota em tempo real
- ✅ Geocoding reverso para endereços

**Navegação:**
- **Origem:** Dashboard (`/dashboard`)
- **Destino:** Detalhes da Corrida (`/ride-request/details`)

---

### 2️⃣ **TripDetailsPage.tsx** - Detalhes da Corrida
**Rota:** `/ride-request/details`

**Funcionalidades:**
- ✅ Resumo da rota (origem → destino)
- ✅ Seleção de veículo (Moto, Carro, SUV)
- ✅ Formas de pagamento (Cartão, Dinheiro, PIX)
- ✅ Campo de observações opcional
- ✅ Cálculo de preço dinâmico
- ✅ Validação de seleções obrigatórias

**Navegação:**
- **Origem:** Seleção de Destino (`/ride-request/map`)
- **Destino:** Aguardando Motorista (`/ride-request/waiting`)

---

### 3️⃣ **WaitingDriverPage.tsx** - Aguardando Motorista
**Rota:** `/ride-request/waiting`

**Funcionalidades:**
- ✅ Animação de busca por motorista
- ✅ Informações do motorista encontrado
- ✅ Tempo estimado de chegada (ETA)
- ✅ Botões de contato (ligar/mensagem)
- ✅ Mapa com localização do motorista
- ✅ Botão de emergência sempre acessível
- ✅ Resumo da corrida

**Navegação:**
- **Origem:** Detalhes da Corrida (`/ride-request/details`)
- **Destino:** Corrida em Andamento (`/ride-request/riding`)

---

### 4️⃣ **RidingPage.tsx** - Corrida em Andamento
**Rota:** `/ride-request/riding`

**Funcionalidades:**
- ✅ Informações do motorista
- ✅ Mapa com rota em tempo real
- ✅ Progresso da viagem (distância, tempo)
- ✅ Botões de ação (emergência, compartilhar)
- ✅ Botão "Finalizar" quando próximo ao destino
- ✅ Resumo da corrida

**Navegação:**
- **Origem:** Aguardando Motorista (`/ride-request/waiting`)
- **Destino:** Avaliação (`/ride-request/rating`)

---

### 5️⃣ **RatingPage.tsx** - Avaliação do Motorista
**Rota:** `/ride-request/rating`

**Funcionalidades:**
- ✅ Confirmação de corrida finalizada
- ✅ Resumo completo da viagem
- ✅ Sistema de avaliação por estrelas (1-5)
- ✅ Campo de comentário opcional
- ✅ Informações do motorista
- ✅ Opção de pular avaliação

**Navegação:**
- **Origem:** Corrida em Andamento (`/ride-request/riding`)
- **Destino:** Dashboard (`/dashboard`)

---

## 🔄 Fluxo Completo de Navegação

```
Dashboard
    ↓ (Solicitar Corrida)
1. MapSelectionPage (/ride-request/map)
    ↓ (Confirmar destino)
2. TripDetailsPage (/ride-request/details)
    ↓ (Solicitar motorista)
3. WaitingDriverPage (/ride-request/waiting)
    ↓ (Motorista chegou)
4. RidingPage (/ride-request/riding)
    ↓ (Finalizar corrida)
5. RatingPage (/ride-request/rating)
    ↓ (Finalizar avaliação)
Dashboard
```

---

## 💾 Gerenciamento de Estado

### **SessionStorage Keys:**
- `rideDestination` - Destino selecionado no mapa
- `rideOrigin` - Origem da corrida (localização atual)
- `tripData` - Dados da corrida (veículo, pagamento, etc.)
- `rideData` - Dados da corrida em andamento
- `completedRide` - Dados da corrida finalizada

### **Limpeza Automática:**
- Todos os dados são limpos automaticamente ao finalizar a avaliação
- Redirecionamento para dashboard se dados estiverem ausentes

---

## 🎨 Design e UX

### **Características Visuais:**
- ✅ Tema escuro consistente
- ✅ Glass morphism com backdrop blur
- ✅ Gradientes azul-roxo do MobiDrive
- ✅ Animações Framer Motion
- ✅ Design mobile-first responsivo

### **Experiência do Usuário:**
- ✅ Navegação linear e intuitiva
- ✅ Validações em tempo real
- ✅ Feedback visual para todas as ações
- ✅ Estados de loading e erro
- ✅ Botão de emergência sempre acessível

---

## 🔧 Tecnologias Utilizadas

### **Core:**
- React 18 + TypeScript
- React Router v6
- Framer Motion (animações)

### **Mapas e Localização:**
- Mapbox GL JS
- Mapbox Geocoding API
- Mapbox Directions API
- Geolocation API

### **Hooks Customizados:**
- `useMapboxSearch` - Pesquisa e geocoding
- `useNoZoom` - Prevenção de zoom
- `useAuth` - Autenticação

---

## 🚀 Vantagens da Separação

### **Manutenibilidade:**
- ✅ Código organizado em arquivos menores
- ✅ Responsabilidades bem definidas
- ✅ Fácil localização de bugs
- ✅ Desenvolvimento paralelo por equipe

### **Performance:**
- ✅ Code splitting automático
- ✅ Lazy loading de componentes
- ✅ Bundle size otimizado
- ✅ Carregamento sob demanda

### **Escalabilidade:**
- ✅ Fácil adição de novas funcionalidades
- ✅ Modificações isoladas por página
- ✅ Testes unitários específicos
- ✅ Deploy independente de páginas

---

## 📱 Compatibilidade

### **Dispositivos:**
- ✅ Mobile (iOS/Android)
- ✅ Desktop (Chrome, Firefox, Safari)
- ✅ Tablet (iPad, Android)

### **Funcionalidades:**
- ✅ Touch gestures
- ✅ Geolocalização
- ✅ Reconhecimento de voz
- ✅ Compartilhamento nativo
- ✅ Haptic feedback (mobile)

---

## 🔒 Segurança

### **Autenticação:**
- ✅ Rotas protegidas com `ProtectedRoute`
- ✅ Redirecionamento automático para login
- ✅ Validação de sessão em cada página

### **Dados:**
- ✅ Limpeza automática de dados sensíveis
- ✅ Validação de entrada em todos os campos
- ✅ Sanitização de dados do usuário

---

## 🎯 Próximos Passos

### **Melhorias Futuras:**
- [ ] Integração com API real de motoristas
- [ ] Push notifications para atualizações
- [ ] Histórico de corridas
- [ ] Favoritos de destinos
- [ ] Integração com pagamentos reais
- [ ] Suporte offline básico

### **Otimizações:**
- [ ] Preload de próximas páginas
- [ ] Cache de resultados de pesquisa
- [ ] Compressão de imagens
- [ ] Service Worker para cache

---

**Desenvolvido com ❤️ para o MobiDrive**
