import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Navigate } from 'react-router-dom'
import { Car, MapPin, Clock, User, Settings, LogOut, Coins, Crown, Power, DollarSign, Navigation, Phone } from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { GradientBackground } from '../components/GradientBackground'

import { useNoZoom } from '../hooks/useNoZoom'
import '../styles/no-zoom.css'

// 📱 DASHBOARD MOBILE DRIVER - DESIGN FIEL AO LOGIN + CONVERSÃO ANDROID NATIVA

export const DashboardMobile: React.FC = () => {
  const { user, signOut, loading } = useAuth()
  const [isOnline, setIsOnline] = useState(false)
  const [earnings, setEarnings] = useState(125.50)
  const [ridesCompleted, setRidesCompleted] = useState(8)

  // 🚫 DESABILITA ZOOM COMPLETAMENTE + CONFIGURAÇÕES ANDROID NATIVAS
  useNoZoom()

  // Configurações Android nativas (mantendo design original)
  useEffect(() => {
    // Meta viewport para Android nativo
    let metaViewport = document.querySelector('meta[name="viewport"]')
    if (!metaViewport) {
      metaViewport = document.createElement('meta')
      metaViewport.setAttribute('name', 'viewport')
      document.head.appendChild(metaViewport)
    }
    metaViewport.setAttribute('content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )

    // Configurações de overflow para Android
    document.body.style.margin = '0'
    document.body.style.padding = '0'
    document.body.style.overflow = 'hidden'
    document.documentElement.style.margin = '0'
    document.documentElement.style.padding = '0'
    document.documentElement.style.overflow = 'hidden'
  }, [])

  // Redirect se não logado
  if (!user && !loading) {
    return <Navigate to="/login" replace />
  }

  const handleLogout = async () => {
    await signOut()
  }

  const handleToggleOnline = () => {
    setIsOnline(!isOnline)
    if (!isOnline) {
      alert('Você está agora ONLINE e pode receber corridas!')
    } else {
      alert('Você está agora OFFLINE')
    }
  }

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">
      {/* Background Gradient Verde (TEMA DRIVER) */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-emerald-600 opacity-70"></div>

      {/* Overlay muito sutil para legibilidade */}
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Conteúdo Principal */}
      <div className="relative z-10 flex flex-col h-full min-h-screen">

        {/* Header com Logo (DESIGN FIEL AO LOGIN) */}
        <motion.div
          className="pt-8 pb-6 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10">
            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <Car className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-green-400">DRIVE</span>
              </h1>
              <p className="text-xs text-white/70">Painel do Motorista</p>
            </div>
          </div>
        </motion.div>

        {/* Dashboard Principal */}
        <div className="flex-1 px-4 pb-4">
          <div className="max-w-xs mx-auto space-y-4">

            {/* Toggle Online/Offline (DESIGN FIEL) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="text-center">
                <motion.button
                  onClick={handleToggleOnline}
                  className={`w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 transition-all ${
                    isOnline
                      ? 'bg-green-500 text-white shadow-lg'
                      : 'bg-white/20 text-white border-2 border-white/30'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Power className="w-10 h-10" />
                </motion.button>
                <h2 className="text-lg font-bold text-white mb-2">
                  {isOnline ? 'Você está Online' : 'Você está Offline'}
                </h2>
                <p className="text-white/70 text-sm">
                  {isOnline
                    ? 'Pronto para receber corridas'
                    : 'Toque para ficar online'
                  }
                </p>
              </div>
            </motion.div>

            {/* Estatísticas (DESIGN FIEL) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <h3 className="text-lg font-semibold text-white mb-4 text-center">📊 Estatísticas de Hoje</h3>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-green-400 mb-1">R$ {earnings.toFixed(2)}</div>
                  <div className="text-xs text-white/70">Ganhos</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-400 mb-1">{ridesCompleted}</div>
                  <div className="text-xs text-white/70">Corridas</div>
                </div>
              </div>
            </motion.div>

        {/* Ride Request Simulation */}
        {isOnline && (
          <div className="bg-white/95 backdrop-blur-lg rounded-2xl shadow-lg p-6 border border-white/30 animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-900">🚗 Nova Corrida!</h3>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-600">15s</div>
                <p className="text-xs text-gray-500">para responder</p>
              </div>
            </div>

            <div className="space-y-3 mb-4">
              <div className="flex items-start space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm text-gray-500">Origem</p>
                  <p className="font-medium">Rua das Flores, 123</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-3 h-3 bg-red-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm text-gray-500">Destino</p>
                  <p className="font-medium">Shopping Center Norte</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <MapPin className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                <p className="text-sm font-semibold">2.5 km</p>
                <p className="text-xs text-gray-500">Distância</p>
              </div>
              <div className="text-center">
                <Clock className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                <p className="text-sm font-semibold">8 min</p>
                <p className="text-xs text-gray-500">Duração</p>
              </div>
              <div className="text-center">
                <DollarSign className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                <p className="text-sm font-semibold">R$ 12.50</p>
                <p className="text-xs text-gray-500">Estimado</p>
              </div>
            </div>

            <div className="flex space-x-3">
              <button className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-semibold hover:bg-gray-200 transition-colors">
                Recusar
              </button>
              <button 
                onClick={handleAcceptRide}
                className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-green-700 transition-colors"
              >
                Aceitar
              </button>
            </div>
          </div>
        )}

            {/* Solicitar Corrida (NOVO SISTEMA 5 TELAS) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <h3 className="text-lg font-semibold text-white mb-4 text-center">🚗 Solicitar Corrida</h3>
              <motion.button
                onClick={() => window.location.href = '/ride-request/map'}
                className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white py-4 px-6 rounded-xl font-semibold flex items-center justify-center space-x-3 transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <MapPin className="w-6 h-6" />
                <div className="text-left">
                  <div className="text-base font-semibold">Solicitar Corrida</div>
                  <div className="text-sm opacity-90">Sistema de 5 telas</div>
                </div>
              </motion.button>
            </motion.div>

            {/* Ações Rápidas (DESIGN FIEL) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <h3 className="text-lg font-semibold text-white mb-4 text-center">⚡ Ações Rápidas</h3>
              <div className="grid grid-cols-2 gap-3">
                <motion.button
                  className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Navigation className="w-6 h-6" />
                  <span className="text-sm font-medium">Navegação</span>
                </motion.button>

                <motion.button
                  className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Phone className="w-6 h-6" />
                  <span className="text-sm font-medium">Suporte</span>
                </motion.button>
              </div>
            </motion.div>

            {/* Informações do Usuário (DESIGN FIEL) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
            >
              <h3 className="text-lg font-semibold text-white mb-4 text-center">👤 Minha Conta</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-white/70">Email:</span>
                  <span className="text-sm font-medium text-white">{user?.email}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-white/70">Nome:</span>
                  <span className="text-sm font-medium text-white">
                    {user?.user_metadata?.full_name || 'Motorista'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-white/70">Status:</span>
                  <span className={`text-sm font-medium ${isOnline ? 'text-green-400' : 'text-red-400'}`}>
                    {isOnline ? 'Online' : 'Offline'}
                  </span>
                </div>
              </div>
            </motion.div>

            {/* Botão de Logout (DESIGN FIEL) */}
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
            >
              <motion.button
                onClick={handleLogout}
                className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2 transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <LogOut className="w-4 h-4" />
                <span>Sair da Conta</span>
              </motion.button>
            </motion.div>

          </div>
        </div>

        {/* Footer Simples (DESIGN FIEL) */}
        <motion.div
          className="pb-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <p className="text-white/50 text-xs">
            © 2024 MobiDrive Driver. Todos os direitos reservados.
          </p>
        </motion.div>
      </div>
    </div>
  )
}

export const Dashboard = DashboardMobile
export default DashboardMobile
