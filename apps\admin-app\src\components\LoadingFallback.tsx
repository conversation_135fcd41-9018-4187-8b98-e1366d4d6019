import React, { useEffect } from 'react'
import { motion } from 'framer-motion'

interface LoadingFallbackProps {
  message?: string
  timeout?: number
  onTimeout?: () => void
}

const LoadingFallback: React.FC<LoadingFallbackProps> = ({ 
  message = 'Carregando...', 
  timeout = 10000,
  onTimeout 
}) => {
  useEffect(() => {
    if (timeout && onTimeout) {
      const timer = setTimeout(onTimeout, timeout)
      return () => clearTimeout(timer)
    }
  }, [timeout, onTimeout])

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-600 to-emerald-700 flex items-center justify-center">
      <div className="text-center">
        <motion.div
          className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full mx-auto mb-4"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
        />
        <p className="text-white text-lg font-medium">{message}</p>
      </div>
    </div>
  )
}

export default LoadingFallback
