// Formatadores
export const formatters = {
  currency: (value: number, currency = 'BRL') => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency
    }).format(value);
  },

  date: (date: string | Date, format: 'short' | 'long' | 'time' = 'short') => {
    const d = new Date(date);
    
    switch (format) {
      case 'short':
        return d.toLocaleDateString('pt-BR');
      case 'long':
        return d.toLocaleDateString('pt-BR', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      case 'time':
        return d.toLocaleTimeString('pt-BR', {
          hour: '2-digit',
          minute: '2-digit'
        });
      default:
        return d.toLocaleDateString('pt-BR');
    }
  },

  phone: (phone: string) => {
    const cleaned = phone.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{2})(\d{5})(\d{4})$/);
    
    if (match) {
      return '(' + match[1] + ') ' + match[2] + '-' + match[3];
    }
    
    return phone;
  },

  distance: (meters: number) => {
    if (meters < 1000) {
      return meters + 'm';
    }
    return (meters / 1000).toFixed(1) + 'km';
  },

  duration: (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return hours + 'h ' + minutes + 'min';
    }
    return minutes + 'min';
  }
};