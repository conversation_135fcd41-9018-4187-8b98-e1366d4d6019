import React, { useEffect } from 'react'
import { LoginMobile } from '../pages/LoginMobile'
import { Login } from '../pages/Login'
import { <PERSON>ceWrapper } from './DeviceWrapper'
import { useDeviceDetection, shouldUseMobileNativeUI, shouldUseDesktopMockup, logDeviceInfo } from '../utils/deviceDetector'

// 🔄 WRAPPER ADAPTATIVO DE LOGIN
// Escolhe automaticamente entre UI mobile nativa e mockup desktop
// Baseado na detecção real do dispositivo

interface AdaptiveLoginWrapperProps {
  onLogin?: (email: string, password: string) => void
}

export const AdaptiveLoginWrapper: React.FC<AdaptiveLoginWrapperProps> = ({ onLogin }) => {
  const deviceInfo = useDeviceDetection()

  // Log informações do dispositivo para debug
  useEffect(() => {
    logDeviceInfo()
  }, [])

  // Decide qual UI usar baseado no dispositivo
  const useMobileNativeUI = shouldUseMobileNativeUI()
  const useDesktopMockup = shouldUseDesktopMockup()

  // Debug removido para console limpo

  // MOBILE NATIVO: Usa React Native Elements diretamente
  if (useMobileNativeUI) {
    return (
      <div style={{ 
        width: '100vw', 
        height: '100vh', 
        overflow: 'hidden',
        backgroundColor: '#f5f5f5'
      }}>
        <LoginMobile onLogin={onLogin} />
      </div>
    )
  }

  // DESKTOP MOCKUP: Usa o sistema de mockup iPhone
  if (useDesktopMockup) {
    return (
      <DeviceWrapper>
        <Login />
      </DeviceWrapper>
    )
  }

  // FALLBACK: Usa mobile nativo como padrão
  return (
    <div style={{ 
      width: '100vw', 
      height: '100vh', 
      overflow: 'hidden',
      backgroundColor: '#f5f5f5'
    }}>
      <LoginMobile onLogin={onLogin} />
    </div>
  )
}

export default AdaptiveLoginWrapper
