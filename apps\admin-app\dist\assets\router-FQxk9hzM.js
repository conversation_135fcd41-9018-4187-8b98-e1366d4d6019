import{r as u,R as se}from"./vendor-BdeVZlrH.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)}var P;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(P||(P={}));const J="popstate";function ue(e){e===void 0&&(e={});function t(r,a){let{pathname:l,search:i,hash:s}=r.location;return $("",{pathname:l,search:i,hash:s},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:T(a)}return fe(t,n,null,e)}function v(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Q(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ce(){return Math.random().toString(36).substr(2,8)}function z(e,t){return{usr:e.state,key:e.key,idx:t}}function $(e,t,n,r){return n===void 0&&(n=null),B({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?U(t):t,{state:n,key:t&&t.key||r||ce()})}function T(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function U(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function fe(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,s=P.Pop,o=null,f=h();f==null&&(f=0,i.replaceState(B({},i.state,{idx:f}),""));function h(){return(i.state||{idx:null}).idx}function c(){s=P.Pop;let d=h(),x=d==null?null:d-f;f=d,o&&o({action:s,location:m.location,delta:x})}function p(d,x){s=P.Push;let E=$(m.location,d,x);f=h()+1;let C=z(E,f),w=m.createHref(E);try{i.pushState(C,"",w)}catch(L){if(L instanceof DOMException&&L.name==="DataCloneError")throw L;a.location.assign(w)}l&&o&&o({action:s,location:m.location,delta:1})}function y(d,x){s=P.Replace;let E=$(m.location,d,x);f=h();let C=z(E,f),w=m.createHref(E);i.replaceState(C,"",w),l&&o&&o({action:s,location:m.location,delta:0})}function g(d){let x=a.location.origin!=="null"?a.location.origin:a.location.href,E=typeof d=="string"?d:T(d);return E=E.replace(/ $/,"%20"),v(x,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,x)}let m={get action(){return s},get location(){return e(a,i)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(J,c),o=d,()=>{a.removeEventListener(J,c),o=null}},createHref(d){return t(a,d)},createURL:g,encodeLocation(d){let x=g(d);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:y,go(d){return i.go(d)}};return m}var A;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(A||(A={}));function he(e,t,n){return n===void 0&&(n="/"),de(e,t,n)}function de(e,t,n,r){let a=typeof t=="string"?U(t):t,l=F(a.pathname||"/",n);if(l==null)return null;let i=Y(e);pe(i);let s=null;for(let o=0;s==null&&o<i.length;++o){let f=Se(l);s=Pe(i[o],f)}return s}function Y(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(l,i,s)=>{let o={relativePath:s===void 0?l.path||"":s,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};o.relativePath.startsWith("/")&&(v(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let f=R([r,o.relativePath]),h=n.concat(o);l.children&&l.children.length>0&&(v(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),Y(l.children,t,h,f)),!(l.path==null&&!l.index)&&t.push({path:f,score:Ee(f,l.index),routesMeta:h})};return e.forEach((l,i)=>{var s;if(l.path===""||!((s=l.path)!=null&&s.includes("?")))a(l,i);else for(let o of Z(l.path))a(l,i,o)}),t}function Z(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return a?[l,""]:[l];let i=Z(r.join("/")),s=[];return s.push(...i.map(o=>o===""?l:[l,o].join("/"))),a&&s.push(...i),s.map(o=>e.startsWith("/")&&o===""?"/":o)}function pe(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:we(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const me=/^:[\w-]+$/,ve=3,ge=2,ye=1,xe=10,Ce=-2,K=e=>e==="*";function Ee(e,t){let n=e.split("/"),r=n.length;return n.some(K)&&(r+=Ce),t&&(r+=ge),n.filter(a=>!K(a)).reduce((a,l)=>a+(me.test(l)?ve:l===""?ye:xe),r)}function we(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function Pe(e,t,n){let{routesMeta:r}=e,a={},l="/",i=[];for(let s=0;s<r.length;++s){let o=r[s],f=s===r.length-1,h=l==="/"?t:t.slice(l.length)||"/",c=Re({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},h),p=o.route;if(!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:R([l,c.pathname]),pathnameBase:Be(R([l,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(l=R([l,c.pathnameBase]))}return i}function Re(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=be(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce((f,h,c)=>{let{paramName:p,isOptional:y}=h;if(p==="*"){let m=s[c]||"";i=l.slice(0,l.length-m.length).replace(/(.)\/+$/,"$1")}const g=s[c];return y&&!g?f[p]=void 0:f[p]=(g||"").replace(/%2F/g,"/"),f},{}),pathname:l,pathnameBase:i,pattern:e}}function be(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Q(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,s,o)=>(r.push({paramName:s,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function Se(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Q(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function F(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Le(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?U(e):e;return{pathname:n?n.startsWith("/")?n:Ue(n,t):t,search:Ie(r),hash:Ne(a)}}function Ue(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function k(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Oe(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function M(e,t){let n=Oe(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function V(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=U(e):(a=B({},e),v(!a.pathname||!a.pathname.includes("?"),k("?","pathname","search",a)),v(!a.pathname||!a.pathname.includes("#"),k("#","pathname","hash",a)),v(!a.search||!a.search.includes("#"),k("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,s;if(i==null)s=n;else{let c=t.length-1;if(!r&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}s=c>=0?t[c]:"/"}let o=Le(a,s),f=i&&i!=="/"&&i.endsWith("/"),h=(l||i===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(f||h)&&(o.pathname+="/"),o}const R=e=>e.join("/").replace(/\/\/+/g,"/"),Be=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ie=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ne=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Te(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const ee=["post","put","patch","delete"];new Set(ee);const je=["get",...ee];new Set(je);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},I.apply(this,arguments)}const D=u.createContext(null),ke=u.createContext(null),b=u.createContext(null),j=u.createContext(null),S=u.createContext({outlet:null,matches:[],isDataRoute:!1}),te=u.createContext(null);function $e(e,t){let{relative:n}=t===void 0?{}:t;O()||v(!1);let{basename:r,navigator:a}=u.useContext(b),{hash:l,pathname:i,search:s}=ae(e,{relative:n}),o=i;return r!=="/"&&(o=i==="/"?r:R([r,i])),a.createHref({pathname:o,search:s,hash:l})}function O(){return u.useContext(j)!=null}function N(){return O()||v(!1),u.useContext(j).location}function ne(e){u.useContext(b).static||u.useLayoutEffect(e)}function re(){let{isDataRoute:e}=u.useContext(S);return e?Xe():_e()}function _e(){O()||v(!1);let e=u.useContext(D),{basename:t,future:n,navigator:r}=u.useContext(b),{matches:a}=u.useContext(S),{pathname:l}=N(),i=JSON.stringify(M(a,n.v7_relativeSplatPath)),s=u.useRef(!1);return ne(()=>{s.current=!0}),u.useCallback(function(f,h){if(h===void 0&&(h={}),!s.current)return;if(typeof f=="number"){r.go(f);return}let c=V(f,JSON.parse(i),l,h.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:R([t,c.pathname])),(h.replace?r.replace:r.push)(c,h.state,h)},[t,r,i,l,e])}function ae(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=u.useContext(b),{matches:a}=u.useContext(S),{pathname:l}=N(),i=JSON.stringify(M(a,r.v7_relativeSplatPath));return u.useMemo(()=>V(e,JSON.parse(i),l,n==="path"),[e,i,l,n])}function We(e,t){return Fe(e,t)}function Fe(e,t,n,r){O()||v(!1);let{navigator:a}=u.useContext(b),{matches:l}=u.useContext(S),i=l[l.length-1],s=i?i.params:{};i&&i.pathname;let o=i?i.pathnameBase:"/";i&&i.route;let f=N(),h;if(t){var c;let d=typeof t=="string"?U(t):t;o==="/"||(c=d.pathname)!=null&&c.startsWith(o)||v(!1),h=d}else h=f;let p=h.pathname||"/",y=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let g=he(e,{pathname:y}),m=ze(g&&g.map(d=>Object.assign({},d,{params:Object.assign({},s,d.params),pathname:R([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:R([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),l,n,r);return t&&m?u.createElement(j.Provider,{value:{location:I({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:P.Pop}},m):m}function Me(){let e=Ge(),t=Te(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return u.createElement(u.Fragment,null,u.createElement("h2",null,"Unexpected Application Error!"),u.createElement("h3",{style:{fontStyle:"italic"}},t),n?u.createElement("pre",{style:a},n):null,null)}const Ve=u.createElement(Me,null);class De extends u.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?u.createElement(S.Provider,{value:this.props.routeContext},u.createElement(te.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Je(e){let{routeContext:t,match:n,children:r}=e,a=u.useContext(D);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),u.createElement(S.Provider,{value:t},r)}function ze(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var l;if(!n)return null;if(n.errors)e=n.matches;else if((l=r)!=null&&l.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,s=(a=n)==null?void 0:a.errors;if(s!=null){let h=i.findIndex(c=>c.route.id&&(s==null?void 0:s[c.route.id])!==void 0);h>=0||v(!1),i=i.slice(0,Math.min(i.length,h+1))}let o=!1,f=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<i.length;h++){let c=i[h];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(f=h),c.route.id){let{loaderData:p,errors:y}=n,g=c.route.loader&&p[c.route.id]===void 0&&(!y||y[c.route.id]===void 0);if(c.route.lazy||g){o=!0,f>=0?i=i.slice(0,f+1):i=[i[0]];break}}}return i.reduceRight((h,c,p)=>{let y,g=!1,m=null,d=null;n&&(y=s&&c.route.id?s[c.route.id]:void 0,m=c.route.errorElement||Ve,o&&(f<0&&p===0?(He("route-fallback"),g=!0,d=null):f===p&&(g=!0,d=c.route.hydrateFallbackElement||null)));let x=t.concat(i.slice(0,p+1)),E=()=>{let C;return y?C=m:g?C=d:c.route.Component?C=u.createElement(c.route.Component,null):c.route.element?C=c.route.element:C=h,u.createElement(Je,{match:c,routeContext:{outlet:h,matches:x,isDataRoute:n!=null},children:C})};return n&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?u.createElement(De,{location:n.location,revalidation:n.revalidation,component:m,error:y,children:E(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):E()},null)}var le=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(le||{}),ie=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ie||{});function Ae(e){let t=u.useContext(D);return t||v(!1),t}function Ke(e){let t=u.useContext(ke);return t||v(!1),t}function qe(e){let t=u.useContext(S);return t||v(!1),t}function oe(e){let t=qe(),n=t.matches[t.matches.length-1];return n.route.id||v(!1),n.route.id}function Ge(){var e;let t=u.useContext(te),n=Ke(),r=oe();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Xe(){let{router:e}=Ae(le.UseNavigateStable),t=oe(ie.UseNavigateStable),n=u.useRef(!1);return ne(()=>{n.current=!0}),u.useCallback(function(a,l){l===void 0&&(l={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,I({fromRouteId:t},l)))},[e,t])}const q={};function He(e,t,n){q[e]||(q[e]=!0)}function Qe(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function ct(e){let{to:t,replace:n,state:r,relative:a}=e;O()||v(!1);let{future:l,static:i}=u.useContext(b),{matches:s}=u.useContext(S),{pathname:o}=N(),f=re(),h=V(t,M(s,l.v7_relativeSplatPath),o,a==="path"),c=JSON.stringify(h);return u.useEffect(()=>f(JSON.parse(c),{replace:n,state:r,relative:a}),[f,c,a,n,r]),null}function Ye(e){v(!1)}function Ze(e){let{basename:t="/",children:n=null,location:r,navigationType:a=P.Pop,navigator:l,static:i=!1,future:s}=e;O()&&v(!1);let o=t.replace(/^\/*/,"/"),f=u.useMemo(()=>({basename:o,navigator:l,static:i,future:I({v7_relativeSplatPath:!1},s)}),[o,s,l,i]);typeof r=="string"&&(r=U(r));let{pathname:h="/",search:c="",hash:p="",state:y=null,key:g="default"}=r,m=u.useMemo(()=>{let d=F(h,o);return d==null?null:{location:{pathname:d,search:c,hash:p,state:y,key:g},navigationType:a}},[o,h,c,p,y,g,a]);return m==null?null:u.createElement(b.Provider,{value:f},u.createElement(j.Provider,{children:n,value:m}))}function ft(e){let{children:t,location:n}=e;return We(_(t),n)}new Promise(()=>{});function _(e,t){t===void 0&&(t=[]);let n=[];return u.Children.forEach(e,(r,a)=>{if(!u.isValidElement(r))return;let l=[...t,a];if(r.type===u.Fragment){n.push.apply(n,_(r.props.children,l));return}r.type!==Ye&&v(!1),!r.props.index||!r.props.children||v(!1);let i={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=_(r.props.children,l)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},W.apply(this,arguments)}function et(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,l;for(l=0;l<r.length;l++)a=r[l],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function tt(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function nt(e,t){return e.button===0&&(!t||t==="_self")&&!tt(e)}const rt=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],at="6";try{window.__reactRouterVersion=at}catch{}const lt="startTransition",G=se[lt];function ht(e){let{basename:t,children:n,future:r,window:a}=e,l=u.useRef();l.current==null&&(l.current=ue({window:a,v5Compat:!0}));let i=l.current,[s,o]=u.useState({action:i.action,location:i.location}),{v7_startTransition:f}=r||{},h=u.useCallback(c=>{f&&G?G(()=>o(c)):o(c)},[o,f]);return u.useLayoutEffect(()=>i.listen(h),[i,h]),u.useEffect(()=>Qe(r),[r]),u.createElement(Ze,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:i,future:r})}const it=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ot=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,dt=u.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:l,replace:i,state:s,target:o,to:f,preventScrollReset:h,viewTransition:c}=t,p=et(t,rt),{basename:y}=u.useContext(b),g,m=!1;if(typeof f=="string"&&ot.test(f)&&(g=f,it))try{let C=new URL(window.location.href),w=f.startsWith("//")?new URL(C.protocol+f):new URL(f),L=F(w.pathname,y);w.origin===C.origin&&L!=null?f=L+w.search+w.hash:m=!0}catch{}let d=$e(f,{relative:a}),x=st(f,{replace:i,state:s,target:o,preventScrollReset:h,relative:a,viewTransition:c});function E(C){r&&r(C),C.defaultPrevented||x(C)}return u.createElement("a",W({},p,{href:g||d,onClick:m||l?r:E,ref:n,target:o}))});var X;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(X||(X={}));var H;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(H||(H={}));function st(e,t){let{target:n,replace:r,state:a,preventScrollReset:l,relative:i,viewTransition:s}=t===void 0?{}:t,o=re(),f=N(),h=ae(e,{relative:i});return u.useCallback(c=>{if(nt(c,n)){c.preventDefault();let p=r!==void 0?r:T(f)===T(h);o(e,{replace:p,state:a,preventScrollReset:l,relative:i,viewTransition:s})}},[f,o,h,r,a,n,e,l,i,s])}export{ht as B,dt as L,ct as N,ft as R,Ye as a,re as u};
