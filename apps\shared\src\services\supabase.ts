import { createClient } from '@supabase/supabase-js';

// URL e chave do Supabase
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://udquhavmgqtpkubrfzdm.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI';

// Detectar se estamos no navegador ou no servidor
const isBrowser = typeof window !== 'undefined';

// Configurações de autenticação otimizadas
const authOptions = {
  autoRefreshToken: true,
  persistSession: true,
  storage: isBrowser ? localStorage : null,
  detectSessionInUrl: false,
};

// Criar uma única instância do cliente Supabase com configurações otimizadas
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: authOptions,
  // Configurações globais para melhorar performance
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'X-Client-Info': 'MobiDrive/1.0.0',
    },
  },
  realtime: {
    // Desativar realtime por padrão para economizar recursos
    autoconnect: false,
  }
});

// Singleton para garantir que apenas uma instância seja usada em todo o app
export default supabase;
