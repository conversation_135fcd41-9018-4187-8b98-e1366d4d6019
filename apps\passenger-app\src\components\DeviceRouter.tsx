import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { getExperienceType, logDeviceInfo } from '../utils/deviceDetector';
import { motion } from 'framer-motion';

// 🎯 DEVICE ROUTER - DIRECIONAMENTO INTELIGENTE
// Detecta o dispositivo e redireciona para a experiência apropriada

interface DeviceRouterProps {
  children?: React.ReactNode;
}

export const DeviceRouter: React.FC<DeviceRouterProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isRedirecting, setIsRedirecting] = useState(true);
  const [experienceType, setExperienceType] = useState<'mobile' | 'desktop'>('mobile');

  useEffect(() => {
    // Log informações do dispositivo para debug
    logDeviceInfo();

    // Detecta o tipo de experiência
    const experience = getExperienceType();
    setExperienceType(experience);

    // Verifica se já está na rota correta
    const currentPath = location.pathname;
    const isInMobileRoute = currentPath.startsWith('/mobile');
    const isInDesktopRoute = currentPath.startsWith('/desktop');
    const isInRootRoute = currentPath === '/' || currentPath === '/login';

    console.log('🎯 DeviceRouter:', {
      experience,
      currentPath,
      isInMobileRoute,
      isInDesktopRoute,
      isInRootRoute
    });

    // Se está na rota correta, não redireciona
    if ((experience === 'mobile' && isInMobileRoute) || 
        (experience === 'desktop' && isInDesktopRoute)) {
      setIsRedirecting(false);
      return;
    }

    // Se está na raiz ou rota incorreta, redireciona
    if (isInRootRoute || 
        (experience === 'mobile' && isInDesktopRoute) ||
        (experience === 'desktop' && isInMobileRoute)) {
      
      const targetPath = experience === 'mobile' ? '/mobile/login' : '/desktop/login';
      
      console.log(`🔄 Redirecionando para: ${targetPath}`);
      
      // Pequeno delay para mostrar a tela de carregamento
      setTimeout(() => {
        navigate(targetPath, { replace: true });
        setIsRedirecting(false);
      }, 1500);
    } else {
      setIsRedirecting(false);
    }
  }, [navigate, location.pathname]);

  // Tela de carregamento durante o redirecionamento
  if (isRedirecting) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          {/* Logo/Ícone */}
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center"
          >
            <span className="text-2xl">🚗</span>
          </motion.div>

          {/* Título */}
          <motion.h1
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="text-2xl font-bold text-white mb-2"
          >
            MobiDrive
          </motion.h1>

          {/* Subtítulo */}
          <motion.p
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="text-gray-400 mb-6"
          >
            Detectando dispositivo...
          </motion.p>

          {/* Indicador de experiência */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.7 }}
            className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2"
          >
            <div className={`w-2 h-2 rounded-full ${
              experienceType === 'mobile' ? 'bg-green-500' : 'bg-blue-500'
            }`}></div>
            <span className="text-white text-sm">
              {experienceType === 'mobile' ? '📱 Experiência Mobile' : '💻 Experiência Desktop'}
            </span>
          </motion.div>

          {/* Barra de progresso */}
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: '100%' }}
            transition={{ duration: 1.5, ease: "easeInOut" }}
            className="h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-6 mx-auto max-w-xs"
          />
        </motion.div>
      </div>
    );
  }

  // Se não está redirecionando, renderiza o conteúdo
  return <>{children}</>;
};

export default DeviceRouter;
