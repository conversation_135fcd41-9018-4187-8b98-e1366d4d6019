#!/usr/bin/env node

/**
 * Script para limpar projetos conflitantes no Vercel
 * Executa: node cleanup-vercel.js
 */

const { execSync } = require('child_process');

// Projetos que devem ser MANTIDOS
const KEEP_PROJECTS = [
  'mobidrive-passenger',
  'driver-app', 
  'admin-app'
];

// Projetos que devem ser DELETADOS
const DELETE_PROJECTS = [
  'dist',
  'mobidrive-driver',
  'passenger-app-clean',
  'mobidrive-deploy',
  'mundodainovacao-update',
  'mundodainovacao-fixed',
  'mobi-drive-main',
  'mobidrive-admin',
  'passenger-app',
  'driver-app-simple',
  'admin-app-simple'
];

console.log('🧹 Limpando projetos conflitantes no Vercel...\n');

// Função para executar comando
function runCommand(command) {
  try {
    console.log(`📋 Executando: ${command}`);
    const result = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    console.log(`✅ Sucesso: ${result.trim()}\n`);
    return true;
  } catch (error) {
    console.log(`❌ Erro: ${error.message}\n`);
    return false;
  }
}

// Função para deletar projeto
function deleteProject(projectName) {
  console.log(`🗑️ Deletando projeto: ${projectName}`);
  
  // Usar echo para confirmar automaticamente
  const command = `echo "y" | npx vercel projects rm ${projectName}`;
  return runCommand(command);
}

// Executar limpeza
async function main() {
  console.log('🎯 Projetos que serão MANTIDOS:');
  KEEP_PROJECTS.forEach(project => {
    console.log(`  ✅ ${project}`);
  });
  
  console.log('\n🗑️ Projetos que serão DELETADOS:');
  DELETE_PROJECTS.forEach(project => {
    console.log(`  ❌ ${project}`);
  });
  
  console.log('\n🚀 Iniciando limpeza...\n');
  
  let deletedCount = 0;
  let errorCount = 0;
  
  for (const project of DELETE_PROJECTS) {
    const success = deleteProject(project);
    if (success) {
      deletedCount++;
    } else {
      errorCount++;
    }
    
    // Aguardar um pouco entre as operações
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 RESULTADO DA LIMPEZA:');
  console.log(`✅ Projetos deletados: ${deletedCount}`);
  console.log(`❌ Erros: ${errorCount}`);
  
  if (errorCount === 0) {
    console.log('\n🎊 Limpeza concluída com sucesso!');
  } else {
    console.log('\n⚠️ Limpeza concluída com alguns erros.');
  }
  
  // Verificar projetos restantes
  console.log('\n🔍 Verificando projetos restantes...');
  runCommand('npx vercel projects ls');
}

// Executar
main().catch(error => {
  console.error('💥 Erro na limpeza:', error.message);
  process.exit(1);
});
