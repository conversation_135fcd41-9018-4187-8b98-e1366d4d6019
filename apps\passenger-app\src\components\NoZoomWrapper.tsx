import React, { ReactNode } from 'react'
import { useNoZoom } from '../hooks/useNoZoom'
import '../styles/no-zoom.css'

// 🚫 WRAPPER PARA DESABILITAR ZOOM EM PÁGINAS DO APP
// Aplica automaticamente todas as configurações anti-zoom

interface NoZoomWrapperProps {
  children: ReactNode
  className?: string
  debug?: boolean
}

export const NoZoomWrapper: React.FC<NoZoomWrapperProps> = ({ 
  children, 
  className = "",
  debug = false 
}) => {
  // Aplica sistema anti-zoom
  useNoZoom()

  return (
    <div className={`no-zoom-page ${className}`}>
      {debug && (
        <div className="no-zoom-debug">
          🚫 NO ZOOM ATIVO
        </div>
      )}
      {children}
    </div>
  )
}

// Wrapper específico para páginas com mapa
interface MapPageWrapperProps {
  children: ReactNode
  className?: string
  debug?: boolean
}

export const MapPageWrapper: React.FC<MapPageWrapperProps> = ({ 
  children, 
  className = "",
  debug = false 
}) => {
  // Aplica sistema anti-zoom mas permite zoom em mapas
  useNoZoom()

  return (
    <div className={`no-zoom-page ${className}`}>
      {debug && (
        <div className="no-zoom-debug">
          🚫 NO ZOOM + 🗺️ MAP ZOOM OK
        </div>
      )}
      {children}
    </div>
  )
}

export default NoZoomWrapper
