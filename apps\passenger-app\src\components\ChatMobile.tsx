import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Send,
  MapPin,
  Phone,
  MoreVertical,
  ArrowLeft,
  User,
  Clock,
  Check,
  CheckCheck
} from 'lucide-react';
import { chatService, ChatMessage, ChatParticipant } from '../services/ChatService';
import { useAuth } from '../contexts/AuthContextSimple';

interface ChatMobileProps {
  rideId: string;
  onClose: () => void;
  isOpen: boolean;
}

export const ChatMobile: React.FC<ChatMobileProps> = ({ rideId, onClose, isOpen }) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [participants, setParticipants] = useState<ChatParticipant[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatSubscription = useRef<any>(null);

  // Carregar mensagens e participantes
  useEffect(() => {
    if (isOpen && rideId) {
      loadChatData();
      subscribeToMessages();
      markAsRead();
    }

    return () => {
      if (chatSubscription.current) {
        chatSubscription.current.unsubscribe();
      }
    };
  }, [isOpen, rideId]);

  // Auto scroll para última mensagem
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadChatData = async () => {
    setLoading(true);
    try {
      const [messagesData, participantsData, unreadCountData] = await Promise.all([
        chatService.getChatMessages(rideId),
        chatService.getChatParticipants(rideId),
        chatService.getUnreadCount(rideId)
      ]);

      setMessages(messagesData);
      setParticipants(participantsData);
      setUnreadCount(unreadCountData);
    } catch (error) {
      console.error('Erro ao carregar chat:', error);
    } finally {
      setLoading(false);
    }
  };

  const subscribeToMessages = () => {
    chatSubscription.current = chatService.subscribeToMessages(rideId, (newMessage) => {
      setMessages(prev => [...prev, newMessage]);
      
      // Marcar como lida se não for do usuário atual
      if (newMessage.sender_id !== user?.id) {
        setTimeout(() => markAsRead(), 1000);
      }
    });
  };

  const markAsRead = async () => {
    await chatService.markMessagesAsRead(rideId);
    setUnreadCount(0);
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || loading) return;

    const messageText = newMessage.trim();
    setNewMessage('');
    setLoading(true);

    try {
      const sentMessage = await chatService.sendTextMessage(rideId, messageText);
      if (sentMessage) {
        // Mensagem será adicionada via subscription
      }
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      setNewMessage(messageText); // Restaurar mensagem em caso de erro
    } finally {
      setLoading(false);
    }
  };

  const sendLocation = async () => {
    if (!navigator.geolocation) return;

    setLoading(true);
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const location: [number, number] = [
            position.coords.longitude,
            position.coords.latitude
          ];
          await chatService.sendLocationMessage(rideId, location);
        } catch (error) {
          console.error('Erro ao enviar localização:', error);
        } finally {
          setLoading(false);
        }
      },
      (error) => {
        console.error('Erro ao obter localização:', error);
        setLoading(false);
      }
    );
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getOtherParticipant = () => {
    return participants.find(p => p.id !== user?.id);
  };

  const otherParticipant = getOtherParticipant();

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: '100%' }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: '100%' }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className="fixed inset-0 z-50 bg-black flex flex-col"
    >
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-md border-b border-white/10 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.button
              onClick={onClose}
              className="p-2 text-white/80 hover:text-white transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowLeft className="w-5 h-5" />
            </motion.button>
            
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-white font-semibold">
                  {otherParticipant?.name || 'Chat'}
                </h3>
                <p className="text-white/60 text-sm">
                  {otherParticipant?.is_online ? 'Online' : 'Offline'}
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <motion.button
              className="p-2 text-white/80 hover:text-white transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Phone className="w-5 h-5" />
            </motion.button>
            <motion.button
              className="p-2 text-white/80 hover:text-white transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <MoreVertical className="w-5 h-5" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {loading && messages.length === 0 ? (
          <div className="flex justify-center items-center h-full">
            <div className="text-white/60">Carregando mensagens...</div>
          </div>
        ) : (
          <AnimatePresence>
            {messages.map((message, index) => {
              const isOwn = message.sender_id === user?.id;
              const isSystem = message.message_type === 'system';
              
              return (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className={`flex ${isOwn ? 'justify-end' : 'justify-start'} ${isSystem ? 'justify-center' : ''}`}
                >
                  {isSystem ? (
                    <div className="bg-white/10 backdrop-blur-md rounded-xl px-4 py-2 max-w-xs">
                      <p className="text-white/80 text-sm text-center">{message.message}</p>
                      <p className="text-white/50 text-xs text-center mt-1">
                        {formatTime(message.created_at)}
                      </p>
                    </div>
                  ) : (
                    <div className={`max-w-xs ${isOwn ? 'ml-12' : 'mr-12'}`}>
                      <div
                        className={`rounded-2xl px-4 py-3 ${
                          isOwn
                            ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                            : 'bg-white/10 backdrop-blur-md text-white border border-white/20'
                        }`}
                      >
                        {message.message_type === 'location' ? (
                          <div className="flex items-center space-x-2">
                            <MapPin className="w-4 h-4" />
                            <span className="text-sm">Localização compartilhada</span>
                          </div>
                        ) : (
                          <p className="text-sm">{message.message}</p>
                        )}
                      </div>
                      
                      <div className={`flex items-center space-x-1 mt-1 ${isOwn ? 'justify-end' : 'justify-start'}`}>
                        <span className="text-white/50 text-xs">
                          {formatTime(message.created_at)}
                        </span>
                        {isOwn && (
                          <div className="text-white/50">
                            {message.read_at ? (
                              <CheckCheck className="w-3 h-3" />
                            ) : (
                              <Check className="w-3 h-3" />
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </motion.div>
              );
            })}
          </AnimatePresence>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="bg-black/20 backdrop-blur-md border-t border-white/10 p-4">
        <div className="flex items-center space-x-3">
          <motion.button
            onClick={sendLocation}
            disabled={loading}
            className="p-3 bg-white/10 backdrop-blur-md rounded-xl text-white/80 hover:text-white transition-colors disabled:opacity-50"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <MapPin className="w-5 h-5" />
          </motion.button>

          <div className="flex-1 relative">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
              placeholder="Digite sua mensagem..."
              className="w-full bg-white/10 backdrop-blur-md border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={loading}
            />
          </div>

          <motion.button
            onClick={sendMessage}
            disabled={!newMessage.trim() || loading}
            className="p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl text-white disabled:opacity-50 disabled:cursor-not-allowed"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Send className="w-5 h-5" />
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default ChatMobile;
