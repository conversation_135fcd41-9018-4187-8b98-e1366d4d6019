# Script PowerShell para deletar projetos do Vercel automaticamente

$projectsToDelete = @(
    "mundodainovacao-update",
    "mundodainovacao-fixed", 
    "mobi-drive-main"
)

Write-Host "🗑️ Deletando projetos restantes do Vercel..." -ForegroundColor Yellow

foreach ($project in $projectsToDelete) {
    Write-Host "🔄 Deletando: $project" -ForegroundColor Cyan
    
    # Usar echo para responder automaticamente "yes"
    $command = "echo y | npx vercel projects rm $project"
    
    try {
        Invoke-Expression $command
        Write-Host "✅ $project deletado com sucesso!" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Erro ao deletar $project : $_" -ForegroundColor Red
    }
    
    # Aguardar um pouco entre as operações
    Start-Sleep -Seconds 2
}

Write-Host "🔍 Verificando projetos restantes..." -ForegroundColor Yellow
npx vercel projects ls

Write-Host "🎊 Limpeza concluída!" -ForegroundColor Green
