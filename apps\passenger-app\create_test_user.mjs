// =====================================================
// SCRIPT PARA CRIAR USUÁRIO DE TESTE NO SUPABASE
// Cria o usuário <EMAIL> para testes
// =====================================================

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

const supabase = createClient(supabaseUrl, supabase<PERSON>nonKey)

async function createTestUser() {
  console.log('👤 CRIANDO USUÁRIO DE TESTE')
  console.log('=' .repeat(50))
  
  const email = '<EMAIL>'
  const password = '123456'
  
  try {
    console.log(`📧 Email: ${email}`)
    console.log(`🔒 Senha: ${password}`)
    console.log('\n🔄 Tentando criar usuário...')
    
    // Tentar criar o usuário
    const { data, error } = await supabase.auth.signUp({
      email: email,
      password: password,
      options: {
        data: {
          full_name: 'Jeremias Teste',
          user_type: 'passenger'
        }
      }
    })
    
    if (error) {
      console.log('❌ Erro ao criar usuário:', error.message)
      
      if (error.message.includes('already registered')) {
        console.log('ℹ️ Usuário já existe, tentando fazer login...')
        
        // Tentar fazer login
        const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
          email: email,
          password: password
        })
        
        if (loginError) {
          console.log('❌ Erro no login:', loginError.message)
          
          if (loginError.message.includes('Invalid login credentials')) {
            console.log('⚠️ Usuário existe mas senha está incorreta')
            console.log('💡 Tentando resetar senha...')
            
            const { error: resetError } = await supabase.auth.resetPasswordForEmail(email)
            
            if (resetError) {
              console.log('❌ Erro ao resetar senha:', resetError.message)
            } else {
              console.log('✅ Email de reset enviado!')
              console.log('📧 Verifique o email para resetar a senha')
            }
          }
        } else {
          console.log('✅ Login realizado com sucesso!')
          console.log('👤 Usuário:', loginData.user?.email)
          console.log('🔑 Token:', loginData.session?.access_token ? 'Presente' : 'Ausente')
        }
      }
    } else {
      console.log('✅ Usuário criado com sucesso!')
      console.log('👤 Usuário:', data.user?.email)
      console.log('📧 Confirmação:', data.user?.email_confirmed_at ? 'Confirmado' : 'Pendente')
      
      if (!data.user?.email_confirmed_at) {
        console.log('⚠️ Email não confirmado automaticamente')
        console.log('💡 Verificando se precisa de confirmação...')
      }
    }
    
  } catch (error) {
    console.error('💥 Erro inesperado:', error.message)
  }
}

async function testLogin() {
  console.log('\n🧪 TESTANDO LOGIN APÓS CRIAÇÃO')
  console.log('-' .repeat(40))
  
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: '123456'
    })
    
    if (error) {
      console.log('❌ Erro no teste de login:', error.message)
      return false
    } else {
      console.log('✅ Teste de login bem-sucedido!')
      console.log('👤 Usuário logado:', data.user?.email)
      console.log('🔑 Session ID:', data.session?.access_token?.substring(0, 20) + '...')
      
      // Fazer logout
      await supabase.auth.signOut()
      console.log('🚪 Logout realizado')
      
      return true
    }
  } catch (error) {
    console.error('💥 Erro no teste:', error.message)
    return false
  }
}

async function checkExistingUsers() {
  console.log('\n📋 VERIFICANDO USUÁRIOS EXISTENTES')
  console.log('-' .repeat(40))
  
  try {
    // Tentar listar perfis (se a tabela existir e for acessível)
    const { data, error } = await supabase
      .from('profiles')
      .select('id, email, full_name, user_type')
      .limit(5)
    
    if (error) {
      console.log('⚠️ Não foi possível listar usuários:', error.message)
    } else {
      console.log(`📊 Encontrados ${data?.length || 0} perfis:`)
      data?.forEach((profile, i) => {
        console.log(`  ${i + 1}. ${profile.email || 'N/A'} - ${profile.full_name || 'N/A'}`)
      })
    }
  } catch (error) {
    console.log('⚠️ Erro ao verificar usuários:', error.message)
  }
}

// Executar script
async function main() {
  console.log('🎯 SCRIPT DE CRIAÇÃO DE USUÁRIO DE TESTE')
  console.log('=' .repeat(60))
  
  await checkExistingUsers()
  await createTestUser()
  const loginSuccess = await testLogin()
  
  console.log('\n📊 RESUMO:')
  console.log('=' .repeat(30))
  console.log(`✅ Login funcionando: ${loginSuccess ? 'SIM' : 'NÃO'}`)
  
  if (loginSuccess) {
    console.log('\n🎉 SUCESSO! Usuário de teste criado e funcionando!')
    console.log('🔑 Credenciais para teste:')
    console.log('   📧 Email: <EMAIL>')
    console.log('   🔒 Senha: 123456')
  } else {
    console.log('\n⚠️ Problema com o usuário de teste')
    console.log('💡 Verifique as configurações do Supabase')
  }
}

main()
  .then(() => {
    console.log('\n🏁 Script concluído!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
