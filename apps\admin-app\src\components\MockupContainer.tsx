import React, { ReactNode } from 'react';
import { getExperienceType } from '../utils/deviceDetector';
import { DeviceWrapper } from './DeviceWrapper';

// 🎯 MOCKUP CONTAINER - COMPONENTE INTELIGENTE
// Decide automaticamente se usa mockup (desktop) ou renderiza direto (mobile)

interface MockupContainerProps {
  children: ReactNode;
}

export const MockupContainer: React.FC<MockupContainerProps> = ({ children }) => {
  const experienceType = getExperienceType();

  // 📱 MOBILE REAL: Renderiza as páginas diretamente (sem mockup)
  if (experienceType === 'mobile') {
    return (
      <div className="mobile-native-container">
        {children}
      </div>
    );
  }

  // 💻 DESKTOP: Renderiza as páginas DENTRO do mockup iPhone
  return (
    <DeviceWrapper>
      {children}
    </DeviceWrapper>
  );
};

export default MockupContainer;
