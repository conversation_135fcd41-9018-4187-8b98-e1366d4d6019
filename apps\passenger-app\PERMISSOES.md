# Permissões do MobiDrive Passageiro

## Permissões Solicitadas:

### 📍 LOCALIZAÇÃO:
- **ACCESS_FINE_LOCATION**: Localização precisa via GPS
- **ACCESS_COARSE_LOCATION**: Localização aproximada via rede
- **ACCESS_BACKGROUND_LOCATION**: Localização em segundo plano

### 🌐 REDE:
- **INTERNET**: Acesso à internet para API calls
- **ACCESS_NETWORK_STATE**: Verificar status da conexão
- **ACCESS_WIFI_STATE**: Verificar status do WiFi

### 📱 DISPOSITIVO:
- **CAMERA**: Tirar fotos (documentos, perfil)
- **READ_EXTERNAL_STORAGE**: Ler arquivos do dispositivo
- **WRITE_EXTERNAL_STORAGE**: Salvar arquivos no dispositivo
- **CALL_PHONE**: Fazer chamadas telefônicas
- **SEND_SMS**: Enviar mensagens SMS
- **VIBRATE**: Vibração para notificações
- **WAKE_LOCK**: Manter tela ativa durante uso



## Justificativas:

### Para MobiDrive Passageiro:

- **Localização**: Necessária para mostrar posição atual e solicitar corridas
- **Câmera**: Para foto de perfil e documentos
- **Telefone/SMS**: Para contato com motorista
- **Armazenamento**: Para cache de mapas e dados offline


## Configuração no Capacitor:

```json
{
  "plugins": {
    "Geolocation": {
      "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION"],
      "accuracy": "high"
    }
  }
}
```

## Como o usuário verá:

1. **Primeira abertura**: Solicitação de permissões essenciais
2. **Localização**: "Permitir que MobiDrive Passageiro acesse sua localização?"
3. **Câmera**: "Permitir que MobiDrive Passageiro tire fotos?"
4. **Armazenamento**: "Permitir acesso aos arquivos do dispositivo?"

Todas as permissões são necessárias para o funcionamento completo do app.
