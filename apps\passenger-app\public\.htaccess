# Cache de assets estáticos
<IfModule mod_expires.c>
  ExpiresActive on
  
  # Imagens
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/jpg "access plus 1 year"
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  
  # CSS e JS
  ExpiresByType text/css "access plus 1 year"
  ExpiresByType application/javascript "access plus 1 year"
  
  # Manifest
  ExpiresByType application/manifest+json "access plus 1 week"
</IfModule>

# Compressão gzip
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE text/html
</IfModule>