import { useState, useEffect } from 'react'

// 📱 MOBILE FORCER - FORÇA EXPERIÊNCIA MOBILE EM DESKTOP
// Usado pelo DeviceWrapper para decidir se mostra mockup

export const useMobileForcer = (): boolean => {
  const [shouldUseMockup, setShouldUseMockup] = useState(true)

  useEffect(() => {
    // Detectar se é realmente mobile
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    const isSmallScreen = window.innerWidth <= 768
    const hasTouch = 'ontouchstart' in window

    // Se é mobile real, não usar mockup
    if (isMobile || (isSmallScreen && hasTouch)) {
      setShouldUseMockup(false)
    } else {
      // Se é desktop, usar mockup
      setShouldUseMockup(true)
    }
  }, [])

  return shouldUseMockup
}
