import { useState, useCallback } from 'react'
import { rideService, RideRequest } from '../services/RideService'
import { useAuth } from '../contexts/AuthContext'

export interface RideRequestData {
  origin_address: string
  origin_coords: [number, number]
  destination_address: string
  destination_coords: [number, number]
  distance: number
  duration: number
  estimated_price: number
  vehicle_type: string
  payment_method_id?: string
  payment_method_type: string
  passenger_notes?: string
}

export interface RideRequestResponse {
  id: string
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled'
  created_at: string
  driver_id?: string
  driver_assigned: boolean
}

export const useRideRequest = () => {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createRideRequest = useCallback(async (data: RideRequestData): Promise<RideRequestResponse | null> => {
    if (!user?.id) {
      setError('Usuá<PERSON> não autenticado')
      return null
    }

    setIsLoading(true)
    setError(null)

    try {
      console.log('🚗 Criando solicitação de corrida com RideService melhorado...', data)

      // Usar o novo RideService com atribuição automática de motorista
      const rideRequest = await rideService.createRideRequest(
        data.origin_address,
        data.origin_coords,
        data.destination_address,
        data.destination_coords,
        data.vehicle_type,
        data.payment_method_type
      )

      if (!rideRequest) {
        throw new Error('Falha ao criar solicitação de corrida')
      }

      console.log('✅ Solicitação criada com sucesso:', {
        id: rideRequest.id,
        status: rideRequest.status,
        driver_assigned: !!rideRequest.driver_id
      })

      return {
        id: rideRequest.id,
        status: rideRequest.status,
        created_at: rideRequest.created_at,
        driver_id: rideRequest.driver_id,
        driver_assigned: !!rideRequest.driver_id
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido'
      console.error('❌ Erro ao criar ride request:', errorMessage)
      setError(errorMessage)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [user?.id])

  const cancelRideRequest = useCallback(async (rideId: string): Promise<boolean> => {
    if (!user?.id) {
      setError('Usuário não autenticado')
      return false
    }

    setIsLoading(true)
    setError(null)

    try {
      const { error } = await supabase
        .from('ride_requests')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString()
        })
        .eq('id', rideId)
        .eq('user_id', user.id) // Garantir que só o usuário pode cancelar sua própria corrida

      if (error) {
        throw new Error(`Erro ao cancelar ride request: ${error.message}`)
      }

      console.log('✅ Ride request cancelado:', rideId)
      return true

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido'
      console.error('❌ Erro ao cancelar ride request:', errorMessage)
      setError(errorMessage)
      return false
    } finally {
      setIsLoading(false)
    }
  }, [user?.id])

  const notifyNearbyDrivers = useCallback(async (rideRequestId: string, originCoords: [number, number]): Promise<number> => {
    try {
      console.log('📢 Notificando motoristas próximos...', { rideRequestId, originCoords })

      // Buscar motoristas ativos próximos
      const { data: nearbyDrivers, error } = await supabase
        .from('driver_locations')
        .select('user_id, location, is_available')
        .eq('is_active', true)
        .eq('is_available', true)

      if (error) {
        console.error('Erro ao buscar motoristas:', error)
        return 0
      }

      const driversCount = nearbyDrivers?.length || 0
      console.log(`📍 Encontrados ${driversCount} motoristas ativos`)

      if (driversCount === 0) {
        return 0
      }

      // Criar notificações para motoristas próximos
      const notifications = nearbyDrivers!.map(driver => ({
        user_id: driver.user_id,
        type: 'ride_request',
        title: 'Nova solicitação de corrida',
        message: `Nova corrida disponível próxima a você`,
        data: {
          ride_request_id: rideRequestId,
          origin_coords: originCoords,
          distance: 'Calculando...'
        },
        created_at: new Date().toISOString()
      }))

      const { error: notificationError } = await supabase
        .from('notifications')
        .insert(notifications)

      if (notificationError) {
        console.error('Erro ao criar notificações:', notificationError)
        return 0
      }

      console.log(`✅ ${notifications.length} notificações enviadas para motoristas`)
      return notifications.length

    } catch (error) {
      console.error('Erro ao notificar motoristas:', error)
      return 0
    }
  }, [])

  return {
    createRideRequest,
    cancelRideRequest,
    notifyNearbyDrivers,
    isLoading,
    error,
    clearError: () => setError(null)
  }
}
