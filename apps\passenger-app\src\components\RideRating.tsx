import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Star, MessageSquare, ThumbsUp, ThumbsDown, Send } from 'lucide-react'

interface RideRatingProps {
  rideId: string
  driverName: string
  driverPhoto?: string
  onSubmitRating: (rating: RatingData) => void
  onSkip?: () => void
}

interface RatingData {
  rating: number
  feedback: string
  categories: {
    punctuality: number
    cleanliness: number
    driving: number
    courtesy: number
  }
  wouldRecommend: boolean
}

export const RideRating: React.FC<RideRatingProps> = ({
  rideId,
  driverName,
  driverPhoto,
  onSubmitRating,
  onSkip
}) => {
  const [rating, setRating] = useState(0)
  const [feedback, setFeedback] = useState('')
  const [categories, setCategories] = useState({
    punctuality: 0,
    cleanliness: 0,
    driving: 0,
    courtesy: 0
  })
  const [wouldRecommend, setWouldRecommend] = useState<boolean | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleStarClick = (value: number) => {
    setRating(value)
  }

  const handleCategoryRating = (category: keyof typeof categories, value: number) => {
    setCategories(prev => ({ ...prev, [category]: value }))
  }

  const handleSubmit = async () => {
    if (rating === 0) return

    setIsSubmitting(true)
    
    const ratingData: RatingData = {
      rating,
      feedback,
      categories,
      wouldRecommend: wouldRecommend ?? true
    }

    try {
      await onSubmitRating(ratingData)
    } catch (error) {
      console.error('Erro ao enviar avaliação:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const StarRating = ({ value, onChange, size = 'w-8 h-8' }: { 
    value: number
    onChange: (value: number) => void
    size?: string 
  }) => (
    <div className="flex space-x-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <motion.button
          key={star}
          onClick={() => onChange(star)}
          className={`${size} ${
            star <= value ? 'text-yellow-400' : 'text-gray-300'
          } transition-colors`}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Star className="w-full h-full fill-current" />
        </motion.button>
      ))}
    </div>
  )

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-2xl p-6 shadow-xl max-w-md mx-auto"
    >
      {/* Header */}
      <div className="text-center mb-6">
        <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto mb-4 overflow-hidden">
          {driverPhoto ? (
            <img src={driverPhoto} alt={driverName} className="w-full h-full object-cover" />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-2xl font-bold">
              {driverName.charAt(0)}
            </div>
          )}
        </div>
        <h3 className="text-xl font-bold text-gray-800">Como foi sua corrida?</h3>
        <p className="text-gray-600">Avalie {driverName}</p>
      </div>

      {/* Rating Principal */}
      <div className="text-center mb-6">
        <StarRating value={rating} onChange={handleStarClick} />
        <p className="text-sm text-gray-500 mt-2">
          {rating === 0 && 'Toque nas estrelas para avaliar'}
          {rating === 1 && 'Muito ruim'}
          {rating === 2 && 'Ruim'}
          {rating === 3 && 'Regular'}
          {rating === 4 && 'Bom'}
          {rating === 5 && 'Excelente'}
        </p>
      </div>

      {/* Categorias Detalhadas */}
      {rating > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="space-y-4 mb-6"
        >
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-700 mb-1">Pontualidade</p>
              <StarRating 
                value={categories.punctuality} 
                onChange={(value) => handleCategoryRating('punctuality', value)}
                size="w-5 h-5"
              />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700 mb-1">Limpeza</p>
              <StarRating 
                value={categories.cleanliness} 
                onChange={(value) => handleCategoryRating('cleanliness', value)}
                size="w-5 h-5"
              />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700 mb-1">Direção</p>
              <StarRating 
                value={categories.driving} 
                onChange={(value) => handleCategoryRating('driving', value)}
                size="w-5 h-5"
              />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700 mb-1">Cortesia</p>
              <StarRating 
                value={categories.courtesy} 
                onChange={(value) => handleCategoryRating('courtesy', value)}
                size="w-5 h-5"
              />
            </div>
          </div>
        </motion.div>
      )}

      {/* Recomendação */}
      {rating > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mb-6"
        >
          <p className="text-sm font-medium text-gray-700 mb-3">Recomendaria este motorista?</p>
          <div className="flex space-x-4">
            <motion.button
              onClick={() => setWouldRecommend(true)}
              className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-lg border-2 transition-colors ${
                wouldRecommend === true 
                  ? 'border-green-500 bg-green-50 text-green-700' 
                  : 'border-gray-200 text-gray-600'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <ThumbsUp className="w-4 h-4" />
              <span>Sim</span>
            </motion.button>
            <motion.button
              onClick={() => setWouldRecommend(false)}
              className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-lg border-2 transition-colors ${
                wouldRecommend === false 
                  ? 'border-red-500 bg-red-50 text-red-700' 
                  : 'border-gray-200 text-gray-600'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <ThumbsDown className="w-4 h-4" />
              <span>Não</span>
            </motion.button>
          </div>
        </motion.div>
      )}

      {/* Feedback */}
      {rating > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mb-6"
        >
          <div className="relative">
            <MessageSquare className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
            <textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Deixe um comentário (opcional)"
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              maxLength={500}
            />
          </div>
          <p className="text-xs text-gray-400 mt-1">{feedback.length}/500</p>
        </motion.div>
      )}

      {/* Botões */}
      <div className="flex space-x-3">
        {onSkip && (
          <motion.button
            onClick={onSkip}
            className="flex-1 py-3 px-4 border border-gray-200 text-gray-600 rounded-lg font-medium"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            Pular
          </motion.button>
        )}
        
        <motion.button
          onClick={handleSubmit}
          disabled={rating === 0 || isSubmitting}
          className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-4 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          whileHover={{ scale: rating > 0 ? 1.02 : 1 }}
          whileTap={{ scale: rating > 0 ? 0.98 : 1 }}
        >
          {isSubmitting ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <>
              <Send className="w-4 h-4" />
              <span>Enviar</span>
            </>
          )}
        </motion.button>
      </div>
    </motion.div>
  )
}

export default RideRating
