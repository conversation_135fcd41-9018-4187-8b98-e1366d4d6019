import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContextSimple';
import { NotificationProvider, useNotifications } from './contexts/NotificationContext';
import ErrorBoundary from './components/ErrorBoundary';
import { NotificationContainer } from './components/NotificationToast';
import SystemMonitor from './components/SystemMonitor';
import { ProtectedRoute } from './components/ProtectedRoute';
import { MockupContainer } from './components/MockupContainer';

// Páginas Mobile (funcionam perfeitamente)
import LoginMobile from './pages/LoginMobile';
import RegisterMobile from './pages/RegisterMobile';
import DashboardMobile from './pages/DashboardMobile';

// Páginas do Fluxo de Solicitação de Corrida (5 páginas separadas)
import MapSelectionPage from './pages/ride-request/MapSelectionPage';
import TripDetailsPage from './pages/ride-request/TripDetailsPage';
import WaitingDriverPage from './pages/ride-request/WaitingDriverPage';
import RidingPage from './pages/ride-request/RidingPage';
import RatingPage from './pages/ride-request/RatingPage';

// Componente interno para gerenciar notificações
const AppContent = () => {
  const { notifications, removeNotification } = useNotifications();

  return (
    <>
      <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <Routes>
          {/* 🎯 ROTAS SIMPLES - PÁGINAS MOBILE COM MOCKUP AUTOMÁTICO */}
          <Route path="/" element={<MockupContainer><LoginMobile /></MockupContainer>} />
          <Route path="/login" element={<MockupContainer><LoginMobile /></MockupContainer>} />
          <Route path="/register" element={<MockupContainer><RegisterMobile /></MockupContainer>} />

          {/* ROTAS PROTEGIDAS */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <MockupContainer><DashboardMobile /></MockupContainer>
            </ProtectedRoute>
          } />

          {/* 🚗 FLUXO DE SOLICITAÇÃO DE CORRIDA - 5 PÁGINAS SEPARADAS */}

          {/* 1ª Página: Seleção de Destino no Mapa */}
          <Route path="/ride-request/map" element={
            <ProtectedRoute>
              <MockupContainer><MapSelectionPage /></MockupContainer>
            </ProtectedRoute>
          } />

          {/* 2ª Página: Detalhes da Corrida */}
          <Route path="/ride-request/details" element={
            <ProtectedRoute>
              <MockupContainer><TripDetailsPage /></MockupContainer>
            </ProtectedRoute>
          } />

          {/* 3ª Página: Aguardando Motorista */}
          <Route path="/ride-request/waiting" element={
            <ProtectedRoute>
              <MockupContainer><WaitingDriverPage /></MockupContainer>
            </ProtectedRoute>
          } />

          {/* 4ª Página: Corrida em Andamento */}
          <Route path="/ride-request/riding" element={
            <ProtectedRoute>
              <MockupContainer><RidingPage /></MockupContainer>
            </ProtectedRoute>
          } />

          {/* 5ª Página: Avaliação do Motorista */}
          <Route path="/ride-request/rating" element={
            <ProtectedRoute>
              <MockupContainer><RatingPage /></MockupContainer>
            </ProtectedRoute>
          } />

          {/* FALLBACK */}
          <Route path="*" element={<MockupContainer><LoginMobile /></MockupContainer>} />
        </Routes>
      </Router>
      <NotificationContainer
        notifications={notifications}
        onClose={removeNotification}
      />
      <SystemMonitor />
    </>
  );
};

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <NotificationProvider>
          <AppContent />
        </NotificationProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App
