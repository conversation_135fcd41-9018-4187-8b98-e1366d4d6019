import{r as t}from"./vendor-BXK_cukq.js";var e={exports:{}},n={},i=t,s=Symbol.for("react.element"),o=Symbol.for("react.fragment"),r=Object.prototype.hasOwnProperty,a=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(t,e,n){var i,o={},u=null,c=null;for(i in void 0!==n&&(u=""+n),void 0!==e.key&&(u=""+e.key),void 0!==e.ref&&(c=e.ref),e)r.call(e,i)&&!l.hasOwnProperty(i)&&(o[i]=e[i]);if(t&&t.defaultProps)for(i in e=t.defaultProps)void 0===o[i]&&(o[i]=e[i]);return{$$typeof:s,type:t,key:u,ref:c,props:o,_owner:a.current}}n.Fragment=o,n.jsx=u,n.jsxs=u,e.exports=n;var c=e.exports;const h=t.createContext({});function d(e){const n=t.useRef(null);return null===n.current&&(n.current=e()),n.current}const p=t.createContext(null),m=t.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class f extends t.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function v({children:e,isPresent:n}){const i=t.useId(),s=t.useRef(null),o=t.useRef({width:0,height:0,top:0,left:0}),{nonce:r}=t.useContext(m);return t.useInsertionEffect((()=>{const{width:t,height:e,top:a,left:l}=o.current;if(n||!s.current||!t||!e)return;s.current.dataset.motionPopId=i;const u=document.createElement("style");return r&&(u.nonce=r),document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`\n          [data-motion-pop-id="${i}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            top: ${a}px !important;\n            left: ${l}px !important;\n          }\n        `),()=>{document.head.removeChild(u)}}),[n]),c.jsx(f,{isPresent:n,childRef:s,sizeRef:o,children:t.cloneElement(e,{ref:s})})}const g=({children:e,initial:n,isPresent:i,onExitComplete:s,custom:o,presenceAffectsLayout:r,mode:a})=>{const l=d(y),u=t.useId(),h=t.useCallback((t=>{l.set(t,!0);for(const e of l.values())if(!e)return;s&&s()}),[l,s]),m=t.useMemo((()=>({id:u,initial:n,isPresent:i,custom:o,onExitComplete:h,register:t=>(l.set(t,!1),()=>l.delete(t))})),r?[Math.random(),h]:[i,h]);return t.useMemo((()=>{l.forEach(((t,e)=>l.set(e,!1)))}),[i]),t.useEffect((()=>{!i&&!l.size&&s&&s()}),[i]),"popLayout"===a&&(e=c.jsx(v,{isPresent:i,children:e})),c.jsx(p.Provider,{value:m,children:e})};function y(){return new Map}function x(e=!0){const n=t.useContext(p);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:o}=n,r=t.useId();t.useEffect((()=>{e&&o(r)}),[e]);const a=t.useCallback((()=>e&&s&&s(r)),[r,s,e]);return!i&&s?[!1,a]:[!0]}const P=t=>t.key||"";function w(e){const n=[];return t.Children.forEach(e,(e=>{t.isValidElement(e)&&n.push(e)})),n}const T="undefined"!=typeof window,S=T?t.useLayoutEffect:t.useEffect,b=({children:e,custom:n,initial:i=!0,onExitComplete:s,presenceAffectsLayout:o=!0,mode:r="sync",propagate:a=!1})=>{const[l,u]=x(a),p=t.useMemo((()=>w(e)),[e]),m=a&&!l?[]:p.map(P),f=t.useRef(!0),v=t.useRef(p),y=d((()=>new Map)),[T,b]=t.useState(p),[A,E]=t.useState(p);S((()=>{f.current=!1,v.current=p;for(let t=0;t<A.length;t++){const e=P(A[t]);m.includes(e)?y.delete(e):!0!==y.get(e)&&y.set(e,!1)}}),[A,m.length,m.join("-")]);const V=[];if(p!==T){let t=[...p];for(let e=0;e<A.length;e++){const n=A[e],i=P(n);m.includes(i)||(t.splice(e,0,n),V.push(n))}return"wait"===r&&V.length&&(t=V),E(w(t)),void b(p)}const{forceRender:C}=t.useContext(h);return c.jsx(c.Fragment,{children:A.map((t=>{const e=P(t),h=!(a&&!l)&&(p===A||m.includes(e));return c.jsx(g,{isPresent:h,initial:!(f.current&&!i)&&void 0,custom:h?void 0:n,presenceAffectsLayout:o,mode:r,onExitComplete:h?void 0:()=>{if(!y.has(e))return;y.set(e,!0);let t=!0;y.forEach((e=>{e||(t=!1)})),t&&(null==C||C(),E(v.current),a&&(null==u||u()),s&&s())},children:t},e)}))})},A=t=>t;let E=A;function V(t){let e;return()=>(void 0===e&&(e=t()),e)}const C=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i},M=t=>1e3*t,D=t=>t/1e3,R=!1;const k=["read","resolveKeyframes","update","preRender","render","postRender"];function L(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=k.reduce(((t,e)=>(t[e]=function(t){let e=new Set,n=new Set,i=!1,s=!1;const o=new WeakSet;let r={delta:0,timestamp:0,isProcessing:!1};function a(e){o.has(e)&&(l.schedule(e),t()),e(r)}const l={schedule:(t,s=!1,r=!1)=>{const a=r&&i?e:n;return s&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{r=t,i?s=!0:(i=!0,[e,n]=[n,e],e.forEach(a),e.clear(),i=!1,s&&(s=!1,l.process(t)))}};return l}(o),t)),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:d}=r,p=()=>{const o=performance.now();n=!1,s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),h.process(s),d.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(p))};return{schedule:k.reduce(((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(p)),a.schedule(e,o,r)),e}),{}),cancel:t=>{for(let e=0;e<k.length;e++)r[k[e]].cancel(t)},state:s,steps:r}}const{schedule:j,cancel:F,state:B,steps:O}=L("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:A,!0),I=t.createContext({strict:!1}),U={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},N={};for(const ca in U)N[ca]={isEnabled:t=>U[ca].some((e=>!!t[e]))};const $=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function W(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||$.has(t)}let z=t=>!W(t);try{(K=require("@emotion/is-prop-valid").default)&&(z=t=>t.startsWith("on")?!W(t):K(t))}catch(ua){}var K;function Y(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy(((...e)=>t(...e)),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const X=t.createContext({});function H(t){return"string"==typeof t||Array.isArray(t)}function _(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}const G=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],q=["initial",...G];function Z(t){return _(t.animate)||q.some((e=>H(t[e])))}function J(t){return Boolean(Z(t)||t.variants)}function Q(e){const{initial:n,animate:i}=function(t,e){if(Z(t)){const{initial:e,animate:n}=t;return{initial:!1===e||H(e)?e:void 0,animate:H(n)?n:void 0}}return!1!==t.inherit?e:{}}(e,t.useContext(X));return t.useMemo((()=>({initial:n,animate:i})),[tt(n),tt(i)])}function tt(t){return Array.isArray(t)?t.join(" "):t}const et=Symbol.for("motionComponentSymbol");function nt(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function it(e,n,i){return t.useCallback((t=>{t&&e.onMount&&e.onMount(t),n&&(t?n.mount(t):n.unmount()),i&&("function"==typeof i?i(t):nt(i)&&(i.current=t))}),[n])}const st=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),ot="data-"+st("framerAppearId"),{schedule:rt}=L(queueMicrotask,!1),at=t.createContext({});function lt(e,n,i,s,o){var r,a;const{visualElement:l}=t.useContext(X),u=t.useContext(I),c=t.useContext(p),h=t.useContext(m).reducedMotion,d=t.useRef(null);s=s||u.renderer,!d.current&&s&&(d.current=s(e,{visualState:n,parent:l,props:i,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:h}));const f=d.current,v=t.useContext(at);!f||f.projection||!o||"html"!==f.type&&"svg"!==f.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:ut(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&nt(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,layoutScroll:l,layoutRoot:u})}(d.current,i,o,v);const g=t.useRef(!1);t.useInsertionEffect((()=>{f&&g.current&&f.update(i,c)}));const y=i[ot],x=t.useRef(Boolean(y)&&!(null===(r=window.MotionHandoffIsComplete)||void 0===r?void 0:r.call(window,y))&&(null===(a=window.MotionHasOptimisedAnimation)||void 0===a?void 0:a.call(window,y)));return S((()=>{f&&(g.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),rt.render(f.render),x.current&&f.animationState&&f.animationState.animateChanges())})),t.useEffect((()=>{f&&(!x.current&&f.animationState&&f.animationState.animateChanges(),x.current&&(queueMicrotask((()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,y)})),x.current=!1))})),f}function ut(t){if(t)return!1!==t.options.allowProjection?t.projection:ut(t.parent)}function ct({preloadedFeatures:e,createVisualElement:n,useRender:i,useVisualState:s,Component:o}){var r,a;function l(e,r){let a;const l={...t.useContext(m),...e,layoutId:ht(e)},{isStatic:u}=l,h=Q(e),d=s(e,u);if(!u&&T){t.useContext(I).strict;const e=function(t){const{drag:e,layout:n}=N;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==n?void 0:n.isEnabled(t))?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(l);a=e.MeasureLayout,h.visualElement=lt(o,d,l,n,e.ProjectionNode)}return c.jsxs(X.Provider,{value:h,children:[a&&h.visualElement?c.jsx(a,{visualElement:h.visualElement,...l}):null,i(o,e,it(d,h.visualElement,r),d,u,h.visualElement)]})}e&&function(t){for(const e in t)N[e]={...N[e],...t[e]}}(e),l.displayName=`motion.${"string"==typeof o?o:`create(${null!==(a=null!==(r=o.displayName)&&void 0!==r?r:o.name)&&void 0!==a?a:""})`}`;const u=t.forwardRef(l);return u[et]=o,u}function ht({layoutId:e}){const n=t.useContext(h).id;return n&&void 0!==e?n+"-"+e:e}const dt=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function pt(t){return"string"==typeof t&&!t.includes("-")&&!!(dt.indexOf(t)>-1||/[A-Z]/u.test(t))}function mt(t){const e=[{},{}];return null==t||t.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function ft(t,e,n,i){if("function"==typeof e){const[s,o]=mt(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=mt(i);e=e(void 0!==n?n:t.custom,s,o)}return e}const vt=t=>Array.isArray(t),gt=t=>Boolean(t&&t.getVelocity);function yt(t){const e=gt(t)?t.get():t;return n=e,Boolean(n&&"object"==typeof n&&n.mix&&n.toValue)?e.toValue():e;var n}const xt=e=>(n,i)=>{const s=t.useContext(X),o=t.useContext(p),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:n},i,s,o){const r={latestValues:Pt(i,s,o,t),renderState:e()};return n&&(r.onMount=t=>n({props:i,current:t,...r}),r.onUpdate=t=>n(t)),r}(e,n,s,o);return i?r():d(r)};function Pt(t,e,n,i){const s={},o=i(t,{});for(const d in o)s[d]=yt(o[d]);let{initial:r,animate:a}=t;const l=Z(t),u=J(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!_(h)){const e=Array.isArray(h)?h:[h];for(let n=0;n<e.length;n++){const i=ft(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const i in n){let t=n[i];if(Array.isArray(t)){t=t[c?t.length-1:0]}null!==t&&(s[i]=t)}for(const i in t)s[i]=t[i]}}}return s}const wt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Tt=new Set(wt),St=t=>e=>"string"==typeof e&&e.startsWith(t),bt=St("--"),At=St("var(--"),Et=t=>!!At(t)&&Vt.test(t.split("/*")[0].trim()),Vt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Ct=(t,e)=>e&&"number"==typeof t?e.transform(t):t,Mt=(t,e,n)=>n>e?e:n<t?t:n,Dt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Rt={...Dt,transform:t=>Mt(0,1,t)},kt={...Dt,default:1},Lt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),jt=Lt("deg"),Ft=Lt("%"),Bt=Lt("px"),Ot=Lt("vh"),It=Lt("vw"),Ut={...Ft,parse:t=>Ft.parse(t)/100,transform:t=>Ft.transform(100*t)},Nt={borderWidth:Bt,borderTopWidth:Bt,borderRightWidth:Bt,borderBottomWidth:Bt,borderLeftWidth:Bt,borderRadius:Bt,radius:Bt,borderTopLeftRadius:Bt,borderTopRightRadius:Bt,borderBottomRightRadius:Bt,borderBottomLeftRadius:Bt,width:Bt,maxWidth:Bt,height:Bt,maxHeight:Bt,top:Bt,right:Bt,bottom:Bt,left:Bt,padding:Bt,paddingTop:Bt,paddingRight:Bt,paddingBottom:Bt,paddingLeft:Bt,margin:Bt,marginTop:Bt,marginRight:Bt,marginBottom:Bt,marginLeft:Bt,backgroundPositionX:Bt,backgroundPositionY:Bt},$t={rotate:jt,rotateX:jt,rotateY:jt,rotateZ:jt,scale:kt,scaleX:kt,scaleY:kt,scaleZ:kt,skew:jt,skewX:jt,skewY:jt,distance:Bt,translateX:Bt,translateY:Bt,translateZ:Bt,x:Bt,y:Bt,z:Bt,perspective:Bt,transformPerspective:Bt,opacity:Rt,originX:Ut,originY:Ut,originZ:Bt},Wt={...Dt,transform:Math.round},zt={...Nt,...$t,zIndex:Wt,size:Bt,fillOpacity:Rt,strokeOpacity:Rt,numOctaves:Wt},Kt={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Yt=wt.length;function Xt(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const t=e[l];if(Tt.has(l))r=!0;else if(bt(l))s[l]=t;else{const e=Ct(t,zt[l]);l.startsWith("origin")?(a=!0,o[l]=e):i[l]=e}}if(e.transform||(r||n?i.transform=function(t,e,n){let i="",s=!0;for(let o=0;o<Yt;o++){const r=wt[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=Ct(a,zt[r]);l||(s=!1,i+=`${Kt[r]||r}(${t}) `),n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}const Ht={offset:"stroke-dashoffset",array:"stroke-dasharray"},_t={offset:"strokeDashoffset",array:"strokeDasharray"};function Gt(t,e,n){return"string"==typeof t?t:Bt.transform(e+n*t)}function qt(t,{attrX:e,attrY:n,attrScale:i,originX:s,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...u},c,h){if(Xt(t,u,h),c)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:d,style:p,dimensions:m}=t;d.transform&&(m&&(p.transform=d.transform),delete d.transform),m&&(void 0!==s||void 0!==o||p.transform)&&(p.transformOrigin=function(t,e,n){return`${Gt(e,t.x,t.width)} ${Gt(n,t.y,t.height)}`}(m,void 0!==s?s:.5,void 0!==o?o:.5)),void 0!==e&&(d.x=e),void 0!==n&&(d.y=n),void 0!==i&&(d.scale=i),void 0!==r&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Ht:_t;t[o.offset]=Bt.transform(-i);const r=Bt.transform(e),a=Bt.transform(n);t[o.array]=`${r} ${a}`}(d,r,a,l,!1)}const Zt=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Jt=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Qt=t=>"string"==typeof t&&"svg"===t.toLowerCase();function te(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const o in n)t.style.setProperty(o,n[o])}const ee=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ne(t,e,n,i){te(t,e,void 0,i);for(const s in e.attrs)t.setAttribute(ee.has(s)?s:st(s),e.attrs[s])}const ie={};function se(t,{layout:e,layoutId:n}){return Tt.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!ie[t]||"opacity"===t)}function oe(t,e,n){var i;const{style:s}=t,o={};for(const r in s)(gt(s[r])||e.style&&gt(e.style[r])||se(r,t)||void 0!==(null===(i=null==n?void 0:n.getValue(r))||void 0===i?void 0:i.liveStyle))&&(o[r]=s[r]);return o}function re(t,e,n){const i=oe(t,e,n);for(const s in t)if(gt(t[s])||gt(e[s])){i[-1!==wt.indexOf(s)?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s]=t[s]}return i}const ae=["x","y","width","height","cx","cy","r"],le={useVisualState:xt({scrapeMotionValuesFromProps:re,createRenderState:Jt,onUpdate:({props:t,prevProps:e,current:n,renderState:i,latestValues:s})=>{if(!n)return;let o=!!t.drag;if(!o)for(const a in s)if(Tt.has(a)){o=!0;break}if(!o)return;let r=!e;if(e)for(let a=0;a<ae.length;a++){const n=ae[a];t[n]!==e[n]&&(r=!0)}r&&j.read((()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(n){e.dimensions={x:0,y:0,width:0,height:0}}}(n,i),j.render((()=>{qt(i,s,Qt(n.tagName),t.transformTemplate),ne(n,i)}))}))}})},ue={useVisualState:xt({scrapeMotionValuesFromProps:oe,createRenderState:Zt})};function ce(t,e,n){for(const i in e)gt(e[i])||se(i,n)||(t[i]=e[i])}function he(e,n){const i={};return ce(i,e.style||{},e),Object.assign(i,function({transformTemplate:e},n){return t.useMemo((()=>{const t={style:{},transform:{},transformOrigin:{},vars:{}};return Xt(t,n,e),Object.assign({},t.vars,t.style)}),[n])}(e,n)),i}function de(t,e){const n={},i=he(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}function pe(e,n,i,s){const o=t.useMemo((()=>{const t={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return qt(t,n,Qt(s),e.transformTemplate),{...t.attrs,style:{...t.style}}}),[n]);if(e.style){const t={};ce(t,e.style,e),o.style={...t,...o.style}}return o}function me(e=!1){return(n,i,s,{latestValues:o},r)=>{const a=(pt(n)?pe:de)(i,o,r,n),l=function(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(z(s)||!0===n&&W(s)||!e&&!W(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}(i,"string"==typeof n,e),u=n!==t.Fragment?{...l,...a,ref:s}:{},{children:c}=i,h=t.useMemo((()=>gt(c)?c.get():c),[c]);return t.createElement(n,{...u,children:h})}}function fe(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return ct({...pt(n)?le:ue,preloadedFeatures:t,useRender:me(i),createVisualElement:e,Component:n})}}function ve(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}function ge(t,e,n){const i=t.getProps();return ft(i,e,void 0!==n?n:i.custom,t)}const ye=V((()=>void 0!==window.ScrollTimeline));class xe{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map((t=>"finished"in t?t.finished:t)))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t,e){const n=this.animations.map((n=>ye()&&n.attachTimeline?n.attachTimeline(t):"function"==typeof e?e(n):void 0));return()=>{n.forEach(((t,e)=>{t&&t(),this.animations[e].stop()}))}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach((e=>e[t]()))}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class Pe extends xe{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function we(t,e){return t?t[e]||t.default||t:void 0}const Te=2e4;function Se(t){let e=0;let n=t.next(e);for(;!n.done&&e<Te;)e+=50,n=t.next(e);return e>=Te?1/0:e}function be(t){return"function"==typeof t}function Ae(t,e){t.timeline=e,t.onfinish=null}const Ee=t=>Array.isArray(t)&&"number"==typeof t[0],Ve={linearEasing:void 0};function Ce(t,e){const n=V(t);return()=>{var t;return null!==(t=Ve[e])&&void 0!==t?t:n()}}const Me=Ce((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),De=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let o=0;o<s;o++)i+=t(C(0,s-1,o))+", ";return`linear(${i.substring(0,i.length-2)})`};function Re(t){return Boolean("function"==typeof t&&Me()||!t||"string"==typeof t&&(t in Le||Me())||Ee(t)||Array.isArray(t)&&t.every(Re))}const ke=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Le={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ke([0,.65,.55,1]),circOut:ke([.55,0,1,.45]),backIn:ke([.31,.01,.66,-.59]),backOut:ke([.33,1.53,.69,.99])};function je(t,e){return t?"function"==typeof t&&Me()?De(t,e):Ee(t)?ke(t):Array.isArray(t)?t.map((t=>je(t,e)||Le.easeOut)):Le[t]:void 0}const Fe={x:!1,y:!1};function Be(){return Fe.x||Fe.y}function Oe(t,e){const n=function(t){if(t instanceof Element)return[t];if("string"==typeof t){const e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Ie(t){return e=>{"touch"===e.pointerType||Be()||t(e)}}const Ue=(t,e)=>!!e&&(t===e||Ue(t,e.parentElement)),Ne=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,$e=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const We=new WeakSet;function ze(t){return e=>{"Enter"===e.key&&t(e)}}function Ke(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Ye(t){return Ne(t)&&!Be()}function Xe(t,e,n={}){const[i,s,o]=Oe(t,n),r=t=>{const i=t.currentTarget;if(!Ye(t)||We.has(i))return;We.add(i);const o=e(t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),Ye(t)&&We.has(i)&&(We.delete(i),"function"==typeof o&&o(t,{success:e}))},a=t=>{r(t,n.useGlobalTarget||Ue(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach((t=>{(function(t){return $e.has(t.tagName)||-1!==t.tabIndex})(t)||null!==t.getAttribute("tabindex")||(t.tabIndex=0);(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=ze((()=>{if(We.has(n))return;Ke(n,"down");const t=ze((()=>{Ke(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>Ke(n,"cancel")),e)}));n.addEventListener("keydown",i,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",i)),e)})(t,s)),s)})),o}const He=new Set(["width","height","top","left","right","bottom",...wt]);let _e;function Ge(){_e=void 0}const qe={now:()=>(void 0===_e&&qe.set(B.isProcessing||R?B.timestamp:performance.now()),_e),set:t=>{_e=t,queueMicrotask(Ge)}};function Ze(t,e){-1===t.indexOf(e)&&t.push(e)}function Je(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class Qe{constructor(){this.subscriptions=[]}add(t){return Ze(this.subscriptions,t),()=>Je(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function tn(t,e){return e?t*(1e3/e):0}class en{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=qe.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=qe.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new Qe);const n=this.events[t].add(e);return"change"===t?()=>{n(),j.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=qe.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return tn(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function nn(t,e){return new en(t,e)}function sn(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,nn(n))}function on(t,e){const n=t.getValue("willChange");if(i=n,Boolean(gt(i)&&i.add))return n.add(e);var i}function rn(t){return t.props[ot]}const an=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function ln(t,e,n,i){if(t===e&&n===i)return A;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=an(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:an(s(t),e,i)}const un=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,cn=t=>e=>1-t(1-e),hn=ln(.33,1.53,.69,.99),dn=cn(hn),pn=un(dn),mn=t=>(t*=2)<1?.5*dn(t):.5*(2-Math.pow(2,-10*(t-1))),fn=t=>1-Math.sin(Math.acos(t)),vn=cn(fn),gn=un(fn),yn=t=>/^0[^.\s]+$/u.test(t);const xn=t=>Math.round(1e5*t)/1e5,Pn=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const wn=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Tn=(t,e)=>n=>Boolean("string"==typeof n&&wn.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),Sn=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(Pn);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},bn={...Dt,transform:t=>Math.round((t=>Mt(0,255,t))(t))},An={test:Tn("rgb","red"),parse:Sn("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+bn.transform(t)+", "+bn.transform(e)+", "+bn.transform(n)+", "+xn(Rt.transform(i))+")"};const En={test:Tn("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:An.transform},Vn={test:Tn("hsl","hue"),parse:Sn("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+Ft.transform(xn(e))+", "+Ft.transform(xn(n))+", "+xn(Rt.transform(i))+")"},Cn={test:t=>An.test(t)||En.test(t)||Vn.test(t),parse:t=>An.test(t)?An.parse(t):Vn.test(t)?Vn.parse(t):En.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?An.transform(t):Vn.transform(t)},Mn=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Dn="number",Rn="color",kn=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ln(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(kn,(t=>(Cn.test(t)?(i.color.push(o),s.push(Rn),n.push(Cn.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push(Dn),n.push(parseFloat(t))),++o,"${}"))).split("${}");return{values:n,split:r,indexes:i,types:s}}function jn(t){return Ln(t).values}function Fn(t){const{split:e,types:n}=Ln(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===Dn?xn(t[o]):e===Rn?Cn.transform(t[o]):t[o]}return s}}const Bn=t=>"number"==typeof t?0:t;const On={test:function(t){var e,n;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(Pn))||void 0===e?void 0:e.length)||0)+((null===(n=t.match(Mn))||void 0===n?void 0:n.length)||0)>0},parse:jn,createTransformer:Fn,getAnimatableNone:function(t){const e=jn(t);return Fn(t)(e.map(Bn))}},In=new Set(["brightness","contrast","saturate","opacity"]);function Un(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(Pn)||[];if(!i)return t;const s=n.replace(i,"");let o=In.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Nn=/\b([a-z-]*)\(.*?\)/gu,$n={...On,getAnimatableNone:t=>{const e=t.match(Nn);return e?e.map(Un).join(" "):t}},Wn={...zt,color:Cn,backgroundColor:Cn,outlineColor:Cn,fill:Cn,stroke:Cn,borderColor:Cn,borderTopColor:Cn,borderRightColor:Cn,borderBottomColor:Cn,borderLeftColor:Cn,filter:$n,WebkitFilter:$n},zn=t=>Wn[t];function Kn(t,e){let n=zn(t);return n!==$n&&(n=On),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Yn=new Set(["auto","none","0"]);const Xn=t=>t===Dt||t===Bt,Hn=(t,e)=>parseFloat(t.split(", ")[e]),_n=(t,e)=>(n,{transform:i})=>{if("none"===i||!i)return 0;const s=i.match(/^matrix3d\((.+)\)$/u);if(s)return Hn(s[1],e);{const e=i.match(/^matrix\((.+)\)$/u);return e?Hn(e[1],t):0}},Gn=new Set(["x","y","z"]),qn=wt.filter((t=>!Gn.has(t)));const Zn={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:_n(4,13),y:_n(5,14)};Zn.translateX=Zn.x,Zn.translateY=Zn.y;const Jn=new Set;let Qn=!1,ti=!1;function ei(){if(ti){const t=Array.from(Jn).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return qn.forEach((n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{var i;null===(i=t.getValue(e))||void 0===i||i.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}ti=!1,Qn=!1,Jn.forEach((t=>t.complete())),Jn.clear()}function ni(){Jn.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(ti=!0)}))}class ii{constructor(t,e,n,i,s,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Jn.add(this),Qn||(Qn=!0,j.read(ni),j.resolveKeyframes(ei))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;for(let s=0;s<t.length;s++)if(null===t[s])if(0===s){const s=null==i?void 0:i.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Jn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Jn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const si=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),oi=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ri(t,e,n=1){const[i,s]=function(t){const e=oi.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${null!=n?n:i}`,s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return si(t)?parseFloat(t):t}return Et(s)?ri(s,e,n+1):s}const ai=t=>e=>e.test(t),li=[Dt,Bt,Ft,jt,It,Ot,{test:t=>"auto"===t,parse:t=>t}],ui=t=>li.find(ai(t));class ci extends ii{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let n=t[a];if("string"==typeof n&&(n=n.trim(),Et(n))){const i=ri(n,e.current);void 0!==i&&(t[a]=i),a===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!He.has(n)||2!==t.length)return;const[i,s]=t,o=ui(i),r=ui(s);if(o!==r)if(Xn(o)&&Xn(r))for(let a=0;a<t.length;a++){const e=t[a];"string"==typeof e&&(t[a]=parseFloat(e))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let s=0;s<t.length;s++)("number"==typeof(i=t[s])?0===i:null===i||"none"===i||"0"===i||yn(i))&&n.push(s);var i;n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!Yn.has(e)&&Ln(e).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=Kn(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Zn[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){var t;const{element:e,name:n,unresolvedKeyframes:i}=this;if(!e||!e.current)return;const s=e.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,r=i[o];i[o]=Zn[n](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach((([t,n])=>{e.getValue(t).set(n)})),this.resolveNoneKeyframes()}}const hi=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!On.test(t)&&"0"!==t||t.startsWith("url(")));function di(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=hi(s,e),a=hi(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||be(n))&&i)}const pi=t=>null!==t;function mi(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(pi),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}class fi{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",...r}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=qe.now(),this.options={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,...r},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(ni(),ei()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=qe.now(),this.hasAttemptedResolve=!0;const{name:n,type:i,velocity:s,delay:o,onComplete:r,onUpdate:a,isGenerator:l}=this.options;if(!l&&!di(t,n,i,s)){if(!o)return a&&a(mi(t,this.options,e)),r&&r(),void this.resolveFinishedPromise();this.options.duration=0}const u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise((t=>{this.resolveFinishedPromise=t}))}}const vi=(t,e,n)=>t+(e-t)*n;function gi(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function yi(t,e){return n=>n>0?e:t}const xi=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},Pi=[En,An,Vn];function wi(t){const e=(n=t,Pi.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===Vn&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=gi(a,i,t+1/3),o=gi(a,i,t),r=gi(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const Ti=(t,e)=>{const n=wi(t),i=wi(e);if(!n||!i)return yi(t,e);const s={...n};return t=>(s.red=xi(n.red,i.red,t),s.green=xi(n.green,i.green,t),s.blue=xi(n.blue,i.blue,t),s.alpha=vi(n.alpha,i.alpha,t),An.transform(s))},Si=(t,e)=>n=>e(t(n)),bi=(...t)=>t.reduce(Si),Ai=new Set(["none","hidden"]);function Ei(t,e){return n=>vi(t,e,n)}function Vi(t){return"number"==typeof t?Ei:"string"==typeof t?Et(t)?yi:Cn.test(t)?Ti:Di:Array.isArray(t)?Ci:"object"==typeof t?Cn.test(t)?Ti:Mi:yi}function Ci(t,e){const n=[...t],i=n.length,s=t.map(((t,n)=>Vi(t)(t,e[n])));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function Mi(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=Vi(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const Di=(t,e)=>{const n=On.createTransformer(e),i=Ln(t),s=Ln(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?Ai.has(t)&&!s.values.length||Ai.has(e)&&!i.values.length?function(t,e){return Ai.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):bi(Ci(function(t,e){var n;const i=[],s={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const r=e.types[o],a=t.indexes[r][s[r]],l=null!==(n=t.values[a])&&void 0!==n?n:0;i[o]=l,s[r]++}return i}(i,s),s.values),n):yi(t,e)};function Ri(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return vi(t,e,n);return Vi(t)(t,e)}function ki(t,e,n){const i=Math.max(e-5,0);return tn(n-t(i),e-i)}const Li=100,ji=10,Fi=1,Bi=0,Oi=800,Ii=.3,Ui=.3,Ni={granular:.01,default:2},$i={granular:.005,default:.5},Wi=.01,zi=10,Ki=.05,Yi=1;function Xi({duration:t=Oi,bounce:e=Ii,velocity:n=Bi,mass:i=Fi}){let s,o,r=1-e;r=Mt(Ki,Yi,r),t=Mt(Wi,zi,D(t)),r<1?(s=e=>{const i=e*r,s=i*t;return.001-(i-n)/_i(e,r)*Math.exp(-s)},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=_i(Math.pow(e,2),r);return(.001-s(e)>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let s=1;s<Hi;s++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=M(t),isNaN(a))return{stiffness:Li,damping:ji,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const Hi=12;function _i(t,e){return t*Math.sqrt(1-e*e)}const Gi=["duration","bounce"],qi=["stiffness","damping","mass"];function Zi(t,e){return e.some((e=>void 0!==t[e]))}function Ji(t=Ui,e=Ii){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:Bi,stiffness:Li,damping:ji,mass:Fi,isResolvedFromDuration:!1,...t};if(!Zi(t,qi)&&Zi(t,Gi))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*Mt(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:Fi,stiffness:s,damping:o}}else{const n=Xi(t);e={...e,...n,mass:Fi},e.isResolvedFromDuration=!0}return e}({...n,velocity:-D(n.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*c)),v=r-o,g=D(Math.sqrt(l/c)),y=Math.abs(v)<5;let x;if(i||(i=y?Ni.granular:Ni.default),s||(s=y?$i.granular:$i.default),f<1){const t=_i(g,f);x=e=>{const n=Math.exp(-f*g*e);return r-n*((m+f*g*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}}else if(1===f)x=t=>r-Math.exp(-g*t)*(v+(m+g*v)*t);else{const t=g*Math.sqrt(f*f-1);x=e=>{const n=Math.exp(-f*g*e),i=Math.min(t*e,300);return r-n*((m+f*g*v)*Math.sinh(i)+t*v*Math.cosh(i))/t}}const P={calculatedDuration:p&&h||null,next:t=>{const e=x(t);if(p)a.done=t>=h;else{let n=0;f<1&&(n=0===t?M(m):ki(x,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(Se(P),Te),e=De((e=>P.next(t*e).value),t,30);return t+"ms "+e}};return P}function Qi({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,v=void 0===r?f:r(f);v!==f&&(m=v-h);const g=t=>-m*Math.exp(-t/i),y=t=>v+g(t),x=t=>{const e=g(t),n=y(t);d.done=Math.abs(e)<=u,d.value=d.done?v:n};let P,w;const T=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(P=t,w=Ji({keyframes:[d.value,p(d.value)],velocity:ki(y,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==P||(e=!0,x(t),T(t)),void 0!==P&&t>=P?w.next(t-P):(!e&&x(t),d)}}}const ts=ln(.42,0,1,1),es=ln(0,0,.58,1),ns=ln(.42,0,.58,1),is={linear:A,easeIn:ts,easeInOut:ns,easeOut:es,circIn:fn,circInOut:gn,circOut:vn,backIn:dn,backInOut:pn,backOut:hn,anticipate:mn},ss=t=>{if(Ee(t)){E(4===t.length);const[e,n,i,s]=t;return ln(e,n,i,s)}return"string"==typeof t?is[t]:t};function os(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(E(o===e.length),1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||Ri,o=t.length-1;for(let r=0;r<o;r++){let n=s(t[r],t[r+1]);if(e){const t=Array.isArray(e)?e[r]||A:e;n=bi(t,n)}i.push(n)}return i}(e,i,s),l=a.length,u=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=C(t[i],t[i+1],n);return a[i](s)};return n?e=>u(Mt(t[0],t[o-1],e)):u}function rs(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=C(0,e,i);t.push(vi(n,1,s))}}(e,t.length-1),e}function as({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map(ss):ss(i),o={done:!1,value:e[0]},r=function(t,e){return t.map((t=>t*e))}(n&&n.length===e.length?n:rs(e),t),a=os(r,e,{ease:Array.isArray(s)?s:(l=e,u=s,l.map((()=>u||ns)).splice(0,l.length-1))});var l,u;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}const ls=t=>{const e=({timestamp:e})=>t(e);return{start:()=>j.update(e,!0),stop:()=>F(e),now:()=>B.isProcessing?B.timestamp:qe.now()}},us={decay:Qi,inertia:Qi,tween:as,keyframes:as,spring:Ji},cs=t=>t/100;class hs extends fi{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:t}=this.options;t&&t()};const{name:e,motionValue:n,element:i,keyframes:s}=this.options,o=(null==i?void 0:i.KeyframeResolver)||ii;this.resolver=new o(s,((t,e)=>this.onKeyframesResolved(t,e)),e,n,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:e="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=this.options,r=be(e)?e:us[e]||as;let a,l;r!==as&&"number"!=typeof t[0]&&(a=bi(cs,Ri(t[0],t[1])),t=[0,100]);const u=r({...this.options,keyframes:t});"mirror"===s&&(l=r({...this.options,keyframes:[...t].reverse(),velocity:-o})),null===u.calculatedDuration&&(u.calculatedDuration=Se(u));const{calculatedDuration:c}=u,h=c+i;return{generator:u,mirroredGenerator:l,mapPercentToKeyframes:a,calculatedDuration:c,resolvedDuration:h,totalDuration:h*(n+1)-i}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){const{resolved:n}=this;if(!n){const{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}const{finalKeyframe:i,generator:s,mirroredGenerator:o,mapPercentToKeyframes:r,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=n;if(null===this.startTime)return s.next(0);const{delay:h,repeat:d,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const v=this.currentTime-h*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=s;if(d){const t=Math.min(this.currentTime,u)/c;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,d+1);Boolean(e%2)&&("reverse"===p?(n=1-n,m&&(n-=m/c)):"mirror"===p&&(x=o)),y=Mt(0,1,n)*c}const P=g?{done:!1,value:a[0]}:x.next(y);r&&(P.value=r(P.value));let{done:w}=P;g||null===l||(w=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&void 0!==i&&(P.value=mi(a,this.options,i)),f&&f(P.value),T&&this.finish(),P}get duration(){const{resolved:t}=this;return t?D(t.calculatedDuration):0}get time(){return D(this.currentTime)}set time(t){t=M(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=D(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved)return void(this.pendingPlayState="running");if(this.isStopped)return;const{driver:t=ls,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const i=this.driver.now();null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=i):this.startTime=null!=n?n:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;this._resolved?(this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0):this.pendingPlayState="paused"}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const ds=new Set(["opacity","clipPath","filter","transform"]);function ps(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeInOut",times:l}={}){const u={[e]:n};l&&(u.offset=l);const c=je(a,s);return Array.isArray(c)&&(u.easing=c),t.animate(u,{delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"})}const ms=V((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));const fs={anticipate:mn,backInOut:pn,circInOut:gn};class vs extends fi{constructor(t){super(t);const{name:e,motionValue:n,element:i,keyframes:s}=this.options;this.resolver=new ci(s,((t,e)=>this.onKeyframesResolved(t,e)),e,n,i),this.resolver.scheduleResolve()}initPlayback(t,e){let{duration:n=300,times:i,ease:s,type:o,motionValue:r,name:a,startTime:l}=this.options;if(!r.owner||!r.owner.current)return!1;var u;if("string"==typeof s&&Me()&&s in fs&&(s=fs[s]),be((u=this.options).type)||"spring"===u.type||!Re(u.ease)){const{onComplete:e,onUpdate:r,motionValue:a,element:l,...u}=this.options,c=function(t,e){const n=new hs({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let i={done:!1,value:t[0]};const s=[];let o=0;for(;!i.done&&o<2e4;)i=n.sample(o),s.push(i.value),o+=10;return{times:void 0,keyframes:s,duration:o-10,ease:"linear"}}(t,u);1===(t=c.keyframes).length&&(t[1]=t[0]),n=c.duration,i=c.times,s=c.ease,o="keyframes"}const c=ps(r.owner.current,a,t,{...this.options,duration:n,times:i,ease:s});return c.startTime=null!=l?l:this.calcStartTime(),this.pendingTimeline?(Ae(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:n}=this.options;r.set(mi(t,this.options,e)),n&&n(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:n,times:i,type:o,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:e}=t;return D(e)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:e}=t;return D(e.currentTime||0)}set time(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.currentTime=M(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:e}=t;return e.playbackRate}set speed(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:e}=t;return e.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){const{resolved:e}=this;if(!e)return A;const{animation:n}=e;Ae(n,t)}else this.pendingTimeline=t;return A}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:e,keyframes:n,duration:i,type:s,ease:o,times:r}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){const{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,c=new hs({...u,keyframes:n,duration:i,type:s,ease:o,times:r,isGenerator:!0}),h=M(this.time);t.setWithVelocity(c.sample(h-10).value,c.sample(h).value,10)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ms()&&n&&ds.has(n)&&!a&&!l&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}}const gs={type:"spring",stiffness:500,damping:25,restSpeed:10},ys={type:"keyframes",duration:.8},xs={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Ps=(t,{keyframes:e})=>e.length>2?ys:Tt.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:gs:xs;const ws=(t,e,n,i={},s,o)=>r=>{const a=we(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=M(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(a)||(c={...c,...Ps(t,c)}),c.duration&&(c.duration=M(c.duration)),c.repeatDelay&&(c.repeatDelay=M(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(h=!0)),h&&!o&&void 0!==e.get()){const t=mi(c.keyframes,a);if(void 0!==t)return j.update((()=>{c.onUpdate(t),c.onComplete()})),new Pe([])}return!o&&vs.supports(c)?new vs(c):new hs(c)};function Ts({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function Ss(t,e,{delay:n=0,transitionOverride:i,type:s}={}){var o;let{transition:r=t.getDefaultTransition(),transitionEnd:a,...l}=e;i&&(r=i);const u=[],c=s&&t.animationState&&t.animationState.getState()[s];for(const h in l){const e=t.getValue(h,null!==(o=t.latestValues[h])&&void 0!==o?o:null),i=l[h];if(void 0===i||c&&Ts(c,h))continue;const s={delay:n,...we(r||{},h)};let a=!1;if(window.MotionHandoffAnimation){const e=rn(t);if(e){const t=window.MotionHandoffAnimation(e,h,j);null!==t&&(s.startTime=t,a=!0)}}on(t,h),e.start(ws(h,e,i,t.shouldReduceMotion&&He.has(h)?{type:!1}:s,t,a));const d=e.animation;d&&u.push(d)}return a&&Promise.all(u).then((()=>{j.update((()=>{a&&function(t,e){const n=ge(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const a in o)sn(t,a,(r=o[a],vt(r)?r[r.length-1]||0:r));var r}(t,a)}))})),u}function bs(t,e,n={}){var i;const s=ge(t,e,"exit"===n.type?null===(i=t.presenceContext)||void 0===i?void 0:i.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(o=n.transitionOverride);const r=s?()=>Promise.all(Ss(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:s=0,staggerChildren:r,staggerDirection:a}=o;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(As).forEach(((t,i)=>{t.notify("AnimationStart",e),r.push(bs(t,e,{...o,delay:n+l(i)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(r)}(t,e,s+i,r,a,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then((()=>e()))}return Promise.all([r(),a(n.delay)])}function As(t,e){return t.sortNodePosition(e)}const Es=q.length;function Vs(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&Vs(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<Es;n++){const i=q[n],s=t.props[i];(H(s)||!1===s)&&(e[i]=s)}return e}const Cs=[...G].reverse(),Ms=G.length;function Ds(t){return e=>Promise.all(e.map((({animation:e,options:n})=>function(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map((e=>bs(t,e,n)));i=Promise.all(s)}else if("string"==typeof e)i=bs(t,e,n);else{const s="function"==typeof e?ge(t,e,n.custom):e;i=Promise.all(Ss(t,s,n))}return i.then((()=>{t.notify("AnimationComplete",e)}))}(t,e,n))))}function Rs(t){let e=Ds(t),n=js(),i=!0;const s=e=>(n,i)=>{var s;const o=ge(t,i,"exit"===e?null===(s=t.presenceContext)||void 0===s?void 0:s.custom:void 0);if(o){const{transition:t,transitionEnd:e,...i}=o;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=Vs(t.parent)||{},l=[],u=new Set;let c={},h=1/0;for(let e=0;e<Ms;e++){const d=Cs[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=H(m),v=d===o?p.isActive:null;!1===v&&(h=e);let g=m===a[d]&&m!==r[d]&&f;if(g&&i&&t.manuallyAnimateOnMount&&(g=!1),p.protectedKeys={...c},!p.isActive&&null===v||!m&&!p.prevProp||_(m)||"boolean"==typeof m)continue;const y=ks(p.prevProp,m);let x=y||d===o&&p.isActive&&!g&&f||e>h&&f,P=!1;const w=Array.isArray(m)?m:[m];let T=w.reduce(s(d),{});!1===v&&(T={});const{prevResolvedValues:S={}}=p,b={...S,...T},A=e=>{x=!0,u.has(e)&&(P=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in b){const e=T[t],n=S[t];if(c.hasOwnProperty(t))continue;let i=!1;i=vt(e)&&vt(n)?!ve(e,n):e!==n,i?null!=e?A(t):u.add(t):void 0!==e&&u.has(t)?A(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=T,p.isActive&&(c={...c,...T}),i&&t.blockInitialAnimation&&(x=!1);x&&(!(g&&y)||P)&&l.push(...w.map((t=>({animation:t,options:{type:d}}))))}if(u.size){const e={};u.forEach((n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=null!=i?i:null})),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){var s;if(n[e].isActive===i)return Promise.resolve();null===(s=t.variantChildren)||void 0===s||s.forEach((t=>{var n;return null===(n=t.animationState)||void 0===n?void 0:n.setActive(e,i)})),n[e].isActive=i;const r=o(e);for(const t in n)n[t].protectedKeys={};return r},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=js(),i=!0}}}function ks(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!ve(e,t)}function Ls(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function js(){return{animate:Ls(!0),whileInView:Ls(),whileHover:Ls(),whileTap:Ls(),whileDrag:Ls(),whileFocus:Ls(),exit:Ls()}}class Fs{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Bs=0;const Os={animation:{Feature:class extends Fs{constructor(t){super(t),t.animationState||(t.animationState=Rs(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();_(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}},exit:{Feature:class extends Fs{constructor(){super(...arguments),this.id=Bs++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then((()=>e(this.id)))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}}};function Is(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function Us(t){return{point:{x:t.pageX,y:t.pageY}}}function Ns(t,e,n,i){return Is(t,e,(t=>e=>Ne(e)&&t(e,Us(e)))(n),i)}const $s=(t,e)=>Math.abs(t-e);class Ws{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Ys(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=$s(t.x,e.x),i=$s(t.y,e.y);return Math.sqrt(n**2+i**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=B;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=zs(e,this.transformPagePoint),j.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Ys("pointercancel"===t.type?this.lastMoveEventInfo:zs(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!Ne(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=zs(Us(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=B;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Ys(o,this.history)),this.removeListeners=bi(Ns(this.contextWindow,"pointermove",this.handlePointerMove),Ns(this.contextWindow,"pointerup",this.handlePointerUp),Ns(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),F(this.updatePoint)}}function zs(t,e){return e?{point:e(t.point)}:t}function Ks(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Ys({point:t},e){return{point:t,delta:Ks(t,Hs(e)),offset:Ks(t,Xs(e)),velocity:_s(e,.1)}}function Xs(t){return t[0]}function Hs(t){return t[t.length-1]}function _s(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=Hs(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>M(e)));)n--;if(!i)return{x:0,y:0};const o=D(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Gs(t){return t.max-t.min}function qs(t,e,n,i=.5){t.origin=i,t.originPoint=vi(e.min,e.max,t.origin),t.scale=Gs(n)/Gs(e),t.translate=vi(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function Zs(t,e,n,i){qs(t.x,e.x,n.x,i?i.originX:void 0),qs(t.y,e.y,n.y,i?i.originY:void 0)}function Js(t,e,n){t.min=n.min+e.min,t.max=t.min+Gs(e)}function Qs(t,e,n){t.min=e.min-n.min,t.max=t.min+Gs(e)}function to(t,e,n){Qs(t.x,e.x,n.x),Qs(t.y,e.y,n.y)}function eo(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function no(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const io=.35;function so(t,e,n){return{min:oo(t,e),max:oo(t,n)}}function oo(t,e){return"number"==typeof t?t:t[e]||0}const ro=()=>({x:{min:0,max:0},y:{min:0,max:0}});function ao(t){return[t("x"),t("y")]}function lo({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function uo(t){return void 0===t||1===t}function co({scale:t,scaleX:e,scaleY:n}){return!uo(t)||!uo(e)||!uo(n)}function ho(t){return co(t)||po(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function po(t){return mo(t.x)||mo(t.y)}function mo(t){return t&&"0%"!==t}function fo(t,e,n){return n+e*(t-n)}function vo(t,e,n,i,s){return void 0!==s&&(t=fo(t,s,i)),fo(t,n,i)+e}function go(t,e=0,n=1,i,s){t.min=vo(t.min,e,n,i,s),t.max=vo(t.max,e,n,i,s)}function yo(t,{x:e,y:n}){go(t.x,e.translate,e.scale,e.originPoint),go(t.y,n.translate,n.scale,n.originPoint)}const xo=.999999999999,Po=1.0000000000001;function wo(t,e){t.min=t.min+e,t.max=t.max+e}function To(t,e,n,i,s=.5){go(t,e,n,vi(t.min,t.max,s),i)}function So(t,e){To(t.x,e.x,e.scaleX,e.scale,e.originX),To(t.y,e.y,e.scaleY,e.scale,e.originY)}function bo(t,e){return lo(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const Ao=({current:t})=>t?t.ownerDocument.defaultView:null,Eo=new WeakMap;class Vo{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new Ws(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Us(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=n)||"y"===o?Fe[o]?null:(Fe[o]=!0,()=>{Fe[o]=!1}):Fe.x||Fe.y?null:(Fe.x=Fe.y=!0,()=>{Fe.x=Fe.y=!1}),!this.openDragLock))return;var o;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ao((t=>{let e=this.getAxisMotionValue(t).get()||0;if(Ft.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Gs(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),s&&j.postRender((()=>s(t,e))),on(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>ao((t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())}))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:Ao(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&j.postRender((()=>s(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!Co(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?vi(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?vi(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,s=this.constraints;e&&nt(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!i)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:eo(t.x,n,s),y:eo(t.y,e,i)}}(i.layoutBox,e),this.elastic=function(t=io){return!1===t?t=0:!0===t&&(t=io),{x:so(t,"left","right"),y:so(t,"top","bottom")}}(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ao((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(i.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!nt(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=bo(t,n),{scroll:s}=e;return s&&(wo(i.x,s.offset.x),wo(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:no(t.x,e.x),y:no(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=lo(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=ao((r=>{if(!Co(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)}));return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return on(this.visualElement,t),n.start(ws(t,n,0,e,this.visualElement,!1))}stopAnimation(){ao((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){ao((t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()}))}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){ao((e=>{const{drag:n}=this.getProps();if(!Co(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-vi(n,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!nt(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};ao((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=Gs(t),s=Gs(e);return s>i?n=C(e.min,e.max-i,t.min):i>s&&(n=C(t.min,t.max-s,e.min)),Mt(0,1,n)}({min:n,max:n},this.constraints[t])}}));const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),ao((e=>{if(!Co(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(vi(s,o,i[e]))}))}addListeners(){if(!this.visualElement.current)return;Eo.set(this.visualElement,this);const t=Ns(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();nt(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),j.read(e);const s=Is(window,"resize",(()=>this.scalePositionWithinConstraints())),o=n.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ao((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=io,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function Co(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const Mo=t=>(e,n)=>{t&&j.postRender((()=>t(e,n)))};const Do={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Ro(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const ko={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Bt.test(t))return t;t=parseFloat(t)}return`${Ro(t,e.target.x)}% ${Ro(t,e.target.y)}%`}},Lo={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=On.parse(t);if(s.length>5)return i;const o=On.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=vi(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};class jo extends t.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;var o;o=Bo,Object.assign(ie,o),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",(()=>{this.safeToRemove()})),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Do.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,o=n.projection;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||j.postRender((()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),rt.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Fo(e){const[n,i]=x(),s=t.useContext(h);return c.jsx(jo,{...e,layoutGroup:s,switchLayoutGroup:t.useContext(at),isPresent:n,safeToRemove:i})}const Bo={borderRadius:{...ko,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ko,borderTopRightRadius:ko,borderBottomLeftRadius:ko,borderBottomRightRadius:ko,boxShadow:Lo};const Oo=(t,e)=>t.depth-e.depth;class Io{constructor(){this.children=[],this.isDirty=!1}add(t){Ze(this.children,t),this.isDirty=!0}remove(t){Je(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Oo),this.isDirty=!1,this.children.forEach(t)}}const Uo=["TopLeft","TopRight","BottomLeft","BottomRight"],No=Uo.length,$o=t=>"string"==typeof t?parseFloat(t):t,Wo=t=>"number"==typeof t||Bt.test(t);function zo(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Ko=Xo(0,.5,vn),Yo=Xo(.5,.95,A);function Xo(t,e,n){return i=>i<t?0:i>e?1:n(C(t,e,i))}function Ho(t,e){t.min=e.min,t.max=e.max}function _o(t,e){Ho(t.x,e.x),Ho(t.y,e.y)}function Go(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function qo(t,e,n,i,s){return t=fo(t-=e,1/n,i),void 0!==s&&(t=fo(t,1/s,i)),t}function Zo(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){Ft.test(e)&&(e=parseFloat(e),e=vi(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=vi(o.min,o.max,i);t===o&&(a-=e),t.min=qo(t.min,e,n,a,s),t.max=qo(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const Jo=["x","scaleX","originX"],Qo=["y","scaleY","originY"];function tr(t,e,n,i){Zo(t.x,e,Jo,n?n.x:void 0,i?i.x:void 0),Zo(t.y,e,Qo,n?n.y:void 0,i?i.y:void 0)}function er(t){return 0===t.translate&&1===t.scale}function nr(t){return er(t.x)&&er(t.y)}function ir(t,e){return t.min===e.min&&t.max===e.max}function sr(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function or(t,e){return sr(t.x,e.x)&&sr(t.y,e.y)}function rr(t){return Gs(t.x)/Gs(t.y)}function ar(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class lr{constructor(){this.members=[]}add(t){Ze(this.members,t),t.scheduleRender()}remove(t){if(Je(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let n;for(let i=e;i>=0;i--){const t=this.members[i];if(!1!==t.isPresent){n=t;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const ur={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},cr="undefined"!=typeof window&&void 0!==window.MotionDebug,hr=["","X","Y","Z"],dr={visibility:"hidden"};let pr=0;function mr(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function fr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=rn(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",j,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&fr(i)}function vr({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=(null==e?void 0:e())){this.id=pr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,cr&&(ur.totalNodes=ur.resolvedTargetDeltas=ur.recalculatedProjection=0),this.nodes.forEach(xr),this.nodes.forEach(Er),this.nodes.forEach(Vr),this.nodes.forEach(Pr),cr&&window.MotionDebug.record(ur)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new Io)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new Qe),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,n=this.root.hasTreeAnimated){if(this.instance)return;var i;this.isSVG=(i=e)instanceof SVGElement&&"svg"!==i.tagName,this.instance=e;const{layoutId:s,layout:o,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(o||s)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=qe.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(F(i),t(o-e))};return j.read(i,!0),()=>F(i)}(i,250),Do.hasAnimatedSinceResize&&(Do.hasAnimatedSinceResize=!1,this.nodes.forEach(Ar))}))}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||o)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||r.getDefaultTransition()||Lr,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!or(this.targetLayout,i)||n,u=!e&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...we(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||Ar(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,F(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Cr),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&fr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let s=0;s<this.path.length;s++){const t=this.path[s];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Tr);this.isUpdating||this.nodes.forEach(Sr),this.isUpdating=!1,this.nodes.forEach(br),this.nodes.forEach(gr),this.nodes.forEach(yr),this.clearAllSnapshots();const t=qe.now();B.delta=Mt(0,1e3/60,t-B.timestamp),B.timestamp=t,B.isProcessing=!0,O.update.process(B),O.preRender.process(B),O.render.process(B),B.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rt.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(wr),this.sharedNodes.forEach(Mr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,j.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){j.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nr(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&(e||ho(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Br((i=n).x),Br(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const n=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(Ir))){const{scroll:t}=this.root;t&&(wo(n.x,t.offset.x),wo(n.y,t.offset.y))}return n}removeElementScroll(t){var e;const n={x:{min:0,max:0},y:{min:0,max:0}};if(_o(n,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return n;for(let i=0;i<this.path.length;i++){const e=this.path[i],{scroll:s,options:o}=e;e!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&_o(n,t),wo(n.x,s.offset.x),wo(n.y,s.offset.y))}return n}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};_o(n,t);for(let i=0;i<this.path.length;i++){const t=this.path[i];!e&&t.options.layoutScroll&&t.scroll&&t!==t.root&&So(n,{x:-t.scroll.offset.x,y:-t.scroll.offset.y}),ho(t.latestValues)&&So(n,t.latestValues)}return ho(this.latestValues)&&So(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};_o(e,t);for(let n=0;n<this.path.length;n++){const t=this.path[n];if(!t.instance)continue;if(!ho(t.latestValues))continue;co(t.latestValues)&&t.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};_o(i,t.measurePageBox()),tr(e,t.latestValues,t.snapshot?t.snapshot.layoutBox:void 0,i)}return ho(this.latestValues)&&tr(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==B.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==n;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:s,layoutId:o}=this.options;if(this.layout&&(s||o)){if(this.resolvedRelativeTargetAt=B.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},to(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),_o(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var r,a,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,l=this.relativeParent.target,Js(r.x,a.x,l.x),Js(r.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):_o(this.target,this.layout.layoutBox),yo(this.target,this.targetDelta)):_o(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},to(this.relativeTargetOrigin,this.target,t.target),_o(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}cr&&ur.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!co(this.parent.latestValues)&&!po(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;const e=this.getLead(),n=Boolean(this.resumingFrom)||this!==e;let i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===B.timestamp&&(i=!1),i)return;const{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!o)return;_o(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,a=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&So(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,yo(t,r)),i&&ho(o.latestValues)&&So(t,o.latestValues))}e.x<Po&&e.x>xo&&(e.x=1),e.y<Po&&e.y>xo&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,n),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox,e.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=e;l?(this.projectionDelta&&this.prevProjectionDelta?(Go(this.prevProjectionDelta.x,this.projectionDelta.x),Go(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Zs(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&ar(this.projectionDelta.x,this.prevProjectionDelta.x)&&ar(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),cr&&ur.recalculatedProjection++):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(kr));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d;Dr(o.x,t.x,n),Dr(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(to(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),function(t,e,n,i){Rr(t.x,e.x,n.x,i),Rr(t.y,e.y,n.y,i)}(this.relativeTarget,this.relativeTargetOrigin,r,n),h&&(l=this.relativeTarget,d=h,ir(l.x,d.x)&&ir(l.y,d.y))&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),_o(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=vi(0,void 0!==n.opacity?n.opacity:1,Ko(i)),t.opacityExit=vi(void 0!==e.opacity?e.opacity:1,0,Yo(i))):o&&(t.opacity=vi(void 0!==e.opacity?e.opacity:1,void 0!==n.opacity?n.opacity:1,i));for(let r=0;r<No;r++){const s=`border${Uo[r]}Radius`;let o=zo(e,s),a=zo(n,s);void 0===o&&void 0===a||(o||(o=0),a||(a=0),0===o||0===a||Wo(o)===Wo(a)?(t[s]=Math.max(vi($o(o),$o(a),i),0),(Ft.test(a)||Ft.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||n.rotate)&&(t.rotate=vi(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(F(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=j.update((()=>{Do.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,n){const i=gt(t)?t:nn(t);return i.start(ws("",i,e,n)),i.animation}(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Or(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Gs(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Gs(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}_o(e,n),So(e,s),Zs(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new lr);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){var t;const{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;const{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&mr("z",t,i,this.animationValues);for(let s=0;s<hr.length;s++)mr(`rotate${hr[s]}`,t,i,this.animationValues),mr(`skew${hr[s]}`,t,i,this.animationValues);t.render();for(const s in i)t.setStaticValue(s,i[s]),this.animationValues&&(this.animationValues[s]=i[s]);t.scheduleRender()}getProjectionStyles(t){var e,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return dr;const i={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=yt(null==t?void 0:t.pointerEvents)||"",i.transform=s?s(this.latestValues,""):"none",i;const o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=yt(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!ho(this.latestValues)&&(e.transform=s?s({},""):"none",this.hasProjected=!1),e}const r=o.animationValues||o.latestValues;this.applyTransformsToTarget(),i.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=(null==n?void 0:n.z)||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),s&&(i.transform=s(r,i.transform));const{x:a,y:l}=this.projectionDelta;i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?i.opacity=o===this?null!==(n=null!==(e=r.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:i.opacity=o===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0;for(const u in ie){if(void 0===r[u])continue;const{correct:t,applyTo:e}=ie[u],n="none"===i.transform?r[u]:t(r[u],o);if(e){const t=e.length;for(let s=0;s<t;s++)i[e[s]]=n}else i[u]=n}return this.options.layoutId&&(i.pointerEvents=o===this?yt(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()})),this.root.nodes.forEach(Tr),this.root.sharedNodes.clear()}}}function gr(t){t.updateLayout()}function yr(t){var e;const n=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:e,measuredBox:i}=t.layout,{animationType:s}=t.options,o=n.source!==t.layout.source;"size"===s?ao((t=>{const i=o?n.measuredBox[t]:n.layoutBox[t],s=Gs(i);i.min=e[t].min,i.max=i.min+s})):Or(s,n.layoutBox,e)&&ao((i=>{const s=o?n.measuredBox[i]:n.layoutBox[i],r=Gs(e[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)}));const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Zs(r,e,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Zs(a,t.applyTransform(i,!0),n.measuredBox):Zs(a,e,n.layoutBox);const l=!nr(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};to(r,n.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};to(a,e,o.layoutBox),or(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:n,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function xr(t){cr&&ur.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Pr(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function wr(t){t.clearSnapshot()}function Tr(t){t.clearMeasurements()}function Sr(t){t.isLayoutDirty=!1}function br(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Ar(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Er(t){t.resolveTargetDelta()}function Vr(t){t.calcProjection()}function Cr(t){t.resetSkewAndRotation()}function Mr(t){t.removeLeadSnapshot()}function Dr(t,e,n){t.translate=vi(e.translate,0,n),t.scale=vi(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Rr(t,e,n,i){t.min=vi(e.min,n.min,i),t.max=vi(e.max,n.max,i)}function kr(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Lr={duration:.45,ease:[.4,0,.1,1]},jr=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Fr=jr("applewebkit/")&&!jr("chrome/")?Math.round:A;function Br(t){t.min=Fr(t.min),t.max=Fr(t.max)}function Or(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=rr(e),s=rr(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Ir(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}const Ur=vr({attachResizeListener:(t,e)=>Is(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Nr={current:void 0},$r=vr({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Nr.current){const t=new Ur({});t.mount(window),t.setOptions({layoutScroll:!0}),Nr.current=t}return Nr.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),Wr={pan:{Feature:class extends Fs{constructor(){super(...arguments),this.removePointerDownListener=A}onPointerDown(t){this.session=new Ws(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Ao(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:Mo(t),onStart:Mo(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&j.postRender((()=>i(t,e)))}}}mount(){this.removePointerDownListener=Ns(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Fs{constructor(t){super(t),this.removeGroupControls=A,this.removeListeners=A,this.controls=new Vo(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||A}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:$r,MeasureLayout:Fo}};function zr(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&j.postRender((()=>s(e,Us(e))))}function Kr(t,e,n){const{props:i}=t;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&j.postRender((()=>s(e,Us(e))))}const Yr=new WeakMap,Xr=new WeakMap,Hr=t=>{const e=Yr.get(t.target);e&&e(t)},_r=t=>{t.forEach(Hr)};function Gr(t,e,n){const i=function({root:t,...e}){const n=t||document;Xr.has(n)||Xr.set(n,{});const i=Xr.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(_r,{root:t,...e})),i[s]}(e);return Yr.set(t,n),i.observe(t),()=>{Yr.delete(t),i.unobserve(t)}}const qr={some:0,all:1};const Zr={inView:{Feature:class extends Fs{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:qr[i]};return Gr(this.node.current,o,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Fs{mount(){const{current:t}=this.node;t&&(this.unmount=Xe(t,(t=>(Kr(this.node,t,"Start"),(t,{success:e})=>Kr(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Fs{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=bi(Is(this.node.current,"focus",(()=>this.onFocus())),Is(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Fs{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[i,s,o]=Oe(t,n),r=Ie((t=>{const{target:n}=t,i=e(t);if("function"!=typeof i||!n)return;const o=Ie((t=>{i(t),n.removeEventListener("pointerleave",o)}));n.addEventListener("pointerleave",o,s)}));return i.forEach((t=>{t.addEventListener("pointerenter",r,s)})),o}(t,(t=>(zr(this.node,t,"Start"),t=>zr(this.node,t,"End")))))}unmount(){}}}},Jr={layout:{ProjectionNode:$r,MeasureLayout:Fo}},Qr={current:null},ta={current:!1};const ea=[...li,Cn,On],na=new WeakMap;const ia=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sa{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ii,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=qe.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,j.render(this.render,!1,!0))};const{latestValues:a,renderState:l,onUpdate:u}=o;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=Z(e),this.isVariantNode=J(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const d in h){const t=h[d];void 0!==a[d]&&gt(t)&&t.set(a[d],!1)}}mount(t){this.current=t,na.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),ta.current||function(){if(ta.current=!0,T)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Qr.current=t.matches;t.addListener(e),e()}else Qr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Qr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){na.delete(this.current),this.projection&&this.projection.unmount(),F(this.notifyUpdate),F(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Tt.has(t),i=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&j.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{i(),s(),o&&o(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in N){const e=N[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let n=0;n<ia.length;n++){const e=ia[n];this.propEventSubscriptions[e]&&(this.propEventSubscriptions[e](),delete this.propEventSubscriptions[e]);const i=t["on"+e];i&&(this.propEventSubscriptions[e]=this.on(e,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(gt(s))t.addValue(i,s);else if(gt(o))t.addValue(i,nn(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,nn(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=nn(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){var n;let i=void 0===this.latestValues[t]&&this.current?null!==(n=this.getBaseTargetFromProps(this.props,t))&&void 0!==n?n:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var s;return null!=i&&("string"==typeof i&&(si(i)||yn(i))?i=parseFloat(i):(s=i,!ea.find(ai(s))&&On.test(e)&&(i=Kn(t,e))),this.setBaseTarget(t,gt(i)?i.get():i)),gt(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;const{initial:n}=this.props;let i;if("string"==typeof n||"object"==typeof n){const s=ft(this.props,n,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);s&&(i=s[t])}if(n&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||gt(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new Qe),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class oa extends sa{constructor(){super(...arguments),this.KeyframeResolver=ci}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;gt(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}class ra extends oa{constructor(){super(...arguments),this.type="html",this.renderInstance=te}readValueFromInstance(t,e){if(Tt.has(e)){const t=zn(e);return t&&t.default||0}{const i=(n=t,window.getComputedStyle(n)),s=(bt(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return bo(t,e)}build(t,e,n){Xt(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return oe(t,e,n)}}class aa extends oa{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ro}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Tt.has(e)){const t=zn(e);return t&&t.default||0}return e=ee.has(e)?e:st(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return re(t,e,n)}build(t,e,n){qt(t,e,this.isSVGTag,n.transformTemplate)}renderInstance(t,e,n,i){ne(t,e,0,i)}mount(t){this.isSVGTag=Qt(t.tagName),super.mount(t)}}const la=Y(fe({...Os,...Zr,...Wr,...Jr},((e,n)=>pt(e)?new aa(n):new ra(n,{allowProjection:e!==t.Fragment}))));export{b as A,c as j,la as m};
