import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Navigate, useNavigate } from 'react-router-dom'
import {
  ArrowLeft,
  MapPin,
  Navigation,
  Clock,
  CreditCard,
  DollarSign,
  Smartphone,
  Car,
  Truck,
  Bike,
  CheckCircle,
  Loader2,
  TrendingUp
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContextSimple'
import { useNoZoom } from '../../hooks/useNoZoom'
import { MapboxService } from '../../services/MapboxService'
import { DynamicPricingService } from '../../services/DynamicPricingService'
import { RideService } from '../../services/RideService'
import { PaymentMethodService } from '../../services/PaymentMethodService'
import { supabase } from '../../lib/supabase'

// Types
interface VehicleOption {
  id: string
  name: string
  icon: string
  description: string
  basePrice: number
  pricePerKm: number
  eta: number
  available: boolean
}

interface PaymentMethod {
  id: string
  name: string
  icon: string
  description: string
  available: boolean
}

// Mock data
const VEHICLE_OPTIONS: VehicleOption[] = [
  {
    id: 'moto',
    name: 'MobiMoto',
    icon: '🏍️',
    description: 'Rápido e econômico',
    basePrice: 3.50,
    pricePerKm: 0.80,
    eta: 3,
    available: true
  },
  {
    id: 'carro',
    name: 'MobiCar',
    icon: '🚗',
    description: 'Conforto e segurança',
    basePrice: 5.00,
    pricePerKm: 1.20,
    eta: 5,
    available: true
  },
  {
    id: 'suv',
    name: 'MobiSUV',
    icon: '🚙',
    description: 'Espaço e luxo',
    basePrice: 8.00,
    pricePerKm: 1.80,
    eta: 7,
    available: true
  }
]

const PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: 'card',
    name: 'Cartão',
    icon: '💳',
    description: 'Crédito ou débito',
    available: true
  },
  {
    id: 'cash',
    name: 'Dinheiro',
    icon: '💵',
    description: 'Pagamento em espécie',
    available: true
  },
  {
    id: 'pix',
    name: 'PIX',
    icon: '📱',
    description: 'Pagamento instantâneo',
    available: true
  }
]

export const TripDetailsPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  
  useNoZoom()

  // Redirect if not logged in
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // State management
  const [loading, setLoading] = useState(false)
  const [origin, setOrigin] = useState<any>(null)
  const [destination, setDestination] = useState<any>(null)
  const [selectedVehicle, setSelectedVehicle] = useState<VehicleOption | null>(null)
  const [selectedPayment, setSelectedPayment] = useState<PaymentMethod | null>(null)
  const [observations, setObservations] = useState('')

  // Real data states
  const [realRouteData, setRealRouteData] = useState<any>(null)
  const [priceEstimates, setPriceEstimates] = useState<{[key: string]: any}>({})
  const [loadingPrices, setLoadingPrices] = useState(false)
  const [realPaymentMethods, setRealPaymentMethods] = useState<any[]>([])
  const [loadingPayments, setLoadingPayments] = useState(false)

  // Services
  const mapboxService = new MapboxService()
  const pricingService = new DynamicPricingService()
  const rideService = new RideService()

  // Load trip data from sessionStorage and calculate real data
  useEffect(() => {
    const savedDestination = sessionStorage.getItem('rideDestination')
    const savedOrigin = sessionStorage.getItem('rideOrigin')

    if (savedDestination) {
      setDestination(JSON.parse(savedDestination))
    }

    if (savedOrigin) {
      setOrigin(JSON.parse(savedOrigin))
    }

    // If no destination, redirect back to map
    if (!savedDestination) {
      navigate('/ride-request/map')
    }
  }, [navigate])

  // Calculate real route data and prices when origin/destination are available
  useEffect(() => {
    const calculateRealData = async () => {
      if (!origin?.center || !destination?.center) return

      console.log('🧮 Calculating real route data and prices...')
      setLoadingPrices(true)

      try {
        // Get real route data from Mapbox
        const routeData = await mapboxService.getDirections(
          origin.center,
          destination.center,
          {
            profile: 'driving-traffic',
            alternatives: false,
            steps: false
          }
        )

        if (routeData && routeData.length > 0) {
          const route = routeData[0]
          setRealRouteData(route)
          console.log('🗺️ Real route data:', route)

          // Calculate prices for all vehicle types
          const estimates: {[key: string]: any} = {}

          for (const vehicle of VEHICLE_OPTIONS) {
            try {
              const priceEstimate = await pricingService.calculatePrice(
                vehicle.id,
                route.distance / 1000, // Convert to km
                route.duration / 60,   // Convert to minutes
                origin.center[1],      // lat
                origin.center[0],      // lng
                destination.center[1], // lat
                destination.center[0]  // lng
              )

              estimates[vehicle.id] = priceEstimate
              console.log(`💰 Price for ${vehicle.name}:`, priceEstimate)
            } catch (error) {
              console.warn(`⚠️ Failed to calculate price for ${vehicle.name}:`, error)
              // Fallback to mock calculation
              estimates[vehicle.id] = {
                finalPrice: vehicle.basePrice + ((route.distance / 1000) * vehicle.pricePerKm),
                surge: 1.0,
                explanation: ['Preço base calculado']
              }
            }
          }

          setPriceEstimates(estimates)
        }
      } catch (error) {
        console.error('❌ Failed to calculate real data:', error)
      } finally {
        setLoadingPrices(false)
      }
    }

    calculateRealData()
  }, [origin, destination])

  // Load real payment methods from Supabase
  useEffect(() => {
    const loadPaymentMethods = async () => {
      if (!user?.id) return

      console.log('💳 Loading real payment methods from Supabase...')
      setLoadingPayments(true)

      try {
        const methods = await PaymentMethodService.getUserPaymentMethods(user.id)
        setRealPaymentMethods(methods)
        console.log('✅ Payment methods loaded:', methods)

        // Auto-select default payment method
        const defaultMethod = methods.find(m => m.is_default)
        if (defaultMethod && !selectedPayment) {
          setSelectedPayment(defaultMethod)
          console.log('🎯 Auto-selected default payment method:', defaultMethod)
        }
      } catch (error) {
        console.error('❌ Failed to load payment methods:', error)
        // Fallback to mock data
        setRealPaymentMethods([])
      } finally {
        setLoadingPayments(false)
      }
    }

    loadPaymentMethods()
  }, [user?.id])

  // Calculate price for selected vehicle (now using real data)
  const calculatePrice = useCallback((vehicle: VehicleOption) => {
    // Use real price estimate if available
    if (priceEstimates[vehicle.id]) {
      return priceEstimates[vehicle.id].finalPrice
    }

    // Fallback to real route distance if available
    if (realRouteData) {
      const distanceKm = realRouteData.distance / 1000
      return vehicle.basePrice + (distanceKm * vehicle.pricePerKm)
    }

    // Final fallback to mock data
    const distance = 5 // Mock distance in km
    return vehicle.basePrice + (distance * vehicle.pricePerKm)
  }, [priceEstimates, realRouteData])

  // Get real distance and duration
  const getRealDistance = useCallback(() => {
    if (realRouteData) {
      return (realRouteData.distance / 1000).toFixed(1) // km
    }
    return '5.0' // fallback
  }, [realRouteData])

  const getRealDuration = useCallback(() => {
    if (realRouteData) {
      return Math.round(realRouteData.duration / 60) // minutes
    }
    return 15 // fallback
  }, [realRouteData])

  // Event handlers
  const handleVehicleSelect = useCallback((vehicle: VehicleOption) => {
    setSelectedVehicle(vehicle)
  }, [])

  const handlePaymentSelect = useCallback((payment: PaymentMethod) => {
    setSelectedPayment(payment)
  }, [])

  const handleRequestDriver = useCallback(async () => {
    if (!selectedVehicle || !selectedPayment || !destination || !origin) {
      console.warn('⚠️ Missing required data for ride request')
      return
    }

    if (!user?.id) {
      console.error('❌ User not authenticated')
      return
    }

    setLoading(true)
    console.log('🚗 Creating real ride request in Supabase...')

    try {
      // Create ride request using RideService
      const rideRequest = await rideService.createRideRequest(
        origin.place_name || 'Localização atual',
        origin.center,
        destination.place_name,
        destination.center,
        selectedVehicle.id,
        selectedPayment.type || 'cash'
      )

      if (!rideRequest) {
        throw new Error('Failed to create ride request')
      }

      console.log('✅ Ride request created successfully:', rideRequest)

      // Store complete trip data for next page
      const tripData = {
        rideId: rideRequest.id,
        origin,
        destination,
        vehicle: selectedVehicle,
        payment: selectedPayment,
        observations,
        price: calculatePrice(selectedVehicle),
        requestTime: new Date().toISOString(),
        status: rideRequest.status,
        distance: realRouteData?.distance || null,
        duration: realRouteData?.duration || null,
        driver_id: rideRequest.driver_id || null
      }

      sessionStorage.setItem('tripData', JSON.stringify(tripData))
      sessionStorage.setItem('currentRideId', rideRequest.id)

      console.log('📱 Navigating to waiting page...')
      navigate('/ride-request/waiting')

    } catch (error) {
      console.error('❌ Failed to create ride request:', error)
      setLoading(false)
      // TODO: Show error message to user
    }
  }, [selectedVehicle, selectedPayment, destination, origin, observations, calculatePrice, navigate, user?.id, rideService, realRouteData])

  const handleBack = useCallback(() => {
    navigate('/ride-request/map')
  }, [navigate])

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-black/40"></div>
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

      <div className="relative z-10 flex flex-col h-full min-h-screen">
        {/* Header */}
        <motion.div
          className="pt-8 pb-6 px-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="flex items-center justify-between">
            <motion.button
              onClick={handleBack}
              className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowLeft className="w-5 h-5" />
            </motion.button>

            <div className="flex-1 text-center">
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-sm text-white/70">Detalhes da corrida</p>
            </div>

            <div className="w-9"></div>
          </div>
        </motion.div>

        {/* Content */}
        <div className="flex-1 px-4 pb-8">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {/* Route Summary */}
            <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
              <h2 className="text-xl font-bold text-white mb-4">Resumo da Rota</h2>
              
              <div className="space-y-4">
                {/* Origin */}
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-white font-medium">Origem</p>
                    <p className="text-white/70 text-sm">{origin?.place_name || 'Sua localização atual'}</p>
                  </div>
                </div>

                {/* Destination */}
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-white font-medium">Destino</p>
                    <p className="text-white/70 text-sm">{destination?.place_name}</p>
                  </div>
                </div>

                {/* Distance and Time */}
                <div className="flex items-center justify-between pt-2 border-t border-white/20">
                  <div className="flex items-center space-x-2">
                    <Navigation className="w-4 h-4 text-white/60" />
                    <span className="text-white/70 text-sm">
                      {loadingPrices ? (
                        <div className="flex items-center space-x-1">
                          <Loader2 className="w-3 h-3 animate-spin" />
                          <span>...</span>
                        </div>
                      ) : (
                        `${getRealDistance()} km`
                      )}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-white/60" />
                    <span className="text-white/70 text-sm">
                      {loadingPrices ? (
                        <div className="flex items-center space-x-1">
                          <Loader2 className="w-3 h-3 animate-spin" />
                          <span>...</span>
                        </div>
                      ) : (
                        `${getRealDuration()} min`
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Vehicle Selection */}
            <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
              <h2 className="text-xl font-bold text-white mb-4">Escolha o Veículo</h2>
              
              <div className="space-y-3">
                {VEHICLE_OPTIONS.map((vehicle) => (
                  <button
                    key={vehicle.id}
                    onClick={() => handleVehicleSelect(vehicle)}
                    className={`w-full p-4 rounded-xl border transition-all ${
                      selectedVehicle?.id === vehicle.id
                        ? 'bg-blue-500/20 border-blue-500/50 text-white'
                        : 'bg-white/5 border-white/20 text-white/80 hover:bg-white/10'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{vehicle.icon}</span>
                        <div className="text-left">
                          <p className="font-medium">{vehicle.name}</p>
                          <p className="text-sm opacity-70">{vehicle.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        {loadingPrices ? (
                          <div className="flex items-center space-x-1">
                            <Loader2 className="w-4 h-4 animate-spin" />
                            <span className="text-sm">Calculando...</span>
                          </div>
                        ) : (
                          <>
                            <div className="flex items-center space-x-1">
                              <p className="font-bold">R$ {calculatePrice(vehicle).toFixed(2)}</p>
                              {priceEstimates[vehicle.id]?.surge > 1.2 && (
                                <TrendingUp className="w-3 h-3 text-orange-400" />
                              )}
                            </div>
                            <p className="text-sm opacity-70">{vehicle.eta} min</p>
                            {priceEstimates[vehicle.id]?.surge > 1.2 && (
                              <p className="text-xs text-orange-400">
                                Surge {priceEstimates[vehicle.id].surge.toFixed(1)}x
                              </p>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </motion.div>

            {/* Price Breakdown - Show when vehicle is selected */}
            {selectedVehicle && priceEstimates[selectedVehicle.id] && (
              <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                <h2 className="text-xl font-bold text-white mb-4">Detalhes do Preço</h2>

                <div className="space-y-3">
                  <div className="flex justify-between text-white/80">
                    <span>Tarifa base:</span>
                    <span>R$ {priceEstimates[selectedVehicle.id].breakdown?.baseFare?.toFixed(2) || selectedVehicle.basePrice.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-white/80">
                    <span>Distância ({getRealDistance()} km):</span>
                    <span>R$ {priceEstimates[selectedVehicle.id].breakdown?.distanceCost?.toFixed(2) || '0.00'}</span>
                  </div>
                  <div className="flex justify-between text-white/80">
                    <span>Tempo ({getRealDuration()} min):</span>
                    <span>R$ {priceEstimates[selectedVehicle.id].breakdown?.timeCost?.toFixed(2) || '0.00'}</span>
                  </div>
                  {priceEstimates[selectedVehicle.id].breakdown?.surgeAmount > 0 && (
                    <div className="flex justify-between text-orange-400">
                      <span>Surge pricing:</span>
                      <span>+ R$ {priceEstimates[selectedVehicle.id].breakdown.surgeAmount.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="border-t border-white/20 pt-2 flex justify-between text-white font-bold">
                    <span>Total:</span>
                    <span>R$ {calculatePrice(selectedVehicle).toFixed(2)}</span>
                  </div>
                </div>

                {/* Price explanation */}
                {priceEstimates[selectedVehicle.id].explanation && priceEstimates[selectedVehicle.id].explanation.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-white/20">
                    <p className="text-white/60 text-sm mb-2">Fatores de preço:</p>
                    <ul className="text-white/50 text-xs space-y-1">
                      {priceEstimates[selectedVehicle.id].explanation.map((factor: string, index: number) => (
                        <li key={index}>• {factor}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </motion.div>
            )}

            {/* Payment Method */}
            <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
              <h2 className="text-xl font-bold text-white mb-4">Forma de Pagamento</h2>

              {loadingPayments ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin text-white" />
                  <span className="ml-2 text-white/70">Carregando métodos...</span>
                </div>
              ) : (
                <div className="space-y-3">
                  {(realPaymentMethods.length > 0 ? realPaymentMethods : PAYMENT_METHODS).map((method) => (
                    <button
                      key={method.id}
                      onClick={() => handlePaymentSelect(method)}
                      className={`w-full p-4 rounded-xl border transition-all ${
                        selectedPayment?.id === method.id
                          ? 'bg-blue-500/20 border-blue-500/50 text-white'
                          : 'bg-white/5 border-white/20 text-white/80 hover:bg-white/10'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">
                            {method.display_info?.icon || method.icon ||
                             (method.type === 'credit_card' ? '💳' :
                              method.type === 'pix' ? '📱' :
                              method.type === 'cash' ? '💵' : '💰')}
                          </span>
                          <div className="text-left">
                            <p className="font-medium text-white">
                              {method.display_info?.display_name || method.name}
                            </p>
                            <p className="text-sm text-white/70">
                              {method.display_info?.description || method.description ||
                               (method.card_last_four ? `**** **** **** ${method.card_last_four}` :
                                method.type === 'pix' ? 'Pagamento instantâneo' :
                                method.type === 'cash' ? 'Pagamento em dinheiro' : 'Método de pagamento')}
                            </p>
                            {method.is_default && (
                              <span className="text-xs text-green-400">✓ Padrão</span>
                            )}
                          </div>
                        </div>
                        {selectedPayment?.id === method.id && (
                          <CheckCircle className="w-5 h-5 text-white" />
                        )}
                      </div>
                    </button>
                  ))}

                  {realPaymentMethods.length === 0 && (
                    <div className="text-center py-2">
                      <p className="text-white/60 text-sm">Usando métodos padrão</p>
                    </div>
                  )}
                </div>
              )}
            </motion.div>

            {/* Observations */}
            <motion.div variants={itemVariants} className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
              <h2 className="text-xl font-bold text-white mb-4">Observações (opcional)</h2>
              
              <textarea
                value={observations}
                onChange={(e) => setObservations(e.target.value)}
                placeholder="Instruções para o motorista..."
                className="w-full p-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                rows={3}
                maxLength={200}
              />
              <p className="text-white/50 text-xs mt-2">{observations.length}/200 caracteres</p>
            </motion.div>

            {/* Request Button */}
            <motion.button
              onClick={handleRequestDriver}
              disabled={!selectedVehicle || !selectedPayment || loading}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"
              whileHover={{ scale: selectedVehicle && selectedPayment && !loading ? 1.02 : 1 }}
              whileTap={{ scale: selectedVehicle && selectedPayment && !loading ? 0.98 : 1 }}
              variants={itemVariants}
            >
              {loading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>Solicitando...</span>
                </>
              ) : (
                <>
                  <CheckCircle className="w-5 h-5" />
                  <span>Solicitar motorista</span>
                </>
              )}
            </motion.button>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default TripDetailsPage
