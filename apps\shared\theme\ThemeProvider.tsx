import React, { createContext, useContext, useState, useEffect } from 'react';
import { ThemeMode, mobidriveTheme } from './theme-config';

interface ThemeContextType {
  theme: ThemeMode;
  setTheme: (theme: ThemeMode) => void;
  isDarkMode: boolean;
  isTransitioning: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: ThemeMode;
  storageKey?: string;
  transitionDuration?: number;
}

/**
 * MobiDrive Shared ThemeProvider
 * 
 * This component provides theme context for all MobiDrive apps.
 * It handles theme switching, system preference detection, and persistence.
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'system',
  storageKey = 'mobidrive-ui-theme',
  transitionDuration = 300,
}) => {
  const [theme, setThemeState] = useState<ThemeMode>(() => {
    // Check if there's a saved theme in localStorage
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem(storageKey) as ThemeMode;
      return savedTheme || defaultTheme;
    }
    return defaultTheme;
  });

  const [isDarkMode, setIsDarkMode] = useState<boolean>(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const setTheme = (newTheme: ThemeMode) => {
    setIsTransitioning(true);
    setThemeState(newTheme);
    
    // Save theme preference to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem(storageKey, newTheme);
    }

    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, transitionDuration);
  };

  // Effect to apply the theme to the document
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const root = window.document.documentElement;

    // Remove all theme classes
    root.classList.remove('light', 'dark');

    // Apply the theme
    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      root.classList.add(systemTheme);
      setIsDarkMode(systemTheme === 'dark');
    } else {
      root.classList.add(theme);
      setIsDarkMode(theme === 'dark');
    }
  }, [theme]);

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window === 'undefined' || theme !== 'system') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e: MediaQueryListEvent) => {
      setIsTransitioning(true);
      const newTheme = e.matches ? 'dark' : 'light';

      document.documentElement.classList.remove('light', 'dark');
      document.documentElement.classList.add(newTheme);
      setIsDarkMode(newTheme === 'dark');

      setTimeout(() => {
        setIsTransitioning(false);
      }, transitionDuration);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme, transitionDuration]);

  return (
    <ThemeContext.Provider value={{ theme, setTheme, isDarkMode, isTransitioning }}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Hook to use the theme context
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeProvider;
