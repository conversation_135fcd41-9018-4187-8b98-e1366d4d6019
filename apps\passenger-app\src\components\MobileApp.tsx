import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { ProtectedRoute } from './ProtectedRoute';

// Importa as páginas mobile nativas
import LoginMobile from '../pages/LoginMobile';
import RegisterMobile from '../pages/RegisterMobile';
import DashboardMobile from '../pages/DashboardMobile';
import RequestRideMobile from '../pages/RequestRideMobile';
import RideTrackingMobile from '../pages/RideTrackingMobile';
import SetupMobile from '../pages/SetupMobile';

// 📱 MOBILE APP - EXPERIÊNCIA MOBILE NATIVA
// Todas as páginas otimizadas para dispositivos móveis

export const MobileApp: React.FC = () => {
  return (
    <div className="mobile-app">
      <Routes>
        {/* Rotas públicas */}
        <Route path="/mobile/login" element={<LoginMobile />} />
        <Route path="/mobile/register" element={<RegisterMobile />} />
        
        {/* Rotas protegidas */}
        <Route path="/mobile/dashboard" element={
          <ProtectedRoute>
            <DashboardMobile />
          </ProtectedRoute>
        } />
        
        <Route path="/mobile/request-ride" element={
          <ProtectedRoute>
            <RequestRideMobile />
          </ProtectedRoute>
        } />
        
        <Route path="/mobile/ride-tracking" element={
          <ProtectedRoute>
            <RideTrackingMobile />
          </ProtectedRoute>
        } />
        
        <Route path="/mobile/setup" element={
          <ProtectedRoute>
            <SetupMobile />
          </ProtectedRoute>
        } />

        {/* Redirect padrão */}
        <Route path="/mobile/*" element={<LoginMobile />} />
      </Routes>
    </div>
  );
};

export default MobileApp;
