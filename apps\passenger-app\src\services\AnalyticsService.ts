/**
 * Sistema de Analytics Avançado para MobiDrive
 * Monitora performance, uso e comportamento do usuário
 */

import { supabase } from '../lib/supabase'

interface AnalyticsEvent {
  event_type: string
  user_id?: string
  session_id: string
  properties: Record<string, any>
  timestamp: string
  page_url: string
  user_agent: string
}

interface PerformanceMetric {
  metric_name: string
  value: number
  unit: string
  timestamp: string
  context?: Record<string, any>
}

interface UserBehavior {
  action: string
  element: string
  page: string
  duration?: number
  success: boolean
  error_message?: string
}

class AnalyticsService {
  private sessionId: string
  private userId?: string
  private eventQueue: AnalyticsEvent[] = []
  private performanceQueue: PerformanceMetric[] = []
  private isOnline = navigator.onLine

  constructor() {
    this.sessionId = this.generateSessionId()
    this.setupEventListeners()
    this.startPerformanceMonitoring()

    // Flush queue periodicamente
    setInterval(() => this.flushQueues(), 30000) // A cada 30 segundos
  }

  // Configurar event listeners
  private setupEventListeners(): void {
    // Monitorar conexão
    window.addEventListener('online', () => {
      this.isOnline = true
      this.flushQueues()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
    })

    // Monitorar mudanças de página
    window.addEventListener('beforeunload', () => {
      this.flushQueues()
    })

    // Monitorar erros
    window.addEventListener('error', (event) => {
      this.trackError('javascript_error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      })
    })

    // Monitorar erros de Promise
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError('promise_rejection', {
        reason: event.reason?.toString()
      })
    })
  }

  // Configurar usuário
  setUser(userId: string): void {
    this.userId = userId
    this.track('user_session_start', {
      user_id: userId,
      session_duration: 0
    })
  }

  // Rastrear evento genérico
  track(eventType: string, properties: Record<string, any> = {}): void {
    const event: AnalyticsEvent = {
      event_type: eventType,
      user_id: this.userId,
      session_id: this.sessionId,
      properties: {
        ...properties,
        timestamp_client: Date.now()
      },
      timestamp: new Date().toISOString(),
      page_url: window.location.href,
      user_agent: navigator.userAgent
    }

    this.eventQueue.push(event)

    // Flush imediatamente para eventos críticos
    if (this.isCriticalEvent(eventType)) {
      this.flushQueues()
    }
  }

  // Rastrear eventos específicos do MobiDrive
  trackRideRequest(origin: string, destination: string, vehicleType: string): void {
    this.track('ride_request_created', {
      origin,
      destination,
      vehicle_type: vehicleType,
      timestamp: Date.now()
    })
  }

  trackRideAccepted(rideId: string, driverId: string, eta: number): void {
    this.track('ride_accepted', {
      ride_id: rideId,
      driver_id: driverId,
      eta_minutes: eta
    })
  }

  trackEmergencyActivated(rideId?: string): void {
    this.track('emergency_activated', {
      ride_id: rideId,
      activation_method: 'sos_button',
      response_time: 0
    })
  }

  trackMapInteraction(action: string, details: Record<string, any>): void {
    this.track('map_interaction', {
      action,
      ...details
    })
  }

  trackSearchQuery(query: string, results: number, selected?: string): void {
    this.track('search_performed', {
      query,
      results_count: results,
      selected_result: selected
    })
  }

  trackPageView(page: string, loadTime?: number): void {
    this.track('page_view', {
      page,
      load_time_ms: loadTime,
      referrer: document.referrer
    })
  }

  trackUserBehavior(behavior: UserBehavior): void {
    this.track('user_behavior', behavior)
  }

  // Rastrear métricas de performance
  trackPerformance(metricName: string, value: number, unit: string, context?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      metric_name: metricName,
      value,
      unit,
      timestamp: new Date().toISOString(),
      context
    }

    this.performanceQueue.push(metric)
  }

  // Rastrear erros
  trackError(errorType: string, details: Record<string, any>): void {
    this.track('error_occurred', {
      error_type: errorType,
      ...details,
      stack_trace: details.stack?.toString()
    })
  }

  // Monitoramento automático de performance
  private startPerformanceMonitoring(): void {
    // Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.trackPerformance('lcp', entry.startTime, 'ms')
        }
      }).observe({ entryTypes: ['largest-contentful-paint'] })

      // First Input Delay
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.trackPerformance('fid', (entry as any).processingStart - entry.startTime, 'ms')
        }
      }).observe({ entryTypes: ['first-input'] })

      // Cumulative Layout Shift
      new PerformanceObserver((list) => {
        let clsValue = 0
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value
          }
        }
        this.trackPerformance('cls', clsValue, 'score')
      }).observe({ entryTypes: ['layout-shift'] })
    }

    // Monitorar uso de memória
    setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        this.trackPerformance('memory_used', memory.usedJSHeapSize, 'bytes')
        this.trackPerformance('memory_total', memory.totalJSHeapSize, 'bytes')
      }
    }, 60000) // A cada minuto
  }

  // Verificar se é evento crítico
  private isCriticalEvent(eventType: string): boolean {
    const criticalEvents = [
      'emergency_activated',
      'ride_request_created',
      'error_occurred',
      'user_session_start'
    ]
    return criticalEvents.includes(eventType)
  }

  // Enviar dados para Supabase (temporariamente desabilitado)
  private async flushQueues(): Promise<void> {
    // Temporariamente desabilitado para evitar erros 404
    // As tabelas analytics_events e performance_metrics serão implementadas futuramente

    // Limpar filas para evitar acúmulo de dados
    this.eventQueue = []
    this.performanceQueue = []
  }

  // Gerar ID de sessão único
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Obter estatísticas da sessão atual
  getSessionStats(): {
    sessionId: string
    userId?: string
    eventsQueued: number
    metricsQueued: number
    isOnline: boolean
  } {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      eventsQueued: this.eventQueue.length,
      metricsQueued: this.performanceQueue.length,
      isOnline: this.isOnline
    }
  }

  // Forçar envio de dados
  async flush(): Promise<void> {
    await this.flushQueues()
  }
}

// Singleton instance
export const analyticsService = new AnalyticsService()

export default analyticsService
