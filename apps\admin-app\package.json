{"name": "mobidrive-admin-simple", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 4000", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "framer-motion": "^11.0.0", "lucide-react": "^0.344.0", "mapbox-gl": "^3.12.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1", "recharts": "^2.10.0"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.16.1", "typescript": "^5.2.2", "vite": "^5.1.0"}}