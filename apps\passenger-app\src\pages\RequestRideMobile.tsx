import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Navigate, useLocation } from 'react-router-dom'
import { MapPin, ArrowRight, User, Star, Phone, MessageCircle, Zap, Car, Clock, CreditCard } from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { GradientBackground } from '../components/GradientBackground'
import { PaymentMethodSelector } from '../components/PaymentMethodSelector'
import { useNoZoom } from '../hooks/useNoZoom'
import '../styles/no-zoom.css'

// 📱 REQUEST RIDE MOBILE ANDROID NATIVA
// MANTÉM DESIGN ORIGINAL + CONVERSÃO ANDROID NATIVA

interface LocationData {
  pickup: string
  destination: string
  pickupCoords?: [number, number]
  destinationCoords?: [number, number]
}

interface RideOption {
  id: string
  name: string
  price: number
  estimatedTime: string
  description: string
  icon: string
}

interface Driver {
  id: string
  name: string
  rating: number
  vehicle: {
    model: string
    plate: string
    color: string
  }
  estimatedArrival: string
}

interface PaymentMethod {
  id: string
  name: string
  display_info?: {
    display_name?: string
  }
}

type Step = 'location' | 'options' | 'driver' | 'tracking'

export const RequestRideMobile: React.FC = () => {
  const { user } = useAuth()
  const location = useLocation()
  const [step, setStep] = useState<Step>('location')
  const [locationData, setLocationData] = useState<LocationData>({
    pickup: '',
    destination: ''
  })
  const [selectedOption, setSelectedOption] = useState<RideOption | null>(null)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null)
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null)

  // 🚫 DESABILITA ZOOM COMPLETAMENTE + CONFIGURAÇÕES ANDROID NATIVAS
  useNoZoom()

  // Configurações Android nativas (mantendo design original)
  useEffect(() => {
    // Meta viewport para Android nativo
    let metaViewport = document.querySelector('meta[name="viewport"]')
    if (!metaViewport) {
      metaViewport = document.createElement('meta')
      metaViewport.setAttribute('name', 'viewport')
      document.head.appendChild(metaViewport)
    }
    metaViewport.setAttribute('content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )

    // Configurações de overflow para Android
    document.body.style.margin = '0'
    document.body.style.padding = '0'
    document.body.style.overflow = 'hidden'
    document.documentElement.style.margin = '0'
    document.documentElement.style.padding = '0'
    document.documentElement.style.overflow = 'hidden'
  }, [])

  // Redirect se não logado
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // Dados de exemplo para demonstração
  const rideOptions: RideOption[] = [
    { id: '1', name: 'MobiEconomy', price: 12.50, estimatedTime: '5-8 min', description: 'Opção econômica', icon: '🚗' },
    { id: '2', name: 'MobiComfort', price: 18.90, estimatedTime: '3-6 min', description: 'Conforto premium', icon: '🚙' },
    { id: '3', name: 'MobiXL', price: 25.00, estimatedTime: '4-7 min', description: 'Para grupos', icon: '🚐' }
  ]

  const mockDriver: Driver = {
    id: '1',
    name: 'João Silva',
    rating: 4.8,
    vehicle: { model: 'Honda Civic', plate: 'ABC-1234', color: 'Prata' },
    estimatedArrival: '3 min'
  }

  const handleLocationSubmit = () => {
    if (locationData.pickup && locationData.destination) {
      setStep('options')
    }
  }

  const handleOptionSelect = (option: RideOption) => {
    setSelectedOption(option)
    if (selectedPaymentMethod) {
      // Simular busca por motorista
      setTimeout(() => {
        setSelectedDriver(mockDriver)
        setStep('driver')
      }, 2000)
    }
  }

  const handleConfirmRide = () => {
    setStep('tracking')
    // Simular redirecionamento após confirmação
    setTimeout(() => {
      window.location.href = '/ride-tracking'
    }, 3000)
  }

  // Animações simples e limpas (MANTENDO ORIGINAIS)
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">

      {/* Background Gradient Sutil (FIEL AO LOGIN) */}
      <GradientBackground
        variant="static"
        opacity={0.7}
      />

      {/* Overlay muito sutil para legibilidade (FIEL AO LOGIN) */}
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Conteúdo Principal (FIEL AO LOGIN) */}
      <div className="relative z-10 flex flex-col h-full min-h-screen">

        {/* Header com Logo (FIEL AO LOGIN) */}
        <motion.div
          className="pt-8 pb-6 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <img
                src="/icons/icon-48x48.png"
                alt="MobiDrive"
                className="w-6 h-6"
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-xs text-white/70">Solicitar Corrida</p>
            </div>
          </div>
        </motion.div>

        {/* Conteúdo Central (FIEL AO LOGIN) */}
        <div className="flex-1 flex flex-col justify-center px-4 space-y-6">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {/* Step Indicator (FIEL AO LOGIN) */}
            <motion.div variants={itemVariants}>
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                <div className="flex items-center justify-between">
                  {['location', 'options', 'driver', 'tracking'].map((stepName, index) => (
                    <div key={stepName} className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                        step === stepName ? 'bg-blue-500 text-white' :
                        ['location', 'options', 'driver', 'tracking'].indexOf(step) > index ? 'bg-green-500 text-white' : 'bg-white/20 text-white/60'
                      }`}>
                        {index + 1}
                      </div>
                      {index < 3 && (
                        <div className={`w-8 h-1 mx-2 ${
                          ['location', 'options', 'driver', 'tracking'].indexOf(step) > index ? 'bg-green-500' : 'bg-white/20'
                        }`} />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Location Step (FIEL AO LOGIN) */}
            {step === 'location' && (
              <motion.div variants={itemVariants}>
                <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                  <h3 className="text-lg font-semibold text-white mb-4 text-center">📍 Onde você está indo?</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-white/90 mb-2">
                        📍 Local de partida
                      </label>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                        <input
                          type="text"
                          value={locationData.pickup}
                          onChange={(e) => setLocationData(prev => ({ ...prev, pickup: e.target.value }))}
                          placeholder="Digite o endereço de partida"
                          className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-white/90 mb-2">
                        🎯 Destino
                      </label>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                        <input
                          type="text"
                          value={locationData.destination}
                          onChange={(e) => setLocationData(prev => ({ ...prev, destination: e.target.value }))}
                          placeholder="Digite o endereço de destino"
                          className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                        />
                      </div>
                    </div>
                    <motion.button
                      onClick={handleLocationSubmit}
                      disabled={!locationData.pickup || !locationData.destination}
                      className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span>Continuar</span>
                      <ArrowRight className="w-4 h-4" />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            )}

        {/* Options Step (MANTENDO DESIGN ORIGINAL) */}
        {step === 'options' && (
          <motion.div variants={itemVariants}>
            <ModernCard title="Escolha seu veículo" icon="🚗">
              <div className="space-y-3">
                {rideOptions.map((option) => (
                  <motion.div
                    key={option.id}
                    onClick={() => handleOptionSelect(option)}
                    className={`p-4 rounded-xl border-2 cursor-pointer transition-all ${
                      selectedOption?.id === option.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{option.icon}</span>
                        <div>
                          <h3 className="font-semibold text-gray-900">{option.name}</h3>
                          <p className="text-sm text-gray-600">{option.description}</p>
                          <p className="text-xs text-gray-500">{option.estimatedTime}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-gray-900">R$ {option.price.toFixed(2)}</div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </ModernCard>

            {/* Payment Method (MANTENDO DESIGN ORIGINAL) */}
            <ModernCard title="Forma de pagamento" icon="💳">
              <PaymentMethodSelector
                selectedMethodId={selectedPaymentMethod?.id}
                onMethodSelect={setSelectedPaymentMethod}
                showAddButton={false}
              />
            </ModernCard>

            {/* Confirm Button (MANTENDO DESIGN ORIGINAL) */}
            {selectedOption && selectedPaymentMethod && (
              <ModernCard glass>
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Veículo:</span>
                    <span className="font-medium">{selectedOption.name}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Pagamento:</span>
                    <span className="font-medium">
                      {selectedPaymentMethod.display_info?.display_name || selectedPaymentMethod.name}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span>R$ {selectedOption.price.toFixed(2)}</span>
                  </div>
                  <motion.button
                    onClick={() => handleOptionSelect(selectedOption)}
                    className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-4 rounded-xl font-semibold flex items-center justify-center space-x-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span>Confirmar Solicitação</span>
                    <ArrowRight className="w-4 h-4" />
                  </motion.button>
                </div>
              </ModernCard>
            )}
          </motion.div>
        )}

        {/* Driver Step (MANTENDO DESIGN ORIGINAL) */}
        {step === 'driver' && selectedDriver && (
          <motion.div variants={itemVariants}>
            <ModernCard title="Motorista encontrado!" icon="🎉">
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                    <User className="w-8 h-8 text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{selectedDriver.name}</h4>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span>{selectedDriver.rating}</span>
                      <span>•</span>
                      <span>{selectedDriver.vehicle.model}</span>
                      <span>•</span>
                      <span>{selectedDriver.vehicle.plate}</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <motion.button
                      className="p-3 bg-green-500 text-white rounded-xl"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Phone className="w-5 h-5" />
                    </motion.button>
                    <motion.button
                      className="p-3 bg-blue-500 text-white rounded-xl"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <MessageCircle className="w-5 h-5" />
                    </motion.button>
                  </div>
                </div>
                <motion.button
                  onClick={handleConfirmRide}
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-4 rounded-xl font-semibold"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Confirmar Corrida
                </motion.button>
              </div>
            </ModernCard>
          </motion.div>
        )}

        {/* Tracking Step (MANTENDO DESIGN ORIGINAL) */}
        {step === 'tracking' && (
          <motion.div variants={itemVariants}>
            <ModernCard glass className="text-center">
              <motion.div
                className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Zap className="w-8 h-8 text-white" />
              </motion.div>
              <h3 className="text-lg font-semibold text-white mb-2">Corrida confirmada!</h3>
              <p className="text-white/80">Redirecionando para acompanhamento...</p>
              <motion.div
                className="mt-4 w-32 h-1 bg-white/20 rounded-full overflow-hidden mx-auto"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
              >
                <motion.div
                  className="h-full bg-green-500 rounded-full"
                  initial={{ width: "0%" }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 2 }}
                />
              </motion.div>
            </ModernCard>
          </motion.div>
            )}
          </motion.div>
        </div>

        {/* Footer Simples (FIEL AO LOGIN) */}
        <motion.div
          className="pb-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <p className="text-white/50 text-xs">
            © 2024 MobiDrive. Todos os direitos reservados.
          </p>
        </motion.div>
      </div>
    </div>
  )
}

export default RequestRideMobile
