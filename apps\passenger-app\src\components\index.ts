// 📱 EXPORTAÇÕES DE COMPONENTES - MOBILE ONLY

// === COMPONENTE 3D PRINCIPAL ===
export { Car3DMobile } from './mobile/Car3DMobile'

// === BACKGROUND COMPONENTS ===
export { GradientBackground, GradientBackgroundSelector } from './GradientBackground'

// === DEVICE WRAPPER ===
export { <PERSON>ceWrapper } from './DeviceWrapper'
export { MobileMockup } from './MobileMockup'
export { MobileWrapper } from './MobileWrapper'

// === COMPONENTES LEGADOS (para compatibilidade) ===
export { Car3DSimpleOBJ } from './Car3DSimpleOBJ'
export { Car3DComponent } from './Car3DComponent'
export { Car3DAdvanced } from './Car3DAdvanced'
export { Car3DReal } from './Car3DReal'
export { Car3DSimple } from './Car3DSimple'
export { Car3DFallback } from './Car3DFallback'
export { Car3DDebug } from './Car3DDebug'
export { Car3DWithGif } from './Car3DWithGif'
export { Car3DWithOBJ } from './Car3DWithOBJ'

// === OUTROS COMPONENTES ===
export { AdvancedMapComponent } from './AdvancedMapComponent'
export { AnalyticsDashboard } from './AnalyticsDashboard'
export { ChatComponent } from './ChatComponent'
export { DiagnosticPanel } from './DiagnosticPanel'
export { EmergencyButton } from './EmergencyButton'
export { EnhancedMapboxComponent } from './EnhancedMapboxComponent'
export { ErrorBoundary } from './ErrorBoundary'
export { ExecutiveDashboard } from './ExecutiveDashboard'
export { FavoritesPanel } from './FavoritesPanel'
export { Loading } from './Loading'
export { LoadingFallback } from './LoadingFallback'
export { LocationStatus } from './LocationStatus'
export { MapComponent } from './MapComponent'
export { MapboxSearchComponent } from './MapboxSearchComponent'
export { MapboxTestComponent } from './MapboxTestComponent'
export { NotificationToast } from './NotificationToast'
export { ParticleBackground } from './ParticleBackground'
export { PaymentMethodSelector } from './PaymentMethodSelector'
export { PaymentSetup } from './PaymentSetup'
export { PaymentSystem } from './PaymentSystem'
export { ProtectedRoute } from './ProtectedRoute'
export { RealTimeETA } from './RealTimeETA'
export { RideRatingSystem } from './RideRatingSystem'
export { RideRequestLoader } from './RideRequestLoader'
export { RideStatusManager } from './RideStatusManager'
export { RouteMapComponent } from './RouteMapComponent'
export { SystemMonitor } from './SystemMonitor'
export { SystemStatusPanel } from './SystemStatusPanel'
export { TripSharingComponent } from './TripSharingComponent'

// === NOTA ===
// Componentes desktop foram movidos para src/components/backup/
// Para restaurar: copie os arquivos de backup de volta
