import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Crown, Star, Zap, Gift, Check, X, 
  CreditCard, Smartphone, Calendar, 
  TrendingUp, Award, Infinity, Shield
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { supabase } from '../lib/supabase'

interface SubscriptionPlan {
  id: string
  name: string
  description: string
  price_monthly: number
  price_yearly: number
  features: any
  daily_ad_limit: number
  reward_multiplier: number
  priority_ads: boolean
  exclusive_offers: boolean
}

interface UserSubscription {
  id: string
  plan_id: string
  status: string
  billing_cycle: string
  subscription_end_date: string
  trial_end_date?: string
  plan: SubscriptionPlan
}

export const PremiumSubscriptionSystem: React.FC = () => {
  const { user } = useAuth()
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [userSubscription, setUserSubscription] = useState<UserSubscription | null>(null)
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null)
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [couponCode, setCouponCode] = useState('')
  const [appliedDiscount, setAppliedDiscount] = useState<any>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    if (user) {
      loadSubscriptionPlans()
      loadUserSubscription()
    }
  }, [user])

  const loadSubscriptionPlans = async () => {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('active', true)
        .order('price_monthly', { ascending: true })

      if (data) {
        setPlans(data)
      }
    } catch (error) {
      console.error('Erro ao carregar planos:', error)
    }
  }

  const loadUserSubscription = async () => {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          plan:subscription_plans(*)
        `)
        .eq('user_id', user?.id)
        .eq('status', 'active')
        .single()

      if (data) {
        setUserSubscription(data)
      }
    } catch (error) {
      // Usuário não tem assinatura ativa
      setUserSubscription(null)
    }
  }

  const applyCoupon = async () => {
    if (!couponCode || !selectedPlan) return

    try {
      const { data, error } = await supabase
        .rpc('apply_discount_coupon', {
          p_coupon_code: couponCode,
          p_user_id: user?.id,
          p_plan_id: selectedPlan.id
        })

      if (data?.valid) {
        setAppliedDiscount(data)
        alert(`Cupom aplicado! Desconto de R$ ${data.discount_amount.toFixed(2)}`)
      } else {
        alert(data?.error || 'Cupom inválido')
        setAppliedDiscount(null)
      }
    } catch (error) {
      console.error('Erro ao aplicar cupom:', error)
      alert('Erro ao aplicar cupom')
    }
  }

  const startSubscription = async (plan: SubscriptionPlan) => {
    setSelectedPlan(plan)
    setShowPaymentModal(true)
  }

  const processPayment = async (paymentMethod: string) => {
    if (!selectedPlan) return

    setIsProcessing(true)

    try {
      const price = billingCycle === 'yearly' ? selectedPlan.price_yearly : selectedPlan.price_monthly
      const finalPrice = appliedDiscount ? appliedDiscount.final_price : price

      // Criar assinatura
      const { data: subscription, error: subError } = await supabase
        .from('user_subscriptions')
        .insert([{
          user_id: user?.id,
          plan_id: selectedPlan.id,
          billing_cycle: billingCycle,
          payment_method: paymentMethod,
          status: 'active',
          subscription_end_date: new Date(Date.now() + (billingCycle === 'yearly' ? 365 : 30) * 24 * 60 * 60 * 1000).toISOString()
        }])
        .select()
        .single()

      if (subscription && !subError) {
        // Processar pagamento
        const { data: payment, error: payError } = await supabase
          .rpc('process_subscription_payment', {
            p_subscription_id: subscription.id,
            p_amount: finalPrice,
            p_payment_method: paymentMethod,
            p_external_payment_id: `mock_${Date.now()}`
          })

        if (!payError) {
          // Registrar uso do cupom se aplicado
          if (appliedDiscount) {
            await supabase
              .from('coupon_usage')
              .insert([{
                coupon_id: appliedDiscount.coupon_id,
                user_id: user?.id,
                subscription_id: subscription.id,
                discount_applied: appliedDiscount.discount_amount
              }])
          }

          alert(`Assinatura ${selectedPlan.name} ativada com sucesso!`)
          setShowPaymentModal(false)
          setSelectedPlan(null)
          setAppliedDiscount(null)
          setCouponCode('')
          loadUserSubscription()
        }
      }
    } catch (error) {
      console.error('Erro ao processar pagamento:', error)
      alert('Erro ao processar pagamento. Tente novamente.')
    } finally {
      setIsProcessing(false)
    }
  }

  const cancelSubscription = async () => {
    if (!userSubscription) return

    const confirmed = confirm('Tem certeza que deseja cancelar sua assinatura?')
    if (!confirmed) return

    try {
      const { error } = await supabase
        .from('user_subscriptions')
        .update({ status: 'cancelled', auto_renew: false })
        .eq('id', userSubscription.id)

      if (!error) {
        alert('Assinatura cancelada. Você ainda terá acesso aos benefícios até o final do período pago.')
        loadUserSubscription()
      }
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error)
      alert('Erro ao cancelar assinatura')
    }
  }

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'gratuito': return <Gift className="w-8 h-8" />
      case 'premium': return <Crown className="w-8 h-8" />
      case 'premium plus': return <Star className="w-8 h-8" />
      default: return <Award className="w-8 h-8" />
    }
  }

  const getPlanColor = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'gratuito': return 'from-gray-500 to-gray-600'
      case 'premium': return 'from-purple-500 to-blue-500'
      case 'premium plus': return 'from-yellow-500 to-orange-500'
      default: return 'from-blue-500 to-purple-500'
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price)
  }

  const getYearlySavings = (monthly: number, yearly: number) => {
    const monthlyCost = monthly * 12
    const savings = monthlyCost - yearly
    const percentage = Math.round((savings / monthlyCost) * 100)
    return { savings, percentage }
  }

  return (
    <div className="premium-subscription-system p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-3 rounded-2xl mb-4"
        >
          <Crown className="w-6 h-6" />
          <span className="font-bold">MobiDrive Premium</span>
        </motion.div>
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          Maximize Seus Ganhos
        </h1>
        <p className="text-gray-600">
          Desbloqueie todo o potencial do MobiDrive com nossos planos premium
        </p>
      </div>

      {/* Status da Assinatura Atual */}
      {userSubscription && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl p-6 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold flex items-center gap-2">
                <Shield className="w-6 h-6" />
                Assinatura Ativa
              </h3>
              <p className="text-green-100">
                Plano {userSubscription.plan.name} - {userSubscription.billing_cycle === 'yearly' ? 'Anual' : 'Mensal'}
              </p>
              <p className="text-sm text-green-200">
                Válido até: {new Date(userSubscription.subscription_end_date).toLocaleDateString('pt-BR')}
              </p>
            </div>
            <button
              onClick={cancelSubscription}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-sm font-medium transition-all"
            >
              Cancelar
            </button>
          </div>
        </motion.div>
      )}

      {/* Toggle de Ciclo de Cobrança */}
      <div className="flex justify-center">
        <div className="bg-gray-100 rounded-2xl p-2 flex">
          <button
            onClick={() => setBillingCycle('monthly')}
            className={`px-6 py-2 rounded-xl font-medium transition-all ${
              billingCycle === 'monthly'
                ? 'bg-white text-gray-800 shadow-md'
                : 'text-gray-600'
            }`}
          >
            Mensal
          </button>
          <button
            onClick={() => setBillingCycle('yearly')}
            className={`px-6 py-2 rounded-xl font-medium transition-all ${
              billingCycle === 'yearly'
                ? 'bg-white text-gray-800 shadow-md'
                : 'text-gray-600'
            }`}
          >
            Anual
            <span className="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
              Economize até 50%
            </span>
          </button>
        </div>
      </div>

      {/* Planos de Assinatura */}
      <div className="grid md:grid-cols-3 gap-6">
        {plans.map((plan, index) => {
          const isCurrentPlan = userSubscription?.plan_id === plan.id
          const price = billingCycle === 'yearly' ? plan.price_yearly : plan.price_monthly
          const savings = billingCycle === 'yearly' ? getYearlySavings(plan.price_monthly, plan.price_yearly) : null
          
          return (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`relative bg-white rounded-2xl p-6 shadow-lg border-2 transition-all ${
                isCurrentPlan 
                  ? 'border-green-500 bg-green-50' 
                  : plan.name === 'Premium' 
                    ? 'border-purple-500 transform scale-105' 
                    : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              {plan.name === 'Premium' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                    Mais Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <div className={`w-16 h-16 bg-gradient-to-r ${getPlanColor(plan.name)} rounded-full flex items-center justify-center mx-auto mb-4 text-white`}>
                  {getPlanIcon(plan.name)}
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-2">{plan.name}</h3>
                <p className="text-gray-600 text-sm mb-4">{plan.description}</p>
                
                <div className="mb-4">
                  <div className="text-4xl font-bold text-gray-800">
                    {formatPrice(price)}
                  </div>
                  <div className="text-gray-500 text-sm">
                    por {billingCycle === 'yearly' ? 'ano' : 'mês'}
                  </div>
                  {savings && savings.percentage > 0 && (
                    <div className="text-green-600 text-sm font-medium">
                      Economize {savings.percentage}% ({formatPrice(savings.savings)})
                    </div>
                  )}
                </div>
              </div>

              {/* Recursos do Plano */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                    {plan.daily_ad_limit === 20 ? (
                      <span className="text-green-600 text-xs font-bold">20</span>
                    ) : (
                      <Infinity className="w-3 h-3 text-green-600" />
                    )}
                  </div>
                  <span className="text-gray-700 text-sm">
                    {plan.daily_ad_limit === 20 ? '20 anúncios/dia' : `${plan.daily_ad_limit.toLocaleString()} anúncios/dia`}
                  </span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="w-3 h-3 text-blue-600" />
                  </div>
                  <span className="text-gray-700 text-sm">
                    {plan.reward_multiplier}x recompensas
                  </span>
                </div>

                {plan.priority_ads && (
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-purple-100 rounded-full flex items-center justify-center">
                      <Zap className="w-3 h-3 text-purple-600" />
                    </div>
                    <span className="text-gray-700 text-sm">Anúncios prioritários</span>
                  </div>
                )}

                {plan.exclusive_offers && (
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center">
                      <Gift className="w-3 h-3 text-yellow-600" />
                    </div>
                    <span className="text-gray-700 text-sm">Ofertas exclusivas</span>
                  </div>
                )}
              </div>

              {/* Botão de Ação */}
              {isCurrentPlan ? (
                <div className="text-center">
                  <div className="bg-green-100 text-green-800 py-3 px-4 rounded-xl font-bold">
                    <Check className="w-5 h-5 inline mr-2" />
                    Plano Atual
                  </div>
                </div>
              ) : (
                <button
                  onClick={() => startSubscription(plan)}
                  disabled={plan.price_monthly === 0}
                  className={`w-full py-3 px-4 rounded-xl font-bold transition-all ${
                    plan.price_monthly === 0
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : `bg-gradient-to-r ${getPlanColor(plan.name)} text-white hover:shadow-lg transform hover:scale-105`
                  }`}
                >
                  {plan.price_monthly === 0 ? 'Plano Atual' : 'Assinar Agora'}
                </button>
              )}
            </motion.div>
          )
        })}
      </div>

      {/* Modal de Pagamento */}
      <AnimatePresence>
        {showPaymentModal && selectedPlan && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 max-w-md w-full max-h-[90vh] overflow-y-auto"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-800">
                  Assinar {selectedPlan.name}
                </h3>
                <button
                  onClick={() => setShowPaymentModal(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Resumo do Plano */}
              <div className="bg-gray-50 rounded-xl p-4 mb-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">Plano:</span>
                  <span>{selectedPlan.name}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">Ciclo:</span>
                  <span>{billingCycle === 'yearly' ? 'Anual' : 'Mensal'}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">Preço:</span>
                  <span className="font-bold">
                    {formatPrice(billingCycle === 'yearly' ? selectedPlan.price_yearly : selectedPlan.price_monthly)}
                  </span>
                </div>
                {appliedDiscount && (
                  <>
                    <div className="flex justify-between items-center mb-2 text-green-600">
                      <span className="font-medium">Desconto:</span>
                      <span>-{formatPrice(appliedDiscount.discount_amount)}</span>
                    </div>
                    <div className="flex justify-between items-center font-bold text-lg border-t pt-2">
                      <span>Total:</span>
                      <span>{formatPrice(appliedDiscount.final_price)}</span>
                    </div>
                  </>
                )}
              </div>

              {/* Cupom de Desconto */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Cupom de Desconto (Opcional)
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={couponCode}
                    onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                    placeholder="Digite o código"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    onClick={applyCoupon}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    Aplicar
                  </button>
                </div>
              </div>

              {/* Métodos de Pagamento */}
              <div className="space-y-3">
                <h4 className="font-medium text-gray-800">Método de Pagamento</h4>
                
                <button
                  onClick={() => processPayment('pix')}
                  disabled={isProcessing}
                  className="w-full flex items-center gap-3 p-4 border border-gray-300 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all disabled:opacity-50"
                >
                  <Smartphone className="w-6 h-6 text-blue-600" />
                  <div className="text-left">
                    <div className="font-medium">PIX</div>
                    <div className="text-sm text-gray-600">Pagamento instantâneo</div>
                  </div>
                </button>

                <button
                  onClick={() => processPayment('credit_card')}
                  disabled={isProcessing}
                  className="w-full flex items-center gap-3 p-4 border border-gray-300 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all disabled:opacity-50"
                >
                  <CreditCard className="w-6 h-6 text-green-600" />
                  <div className="text-left">
                    <div className="font-medium">Cartão de Crédito</div>
                    <div className="text-sm text-gray-600">Visa, Mastercard, Elo</div>
                  </div>
                </button>
              </div>

              {isProcessing && (
                <div className="mt-4 text-center">
                  <div className="inline-flex items-center gap-2 text-blue-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    Processando pagamento...
                  </div>
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default PremiumSubscriptionSystem
