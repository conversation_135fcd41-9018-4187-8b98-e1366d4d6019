-- 🔍 MOBIDRIVE DATABASE VALIDATION QUERIES
-- <PERSON>ript para validar a integridade e estrutura do banco de dados

-- =====================================================
-- 1. VERIFICAÇÃO DE TABELAS PRINCIPAIS
-- =====================================================

-- Verificar se todas as tabelas existem
SELECT 
    schemaname,
    tablename,
    tableowner,
    hasindexes,
    hasrules,
    hastriggers
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN (
    'profiles',
    'ride_requests', 
    'driver_locations',
    'notifications',
    'chat_messages',
    'notification_settings',
    'push_subscriptions',
    'analytics_sessions',
    'analytics_events',
    'analytics_performance'
)
ORDER BY tablename;

-- =====================================================
-- 2. VERIFICAÇÃO DE COLUNAS E TIPOS
-- =====================================================

-- Verificar estrutura da tabela profiles
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Verificar estrutura da tabela ride_requests
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'ride_requests' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Verificar estrutura da tabela chat_messages
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'chat_messages' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- =====================================================
-- 3. VERIFICAÇÃO DE RELACIONAMENTOS (FOREIGN KEYS)
-- =====================================================

SELECT
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    tc.constraint_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_schema = 'public'
AND tc.table_name IN (
    'ride_requests',
    'chat_messages',
    'notifications',
    'notification_settings',
    'push_subscriptions',
    'analytics_sessions',
    'analytics_events',
    'analytics_performance'
)
ORDER BY tc.table_name, kcu.column_name;

-- =====================================================
-- 4. VERIFICAÇÃO DE ÍNDICES
-- =====================================================

SELECT
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
AND tablename IN (
    'profiles',
    'ride_requests',
    'driver_locations',
    'chat_messages',
    'notifications',
    'analytics_events'
)
ORDER BY tablename, indexname;

-- =====================================================
-- 5. VERIFICAÇÃO DE POLÍTICAS RLS
-- =====================================================

SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- =====================================================
-- 6. VERIFICAÇÃO DE TRIGGERS
-- =====================================================

SELECT
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement,
    action_timing
FROM information_schema.triggers
WHERE trigger_schema = 'public'
ORDER BY event_object_table, trigger_name;

-- =====================================================
-- 7. VERIFICAÇÃO DE FUNÇÕES
-- =====================================================

SELECT
    routine_name,
    routine_type,
    data_type,
    routine_definition
FROM information_schema.routines
WHERE routine_schema = 'public'
AND routine_name LIKE '%mobidrive%' OR routine_name LIKE '%update_updated_at%'
ORDER BY routine_name;

-- =====================================================
-- 8. VERIFICAÇÃO DE DADOS DE TESTE
-- =====================================================

-- Contar registros em cada tabela
SELECT 'profiles' as table_name, COUNT(*) as record_count FROM profiles
UNION ALL
SELECT 'ride_requests', COUNT(*) FROM ride_requests
UNION ALL
SELECT 'driver_locations', COUNT(*) FROM driver_locations
UNION ALL
SELECT 'notifications', COUNT(*) FROM notifications
UNION ALL
SELECT 'chat_messages', COUNT(*) FROM chat_messages
UNION ALL
SELECT 'notification_settings', COUNT(*) FROM notification_settings
UNION ALL
SELECT 'push_subscriptions', COUNT(*) FROM push_subscriptions
UNION ALL
SELECT 'analytics_sessions', COUNT(*) FROM analytics_sessions
UNION ALL
SELECT 'analytics_events', COUNT(*) FROM analytics_events
UNION ALL
SELECT 'analytics_performance', COUNT(*) FROM analytics_performance
ORDER BY table_name;

-- =====================================================
-- 9. VERIFICAÇÃO DE INTEGRIDADE REFERENCIAL
-- =====================================================

-- Verificar corridas órfãs (sem usuário)
SELECT 
    COUNT(*) as orphaned_rides,
    'ride_requests without valid user_id' as issue
FROM ride_requests r
LEFT JOIN profiles p ON r.user_id = p.id
WHERE p.id IS NULL;

-- Verificar mensagens órfãs (sem corrida)
SELECT 
    COUNT(*) as orphaned_messages,
    'chat_messages without valid ride_id' as issue
FROM chat_messages c
LEFT JOIN ride_requests r ON c.ride_id = r.id
WHERE r.id IS NULL;

-- Verificar notificações órfãs (sem usuário)
SELECT 
    COUNT(*) as orphaned_notifications,
    'notifications without valid user_id' as issue
FROM notifications n
LEFT JOIN profiles p ON n.user_id = p.id
WHERE p.id IS NULL;

-- =====================================================
-- 10. VERIFICAÇÃO DE PERFORMANCE
-- =====================================================

-- Verificar tamanho das tabelas
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN (
    'profiles',
    'ride_requests',
    'driver_locations',
    'notifications',
    'chat_messages',
    'analytics_events'
)
ORDER BY size_bytes DESC;

-- Verificar estatísticas de uso das tabelas
SELECT
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
WHERE schemaname = 'public'
ORDER BY n_live_tup DESC;

-- =====================================================
-- 11. VERIFICAÇÃO DE SEGURANÇA
-- =====================================================

-- Verificar se RLS está habilitado
SELECT
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN (
    'profiles',
    'ride_requests',
    'notifications',
    'chat_messages'
)
ORDER BY tablename;

-- Verificar permissões de usuários
SELECT
    grantee,
    table_schema,
    table_name,
    privilege_type,
    is_grantable
FROM information_schema.role_table_grants
WHERE table_schema = 'public'
AND table_name IN (
    'profiles',
    'ride_requests',
    'notifications',
    'chat_messages'
)
ORDER BY table_name, grantee, privilege_type;

-- =====================================================
-- 12. QUERIES DE TESTE FUNCIONAL
-- =====================================================

-- Teste de inserção de perfil (simulado)
-- INSERT INTO profiles (id, email, full_name, phone) 
-- VALUES (gen_random_uuid(), '<EMAIL>', 'Test User', '+5511999999999');

-- Teste de inserção de corrida (simulado)
-- INSERT INTO ride_requests (user_id, origin_address, destination_address, status)
-- VALUES ((SELECT id FROM profiles LIMIT 1), 'Teste Origem', 'Teste Destino', 'pending');

-- Teste de inserção de mensagem de chat (simulado)
-- INSERT INTO chat_messages (ride_id, sender_id, sender_type, message)
-- VALUES (
--     (SELECT id FROM ride_requests LIMIT 1),
--     (SELECT user_id FROM ride_requests LIMIT 1),
--     'passenger',
--     'Mensagem de teste'
-- );

-- =====================================================
-- 13. VERIFICAÇÃO DE BACKUP E RECOVERY
-- =====================================================

-- Verificar configurações de backup
SELECT name, setting, unit, context 
FROM pg_settings 
WHERE name IN (
    'wal_level',
    'archive_mode',
    'archive_command',
    'max_wal_senders',
    'wal_keep_segments'
);

-- =====================================================
-- 14. RELATÓRIO FINAL DE VALIDAÇÃO
-- =====================================================

-- Resumo geral do banco
SELECT 
    'Database Validation Summary' as report_section,
    (SELECT COUNT(*) FROM pg_tables WHERE schemaname = 'public') as total_tables,
    (SELECT COUNT(*) FROM information_schema.table_constraints WHERE constraint_type = 'FOREIGN KEY' AND table_schema = 'public') as foreign_keys,
    (SELECT COUNT(*) FROM pg_indexes WHERE schemaname = 'public') as total_indexes,
    (SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public') as rls_policies,
    (SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_schema = 'public') as triggers,
    NOW() as validation_timestamp;
