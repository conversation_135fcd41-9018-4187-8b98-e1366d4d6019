/**
 * 🎯 CONFIGURAÇÃO APPODEAL - MOBIDRIVE
 * Configurações centralizadas para integração Appodeal
 */

export interface AppodealEnvironmentConfig {
  appKey: string;
  testMode: boolean;
  adTypes: string[];
  mediationSettings: {
    autoCache: boolean;
    smartBanners: boolean;
    tabletBanners: boolean;
    childDirectedTreatment?: boolean;
  };
  revenueOptimization: {
    enableHeaderBidding: boolean;
    enableProgrammatic: boolean;
    enableDirectDeals: boolean;
  };
}

export interface AppodealPlatformConfig {
  web: AppodealEnvironmentConfig;
  ios: AppodealEnvironmentConfig;
  android: AppodealEnvironmentConfig;
}

// Configuração principal do Appodeal
export const APPODEAL_CONFIG: AppodealPlatformConfig = {
  // Configuração para ambiente WEB/PWA (simulação)
  web: {
    appKey: process.env.REACT_APP_APPODEAL_WEB_KEY || 'web-simulation-key',
    testMode: process.env.NODE_ENV !== 'production',
    adTypes: ['rewarded_video', 'interstitial', 'banner'],
    mediationSettings: {
      autoCache: true,
      smartBanners: true,
      tabletBanners: false,
      childDirectedTreatment: false
    },
    revenueOptimization: {
      enableHeaderBidding: false, // Não disponível para web
      enableProgrammatic: false,
      enableDirectDeals: false
    }
  },

  // Configuração para iOS (futuro)
  ios: {
    appKey: process.env.REACT_APP_APPODEAL_IOS_KEY || 'YOUR_IOS_APP_KEY',
    testMode: process.env.NODE_ENV !== 'production',
    adTypes: ['rewarded_video', 'interstitial', 'banner', 'native'],
    mediationSettings: {
      autoCache: true,
      smartBanners: true,
      tabletBanners: true,
      childDirectedTreatment: false
    },
    revenueOptimization: {
      enableHeaderBidding: true,
      enableProgrammatic: true,
      enableDirectDeals: true
    }
  },

  // Configuração para Android (futuro)
  android: {
    appKey: process.env.REACT_APP_APPODEAL_ANDROID_KEY || 'YOUR_ANDROID_APP_KEY',
    testMode: process.env.NODE_ENV !== 'production',
    adTypes: ['rewarded_video', 'interstitial', 'banner', 'native'],
    mediationSettings: {
      autoCache: true,
      smartBanners: true,
      tabletBanners: true,
      childDirectedTreatment: false
    },
    revenueOptimization: {
      enableHeaderBidding: true,
      enableProgrammatic: true,
      enableDirectDeals: true
    }
  }
};

// Configurações de anúncios por categoria
export const AD_CATEGORIES = {
  high_revenue: {
    minCPM: 50, // R$ 0,50
    maxCPM: 100, // R$ 1,00
    priority: 1,
    networks: ['AppLovin', 'Unity Ads', 'Vungle', 'IronSource']
  },
  medium_revenue: {
    minCPM: 25, // R$ 0,25
    maxCPM: 60, // R$ 0,60
    priority: 2,
    networks: ['AdMob', 'Facebook', 'Chartboost', 'Tapjoy']
  },
  low_revenue: {
    minCPM: 10, // R$ 0,10
    maxCPM: 30, // R$ 0,30
    priority: 3,
    networks: ['StartApp', 'Mobvista', 'InMobi', 'Smaato']
  }
};

// Configurações de compliance e políticas
export const COMPLIANCE_CONFIG = {
  gdpr: {
    enabled: true,
    consentRequired: true,
    privacyPolicyUrl: 'https://mundodainovacao.com/privacy-policy'
  },
  coppa: {
    enabled: false, // MobiDrive é 18+
    childDirectedTreatment: false
  },
  ccpa: {
    enabled: true,
    doNotSellEnabled: true
  },
  lgpd: {
    enabled: true, // Lei Geral de Proteção de Dados (Brasil)
    consentRequired: true
  }
};

// Configurações de teste e debug
export const DEBUG_CONFIG = {
  enableLogging: process.env.NODE_ENV !== 'production',
  enableTestAds: process.env.NODE_ENV !== 'production',
  logLevel: process.env.NODE_ENV === 'production' ? 'error' : 'debug',
  enableAnalytics: true,
  enableCrashReporting: true
};

// URLs e endpoints importantes
export const APPODEAL_ENDPOINTS = {
  dashboard: 'https://app.appodeal.com',
  documentation: 'https://docs.appodeal.com',
  support: 'https://appodeal.com/support',
  status: 'https://status.appodeal.com',
  api: {
    base: 'https://api.appodeal.com/v1',
    reporting: 'https://api.appodeal.com/v1/reporting',
    mediation: 'https://api.appodeal.com/v1/mediation'
  }
};

// Configurações de otimização de receita
export const REVENUE_OPTIMIZATION = {
  // Configurações de waterfall
  waterfall: {
    enableOptimization: true,
    refreshInterval: 3600000, // 1 hora
    minBidFloor: 10 // R$ 0,10 mínimo
  },
  
  // Configurações de header bidding
  headerBidding: {
    enabled: false, // Apenas para apps nativos
    timeout: 5000, // 5 segundos
    maxBidders: 5
  },
  
  // Configurações de A/B testing
  abTesting: {
    enabled: true,
    testGroups: ['control', 'optimized'],
    trafficSplit: 50 // 50/50
  }
};

// Configurações específicas do MobiDrive
export const MOBIDRIVE_AD_CONFIG = {
  dailyLimit: 1000, // R$ 10 em centavos
  maxAdsPerDay: 50,
  minWatchTime: 0.8, // 80% do vídeo
  rewardMultiplier: 1.0,
  bonusForCompletion: 0.1, // 10% bônus por assistir completo
  
  // Configurações de frequência
  frequency: {
    maxAdsPerHour: 10,
    cooldownBetweenAds: 30000, // 30 segundos
    maxConsecutiveAds: 3
  },
  
  // Configurações de targeting
  targeting: {
    ageRange: [18, 65],
    interests: ['transportation', 'mobility', 'urban', 'technology'],
    location: 'BR', // Brasil
    language: 'pt-BR'
  }
};

// Função para obter configuração por ambiente
export function getAppodealConfig(environment: 'web' | 'ios' | 'android'): AppodealEnvironmentConfig {
  return APPODEAL_CONFIG[environment];
}

// Função para validar configuração
export function validateAppodealConfig(config: AppodealEnvironmentConfig): boolean {
  return !!(
    config.appKey &&
    config.appKey !== 'YOUR_IOS_APP_KEY' &&
    config.appKey !== 'YOUR_ANDROID_APP_KEY' &&
    config.adTypes.length > 0
  );
}

// Função para obter configuração de anúncio por categoria
export function getAdCategoryConfig(category: keyof typeof AD_CATEGORIES) {
  return AD_CATEGORIES[category];
}

// Exportar configuração padrão
export default APPODEAL_CONFIG;
