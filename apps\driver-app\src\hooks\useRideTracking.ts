import { useState, useEffect, useCallback, useRef } from 'react'
import { supabase } from '../lib/supabase'
import { rideService } from '../services/RideService'
import { driverLocationService, DriverLocationUpdate } from '../services/DriverLocationService'

export interface Driver {
  id: string
  name: string
  phone: string
  rating: number
  vehicle: {
    model: string
    color: string
    plate: string
  }
  location: [number, number]
  bearing: number
  speed: number
  eta: number
  status: 'searching' | 'assigned' | 'en_route' | 'arrived'
}

export interface Ride {
  id: string
  passenger_id: string
  driver_id?: string
  status: 'requested' | 'assigned' | 'en_route' | 'in_progress' | 'completed' | 'cancelled'
  origin: {
    latitude: number
    longitude: number
    address: string
  }
  destination: {
    latitude: number
    longitude: number
    address: string
  }
  estimated_duration: number
  estimated_distance: number
  estimated_price: number
  driver?: Driver
}

interface UseRideTrackingOptions {
  rideId?: string
  autoStart?: boolean
}

export const useRideTracking = (options: UseRideTrackingOptions = {}) => {
  const { rideId, autoStart = true } = options

  // State
  const [ride, setRide] = useState<Ride | null>(null)
  const [driver, setDriver] = useState<Driver | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchPhase, setSearchPhase] = useState(0)

  // Refs for subscriptions
  const rideSubscription = useRef<any>(null)
  const driverLocationSubscription = useRef<any>(null)

  /**
   * Create a new ride request
   */
  const createRide = useCallback(async (
    origin: { latitude: number; longitude: number; address: string },
    destination: { latitude: number; longitude: number; address: string },
    estimatedDuration: number,
    estimatedDistance: number,
    estimatedPrice: number,
    paymentMethod: string = 'cash'
  ): Promise<string | null> => {
    try {
      setIsLoading(true)
      setError(null)

      console.log('🚗 Creating ride request...')
      console.log('📊 Supabase URL:', supabase.supabaseUrl)
      console.log('🔑 Supabase Key exists:', !!supabase.supabaseKey)

      const { data: { user }, error: authError } = await supabase.auth.getUser()
      console.log('👤 Auth check:', { user: user?.id, error: authError })

      if (!user) {
        console.warn('⚠️ User not authenticated, using mock user')
        // For testing, create a mock user ID
        const mockUserId = 'mock-user-' + Date.now()
        console.log('🎭 Using mock user ID:', mockUserId)
      }

      // For now, create a mock ride without Supabase
      const mockRideId = 'ride-' + Date.now()
      const mockRide = {
        id: mockRideId,
        passenger_id: user?.id || 'mock-user',
        status: 'requested' as const,
        origin,
        destination,
        estimated_duration: estimatedDuration,
        estimated_distance: estimatedDistance,
        estimated_price: estimatedPrice
      }

      console.log('✅ Mock ride created:', mockRide)
      setRide(mockRide)

      // Start searching for drivers
      startDriverSearch(mockRideId, [origin.longitude, origin.latitude])

      return mockRideId

    } catch (error) {
      console.error('❌ Exception creating ride:', error)
      setError('Erro inesperado ao criar corrida')
      return null
    } finally {
      setIsLoading(false)
    }
  }, [])

  /**
   * Start driver search simulation
   */
  const startDriverSearch = useCallback(async (rideId: string, location: [number, number]) => {
    console.log('🔍 Starting driver search for ride:', rideId)

    const searchPhases = [
      'Procurando motoristas próximos...',
      'Analisando disponibilidade...',
      'Motorista encontrado!',
      'Confirmando corrida...'
    ]

    let currentPhase = 0
    const searchInterval = setInterval(() => {
      setSearchPhase(currentPhase)
      currentPhase++
      
      if (currentPhase >= searchPhases.length) {
        clearInterval(searchInterval)
        // Simulate driver assignment
        assignMockDriver(rideId, location)
      }
    }, 1500)

    // Cleanup function
    return () => clearInterval(searchInterval)
  }, [])

  /**
   * Assign a mock driver (for testing)
   */
  const assignMockDriver = useCallback(async (rideId: string, userLocation: [number, number]) => {
    try {
      console.log('👨‍💼 Assigning mock driver to ride:', rideId)

      // Create mock driver location near user
      const driverLocation: [number, number] = [
        userLocation[0] + (Math.random() - 0.5) * 0.02, // ~1km radius
        userLocation[1] + (Math.random() - 0.5) * 0.02
      ]

      const mockDriver: Driver = {
        id: 'mock-driver-' + Date.now(),
        name: 'João Silva',
        phone: '+55 11 99999-9999',
        rating: 4.8,
        vehicle: {
          model: 'Honda Civic',
          color: 'Branco',
          plate: 'ABC-1234'
        },
        location: driverLocation,
        bearing: Math.floor(Math.random() * 360),
        speed: 30 + Math.random() * 20,
        eta: 3 + Math.floor(Math.random() * 7), // 3-10 minutes
        status: 'en_route'
      }

      setDriver(mockDriver)

      // Update ride status
      const { error } = await supabase
        .from('rides')
        .update({
          status: 'assigned',
          driver_id: mockDriver.id,
          assigned_at: new Date().toISOString()
        })
        .eq('id', rideId)

      if (error) {
        console.error('❌ Error updating ride status:', error)
      } else {
        console.log('✅ Driver assigned successfully')
      }

      // Simulate driver location updates
      startDriverLocationUpdates(mockDriver, rideId)

    } catch (error) {
      console.error('❌ Error assigning mock driver:', error)
    }
  }, [])

  /**
   * Start driver location updates simulation (now handled by WaitingDriverMap)
   */
  const startDriverLocationUpdates = useCallback((driver: Driver, rideId: string) => {
    console.log('📍 Driver location updates will be handled by WaitingDriverMap component')

    // The real-time tracking is now handled by the WaitingDriverMap component
    // This function is kept for compatibility but doesn't need to do anything

    return () => {
      // Cleanup handled by WaitingDriverMap
    }
  }, [])

  /**
   * Load ride data
   */
  const loadRide = useCallback(async (rideId: string) => {
    try {
      setIsLoading(true)
      setError(null)

      console.log('📋 Loading ride data:', rideId)

      const { data, error } = await supabase
        .from('rides')
        .select(`
          *,
          drivers (
            id,
            name,
            phone,
            rating,
            vehicle_model,
            vehicle_color,
            vehicle_plate
          )
        `)
        .eq('id', rideId)
        .single()

      if (error) {
        console.error('❌ Error loading ride:', error)
        setError('Erro ao carregar dados da corrida')
        return
      }

      console.log('✅ Ride data loaded:', data)
      setRide(data)

      // If ride has a driver, load driver location
      if (data.driver_id) {
        loadDriverLocation(data.driver_id, rideId)
      }

    } catch (error) {
      console.error('❌ Exception loading ride:', error)
      setError('Erro inesperado ao carregar corrida')
    } finally {
      setIsLoading(false)
    }
  }, [])

  /**
   * Load driver location
   */
  const loadDriverLocation = useCallback(async (driverId: string, rideId: string) => {
    try {
      const { data, error } = await supabase
        .from('driver_locations')
        .select('*')
        .eq('driver_id', driverId)
        .eq('ride_id', rideId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (error) {
        console.warn('⚠️ No driver location found:', error)
        return
      }

      console.log('📍 Driver location loaded:', data)
      
      // Update driver state with location
      setDriver(prev => prev ? {
        ...prev,
        location: [data.longitude, data.latitude],
        bearing: data.bearing || 0,
        speed: data.speed || 0,
        eta: data.eta || 5,
        status: data.status || 'en_route'
      } : null)

    } catch (error) {
      console.error('❌ Exception loading driver location:', error)
    }
  }, [])

  /**
   * Subscribe to ride updates
   */
  const subscribeToRideUpdates = useCallback((rideId: string) => {
    console.log('🔄 Subscribing to ride updates:', rideId)

    rideSubscription.current = supabase
      .channel(`ride-${rideId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'rides',
          filter: `id=eq.${rideId}`
        },
        (payload) => {
          console.log('📡 Ride update received:', payload)
          if (payload.new) {
            setRide(payload.new as Ride)
          }
        }
      )
      .subscribe()

    return () => {
      if (rideSubscription.current) {
        rideSubscription.current.unsubscribe()
        rideSubscription.current = null
      }
    }
  }, [])

  /**
   * Subscribe to driver location updates (melhorado)
   */
  const subscribeToDriverLocationUpdates = useCallback((driverId: string) => {
    console.log('🔄 Subscribing to driver location updates:', driverId)

    // Usar o novo DriverLocationService
    const unsubscribe = driverLocationService.subscribeToDriverLocation(
      driverId,
      (locationUpdate: DriverLocationUpdate) => {
        console.log('📍 Driver location update received:', locationUpdate)

        setDriver(prev => prev ? {
          ...prev,
          location: locationUpdate.location,
          bearing: locationUpdate.heading || 0,
          speed: locationUpdate.speed || 0,
          eta: Math.max(Math.round((prev.location ?
            calculateDistance(prev.location, locationUpdate.location) : 1) * 2), 2), // ETA baseado na distância
          status: 'en_route'
        } : null)
      }
    )

    // Armazenar função de cleanup
    driverLocationSubscription.current = { unsubscribe }

    return unsubscribe
  }, [])

  /**
   * Calcular distância entre dois pontos (Haversine)
   */
  const calculateDistance = useCallback((point1: [number, number], point2: [number, number]): number => {
    const R = 6371 // Raio da Terra em km
    const dLat = (point2[1] - point1[1]) * Math.PI / 180
    const dLon = (point2[0] - point1[0]) * Math.PI / 180
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1[1] * Math.PI / 180) * Math.cos(point2[1] * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }, [])

  /**
   * Cancel ride
   */
  const cancelRide = useCallback(async (reason: string = 'Cancelled by passenger') => {
    if (!ride) return false

    try {
      setIsLoading(true)
      
      const { error } = await supabase
        .from('rides')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString(),
          cancellation_reason: reason
        })
        .eq('id', ride.id)

      if (error) {
        console.error('❌ Error cancelling ride:', error)
        setError('Erro ao cancelar corrida')
        return false
      }

      console.log('✅ Ride cancelled successfully')
      return true

    } catch (error) {
      console.error('❌ Exception cancelling ride:', error)
      setError('Erro inesperado ao cancelar corrida')
      return false
    } finally {
      setIsLoading(false)
    }
  }, [ride])

  // Auto-load ride on mount
  useEffect(() => {
    if (rideId && autoStart) {
      loadRide(rideId)
    }
  }, [rideId, autoStart, loadRide])

  // Subscribe to updates when ride is loaded
  useEffect(() => {
    if (ride?.id) {
      const unsubscribeRide = subscribeToRideUpdates(ride.id)
      const unsubscribeDriver = subscribeToDriverLocationUpdates(ride.id)

      return () => {
        unsubscribeRide()
        unsubscribeDriver()
      }
    }
  }, [ride?.id, subscribeToRideUpdates, subscribeToDriverLocationUpdates])

  // Cleanup subscriptions on unmount
  useEffect(() => {
    return () => {
      if (rideSubscription.current) {
        rideSubscription.current.unsubscribe()
      }
      if (driverLocationSubscription.current) {
        driverLocationSubscription.current.unsubscribe()
      }
    }
  }, [])

  return {
    // State
    ride,
    driver,
    isLoading,
    error,
    searchPhase,

    // Actions
    createRide,
    loadRide,
    cancelRide,

    // Utils
    searchPhases: [
      'Procurando motoristas próximos...',
      'Analisando disponibilidade...',
      'Motorista encontrado!',
      'Confirmando corrida...'
    ]
  }
}

export default useRideTracking
