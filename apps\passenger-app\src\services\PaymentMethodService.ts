// =====================================================
// SERVIÇO DE MÉTODOS DE PAGAMENTO
// Gerencia métodos de pagamento integrado ao Supabase
// =====================================================

import { supabase } from '../lib/supabase'
import { APP_SETTINGS } from '../../../../shared/lib/app-config'

// Tipos locais para métodos de pagamento
export type PaymentMethodType = 'cash' | 'credit_card' | 'debit_card' | 'pix' | 'digital_wallet'

export interface PaymentMethod {
  id: string
  user_id: string
  type: PaymentMethodType
  name: string
  is_default: boolean
  is_active: boolean
  created_at: string
  updated_at?: string
  card_last_four?: string
  card_brand?: string
  card_holder_name?: string
  card_expiry_month?: string
  card_expiry_year?: string
  pix_key?: string
  cash_enabled?: boolean
  cash_change_limit?: number
  cash_notes?: string
  display_info?: {
    icon: string
    display_name: string
    description: string
  }
}

export class PaymentMethodService {

  /**
   * Buscar métodos de pagamento do usuário
   */
  static async getUserPaymentMethods(userId: string): Promise<PaymentMethod[]> {
    try {
      const { data, error } = await supabase
        .from('payment_methods_detailed')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('is_default', { ascending: false })
        .order('type', { ascending: true })

      if (error) {
        console.error('Erro ao buscar métodos de pagamento:', error)
        return this.getDefaultPaymentMethods()
      }

      // Se não há métodos, criar o padrão (dinheiro)
      if (!data || data.length === 0) {
        await this.createDefaultCashMethod(userId)
        return this.getUserPaymentMethods(userId)
      }

      return data
    } catch (error) {
      console.error('Erro no serviço de métodos de pagamento:', error)
      return this.getDefaultPaymentMethods()
    }
  }

  /**
   * Buscar método de pagamento padrão
   */
  static async getDefaultPaymentMethod(userId: string): Promise<PaymentMethod | null> {
    try {
      const { data, error } = await supabase
        .rpc('get_default_payment_method', { user_uuid: userId })

      if (error) {
        console.error('Erro ao buscar método padrão:', error)
        return this.getDefaultCashMethod()
      }

      return data?.[0] || this.getDefaultCashMethod()
    } catch (error) {
      console.error('Erro ao buscar método padrão:', error)
      return this.getDefaultCashMethod()
    }
  }

  /**
   * Criar método de pagamento em dinheiro padrão
   */
  static async createDefaultCashMethod(userId: string): Promise<PaymentMethod> {
    try {
      const cashMethod = {
        user_id: userId,
        type: 'cash' as PaymentMethodType,
        name: 'Dinheiro',
        is_default: true,
        is_active: true,
        cash_enabled: true,
        cash_change_limit: APP_SETTINGS.payment.cash.defaultChangeLimit,
        cash_notes: APP_SETTINGS.payment.cash.notes
      }

      const { data, error } = await supabase
        .from('payment_methods')
        .insert(cashMethod)
        .select()
        .single()

      if (error) {
        console.error('Erro ao criar método de dinheiro:', error)
        return this.getDefaultCashMethod()
      }

      return data
    } catch (error) {
      console.error('Erro ao criar método padrão:', error)
      return this.getDefaultCashMethod()
    }
  }

  /**
   * Definir método como padrão
   */
  static async setDefaultPaymentMethod(userId: string, methodId: string): Promise<boolean> {
    try {
      // Remover padrão de todos os métodos
      await supabase
        .from('payment_methods')
        .update({ is_default: false })
        .eq('user_id', userId)

      // Definir novo padrão
      const { error } = await supabase
        .from('payment_methods')
        .update({ is_default: true })
        .eq('id', methodId)
        .eq('user_id', userId)

      if (error) {
        console.error('Erro ao definir método padrão:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Erro ao definir método padrão:', error)
      return false
    }
  }

  /**
   * Adicionar novo método de pagamento
   */
  static async addPaymentMethod(
    userId: string,
    method: Partial<PaymentMethod>
  ): Promise<PaymentMethod | null> {
    try {
      const newMethod = {
        user_id: userId,
        type: method.type || 'credit_card',
        name: method.name || 'Novo Método',
        is_default: method.is_default || false,
        is_active: true,
        card_last_four: method.card_last_four,
        card_brand: method.card_brand,
        card_holder_name: method.card_holder_name,
        card_expiry_month: method.card_expiry_month,
        card_expiry_year: method.card_expiry_year,
        pix_key: method.pix_key,
        nickname: method.nickname
      }

      const { data, error } = await supabase
        .from('payment_methods')
        .insert(newMethod)
        .select()
        .single()

      if (error) {
        console.error('Erro ao adicionar método:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Erro ao adicionar método:', error)
      return null
    }
  }

  /**
   * Remover método de pagamento
   */
  static async removePaymentMethod(userId: string, methodId: string): Promise<boolean> {
    try {
      // Não permitir remover se for o único método
      const methods = await this.getUserPaymentMethods(userId)
      if (methods.length <= 1) {
        console.warn('Não é possível remover o único método de pagamento')
        return false
      }

      const { error } = await supabase
        .from('payment_methods')
        .update({ is_active: false })
        .eq('id', methodId)
        .eq('user_id', userId)

      if (error) {
        console.error('Erro ao remover método:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Erro ao remover método:', error)
      return false
    }
  }

  /**
   * Obter métodos padrão (fallback)
   */
  private static getDefaultPaymentMethods(): PaymentMethod[] {
    return [
      this.getDefaultCashMethod(),
      {
        id: 'default-credit',
        user_id: '',
        type: 'credit_card',
        name: 'Cartão de Crédito',
        is_default: false,
        is_active: true,
        created_at: new Date().toISOString(),
        card_last_four: '1234',
        card_brand: 'visa',
        display_info: {
          icon: '💳',
          display_name: 'Cartão de Crédito',
          description: '**** **** **** 1234'
        }
      },
      {
        id: 'default-pix',
        user_id: '',
        type: 'pix',
        name: 'PIX',
        is_default: false,
        is_active: true,
        created_at: new Date().toISOString(),
        display_info: {
          icon: '📱',
          display_name: 'PIX',
          description: 'Pagamento instantâneo'
        }
      }
    ]
  }

  /**
   * Obter método de dinheiro padrão
   */
  private static getDefaultCashMethod(): PaymentMethod {
    return {
      id: 'default-cash',
      user_id: '',
      type: 'cash',
      name: 'Dinheiro',
      is_default: true,
      is_active: true,
      created_at: new Date().toISOString(),
      cash_enabled: true,
      cash_change_limit: APP_SETTINGS.payment.cash.defaultChangeLimit,
      cash_notes: APP_SETTINGS.payment.cash.notes,
      display_info: {
        icon: '💵',
        display_name: 'Dinheiro',
        description: 'Pagamento em espécie'
      }
    }
  }

  /**
   * Validar dados do cartão
   */
  static validateCardData(cardData: {
    number: string
    name: string
    expiry: string
    cvv: string
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // Validar número do cartão
    const cleanNumber = cardData.number.replace(/\s/g, '')
    if (!/^\d{13,19}$/.test(cleanNumber)) {
      errors.push('Número do cartão inválido')
    }

    // Validar nome
    if (!cardData.name || cardData.name.length < 2) {
      errors.push('Nome do portador é obrigatório')
    }

    // Validar data de expiração
    if (!/^\d{2}\/\d{2}$/.test(cardData.expiry)) {
      errors.push('Data de expiração inválida (MM/AA)')
    }

    // Validar CVV
    if (!/^\d{3,4}$/.test(cardData.cvv)) {
      errors.push('CVV inválido')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Detectar bandeira do cartão
   */
  static detectCardBrand(number: string): string {
    const cleaned = number.replace(/\s/g, '')

    if (/^4/.test(cleaned)) return 'visa'
    if (/^5[1-5]/.test(cleaned)) return 'mastercard'
    if (/^3[47]/.test(cleaned)) return 'amex'
    if (/^6/.test(cleaned)) return 'discover'
    if (/^35/.test(cleaned)) return 'jcb'

    return 'unknown'
  }

  /**
   * Formatar número do cartão
   */
  static formatCardNumber(number: string): string {
    const cleaned = number.replace(/\s/g, '')
    const groups = cleaned.match(/.{1,4}/g) || []
    return groups.join(' ').substr(0, 19)
  }

  /**
   * Formatar data de expiração
   */
  static formatExpiryDate(expiry: string): string {
    const cleaned = expiry.replace(/\D/g, '')
    if (cleaned.length >= 2) {
      return cleaned.substr(0, 2) + '/' + cleaned.substr(2, 2)
    }
    return cleaned
  }
}
