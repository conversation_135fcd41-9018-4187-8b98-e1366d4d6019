import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Coins, DollarSign, Play, Gift, TrendingUp,
  Star, Award, Target, Zap, Crown, ArrowLeft
} from 'lucide-react'
// Componentes removidos - migração para Appodeal

export const RewardsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'ads' | 'withdraw'>('ads')

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  }

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Background GIF */}
      <div className="absolute inset-0 z-0">
        <img
          src="/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pb/Pb_00000.gif"
          alt="Background"
          className="w-full h-full object-cover opacity-30"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-purple-900/30 to-black/70"></div>
      </div>

      {/* Header */}
      <div className="relative z-10 p-4">
        <div className="flex items-center justify-between mb-6">
          <motion.button
            onClick={() => window.history.back()}
            className="bg-black/20 backdrop-blur-md p-3 rounded-xl border border-white/10"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <ArrowLeft className="w-6 h-6 text-white" />
          </motion.button>

          <div className="text-center">
            <h1 className="text-2xl font-bold text-white">💰 Recompensas</h1>
            <p className="text-white/70 text-sm">Ganhe dinheiro real!</p>
          </div>

          <div className="w-12"></div>
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 px-4 pb-20">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-6"
        >
          {/* Hero Section */}
          <motion.div variants={itemVariants}>
            <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <Coins className="w-8 h-8 text-white" />
              </motion.div>

              <h1 className="text-2xl font-bold mb-3 text-white">
                💰 Ganhe Dinheiro Real!
              </h1>
              <p className="text-white/70 mb-4 text-sm">
                Assista até 20 anúncios por dia e receba 50% da receita
              </p>
            
              <div className="grid grid-cols-3 gap-2">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <Play className="w-6 h-6 mx-auto mb-1 text-white" />
                  <div className="text-xs font-medium text-white">Assista</div>
                  <div className="text-xs text-white/60">Anúncios</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <Coins className="w-6 h-6 mx-auto mb-1 text-white" />
                  <div className="text-xs font-medium text-white">Ganhe</div>
                  <div className="text-xs text-white/60">MobiCoins</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <DollarSign className="w-6 h-6 mx-auto mb-1 text-white" />
                  <div className="text-xs font-medium text-white">Saque</div>
                  <div className="text-xs text-white/60">Dinheiro Real</div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Como Funciona */}
          <motion.div variants={itemVariants}>
            <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-400" />
                Como Funciona
              </h2>
            
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-2">
                    <Play className="w-6 h-6 text-blue-400" />
                  </div>
                  <h3 className="font-bold text-white mb-1 text-sm">1. Assista</h3>
                  <p className="text-white/70 text-xs">
                    Escolha um anúncio e assista
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-2">
                    <Coins className="w-6 h-6 text-purple-400" />
                  </div>
                  <h3 className="font-bold text-white mb-1 text-sm">2. Ganhe Coins</h3>
                  <p className="text-white/70 text-xs">
                    Receba MobiCoins automaticamente
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-2">
                    <TrendingUp className="w-6 h-6 text-green-400" />
                  </div>
                  <h3 className="font-bold text-white mb-1 text-sm">3. Acumule</h3>
                  <p className="text-white/70 text-xs">
                    Junte coins para saque
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-emerald-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-2">
                    <DollarSign className="w-6 h-6 text-emerald-400" />
                  </div>
                  <h3 className="font-bold text-white mb-1 text-sm">4. Saque</h3>
                  <p className="text-white/70 text-xs">
                    Converta em dinheiro via PIX
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Benefícios */}
          <motion.div variants={itemVariants}>
            <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
              <h2 className="text-xl font-bold mb-4 flex items-center gap-2 text-white">
                <Award className="w-5 h-5 text-yellow-400" />
                Por que Escolher o MobiDrive?
              </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-yellow-500/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                    <Target className="w-5 h-5 text-yellow-400" />
                  </div>
                  <div>
                    <h3 className="font-bold text-white text-sm">50% de Participação</h3>
                    <p className="text-white/70 text-xs">Você recebe metade de toda receita gerada</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-500/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                    <Zap className="w-5 h-5 text-blue-400" />
                  </div>
                  <div>
                    <h3 className="font-bold text-white text-sm">Pagamento Instantâneo</h3>
                    <p className="text-white/70 text-xs">Saque via PIX sem taxas</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-500/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                    <Gift className="w-5 h-5 text-green-400" />
                  </div>
                  <div>
                    <h3 className="font-bold text-white text-sm">Anúncios de Qualidade</h3>
                    <p className="text-white/70 text-xs">Conteúdo relevante e interessante</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-purple-500/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                    <Star className="w-5 h-5 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="font-bold text-white text-sm">Sistema de Níveis</h3>
                    <p className="text-white/70 text-xs">Suba de nível e desbloqueie benefícios</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

          {/* Banner Premium */}
          <motion.div variants={itemVariants}>
            <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-2xl p-6 border border-yellow-400/20 shadow-2xl text-center">
              <div className="flex items-center justify-center gap-2 mb-3">
                <Crown className="w-6 h-6 text-yellow-400" />
                <h2 className="text-xl font-bold text-white">Quer Ganhar Mais?</h2>
              </div>
              <p className="text-white/70 mb-4 text-sm">
                Com o Premium, assista até 1.000 anúncios por dia e ganhe 2x mais MobiCoins!
              </p>
              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <div className="text-lg font-bold text-white">1.000</div>
                  <div className="text-xs text-white/60">anúncios/dia</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                  <div className="text-lg font-bold text-white">2x</div>
                  <div className="text-xs text-white/60">ganhos</div>
                </div>
              </div>
              <motion.button
                onClick={() => window.location.href = '/premium'}
                className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-6 py-3 rounded-xl font-bold transition-all"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Crown className="w-4 h-4 inline mr-2" />
                Assinar Premium
              </motion.button>
            </div>
          </motion.div>

          {/* Tabs de Navegação */}
          <motion.div variants={itemVariants}>
            <div className="bg-black/20 backdrop-blur-md rounded-2xl p-2 border border-white/10 shadow-2xl">
              <div className="flex gap-2">
                <motion.button
                  onClick={() => setActiveTab('ads')}
                  className={`flex-1 py-3 px-4 rounded-xl font-bold transition-all text-sm ${
                    activeTab === 'ads'
                      ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white'
                      : 'text-white/70 hover:bg-white/10'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Play className="w-4 h-4 inline mr-2" />
                  Assistir Anúncios
                </motion.button>
                <motion.button
                  onClick={() => setActiveTab('withdraw')}
                  className={`flex-1 py-3 px-4 rounded-xl font-bold transition-all text-sm ${
                    activeTab === 'withdraw'
                      ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white'
                      : 'text-white/70 hover:bg-white/10'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <DollarSign className="w-4 h-4 inline mr-2" />
                  Sacar Dinheiro
                </motion.button>
              </div>
            </div>
          </motion.div>

          {/* Conteúdo das Tabs */}
          <motion.div variants={itemVariants}>
            {activeTab === 'ads' && (
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center">
                <h3 className="text-lg font-bold text-white mb-4">🚀 Migração para Appodeal</h3>
                <p className="text-white/70 mb-4">
                  Sistema de anúncios sendo migrado para Appodeal para melhor experiência e maiores ganhos.
                </p>
                <motion.button
                  onClick={() => window.location.href = '/free-ads'}
                  className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-3 rounded-xl font-bold"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Acessar Novo Sistema
                </motion.button>
              </div>
            )}
            {activeTab === 'withdraw' && (
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center">
                <h3 className="text-lg font-bold text-white mb-4">💰 Sistema de Saque</h3>
                <p className="text-white/70 mb-4">
                  Sistema de saque será integrado com Appodeal em breve.
                </p>
                <div className="text-yellow-400 text-sm">
                  🔄 Em desenvolvimento...
                </div>
              </div>
            )}
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}

export default RewardsPage
