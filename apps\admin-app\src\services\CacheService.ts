/**
 * Sistema de Cache Avançado para MobiDrive
 * Otimiza performance e reduz chamadas desnecessárias
 */

interface CacheItem<T> {
  data: T
  timestamp: number
  ttl: number // Time to live em milissegundos
}

interface LocationCache {
  lat: number
  lng: number
  address: string
  timestamp: number
}

interface DriverCache {
  drivers: any[]
  location: { lat: number; lng: number }
  timestamp: number
}

class CacheService {
  private cache = new Map<string, CacheItem<any>>()
  private locationCache: LocationCache | null = null
  private driversCache: DriverCache | null = null

  // Cache genérico
  set<T>(key: string, data: T, ttlMinutes: number = 5): void {
    const ttl = ttlMinutes * 60 * 1000 // Converter para milissegundos
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null

    // Verificar se expirou
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data as T
  }

  // Cache específico para localização do usuário
  setUserLocation(lat: number, lng: number, address: string): void {
    this.locationCache = {
      lat,
      lng,
      address,
      timestamp: Date.now()
    }
  }

  getUserLocation(): LocationCache | null {
    if (!this.locationCache) return null

    // Cache de localização válido por 2 minutos
    if (Date.now() - this.locationCache.timestamp > 2 * 60 * 1000) {
      this.locationCache = null
      return null
    }

    return this.locationCache
  }

  // Cache específico para motoristas próximos
  setNearbyDrivers(drivers: any[], userLocation: { lat: number; lng: number }): void {
    this.driversCache = {
      drivers,
      location: userLocation,
      timestamp: Date.now()
    }
  }

  getNearbyDrivers(userLocation: { lat: number; lng: number }): any[] | null {
    if (!this.driversCache) return null

    // Cache válido por 30 segundos
    if (Date.now() - this.driversCache.timestamp > 30 * 1000) {
      this.driversCache = null
      return null
    }

    // Verificar se a localização mudou significativamente (>100m)
    const distance = this.calculateDistance(
      this.driversCache.location.lat,
      this.driversCache.location.lng,
      userLocation.lat,
      userLocation.lng
    )

    if (distance > 0.1) { // 100 metros
      this.driversCache = null
      return null
    }

    return this.driversCache.drivers
  }

  // Cache para rotas do Mapbox
  setRoute(origin: string, destination: string, route: any): void {
    const key = `route_${origin}_${destination}`
    this.set(key, route, 10) // Cache por 10 minutos
  }

  getRoute(origin: string, destination: string): any | null {
    const key = `route_${origin}_${destination}`
    return this.get(key)
  }

  // Cache para geocoding
  setGeocode(address: string, result: any): void {
    const key = `geocode_${address.toLowerCase()}`
    this.set(key, result, 60) // Cache por 1 hora
  }

  getGeocode(address: string): any | null {
    const key = `geocode_${address.toLowerCase()}`
    return this.get(key)
  }

  // Cache para preços estimados
  setPriceEstimate(origin: string, destination: string, prices: any): void {
    const key = `price_${origin}_${destination}`
    this.set(key, prices, 5) // Cache por 5 minutos
  }

  getPriceEstimate(origin: string, destination: string): any | null {
    const key = `price_${origin}_${destination}`
    return this.get(key)
  }

  // Utilitários
  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371 // Raio da Terra em km
    const dLat = this.deg2rad(lat2 - lat1)
    const dLng = this.deg2rad(lng2 - lng1)
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * 
      Math.sin(dLng/2) * Math.sin(dLng/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI/180)
  }

  // Limpar cache
  clear(): void {
    this.cache.clear()
    this.locationCache = null
    this.driversCache = null
  }

  // Limpar cache expirado
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }

  // Estatísticas do cache
  getStats(): {
    totalItems: number
    locationCached: boolean
    driversCached: boolean
    cacheHitRate: number
  } {
    return {
      totalItems: this.cache.size,
      locationCached: !!this.locationCache,
      driversCached: !!this.driversCache,
      cacheHitRate: 0 // TODO: Implementar tracking de hit rate
    }
  }

  // Pré-carregar dados comuns
  async preloadCommonData(): Promise<void> {
    try {
      // Pré-carregar locais populares
      const popularPlaces = [
        'Aeroporto de Guarulhos, São Paulo',
        'Aeroporto de Congonhas, São Paulo',
        'Estação da Sé, São Paulo',
        'Shopping Ibirapuera, São Paulo',
        'Av. Paulista, São Paulo'
      ]

      // Geocodificar locais populares
      for (const place of popularPlaces) {
        if (!this.getGeocode(place)) {
          // TODO: Implementar geocoding e cache
          console.log(`Pré-carregando: ${place}`)
        }
      }
    } catch (error) {
      console.error('Erro ao pré-carregar dados:', error)
    }
  }
}

// Singleton instance
export const cacheService = new CacheService()

// Auto-cleanup a cada 5 minutos
setInterval(() => {
  cacheService.cleanup()
}, 5 * 60 * 1000)

export default cacheService
