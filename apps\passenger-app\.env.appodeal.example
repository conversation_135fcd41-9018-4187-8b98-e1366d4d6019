# 🎯 CONFIGURAÇÃO APPODEAL - MOBIDRIVE
# Copie este arquivo para .env.local e configure suas chaves

# =====================================================
# CHAVES APPODEAL (OBTER EM app.appodeal.com)
# =====================================================

# Chave para iOS (quando app for publicado na App Store)
REACT_APP_APPODEAL_IOS_KEY=your_ios_app_key_here

# Chave para Android (quando app for publicado no Google Play)
REACT_APP_APPODEAL_ANDROID_KEY=your_android_app_key_here

# Chave para ambiente web (simulação atual)
REACT_APP_APPODEAL_WEB_KEY=web_simulation_key

# =====================================================
# CONFIGURAÇÕES DE AMBIENTE
# =====================================================

# Modo de teste (true para desenvolvimento, false para produção)
REACT_APP_APPODEAL_TEST_MODE=true

# Ambiente atual (web, ios, android)
REACT_APP_APPODEAL_ENVIRONMENT=web

# =====================================================
# CONFIGURAÇÕES DE COMPLIANCE
# =====================================================

# GDPR (Europa)
REACT_APP_APPODEAL_GDPR_ENABLED=true

# CCPA (Califórnia)
REACT_APP_APPODEAL_CCPA_ENABLED=true

# LGPD (Brasil)
REACT_APP_APPODEAL_LGPD_ENABLED=true

# COPPA (Crianças) - false para MobiDrive (18+)
REACT_APP_APPODEAL_COPPA_ENABLED=false

# =====================================================
# CONFIGURAÇÕES DE DEBUG
# =====================================================

# Habilitar logs detalhados
REACT_APP_APPODEAL_DEBUG=true

# Nível de log (debug, info, warn, error)
REACT_APP_APPODEAL_LOG_LEVEL=debug

# Habilitar anúncios de teste
REACT_APP_APPODEAL_TEST_ADS=true

# =====================================================
# CONFIGURAÇÕES DE RECEITA
# =====================================================

# CPM mínimo (em centavos)
REACT_APP_APPODEAL_MIN_CPM=10

# CPM máximo (em centavos)
REACT_APP_APPODEAL_MAX_CPM=100

# Multiplicador de recompensa
REACT_APP_APPODEAL_REWARD_MULTIPLIER=1.0

# =====================================================
# CONFIGURAÇÕES DE FREQUÊNCIA
# =====================================================

# Máximo de anúncios por hora
REACT_APP_APPODEAL_MAX_ADS_PER_HOUR=10

# Cooldown entre anúncios (em milissegundos)
REACT_APP_APPODEAL_COOLDOWN=30000

# Máximo de anúncios consecutivos
REACT_APP_APPODEAL_MAX_CONSECUTIVE=3

# =====================================================
# URLS E ENDPOINTS
# =====================================================

# URL da política de privacidade
REACT_APP_PRIVACY_POLICY_URL=https://mundodainovacao.com/privacy-policy

# URL dos termos de uso
REACT_APP_TERMS_OF_USE_URL=https://mundodainovacao.com/terms-of-use

# =====================================================
# INSTRUÇÕES DE CONFIGURAÇÃO
# =====================================================

# 1. CRIAR CONTA APPODEAL:
#    - Acesse: https://app.appodeal.com
#    - Registre-se com email profissional
#    - Complete o perfil da empresa
#    - Aguarde aprovação (24-48h)

# 2. CRIAR APLICATIVO:
#    - Nome: MobiDrive
#    - Categoria: Transportation
#    - Plataformas: iOS, Android
#    - Descrição: App de mobilidade urbana

# 3. OBTER CHAVES:
#    - iOS App Key: Configurações > iOS > App Key
#    - Android App Key: Configurações > Android > App Key
#    - Copie as chaves para as variáveis acima

# 4. CONFIGURAR MEDIATION:
#    - Habilite redes de anúncios
#    - Configure waterfall
#    - Ative otimização automática

# 5. CONFIGURAR COMPLIANCE:
#    - Configure GDPR para Europa
#    - Configure CCPA para Califórnia
#    - Configure LGPD para Brasil
#    - Defina política de privacidade

# 6. TESTAR INTEGRAÇÃO:
#    - Use modo de teste primeiro
#    - Verifique anúncios de teste
#    - Monitore logs e métricas
#    - Valide recompensas

# 7. PRODUÇÃO:
#    - Desabilite modo de teste
#    - Configure chaves de produção
#    - Monitore performance
#    - Otimize receita

# =====================================================
# NOTAS IMPORTANTES
# =====================================================

# - Mantenha as chaves seguras e privadas
# - Não commite este arquivo com chaves reais
# - Use .env.local para desenvolvimento
# - Use variáveis de ambiente do Vercel para produção
# - Teste sempre em modo de desenvolvimento primeiro
# - Monitore compliance e políticas
# - Otimize baseado em métricas reais

# =====================================================
# SUPORTE E DOCUMENTAÇÃO
# =====================================================

# Dashboard: https://app.appodeal.com
# Documentação: https://docs.appodeal.com
# Suporte: https://appodeal.com/support
# Status: https://status.appodeal.com
