import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Navigate } from 'react-router-dom'
import { User, Bell, Shield, CreditCard, MapPin, Settings, Save, Check } from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { Grad<PERSON>Background } from '../components/GradientBackground'
import { useNoZoom } from '../hooks/useNoZoom'
import '../styles/no-zoom.css'

// 📱 SETUP MOBILE ANDROID NATIVA
// MANTÉM DESIGN ORIGINAL + CONVERSÃO ANDROID NATIVA

interface UserProfile {
  full_name: string
  phone: string
  emergency_contact: string
  notification_preferences: {
    ride_updates: boolean
    promotions: boolean
    driver_messages: boolean
  }
  privacy_settings: {
    share_location: boolean
    save_addresses: boolean
    data_analytics: boolean
  }
}

export const SetupMobile: React.FC = () => {
  const { user, updateProfile } = useAuth()
  const [profile, setProfile] = useState<UserProfile>({
    full_name: '',
    phone: '',
    emergency_contact: '',
    notification_preferences: {
      ride_updates: true,
      promotions: false,
      driver_messages: true
    },
    privacy_settings: {
      share_location: true,
      save_addresses: true,
      data_analytics: false
    }
  })
  const [activeTab, setActiveTab] = useState<'profile' | 'notifications' | 'privacy' | 'payment'>('profile')
  const [isSaving, setIsSaving] = useState(false)
  const [saveSuccess, setSaveSuccess] = useState(false)

  // 🚫 DESABILITA ZOOM COMPLETAMENTE + CONFIGURAÇÕES ANDROID NATIVAS
  useNoZoom()

  // Configurações Android nativas (mantendo design original)
  useEffect(() => {
    // Meta viewport para Android nativo
    let metaViewport = document.querySelector('meta[name="viewport"]')
    if (!metaViewport) {
      metaViewport = document.createElement('meta')
      metaViewport.setAttribute('name', 'viewport')
      document.head.appendChild(metaViewport)
    }
    metaViewport.setAttribute('content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )

    // Configurações de overflow para Android
    document.body.style.margin = '0'
    document.body.style.padding = '0'
    document.body.style.overflow = 'hidden'
    document.documentElement.style.margin = '0'
    document.documentElement.style.padding = '0'
    document.documentElement.style.overflow = 'hidden'
  }, [])

  // Redirect se não logado
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // Carregar dados do usuário
  useEffect(() => {
    if (user?.user_metadata) {
      setProfile(prev => ({
        ...prev,
        full_name: user.user_metadata.full_name || '',
        phone: user.user_metadata.phone || '',
        emergency_contact: user.user_metadata.emergency_contact || ''
      }))
    }
  }, [user])

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // Simular salvamento
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Em uma implementação real, salvar via updateProfile
      // await updateProfile(profile)

      setSaveSuccess(true)
      setTimeout(() => setSaveSuccess(false), 3000)
    } catch (error) {
      console.error('Erro ao salvar:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const tabs = [
    { id: 'profile', label: 'Perfil', icon: User },
    { id: 'notifications', label: 'Notificações', icon: Bell },
    { id: 'privacy', label: 'Privacidade', icon: Shield },
    { id: 'payment', label: 'Pagamento', icon: CreditCard }
  ]

  // Animações simples e limpas (MANTENDO ORIGINAIS)
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">

      {/* Background Gradient Sutil (FIEL AO LOGIN) */}
      <GradientBackground
        variant="static"
        opacity={0.7}
      />

      {/* Overlay muito sutil para legibilidade (FIEL AO LOGIN) */}
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Conteúdo Principal (FIEL AO LOGIN) */}
      <div className="relative z-10 flex flex-col h-full min-h-screen">

        {/* Header com Logo (FIEL AO LOGIN) */}
        <motion.div
          className="pt-8 pb-6 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <img
                src="/icons/icon-48x48.png"
                alt="MobiDrive"
                className="w-6 h-6"
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-xs text-white/70">Configurações</p>
            </div>
          </div>
        </motion.div>

        {/* Conteúdo Central (FIEL AO LOGIN) */}
        <div className="flex-1 flex flex-col justify-center px-4 space-y-6">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {/* Tab Navigation (FIEL AO LOGIN) */}
            <motion.div variants={itemVariants}>
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                <div className="grid grid-cols-2 gap-2">
                  {tabs.map((tab) => {
                    const IconComponent = tab.icon
                    return (
                      <motion.button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id as any)}
                        className={`p-3 rounded-xl text-sm font-semibold transition-all duration-200 flex flex-col items-center space-y-1 ${
                          activeTab === tab.id
                            ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                            : 'bg-white/10 text-white/80 hover:bg-white/20'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <IconComponent className="w-5 h-5" />
                        <span>{tab.label}</span>
                      </motion.button>
                    )
                  })}
                </div>
              </div>
            </motion.div>

        {/* Profile Tab (MANTENDO DESIGN ORIGINAL) */}
        {activeTab === 'profile' && (
          <motion.div variants={itemVariants}>
            <ModernCard title="Informações Pessoais" icon="👤">
              <div className="space-y-4">
                <ModernInput
                  label="Nome Completo"
                  icon="👤"
                  value={profile.full_name}
                  onChange={(e) => setProfile(prev => ({ ...prev, full_name: e.target.value }))}
                  placeholder="Seu nome completo"
                />

                <ModernInput
                  label="Telefone"
                  icon="📱"
                  type="tel"
                  value={profile.phone}
                  onChange={(e) => setProfile(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="(11) 99999-9999"
                />

                <ModernInput
                  label="Contato de Emergência"
                  icon="🚨"
                  type="tel"
                  value={profile.emergency_contact}
                  onChange={(e) => setProfile(prev => ({ ...prev, emergency_contact: e.target.value }))}
                  placeholder="(11) 88888-8888"
                />
              </div>
            </ModernCard>
          </motion.div>
        )}

        {/* Notifications Tab (MANTENDO DESIGN ORIGINAL) */}
        {activeTab === 'notifications' && (
          <motion.div variants={itemVariants}>
            <ModernCard title="Preferências de Notificação" icon="🔔">
              <div className="space-y-4">
                {Object.entries(profile.notification_preferences).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {key === 'ride_updates' && 'Atualizações de Corrida'}
                        {key === 'promotions' && 'Promoções e Ofertas'}
                        {key === 'driver_messages' && 'Mensagens do Motorista'}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {key === 'ride_updates' && 'Receber notificações sobre o status da corrida'}
                        {key === 'promotions' && 'Receber ofertas especiais e promoções'}
                        {key === 'driver_messages' && 'Receber mensagens do motorista'}
                      </p>
                    </div>
                    <motion.button
                      onClick={() => setProfile(prev => ({
                        ...prev,
                        notification_preferences: {
                          ...prev.notification_preferences,
                          [key]: !value
                        }
                      }))}
                      className={`w-12 h-6 rounded-full transition-colors ${
                        value ? 'bg-blue-500' : 'bg-gray-300'
                      }`}
                      whileTap={{ scale: 0.95 }}
                    >
                      <motion.div
                        className="w-5 h-5 bg-white rounded-full shadow-md"
                        animate={{ x: value ? 24 : 2 }}
                        transition={{ type: "spring", stiffness: 500, damping: 30 }}
                      />
                    </motion.button>
                  </div>
                ))}
              </div>
            </ModernCard>
          </motion.div>
        )}

        {/* Privacy Tab (MANTENDO DESIGN ORIGINAL) */}
        {activeTab === 'privacy' && (
          <motion.div variants={itemVariants}>
            <ModernCard title="Configurações de Privacidade" icon="🔒">
              <div className="space-y-4">
                {Object.entries(profile.privacy_settings).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {key === 'share_location' && 'Compartilhar Localização'}
                        {key === 'save_addresses' && 'Salvar Endereços'}
                        {key === 'data_analytics' && 'Análise de Dados'}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {key === 'share_location' && 'Permitir compartilhamento de localização em tempo real'}
                        {key === 'save_addresses' && 'Salvar endereços frequentes para acesso rápido'}
                        {key === 'data_analytics' && 'Permitir coleta de dados para melhorar o serviço'}
                      </p>
                    </div>
                    <motion.button
                      onClick={() => setProfile(prev => ({
                        ...prev,
                        privacy_settings: {
                          ...prev.privacy_settings,
                          [key]: !value
                        }
                      }))}
                      className={`w-12 h-6 rounded-full transition-colors ${
                        value ? 'bg-green-500' : 'bg-gray-300'
                      }`}
                      whileTap={{ scale: 0.95 }}
                    >
                      <motion.div
                        className="w-5 h-5 bg-white rounded-full shadow-md"
                        animate={{ x: value ? 24 : 2 }}
                        transition={{ type: "spring", stiffness: 500, damping: 30 }}
                      />
                    </motion.button>
                  </div>
                ))}
              </div>
            </ModernCard>
          </motion.div>
        )}

            {/* Payment Tab (FIEL AO LOGIN) */}
            {activeTab === 'payment' && (
              <motion.div variants={itemVariants}>
                <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                  <h3 className="text-lg font-semibold text-white mb-4 text-center">💳 Métodos de Pagamento</h3>
                  <div className="text-center py-8">
                    <CreditCard className="w-16 h-16 text-white/60 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">Métodos de Pagamento</h3>
                    <p className="text-white/70 mb-6">Gerencie seus cartões e formas de pagamento</p>
                    <motion.button
                      className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-xl font-semibold"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Adicionar Cartão
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Save Button (FIEL AO LOGIN) */}
            <motion.div variants={itemVariants}>
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                {saveSuccess ? (
                  <motion.div
                    className="flex items-center justify-center space-x-2 text-green-400 py-3"
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                  >
                    <Check className="w-5 h-5" />
                    <span className="font-semibold">Configurações salvas com sucesso!</span>
                  </motion.div>
                ) : (
                  <motion.button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-4 rounded-xl font-semibold flex items-center justify-center space-x-2 disabled:opacity-50"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {isSaving ? (
                      <>
                        <motion.div
                          className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        />
                        <span>Salvando...</span>
                      </>
                    ) : (
                      <>
                        <Save className="w-5 h-5" />
                        <span>Salvar Configurações</span>
                      </>
                    )}
                  </motion.button>
                )}
              </div>
            </motion.div>

            {/* Quick Actions (FIEL AO LOGIN) */}
            <motion.div variants={itemVariants}>
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                <h3 className="text-lg font-semibold text-white mb-4 text-center">⚡ Ações Rápidas</h3>
                <div className="grid grid-cols-2 gap-3">
                  <motion.button
                    onClick={() => window.location.href = '/dashboard'}
                    className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Settings className="w-6 h-6" />
                    <span className="text-sm font-medium">Dashboard</span>
                  </motion.button>

                  <motion.button
                    onClick={() => window.location.href = '/request-ride'}
                    className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <MapPin className="w-6 h-6" />
                    <span className="text-sm font-medium">Nova Corrida</span>
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Footer Simples (FIEL AO LOGIN) */}
        <motion.div
          className="pb-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <p className="text-white/50 text-xs">
            © 2024 MobiDrive. Todos os direitos reservados.
          </p>
        </motion.div>
      </div>
    </div>
  )
}

export default SetupMobile
