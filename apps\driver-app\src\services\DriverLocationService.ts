import { supabase } from '../lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface DriverLocation {
  id: string;
  user_id: string;
  latitude: number;
  longitude: number;
  heading?: number;
  speed?: number;
  is_active: boolean;
  is_available: boolean;
  last_ping: string;
  updated_at: string;
}

export interface DriverLocationUpdate {
  driverId: string;
  location: [number, number];
  heading?: number;
  speed?: number;
  timestamp: Date;
}

class DriverLocationService {
  private static instance: DriverLocationService;
  private subscriptions: Map<string, RealtimeChannel> = new Map();
  private updateInterval: NodeJS.Timeout | null = null;

  static getInstance(): DriverLocationService {
    if (!DriverLocationService.instance) {
      DriverLocationService.instance = new DriverLocationService();
    }
    return DriverLocationService.instance;
  }

  /**
   * Atualizar localização do motorista
   */
  async updateDriverLocation(
    driverId: string,
    latitude: number,
    longitude: number,
    heading?: number,
    speed?: number,
    isAvailable: boolean = true
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('driver_locations')
        .upsert({
          user_id: driverId,
          location: `POINT(${longitude} ${latitude})`,
          heading,
          speed,
          is_active: true,
          is_available: isAvailable,
          last_ping: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('Erro ao atualizar localização do motorista:', error);
        return false;
      }

      console.log('📍 Localização do motorista atualizada:', { driverId, latitude, longitude });
      return true;

    } catch (error) {
      console.error('Erro ao atualizar localização:', error);
      return false;
    }
  }

  /**
   * Buscar localização atual do motorista
   */
  async getDriverLocation(driverId: string): Promise<DriverLocation | null> {
    try {
      const { data, error } = await supabase
        .from('driver_locations')
        .select('*')
        .eq('user_id', driverId)
        .single();

      if (error) {
        console.error('Erro ao buscar localização do motorista:', error);
        return null;
      }

      if (!data) return null;

      return {
        id: data.id,
        user_id: data.user_id,
        latitude: data.location?.y || data.location?.coordinates?.[1] || 0,
        longitude: data.location?.x || data.location?.coordinates?.[0] || 0,
        heading: data.heading,
        speed: data.speed,
        is_active: data.is_active,
        is_available: data.is_available,
        last_ping: data.last_ping,
        updated_at: data.updated_at
      };

    } catch (error) {
      console.error('Erro ao buscar localização do motorista:', error);
      return null;
    }
  }

  /**
   * Subscrever a atualizações de localização de um motorista específico
   */
  subscribeToDriverLocation(
    driverId: string,
    callback: (location: DriverLocationUpdate) => void
  ): () => void {
    const channelName = `driver-location-${driverId}`;
    
    console.log('🔄 Subscribing to driver location updates:', driverId);

    try {
      // Remover subscription anterior se existir
      this.unsubscribeFromDriverLocation(driverId);

      const channel = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'driver_locations',
            filter: `user_id=eq.${driverId}`
          },
          (payload) => {
            console.log('📍 Driver location update received:', payload);
            
            if (payload.new) {
              const locationData = payload.new as any;
              
              const update: DriverLocationUpdate = {
                driverId: locationData.user_id,
                location: [
                  locationData.location?.x || locationData.location?.coordinates?.[0] || 0,
                  locationData.location?.y || locationData.location?.coordinates?.[1] || 0
                ],
                heading: locationData.heading,
                speed: locationData.speed,
                timestamp: new Date(locationData.updated_at)
              };
              
              callback(update);
            }
          }
        )
        .subscribe();

      this.subscriptions.set(driverId, channel);

      return () => this.unsubscribeFromDriverLocation(driverId);

    } catch (error) {
      console.error('❌ Erro ao criar subscription de localização:', error);
      return () => {};
    }
  }

  /**
   * Cancelar subscription de localização de motorista
   */
  unsubscribeFromDriverLocation(driverId: string): void {
    const channel = this.subscriptions.get(driverId);
    if (channel) {
      console.log('🛑 Unsubscribing from driver location:', driverId);
      supabase.removeChannel(channel);
      this.subscriptions.delete(driverId);
    }
  }

  /**
   * Buscar motoristas próximos em tempo real
   */
  async findNearbyDrivers(
    userLatitude: number,
    userLongitude: number,
    radiusKm: number = 5
  ): Promise<DriverLocation[]> {
    try {
      const { data, error } = await supabase
        .rpc('find_nearby_drivers', {
          user_lat: userLatitude,
          user_lng: userLongitude,
          radius_km: radiusKm
        });

      if (error) {
        console.error('Erro ao buscar motoristas próximos:', error);
        return [];
      }

      return (data || []).map((driver: any) => ({
        id: driver.driver_id,
        user_id: driver.driver_id,
        latitude: driver.location_lat,
        longitude: driver.location_lng,
        heading: driver.heading,
        speed: driver.speed,
        is_active: true,
        is_available: driver.is_available,
        last_ping: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

    } catch (error) {
      console.error('Erro ao buscar motoristas próximos:', error);
      return [];
    }
  }

  /**
   * Marcar motorista como indisponível
   */
  async setDriverUnavailable(driverId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('driver_locations')
        .update({
          is_available: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', driverId);

      if (error) {
        console.error('Erro ao marcar motorista como indisponível:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('Erro ao atualizar disponibilidade do motorista:', error);
      return false;
    }
  }

  /**
   * Marcar motorista como disponível
   */
  async setDriverAvailable(driverId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('driver_locations')
        .update({
          is_available: true,
          is_active: true,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', driverId);

      if (error) {
        console.error('Erro ao marcar motorista como disponível:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('Erro ao atualizar disponibilidade do motorista:', error);
      return false;
    }
  }

  /**
   * Limpar todas as subscriptions
   */
  cleanup(): void {
    console.log('🧹 Cleaning up driver location subscriptions');
    
    this.subscriptions.forEach((channel, driverId) => {
      this.unsubscribeFromDriverLocation(driverId);
    });

    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }
}

export const driverLocationService = DriverLocationService.getInstance();
