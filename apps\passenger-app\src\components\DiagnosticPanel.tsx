import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '../contexts/AuthContextSimple'
import { supabase } from '../lib/supabase'
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Loader, 
  Eye, 
  EyeOff,
  RefreshCw,
  Database,
  Key,
  Map,
  User,
  Globe
} from 'lucide-react'

interface DiagnosticItem {
  name: string
  status: 'loading' | 'success' | 'error' | 'warning'
  message: string
  details?: string
  action?: () => void
}

export const DiagnosticPanel: React.FC = () => {
  const { user, profile, loading } = useAuth()
  const [isVisible, setIsVisible] = useState(false)
  const [diagnostics, setDiagnostics] = useState<DiagnosticItem[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const runDiagnostics = async () => {
    setIsRunning(true)
    const results: DiagnosticItem[] = []

    // 1. Verificar variáveis de ambiente
    results.push({
      name: 'Supabase URL',
      status: import.meta.env.VITE_SUPABASE_URL ? 'success' : 'error',
      message: import.meta.env.VITE_SUPABASE_URL ? 'Configurado' : 'Não configurado',
      details: import.meta.env.VITE_SUPABASE_URL || 'Variável VITE_SUPABASE_URL não encontrada'
    })

    results.push({
      name: 'Supabase Key',
      status: import.meta.env.VITE_SUPABASE_ANON_KEY ? 'success' : 'error',
      message: import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Configurado' : 'Não configurado',
      details: import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Key presente' : 'Variável VITE_SUPABASE_ANON_KEY não encontrada'
    })

    results.push({
      name: 'Mapbox Token',
      status: import.meta.env.VITE_MAPBOX_ACCESS_TOKEN ? 'success' : 'warning',
      message: import.meta.env.VITE_MAPBOX_ACCESS_TOKEN ? 'Configurado' : 'Não configurado',
      details: import.meta.env.VITE_MAPBOX_ACCESS_TOKEN ? 'Token presente' : 'Mapa pode não funcionar'
    })

    // 2. Verificar conexão com Supabase
    try {
      const { data, error } = await supabase.auth.getSession()
      results.push({
        name: 'Conexão Supabase',
        status: error ? 'error' : 'success',
        message: error ? 'Erro de conexão' : 'Conectado',
        details: error?.message || 'Conexão estabelecida com sucesso'
      })
    } catch (error: any) {
      results.push({
        name: 'Conexão Supabase',
        status: 'error',
        message: 'Falha na conexão',
        details: error.message
      })
    }

    // 3. Verificar tabela profiles
    if (user) {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('count')
          .limit(1)
        
        results.push({
          name: 'Tabela Profiles',
          status: error ? 'error' : 'success',
          message: error ? 'Erro de acesso' : 'Acessível',
          details: error?.message || 'Tabela profiles acessível'
        })
      } catch (error: any) {
        results.push({
          name: 'Tabela Profiles',
          status: 'error',
          message: 'Erro de acesso',
          details: error.message
        })
      }
    }

    // 4. Verificar estado de autenticação
    results.push({
      name: 'Estado Auth',
      status: loading ? 'loading' : (user ? 'success' : 'warning'),
      message: loading ? 'Carregando...' : (user ? 'Autenticado' : 'Não autenticado'),
      details: loading ? 'Verificando autenticação' : (user ? `Usuário: ${user.email}` : 'Nenhum usuário logado')
    })

    // 5. Verificar perfil do usuário
    if (user) {
      results.push({
        name: 'Perfil Usuário',
        status: profile ? 'success' : 'warning',
        message: profile ? 'Carregado' : 'Não encontrado',
        details: profile ? `Nome: ${profile.full_name}` : 'Perfil não encontrado na base de dados'
      })
    }

    // 6. Verificar geolocalização
    results.push({
      name: 'Geolocalização',
      status: navigator.geolocation ? 'success' : 'error',
      message: navigator.geolocation ? 'Suportado' : 'Não suportado',
      details: navigator.geolocation ? 'API de geolocalização disponível' : 'Navegador não suporta geolocalização'
    })

    // 7. Verificar localStorage
    try {
      localStorage.setItem('test', 'test')
      localStorage.removeItem('test')
      results.push({
        name: 'LocalStorage',
        status: 'success',
        message: 'Funcionando',
        details: 'LocalStorage acessível'
      })
    } catch (error) {
      results.push({
        name: 'LocalStorage',
        status: 'error',
        message: 'Bloqueado',
        details: 'LocalStorage não acessível (modo privado?)'
      })
    }

    setDiagnostics(results)
    setIsRunning(false)
  }

  useEffect(() => {
    if (isVisible) {
      runDiagnostics()
    }
  }, [isVisible, user, profile, loading])

  const getStatusIcon = (status: DiagnosticItem['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />
      case 'loading':
        return <Loader className="w-4 h-4 text-blue-500 animate-spin" />
    }
  }

  const getStatusColor = (status: DiagnosticItem['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50'
      case 'error':
        return 'border-red-200 bg-red-50'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50'
      case 'loading':
        return 'border-blue-200 bg-blue-50'
    }
  }

  return (
    <>
      {/* Toggle Button */}
      <motion.button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 left-4 bg-gray-900 text-white p-3 rounded-full shadow-lg z-50"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="Diagnóstico do Sistema"
      >
        {isVisible ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
      </motion.button>

      {/* Diagnostic Panel */}
      {isVisible && (
        <motion.div
          className="fixed bottom-20 left-4 bg-white rounded-lg shadow-xl border border-gray-200 p-4 max-w-md w-full max-h-96 overflow-y-auto z-40"
          initial={{ opacity: 0, x: -100 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -100 }}
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-bold text-gray-900 flex items-center">
              <Database className="w-5 h-5 mr-2" />
              Diagnóstico do Sistema
            </h3>
            <button
              onClick={runDiagnostics}
              disabled={isRunning}
              className="p-1 text-gray-500 hover:text-gray-700 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isRunning ? 'animate-spin' : ''}`} />
            </button>
          </div>

          <div className="space-y-2">
            {diagnostics.map((item, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border ${getStatusColor(item.status)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(item.status)}
                    <span className="font-medium text-sm">{item.name}</span>
                  </div>
                  <span className="text-xs text-gray-600">{item.message}</span>
                </div>
                {item.details && (
                  <p className="text-xs text-gray-500 mt-1 pl-6">{item.details}</p>
                )}
              </div>
            ))}
          </div>

          <div className="mt-4 pt-3 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              Última verificação: {new Date().toLocaleTimeString()}
            </p>
          </div>
        </motion.div>
      )}
    </>
  )
}

export default DiagnosticPanel
