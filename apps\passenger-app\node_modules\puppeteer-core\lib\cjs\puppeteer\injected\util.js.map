{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/injected/util.ts"], "names": [], "mappings": ";;;AA4CA,wBAMC;AAKD,8BAgBC;AAvED;;;;GAIG;AACH,MAAM,wBAAwB,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAExD;;GAEG;AACI,MAAM,eAAe,GAAG,CAC7B,IAAiB,EACjB,OAAiB,EACD,EAAE;IAClB,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,OAAO,KAAK,KAAK,CAAC;IAC3B,CAAC;IACD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,OAAO,GAAG,CACd,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAClD,CAAC;IAEb,MAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC/C,MAAM,SAAS,GACb,KAAK;QACL,CAAC,wBAAwB,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC;QACpD,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAC/B,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AAC9C,CAAC,CAAC;AApBW,QAAA,eAAe,mBAoB1B;AAEF,SAAS,kBAAkB,CAAC,OAAgB;IAC1C,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;IAC7C,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;AAC/C,CAAC;AAED,MAAM,aAAa,GAAG,CAAC,IAAU,EAA2C,EAAE;IAC5E,OAAO,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,YAAY,UAAU,CAAC;AACvE,CAAC,CAAC;AAEF;;GAEG;AACH,QAAe,CAAC,CAAC,MAAM,CAAC,IAAU;IAChC,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,CAAC;IACb,CAAC;AACH,CAAC;AAED;;GAEG;AACH,QAAe,CAAC,CAAC,SAAS,CAAC,IAAU;IACnC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;IACjC,MAAM,IAAI,CAAC;IACX,MAAM,OAAO,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3E,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,IAAI,IAAoB,CAAC;QACzB,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAoB,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACrB,SAAS;YACX,CAAC;YACD,MAAM,IAAI,CAAC,UAAU,CAAC;YACtB,OAAO,CAAC,IAAI,CACV,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,CACpE,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC"}