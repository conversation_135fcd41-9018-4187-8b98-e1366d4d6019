import React, { useState, useEffect, useCallback, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Navigate, useNavigate } from 'react-router-dom'
import mapboxgl from 'mapbox-gl'
import {
  MapPin,
  ArrowLeft,
  Clock,
  CreditCard,
  Car,
  Zap,
  Star,
  Phone,
  MessageCircle,
  Navigation,
  Route,
  DollarSign,
  Loader2,
  CheckCircle,
  AlertCircle,
  User,
  Target,
  Mic,
  MicOff,
  Shield,
  Share2,
  Truck,
  Bike,
  Send,
  Search,
  AlertTriangle
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { useMapboxSearch } from '../hooks/useMapboxSearch'
import { useNoZoom } from '../hooks/useNoZoom'
import 'mapbox-gl/dist/mapbox-gl.css'
import '../styles/no-zoom.css'



// Configure Mapbox token
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'
mapboxgl.accessToken = MAPBOX_TOKEN

// Types
interface VehicleOption {
  id: string
  name: string
  icon: string
  description: string
  basePrice: number
  pricePerKm: number
  estimatedTime: number
  available: boolean
}

interface PaymentMethod {
  id: string
  name: string
  icon: string
  lastFour?: string
}

interface Driver {
  id: string
  name: string
  rating: number
  vehicle: string
  plate: string
  photo?: string
  phone: string
  estimatedArrival: number
  location: [number, number]
}

type Step = 'map' | 'details' | 'waiting' | 'riding' | 'rating'

// Mock data for vehicle options
const VEHICLE_OPTIONS: VehicleOption[] = [
  {
    id: 'moto',
    name: 'Moto',
    icon: '🏍️',
    description: 'Rápido e econômico',
    basePrice: 3.0,
    pricePerKm: 1.5,
    estimatedTime: 3,
    available: true
  },
  {
    id: 'carro',
    name: 'Carro',
    icon: '🚗',
    description: 'Conforto e segurança',
    basePrice: 5.0,
    pricePerKm: 2.0,
    estimatedTime: 5,
    available: true
  },
  {
    id: 'suv',
    name: 'SUV',
    icon: '🚙',
    description: 'Espaço e luxo',
    basePrice: 8.0,
    pricePerKm: 3.0,
    estimatedTime: 7,
    available: true
  }
]

const PAYMENT_METHODS: PaymentMethod[] = [
  { id: 'card', name: 'Cartão', icon: '💳', lastFour: '1234' },
  { id: 'cash', name: 'Dinheiro', icon: '💵' },
  { id: 'pix', name: 'PIX', icon: '📱' }
]

// Mock driver data
const MOCK_DRIVER: Driver = {
  id: 'driver-1',
  name: 'João Silva',
  rating: 4.9,
  vehicle: 'Honda Civic Branco',
  plate: 'ABC-1234',
  phone: '+5511999999999',
  estimatedArrival: 5,
  location: [-46.6333, -23.5505]
}

export const RequestRideNew: React.FC = () => {
  const { user } = useAuth()
  const navigate = useNavigate()

  // State management
  const [step, setStep] = useState<Step>('map')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Location states
  const [destination, setDestination] = useState<any>(null)
  const [currentLocation, setCurrentLocation] = useState<[number, number] | null>(null)
  const [rideEstimate, setRideEstimate] = useState<any>(null)

  // Selection states
  const [selectedVehicle, setSelectedVehicle] = useState<VehicleOption | null>(null)
  const [selectedPayment, setSelectedPayment] = useState<PaymentMethod | null>(null)
  const [observations, setObservations] = useState('')

  // Ride states
  const [driver, setDriver] = useState<Driver | null>(null)
  const [rideStarted, setRideStarted] = useState(false)
  const [rating, setRating] = useState(0)
  const [comment, setComment] = useState('')

  // Voice search
  const [isListening, setIsListening] = useState(false)
  const [speechSupported, setSpeechSupported] = useState(false)

  // Emergency
  const [emergencyMode, setEmergencyMode] = useState(false)

  // Map states
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const [isMapLoaded, setIsMapLoaded] = useState(false)
  const destinationMarker = useRef<mapboxgl.Marker | null>(null)
  const userMarker = useRef<mapboxgl.Marker | null>(null)

  // Enhanced map interaction states
  const [isSelectionMode, setIsSelectionMode] = useState(true)
  const [isFullScreen, setIsFullScreen] = useState(true)
  const [sortedResults, setSortedResults] = useState<any[]>([])

  useNoZoom()

  // Redirect if not logged in
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // Use the Mapbox search hook
  const {
    searchQuery,
    searchResults,
    isSearching,
    searchPlaces,
    selectResult,
    getCurrentLocation
  } = useMapboxSearch()

  // Calculate distance between two coordinates (Haversine formula)
  const calculateDistance = useCallback((coord1: [number, number], coord2: [number, number]): number => {
    const R = 6371 // Earth's radius in kilometers
    const dLat = (coord2[1] - coord1[1]) * Math.PI / 180
    const dLon = (coord2[0] - coord1[0]) * Math.PI / 180
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(coord1[1] * Math.PI / 180) * Math.cos(coord2[1] * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c // Distance in kilometers
  }, [])

  // Sort search results by distance from user location
  const sortResultsByDistance = useCallback((results: any[]) => {
    if (!currentLocation || !results.length) return results

    return results
      .map(result => ({
        ...result,
        distance: calculateDistance(currentLocation, result.center)
      }))
      .sort((a, b) => a.distance - b.distance)
  }, [currentLocation, calculateDistance])

  // Reverse geocoding for address preview
  const reverseGeocode = useCallback(async (coordinates: [number, number]) => {
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${coordinates[0]},${coordinates[1]}.json?access_token=${MAPBOX_TOKEN}&language=pt-BR&limit=1`
      )
      const data = await response.json()
      if (data.features && data.features.length > 0) {
        return data.features[0].place_name
      }
    } catch (error) {
      console.warn('Reverse geocoding failed:', error)
    }
    return `${coordinates[1].toFixed(4)}, ${coordinates[0].toFixed(4)}`
  }, [])

  // Create draggable destination marker with route projection
  const createDestinationMarker = useCallback((coordinates: [number, number]) => {
    // Remove existing marker
    if (destinationMarker.current) {
      destinationMarker.current.remove()
    }

    // Create draggable marker
    destinationMarker.current = new mapboxgl.Marker({
      color: '#ef4444',
      scale: 1.2,
      draggable: true
    })
      .setLngLat(coordinates)
      .addTo(map.current!)

    // Add drag event listeners
    destinationMarker.current.on('dragstart', () => {
      // Remove route while dragging
      if (map.current?.getSource('route')) {
        map.current.removeLayer('route')
        map.current.removeSource('route')
      }
    })

    destinationMarker.current.on('dragend', async () => {
      const lngLat = destinationMarker.current!.getLngLat()
      const coords: [number, number] = [lngLat.lng, lngLat.lat]

      // Update destination with new coordinates
      const address = await reverseGeocode(coords)
      const destinationObj = {
        id: `destination-${Date.now()}`,
        place_name: address,
        center: coords,
        geometry: {
          type: 'Point' as const,
          coordinates: coords
        },
        properties: {},
        context: []
      }

      setDestination(destinationObj)

      // Project route to new destination
      await projectRoute(currentLocation!, coords)

      // Mock ride estimate
      setRideEstimate({
        distance: 5000,
        duration: 900
      })
    })

    return destinationMarker.current
  }, [reverseGeocode, currentLocation])

  // Project route between origin and destination
  const projectRoute = useCallback(async (origin: [number, number], destination: [number, number]) => {
    if (!map.current) return

    try {
      // Call Mapbox Directions API
      const response = await fetch(
        `https://api.mapbox.com/directions/v5/mapbox/driving/${origin[0]},${origin[1]};${destination[0]},${destination[1]}?geometries=geojson&access_token=${MAPBOX_TOKEN}`
      )

      const data = await response.json()

      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0]

        // Remove existing route if any
        if (map.current.getSource('route')) {
          map.current.removeLayer('route')
          map.current.removeSource('route')
        }

        // Add route source
        map.current.addSource('route', {
          type: 'geojson',
          data: {
            type: 'Feature',
            properties: {},
            geometry: route.geometry
          }
        })

        // Add route layer
        map.current.addLayer({
          id: 'route',
          type: 'line',
          source: 'route',
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': '#3b82f6',
            'line-width': 4,
            'line-opacity': 0.8
          }
        })

        // Fit map to show entire route
        const coordinates = route.geometry.coordinates
        const bounds = coordinates.reduce((bounds: any, coord: any) => {
          return bounds.extend(coord)
        }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]))

        map.current.fitBounds(bounds, {
          padding: 50,
          duration: 1000
        })
      }
    } catch (error) {
      console.warn('Failed to project route:', error)
    }
  }, [])

  // Initialize map
  const initializeMap = useCallback((coords: [number, number]) => {
    if (!mapContainer.current || map.current) return

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/dark-v11',
      center: coords,
      zoom: 14,
      attributionControl: false,
      antialias: true,
      pitch: 0,
      bearing: 0
    })

    map.current.on('load', () => {
      setIsMapLoaded(true)

      // Add user location marker with custom styling
      const userElement = document.createElement('div')
      userElement.innerHTML = `
        <div style="
          width: 20px;
          height: 20px;
          background: #3b82f6;
          border: 3px solid white;
          border-radius: 50%;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
        "></div>
      `

      userMarker.current = new mapboxgl.Marker({
        element: userElement
      })
        .setLngLat(coords)
        .addTo(map.current!)

      // Add click handler for destination selection
      map.current!.on('click', (e) => {
        if (isSelectionMode) {
          const coordinates: [number, number] = [e.lngLat.lng, e.lngLat.lat]
          handleMapClick(coordinates)
        }
      })

      // Add cursor change on hover when in selection mode
      map.current!.on('mouseenter', () => {
        if (isSelectionMode) {
          map.current!.getCanvas().style.cursor = 'crosshair'
        }
      })

      map.current!.on('mouseleave', () => {
        map.current!.getCanvas().style.cursor = ''
      })
    })

    // Add navigation controls positioned to avoid UI overlap
    map.current.addControl(new mapboxgl.NavigationControl(), 'bottom-right')
  }, [isSelectionMode])

  // Handle map click for destination selection
  const handleMapClick = useCallback(async (coordinates: [number, number]) => {
    // Create draggable marker
    createDestinationMarker(coordinates)

    // Get address for the location
    const address = await reverseGeocode(coordinates)

    // Create destination object
    const destinationObj = {
      id: `destination-${Date.now()}`,
      place_name: address,
      center: coordinates,
      geometry: {
        type: 'Point' as const,
        coordinates
      },
      properties: {},
      context: []
    }

    setDestination(destinationObj)

    // Project route from current location to destination
    if (currentLocation) {
      await projectRoute(currentLocation, coordinates)
    }

    // Mock ride estimate
    setRideEstimate({
      distance: 5000,
      duration: 900
    })
  }, [createDestinationMarker, reverseGeocode, currentLocation, projectRoute])

  // Get user location on mount and initialize map
  useEffect(() => {
    const initLocation = async () => {
      try {
        const coords = await getCurrentLocation()
        setCurrentLocation(coords)
        initializeMap(coords)
      } catch (error) {
        console.warn('Could not get user location:', error)
        // Fallback to São Paulo
        const fallbackCoords: [number, number] = [-46.6333, -23.5505]
        setCurrentLocation(fallbackCoords)
        initializeMap(fallbackCoords)
      }
    }
    initLocation()
  }, [getCurrentLocation, initializeMap])

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      setSpeechSupported(true)
    }
  }, [])

  // Sort search results by distance whenever they change
  useEffect(() => {
    const sorted = sortResultsByDistance(searchResults)
    setSortedResults(sorted)
  }, [searchResults, sortResultsByDistance])

  // Cleanup map on unmount
  useEffect(() => {
    return () => {
      if (map.current) {
        map.current.remove()
      }
    }
  }, [])

  // Calculate price for selected vehicle
  const calculatePrice = useCallback((vehicle: VehicleOption) => {
    if (!rideEstimate) return vehicle.basePrice
    const distance = rideEstimate.distance / 1000 // Convert to km
    return vehicle.basePrice + (distance * vehicle.pricePerKm)
  }, [rideEstimate])

  // Voice search functionality
  const startVoiceSearch = useCallback(() => {
    if (!speechSupported) return

    setIsListening(true)
    const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition
    const recognition = new SpeechRecognition()

    recognition.lang = 'pt-BR'
    recognition.continuous = false
    recognition.interimResults = false

    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript
      searchPlaces(transcript)
      setIsListening(false)
    }

    recognition.onerror = () => {
      setIsListening(false)
      setError('Erro no reconhecimento de voz')
    }

    recognition.onend = () => {
      setIsListening(false)
    }

    recognition.start()
  }, [speechSupported, searchPlaces])

  // Event handlers
  const handleDestinationSelect = useCallback(async (place: any) => {
    setDestination(place)
    selectResult(place)

    // Add draggable marker to map
    if (map.current && place.center) {
      createDestinationMarker(place.center)

      // Project route from current location to destination
      if (currentLocation) {
        await projectRoute(currentLocation, place.center)
      }

      // Center map on destination with smooth animation
      map.current.flyTo({
        center: place.center,
        zoom: 16,
        duration: 1000,
        essential: true
      })
    }

    // Mock ride estimate
    setRideEstimate({
      distance: 5000,
      duration: 900
    })
  }, [selectResult, createDestinationMarker, currentLocation, projectRoute])

  const handleConfirmDestination = useCallback(() => {
    if (destination) {
      setIsFullScreen(false)
      setIsSelectionMode(false)
      setStep('details')
    }
  }, [destination])

  const handleVehicleSelect = useCallback((vehicle: VehicleOption) => {
    setSelectedVehicle(vehicle)
  }, [])

  const handlePaymentSelect = useCallback((payment: PaymentMethod) => {
    setSelectedPayment(payment)
  }, [])

  const handleRequestDriver = useCallback(async () => {
    if (!selectedVehicle || !selectedPayment || !destination) return

    setLoading(true)
    setStep('waiting')

    // Simulate driver assignment
    setTimeout(() => {
      setDriver(MOCK_DRIVER)
      setLoading(false)
    }, 3000)
  }, [selectedVehicle, selectedPayment, destination])

  const handleStartRide = useCallback(() => {
    setRideStarted(true)
    setStep('riding')
  }, [])

  const handleFinishRide = useCallback(() => {
    setStep('rating')
  }, [])

  const handleRating = useCallback((stars: number) => {
    setRating(stars)
  }, [])

  const handleSubmitRating = useCallback(() => {
    // Submit rating and navigate back
    navigate('/dashboard')
  }, [navigate])

  const handleEmergency = useCallback(() => {
    setEmergencyMode(true)
    // In a real app, this would contact emergency services
    alert('Emergência ativada! Contatos de emergência foram notificados.')
  }, [])

  const handleBack = useCallback(() => {
    switch (step) {
      case 'details':
        setStep('map')
        setSelectedVehicle(null)
        setSelectedPayment(null)
        setObservations('')
        break
      case 'waiting':
        setStep('details')
        setDriver(null)
        break
      case 'riding':
        // Can't go back during ride
        break
      case 'rating':
        // Can't go back from rating
        break
      default:
        navigate('/dashboard')
    }
  }, [step, navigate])

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-black/40"></div>
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

      <div className="relative z-10 flex flex-col h-full min-h-screen">
        {/* Header */}
        <motion.div
          className="pt-8 pb-6 px-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="flex items-center justify-between">
            {step !== 'map' && step !== 'riding' && step !== 'rating' && (
              <motion.button
                onClick={handleBack}
                className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ArrowLeft className="w-5 h-5" />
              </motion.button>
            )}

            <div className="flex-1 text-center">
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-sm text-white/70">
                {step === 'map' && 'Escolha o destino'}
                {step === 'details' && 'Detalhes da corrida'}
                {step === 'waiting' && 'Aguardando motorista'}
                {step === 'riding' && 'Corrida em andamento'}
                {step === 'rating' && 'Avalie sua corrida'}
              </p>
            </div>

            {/* Emergency Button (only during waiting and riding) */}
            {(step === 'waiting' || step === 'riding') && (
              <motion.button
                onClick={handleEmergency}
                className="p-2 rounded-xl bg-red-500/20 backdrop-blur-sm border border-red-500/30 text-red-400"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Shield className="w-5 h-5" />
              </motion.button>
            )}

            {step === 'map' && <div className="w-9"></div>}
          </div>
        </motion.div>

        {/* Error Message */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mx-4 mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-xl backdrop-blur-sm"
            >
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-400" />
                <p className="text-red-200 text-sm">{error}</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Content */}
        <div className="flex-1 px-4 pb-8">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {/* [1ª TELA] MAPA COMPLETO */}
            {step === 'map' && (
              <motion.div
                variants={itemVariants}
                className={`${isFullScreen ? 'fixed inset-0 z-50' : 'space-y-4'}`}
              >
                {/* Full Screen Map Container */}
                <div className={`relative ${isFullScreen ? 'h-screen' : 'h-[70vh] rounded-2xl border border-white/20 shadow-2xl'} overflow-hidden bg-gray-900 ${isSelectionMode ? 'selection-mode' : ''}`}>
                  {/* Map Container */}
                  <div ref={mapContainer} className="w-full h-full" />

                  {/* Header with Back Button (Full Screen Only) */}
                  {isFullScreen && (
                    <div className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/50 to-transparent p-4">
                      <div className="flex items-center justify-between">
                        <button
                          onClick={() => navigate('/dashboard')}
                          className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors"
                        >
                          <ArrowLeft className="w-5 h-5" />
                          <span className="text-sm font-medium">Voltar</span>
                        </button>
                        <div className="text-white/80 text-sm font-medium">
                          Selecione o destino
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Search Box Overlay - Positioned to avoid map controls */}
                  <div className="absolute top-20 left-4 right-4 z-10">
                    <div className="bg-white/10 backdrop-blur-md rounded-xl p-3 border border-white/20 shadow-lg">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                        <input
                          type="text"
                          value={searchQuery}
                          onChange={(e) => searchPlaces(e.target.value)}
                          placeholder="Para onde vamos?"
                          className="w-full pl-10 pr-16 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                        />

                        {/* Voice Search Button */}
                        {speechSupported && (
                          <button
                            onClick={startVoiceSearch}
                            disabled={isListening}
                            className="absolute right-8 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors disabled:opacity-50"
                          >
                            {isListening ? (
                              <MicOff className="w-4 h-4 text-red-400 animate-pulse" />
                            ) : (
                              <Mic className="w-4 h-4" />
                            )}
                          </button>
                        )}

                        {/* Loading Indicator */}
                        {isSearching && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
                          </div>
                        )}
                      </div>

                      {/* Search Results - Ordered by Distance */}
                      {sortedResults.length > 0 && (
                        <div className="mt-3 bg-white/10 rounded-lg border border-white/20 overflow-hidden max-h-48 overflow-y-auto">
                          {sortedResults.map((result, index) => (
                            <button
                              key={index}
                              onClick={() => handleDestinationSelect(result)}
                              className="w-full text-left px-3 py-2 text-white hover:bg-white/10 transition-colors border-b border-white/10 last:border-b-0"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2 flex-1">
                                  <MapPin className="w-3 h-3 text-white/60 flex-shrink-0" />
                                  <div className="flex-1">
                                    <p className="text-sm font-medium">{result.text}</p>
                                    <p className="text-xs text-white/60 truncate">{result.place_name}</p>
                                  </div>
                                </div>
                                {result.distance && (
                                  <div className="text-xs text-white/50 ml-2">
                                    {result.distance < 1
                                      ? `${Math.round(result.distance * 1000)}m`
                                      : `${result.distance.toFixed(1)}km`
                                    }
                                  </div>
                                )}
                              </div>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Selection Mode Indicator - Minimal */}
                  {isSelectionMode && !destination && (
                    <div className="absolute bottom-32 left-1/2 transform -translate-x-1/2 z-10">
                      <div className="bg-black/60 backdrop-blur-sm rounded-full px-4 py-2">
                        <span className="text-white text-sm">Toque no mapa</span>
                      </div>
                    </div>
                  )}

                  {/* Map Loading Indicator */}
                  {!isMapLoaded && (
                    <div className="absolute inset-0 bg-gray-900/80 flex items-center justify-center z-30">
                      <div className="text-center text-white">
                        <Loader2 className="w-12 h-12 animate-spin mx-auto mb-4" />
                        <p className="text-lg font-medium">Carregando mapa...</p>
                        <p className="text-sm text-white/70">Aguarde um momento</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Bottom UI Panel - Only show when destination is selected */}
                {destination && (
                  <motion.div
                    initial={{ opacity: 0, y: 100 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`${isFullScreen ? 'absolute bottom-0 left-0 right-0' : ''} z-20`}
                  >
                    <div className="bg-gradient-to-t from-black/90 via-black/60 to-transparent p-4 pt-8">
                      {/* Simple Destination Card */}
                      <div className="bg-white/10 backdrop-blur-md rounded-xl p-3 mb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                              <MapPin className="w-4 h-4 text-white" />
                            </div>
                            <div className="flex-1">
                              <p className="text-white text-sm font-medium line-clamp-1">{destination.place_name}</p>
                            </div>
                          </div>
                          <button
                            onClick={() => {
                              setDestination(null)
                              if (destinationMarker.current) {
                                destinationMarker.current.remove()
                                destinationMarker.current = null
                              }
                              // Remove route
                              if (map.current?.getSource('route')) {
                                map.current.removeLayer('route')
                                map.current.removeSource('route')
                              }
                            }}
                            className="text-white/60 hover:text-white transition-colors p-1"
                          >
                            ✕
                          </button>
                        </div>
                      </div>

                      {/* Simple Confirm Button */}
                      <motion.button
                        onClick={handleConfirmDestination}
                        className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200"
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}
                      >
                        Confirmar destino
                      </motion.button>
                    </div>
                  </motion.div>
                )}
              </motion.div>
            )}

            {/* [2ª TELA] DETALHES DA CORRIDA */}
            {step === 'details' && (
              <motion.div variants={itemVariants} className="space-y-6">
                {/* Route Summary */}
                <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                  <h3 className="text-xl font-semibold text-white mb-6 text-center">
                    📋 Detalhes da corrida
                  </h3>

                  {/* Origin/Destination Summary */}
                  <div className="space-y-4 mb-6">
                    <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-xl">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <MapPin className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-white">Origem</p>
                        <p className="text-xs text-white/70">Localização atual</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-xl">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <Target className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-white">Destino</p>
                        <p className="text-xs text-white/70">{destination?.place_name || 'Destino selecionado'}</p>
                      </div>
                    </div>

                    {/* Distance and Time */}
                    {rideEstimate && (
                      <div className="flex items-center justify-between p-3 bg-blue-500/10 rounded-xl border border-blue-500/30">
                        <div className="flex items-center space-x-2">
                          <Route className="w-4 h-4 text-blue-400" />
                          <span className="text-sm text-white">{(rideEstimate.distance / 1000).toFixed(1)} km</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4 text-blue-400" />
                          <span className="text-sm text-white">{Math.round(rideEstimate.duration / 60)} min</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Vehicle Options */}
                <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                  <h4 className="text-lg font-semibold text-white mb-4">🚗 Escolha o veículo</h4>

                  <div className="grid grid-cols-3 gap-3">
                    {VEHICLE_OPTIONS.map((vehicle) => {
                      const price = calculatePrice(vehicle)
                      const isSelected = selectedVehicle?.id === vehicle.id

                      return (
                        <motion.button
                          key={vehicle.id}
                          onClick={() => handleVehicleSelect(vehicle)}
                          className={`p-4 rounded-xl border transition-all ${
                            isSelected
                              ? 'bg-blue-500/20 border-blue-500/50 ring-2 ring-blue-500/30'
                              : 'bg-white/5 border-white/20 hover:bg-white/10'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="text-center">
                            <div className="text-2xl mb-2">{vehicle.icon}</div>
                            <h5 className="font-semibold text-white text-sm">{vehicle.name}</h5>
                            <p className="text-xs text-white/60 mb-2">{vehicle.description}</p>
                            <div className="text-sm font-bold text-white">R$ {price.toFixed(2)}</div>
                            <div className="flex items-center justify-center space-x-1 mt-1">
                              <Clock className="w-3 h-3 text-white/60" />
                              <span className="text-xs text-white/60">{vehicle.estimatedTime}min</span>
                            </div>
                          </div>
                        </motion.button>
                      )
                    })}
                  </div>
                </div>

                {/* Payment Options */}
                <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                  <h4 className="text-lg font-semibold text-white mb-4">💳 Forma de pagamento</h4>

                  <div className="space-y-3">
                    {PAYMENT_METHODS.map((method) => {
                      const isSelected = selectedPayment?.id === method.id

                      return (
                        <motion.button
                          key={method.id}
                          onClick={() => handlePaymentSelect(method)}
                          className={`w-full p-4 rounded-xl border transition-all ${
                            isSelected
                              ? 'bg-green-500/20 border-green-500/50 ring-2 ring-green-500/30'
                              : 'bg-white/5 border-white/20 hover:bg-white/10'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <span className="text-xl">{method.icon}</span>
                              <div className="text-left">
                                <h5 className="font-semibold text-white">{method.name}</h5>
                                {method.lastFour && (
                                  <p className="text-sm text-white/70">•••• {method.lastFour}</p>
                                )}
                              </div>
                            </div>
                            {isSelected && (
                              <CheckCircle className="w-5 h-5 text-green-400" />
                            )}
                          </div>
                        </motion.button>
                      )
                    })}
                  </div>
                </div>

                {/* Observations */}
                <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                  <h4 className="text-lg font-semibold text-white mb-4">📝 Observações (opcional)</h4>
                  <textarea
                    value={observations}
                    onChange={(e) => setObservations(e.target.value)}
                    placeholder="Alguma observação para o motorista?"
                    className="w-full p-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                    rows={3}
                  />
                </div>

                {/* Request Driver Button */}
                {selectedVehicle && selectedPayment && (
                  <motion.button
                    onClick={handleRequestDriver}
                    disabled={loading}
                    className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 disabled:opacity-50 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    {loading ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : (
                      <>
                        <Send className="w-5 h-5" />
                        <span>Solicitar motorista</span>
                      </>
                    )}
                  </motion.button>
                )}
              </motion.div>
            )}

            {/* [3ª TELA] AGUARDANDO MOTORISTA */}
            {step === 'waiting' && (
              <motion.div variants={itemVariants} className="space-y-6">
                {/* Driver Search Status */}
                <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 shadow-2xl text-center">
                  {!driver ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                        className="w-16 h-16 mx-auto mb-6"
                      >
                        <div className="w-full h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                          <Car className="w-8 h-8 text-white" />
                        </div>
                      </motion.div>

                      <h3 className="text-xl font-semibold text-white mb-4">
                        🔍 Procurando motorista
                      </h3>

                      <p className="text-white/70 mb-6">
                        Estamos encontrando o melhor motorista para você...
                      </p>

                      <div className="flex items-center justify-center space-x-2">
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </>
                  ) : (
                    <>
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", duration: 0.6 }}
                        className="w-16 h-16 mx-auto mb-6 bg-green-500 rounded-full flex items-center justify-center"
                      >
                        <CheckCircle className="w-8 h-8 text-white" />
                      </motion.div>

                      <h3 className="text-xl font-semibold text-white mb-4">
                        ✅ Motorista encontrado!
                      </h3>

                      <p className="text-white/70 mb-6">
                        Seu motorista está a caminho
                      </p>
                    </>
                  )}
                </div>

                {/* Driver Info */}
                {driver && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl"
                  >
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <User className="w-8 h-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-semibold text-white">{driver.name}</h4>
                        <div className="flex items-center space-x-2">
                          <Star className="w-4 h-4 text-yellow-400" />
                          <span className="text-sm text-white/70">{driver.rating}</span>
                          <span className="text-white/50">•</span>
                          <span className="text-sm text-white/70">{driver.vehicle}</span>
                        </div>
                        <p className="text-sm text-white/60">{driver.plate}</p>
                      </div>
                    </div>

                    <div className="bg-blue-500/10 rounded-xl p-4 border border-blue-500/30">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4 text-blue-400" />
                          <span className="text-white">Tempo estimado</span>
                        </div>
                        <span className="text-lg font-bold text-white">{driver.estimatedArrival} min</span>
                      </div>
                    </div>

                    <div className="flex space-x-3 mt-4">
                      <motion.button
                        onClick={() => window.open(`tel:${driver.phone}`)}
                        className="flex-1 bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Phone className="w-4 h-4" />
                        <span>Ligar</span>
                      </motion.button>

                      <motion.button
                        onClick={() => window.open(`sms:${driver.phone}`)}
                        className="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <MessageCircle className="w-4 h-4" />
                        <span>Mensagem</span>
                      </motion.button>
                    </div>
                  </motion.div>
                )}

                {/* Map with Driver Location */}
                <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl overflow-hidden">
                  <div className="h-64 bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center relative">
                    <div className="text-center text-white/70">
                      <MapPin className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Mapa com motorista aproximando</p>
                    </div>

                    {/* Mock driver location */}
                    {driver && (
                      <div className="absolute top-1/4 left-1/3 transform -translate-x-1/2 -translate-y-1/2">
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                          className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center shadow-lg"
                        >
                          <Car className="w-3 h-3 text-white" />
                        </motion.div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Start Ride Button (appears when driver arrives) */}
                {driver && (
                  <motion.button
                    onClick={handleStartRide}
                    className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 2 }}
                  >
                    <Navigation className="w-5 h-5" />
                    <span>Motorista chegou - Iniciar corrida</span>
                  </motion.button>
                )}
              </motion.div>
            )}

            {/* [4ª TELA] CORRIDA EM ANDAMENTO */}
            {step === 'riding' && (
              <motion.div variants={itemVariants} className="space-y-6">
                {/* Ride Status */}
                <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                  <div className="text-center mb-4">
                    <h3 className="text-xl font-semibold text-white mb-2">
                      🚗 Corrida em andamento
                    </h3>
                    <p className="text-white/70">Você está a caminho do destino</p>
                  </div>

                  {/* Driver Info */}
                  {driver && (
                    <div className="flex items-center space-x-4 p-4 bg-white/5 rounded-xl">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <User className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-white">{driver.name}</h4>
                        <div className="flex items-center space-x-2">
                          <Star className="w-4 h-4 text-yellow-400" />
                          <span className="text-sm text-white/70">{driver.rating}</span>
                          <span className="text-white/50">•</span>
                          <span className="text-sm text-white/70">{driver.vehicle}</span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <motion.button
                          onClick={() => window.open(`tel:${driver.phone}`)}
                          className="p-2 bg-green-500 hover:bg-green-600 rounded-lg transition-colors"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Phone className="w-4 h-4 text-white" />
                        </motion.button>
                        <motion.button
                          onClick={() => window.open(`sms:${driver.phone}`)}
                          className="p-2 bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <MessageCircle className="w-4 h-4 text-white" />
                        </motion.button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Real-time Route Map */}
                <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl overflow-hidden">
                  <div className="h-80 bg-gradient-to-br from-green-500/20 to-blue-500/20 flex items-center justify-center relative">
                    <div className="text-center text-white/70">
                      <Route className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Rota em tempo real</p>
                      <p className="text-xs">Acompanhe sua viagem</p>
                    </div>

                    {/* Mock route line */}
                    <div className="absolute inset-4 border-2 border-dashed border-blue-400/50 rounded-lg"></div>

                    {/* Current position */}
                    <div className="absolute top-1/2 left-1/4 transform -translate-x-1/2 -translate-y-1/2">
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                        className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg"
                      >
                        <Car className="w-3 h-3 text-white" />
                      </motion.div>
                    </div>

                    {/* Destination */}
                    <div className="absolute top-1/2 right-1/4 transform translate-x-1/2 -translate-y-1/2">
                      <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center shadow-lg">
                        <Target className="w-3 h-3 text-white" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Trip Progress */}
                <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                  <h4 className="text-lg font-semibold text-white mb-4">📊 Progresso da viagem</h4>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Distância percorrida</span>
                      <span className="text-white font-semibold">2.3 km</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Tempo decorrido</span>
                      <span className="text-white font-semibold">8 min</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70">Tempo restante</span>
                      <span className="text-white font-semibold">7 min</span>
                    </div>

                    {/* Progress bar */}
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <motion.div
                        className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: '60%' }}
                        transition={{ duration: 2 }}
                      />
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <motion.button
                    onClick={() => {
                      navigator.share?.({
                        title: 'Compartilhar trajeto',
                        text: 'Estou em uma corrida MobiDrive',
                        url: window.location.href
                      })
                    }}
                    className="w-full bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/50 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Share2 className="w-5 h-5" />
                    <span>Compartilhar trajeto</span>
                  </motion.button>

                  {/* Finish Ride Button (only appears near destination) */}
                  <motion.button
                    onClick={handleFinishRide}
                    className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 3 }}
                  >
                    <CheckCircle className="w-5 h-5" />
                    <span>Chegamos ao destino - Finalizar corrida</span>
                  </motion.button>
                </div>
              </motion.div>
            )}

            {/* [5ª TELA] AVALIAÇÃO */}
            {step === 'rating' && (
              <motion.div variants={itemVariants} className="space-y-6">
                {/* Trip Completed */}
                <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 shadow-2xl text-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", duration: 0.6 }}
                    className="w-16 h-16 mx-auto mb-6 bg-green-500 rounded-full flex items-center justify-center"
                  >
                    <CheckCircle className="w-8 h-8 text-white" />
                  </motion.div>

                  <h3 className="text-xl font-semibold text-white mb-4">
                    ✅ Corrida finalizada!
                  </h3>

                  <p className="text-white/70 mb-6">
                    Você chegou ao seu destino com segurança
                  </p>

                  {/* Trip Summary */}
                  <div className="bg-white/5 rounded-xl p-4 mb-6">
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <p className="text-2xl font-bold text-white">5.0</p>
                        <p className="text-xs text-white/60">km</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-white">15</p>
                        <p className="text-xs text-white/60">min</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-white">R$ 12,50</p>
                        <p className="text-xs text-white/60">total</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Driver Rating */}
                <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                  <h4 className="text-lg font-semibold text-white mb-4 text-center">
                    ⭐ Avalie sua experiência
                  </h4>

                  {/* Driver Info */}
                  {driver && (
                    <div className="flex items-center space-x-4 mb-6 p-4 bg-white/5 rounded-xl">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <User className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h5 className="font-semibold text-white">{driver.name}</h5>
                        <p className="text-sm text-white/70">{driver.vehicle}</p>
                      </div>
                    </div>
                  )}

                  {/* Star Rating */}
                  <div className="flex justify-center space-x-2 mb-6">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <motion.button
                        key={star}
                        onClick={() => handleRating(star)}
                        className={`text-3xl transition-colors ${
                          star <= rating ? 'text-yellow-400' : 'text-white/30'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Star className={`w-8 h-8 ${star <= rating ? 'fill-current' : ''}`} />
                      </motion.button>
                    ))}
                  </div>

                  {/* Comment */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-white/90 mb-2">
                      💬 Comentário (opcional)
                    </label>
                    <textarea
                      value={comment}
                      onChange={(e) => setComment(e.target.value)}
                      placeholder="Como foi sua experiência?"
                      className="w-full p-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                      rows={3}
                    />
                  </div>

                  {/* Submit Rating */}
                  <motion.button
                    onClick={handleSubmitRating}
                    disabled={rating === 0}
                    className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"
                    whileHover={{ scale: rating > 0 ? 1.02 : 1 }}
                    whileTap={{ scale: rating > 0 ? 0.98 : 1 }}
                  >
                    <Star className="w-5 h-5" />
                    <span>Finalizar avaliação</span>
                  </motion.button>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  )
}
