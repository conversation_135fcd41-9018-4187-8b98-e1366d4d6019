import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Star, ThumbsUp, ThumbsDown, MessageSquare, Camera, Send, X } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContextSimple'
import { analyticsService } from '../services/AnalyticsService'
import { notificationService } from '../services/NotificationService'

interface RideRatingProps {
  rideId: string
  driverInfo: {
    id: string
    name: string
    photo?: string
    vehicle: string
  }
  onComplete: () => void
}

interface RatingData {
  overall_rating: number
  driver_rating: number
  vehicle_rating: number
  route_rating: number
  safety_rating: number
  punctuality_rating: number
  communication_rating: number
  feedback_text: string
  feedback_categories: string[]
  photos?: string[]
  would_recommend: boolean
}

export const RideRatingSystem: React.FC<RideRatingProps> = ({
  rideId,
  driverInfo,
  onComplete
}) => {
  const { user } = useAuth()
  const [currentStep, setCurrentStep] = useState<'rating' | 'feedback' | 'photos' | 'complete'>('rating')
  const [ratingData, setRatingData] = useState<RatingData>({
    overall_rating: 0,
    driver_rating: 0,
    vehicle_rating: 0,
    route_rating: 0,
    safety_rating: 0,
    punctuality_rating: 0,
    communication_rating: 0,
    feedback_text: '',
    feedback_categories: [],
    photos: [],
    would_recommend: false
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Categorias de feedback predefinidas
  const feedbackCategories = [
    { id: 'clean_vehicle', label: 'Veículo limpo', icon: '🚗' },
    { id: 'safe_driving', label: 'Direção segura', icon: '🛡️' },
    { id: 'friendly_driver', label: 'Motorista simpático', icon: '😊' },
    { id: 'on_time', label: 'Pontual', icon: '⏰' },
    { id: 'good_music', label: 'Boa música', icon: '🎵' },
    { id: 'comfortable_ride', label: 'Viagem confortável', icon: '💺' },
    { id: 'good_conversation', label: 'Boa conversa', icon: '💬' },
    { id: 'helped_with_bags', label: 'Ajudou com bagagens', icon: '🎒' }
  ]

  // Componente de rating com estrelas
  const StarRating: React.FC<{
    value: number
    onChange: (value: number) => void
    label: string
    size?: 'sm' | 'md' | 'lg'
  }> = ({ value, onChange, label, size = 'md' }) => {
    const sizeClasses = {
      sm: 'w-4 h-4',
      md: 'w-6 h-6',
      lg: 'w-8 h-8'
    }

    return (
      <div className="flex items-center justify-between py-3">
        <span className="text-gray-700 font-medium">{label}</span>
        <div className="flex space-x-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <motion.button
              key={star}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => onChange(star)}
              className={`${sizeClasses[size]} transition-colors`}
            >
              <Star
                className={`w-full h-full ${
                  star <= value
                    ? 'text-yellow-400 fill-yellow-400'
                    : 'text-gray-300'
                }`}
              />
            </motion.button>
          ))}
        </div>
      </div>
    )
  }

  // Submeter rating
  const submitRating = async () => {
    if (!user?.id) return

    setIsSubmitting(true)
    try {
      // Salvar rating no banco
      const { error: ratingError } = await supabase
        .from('ride_ratings')
        .insert({
          ride_id: rideId,
          user_id: user.id,
          driver_id: driverInfo.id,
          overall_rating: ratingData.overall_rating,
          driver_rating: ratingData.driver_rating,
          vehicle_rating: ratingData.vehicle_rating,
          route_rating: ratingData.route_rating,
          safety_rating: ratingData.safety_rating,
          punctuality_rating: ratingData.punctuality_rating,
          communication_rating: ratingData.communication_rating,
          feedback_text: ratingData.feedback_text,
          feedback_categories: ratingData.feedback_categories,
          would_recommend: ratingData.would_recommend,
          created_at: new Date().toISOString()
        })

      if (ratingError) {
        console.error('Erro ao salvar rating:', ratingError)
        throw ratingError
      }

      // Atualizar rating médio do motorista
      await updateDriverRating()

      // Analytics
      analyticsService.track('ride_rated', {
        ride_id: rideId,
        driver_id: driverInfo.id,
        overall_rating: ratingData.overall_rating,
        feedback_categories: ratingData.feedback_categories,
        would_recommend: ratingData.would_recommend
      })

      // Notificação de sucesso
      notificationService.showNotification({
        title: '⭐ Avaliação enviada!',
        message: 'Obrigado pelo seu feedback',
        type: 'success',
        priority: 'normal'
      })

      setCurrentStep('complete')
    } catch (error) {
      console.error('Erro ao submeter rating:', error)
      notificationService.showNotification({
        title: '❌ Erro ao enviar avaliação',
        message: 'Tente novamente em alguns instantes',
        type: 'error',
        priority: 'normal'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Atualizar rating médio do motorista
  const updateDriverRating = async () => {
    try {
      // Calcular novo rating médio
      const { data: ratings } = await supabase
        .from('ride_ratings')
        .select('overall_rating')
        .eq('driver_id', driverInfo.id)

      if (ratings && ratings.length > 0) {
        const averageRating = ratings.reduce((sum, r) => sum + r.overall_rating, 0) / ratings.length
        const totalRatings = ratings.length

        // Atualizar perfil do motorista
        await supabase
          .from('driver_profiles')
          .upsert({
            user_id: driverInfo.id,
            average_rating: averageRating,
            total_ratings: totalRatings,
            updated_at: new Date().toISOString()
          })
      }
    } catch (error) {
      console.error('Erro ao atualizar rating do motorista:', error)
    }
  }

  // Renderizar step de rating
  const renderRatingStep = () => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-3 flex items-center justify-center">
          {driverInfo.photo ? (
            <img src={driverInfo.photo} alt={driverInfo.name} className="w-full h-full rounded-full object-cover" />
          ) : (
            <span className="text-2xl">👤</span>
          )}
        </div>
        <h3 className="text-xl font-bold text-gray-900">Como foi sua viagem?</h3>
        <p className="text-gray-600">Avalie {driverInfo.name}</p>
      </div>

      <div className="space-y-2">
        <StarRating
          value={ratingData.overall_rating}
          onChange={(value) => setRatingData(prev => ({ ...prev, overall_rating: value }))}
          label="Avaliação Geral"
          size="lg"
        />
        
        <StarRating
          value={ratingData.driver_rating}
          onChange={(value) => setRatingData(prev => ({ ...prev, driver_rating: value }))}
          label="Motorista"
        />
        
        <StarRating
          value={ratingData.vehicle_rating}
          onChange={(value) => setRatingData(prev => ({ ...prev, vehicle_rating: value }))}
          label="Veículo"
        />
        
        <StarRating
          value={ratingData.safety_rating}
          onChange={(value) => setRatingData(prev => ({ ...prev, safety_rating: value }))}
          label="Segurança"
        />
        
        <StarRating
          value={ratingData.punctuality_rating}
          onChange={(value) => setRatingData(prev => ({ ...prev, punctuality_rating: value }))}
          label="Pontualidade"
        />
      </div>

      <div className="flex items-center justify-center space-x-4 pt-4">
        <button
          onClick={() => setRatingData(prev => ({ ...prev, would_recommend: false }))}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
            ratingData.would_recommend === false
              ? 'bg-red-100 text-red-700 border-2 border-red-300'
              : 'bg-gray-100 text-gray-600 border-2 border-transparent'
          }`}
        >
          <ThumbsDown className="w-5 h-5" />
          <span>Não recomendo</span>
        </button>
        
        <button
          onClick={() => setRatingData(prev => ({ ...prev, would_recommend: true }))}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
            ratingData.would_recommend === true
              ? 'bg-green-100 text-green-700 border-2 border-green-300'
              : 'bg-gray-100 text-gray-600 border-2 border-transparent'
          }`}
        >
          <ThumbsUp className="w-5 h-5" />
          <span>Recomendo</span>
        </button>
      </div>
    </div>
  )

  // Renderizar step de feedback
  const renderFeedbackStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-bold text-gray-900 mb-2">Conte-nos mais</h3>
        <p className="text-gray-600">O que mais gostou na viagem?</p>
      </div>

      <div className="grid grid-cols-2 gap-3">
        {feedbackCategories.map((category) => (
          <button
            key={category.id}
            onClick={() => {
              const isSelected = ratingData.feedback_categories.includes(category.id)
              setRatingData(prev => ({
                ...prev,
                feedback_categories: isSelected
                  ? prev.feedback_categories.filter(c => c !== category.id)
                  : [...prev.feedback_categories, category.id]
              }))
            }}
            className={`p-3 rounded-xl border-2 transition-colors text-left ${
              ratingData.feedback_categories.includes(category.id)
                ? 'border-blue-300 bg-blue-50 text-blue-700'
                : 'border-gray-200 bg-white text-gray-600'
            }`}
          >
            <div className="flex items-center space-x-2">
              <span className="text-lg">{category.icon}</span>
              <span className="text-sm font-medium">{category.label}</span>
            </div>
          </button>
        ))}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Comentário adicional (opcional)
        </label>
        <textarea
          value={ratingData.feedback_text}
          onChange={(e) => setRatingData(prev => ({ ...prev, feedback_text: e.target.value }))}
          placeholder="Compartilhe sua experiência..."
          className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          rows={4}
        />
      </div>
    </div>
  )

  // Renderizar step completo
  const renderCompleteStep = () => (
    <div className="text-center space-y-6">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className="w-20 h-20 bg-green-100 rounded-full mx-auto flex items-center justify-center"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
        >
          ⭐
        </motion.div>
      </motion.div>
      
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Obrigado!</h3>
        <p className="text-gray-600">Sua avaliação foi enviada com sucesso</p>
      </div>

      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={onComplete}
        className="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-xl transition-colors"
      >
        Finalizar
      </motion.button>
    </div>
  )

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="bg-white rounded-2xl w-full max-w-md max-h-[80vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              {currentStep === 'rating' && 'Avaliar Viagem'}
              {currentStep === 'feedback' && 'Feedback'}
              {currentStep === 'complete' && 'Concluído'}
            </h2>
            {currentStep !== 'complete' && (
              <button
                onClick={onComplete}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            )}
          </div>
          
          {/* Progress bar */}
          {currentStep !== 'complete' && (
            <div className="mt-4">
              <div className="flex space-x-2">
                <div className={`flex-1 h-2 rounded-full ${currentStep === 'rating' ? 'bg-blue-500' : 'bg-gray-200'}`} />
                <div className={`flex-1 h-2 rounded-full ${currentStep === 'feedback' ? 'bg-blue-500' : 'bg-gray-200'}`} />
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          <AnimatePresence mode="wait">
            {currentStep === 'rating' && (
              <motion.div
                key="rating"
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: 20, opacity: 0 }}
              >
                {renderRatingStep()}
              </motion.div>
            )}
            
            {currentStep === 'feedback' && (
              <motion.div
                key="feedback"
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: 20, opacity: 0 }}
              >
                {renderFeedbackStep()}
              </motion.div>
            )}
            
            {currentStep === 'complete' && (
              <motion.div
                key="complete"
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
              >
                {renderCompleteStep()}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Footer */}
        {currentStep !== 'complete' && (
          <div className="p-6 border-t border-gray-100">
            <div className="flex space-x-3">
              {currentStep === 'feedback' && (
                <button
                  onClick={() => setCurrentStep('rating')}
                  className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-colors"
                >
                  Voltar
                </button>
              )}
              
              <button
                onClick={() => {
                  if (currentStep === 'rating') {
                    if (ratingData.overall_rating === 0) return
                    setCurrentStep('feedback')
                  } else if (currentStep === 'feedback') {
                    submitRating()
                  }
                }}
                disabled={
                  (currentStep === 'rating' && ratingData.overall_rating === 0) ||
                  isSubmitting
                }
                className="flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white font-semibold py-3 px-4 rounded-xl transition-colors flex items-center justify-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Enviando...</span>
                  </>
                ) : (
                  <>
                    <span>{currentStep === 'rating' ? 'Continuar' : 'Enviar Avaliação'}</span>
                    <Send className="w-4 h-4" />
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </motion.div>
    </motion.div>
  )
}

export default RideRatingSystem
