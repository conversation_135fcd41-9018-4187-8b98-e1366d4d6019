// =====================================================
// SCRIPT DE LIMPEZA DE MÉTODOS DE PAGAMENTO DUPLICADOS
// Remove métodos duplicados do banco Supabase
// =====================================================

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

const supabase = createClient(supabaseUrl, supabaseKey)

async function cleanupDuplicatePayments() {
  console.log('🧹 INICIANDO LIMPEZA DE MÉTODOS DUPLICADOS')
  console.log('=' .repeat(50))

  try {
    // 1. Buscar todos os métodos de pagamento
    console.log('📋 Buscando todos os métodos de pagamento...')
    const { data: allMethods, error: fetchError } = await supabase
      .from('payment_methods')
      .select('*')
      .eq('is_active', true)
      .order('user_id')
      .order('type')
      .order('created_at', { ascending: false })

    if (fetchError) {
      console.error('❌ Erro ao buscar métodos:', fetchError)
      return
    }

    console.log(`📊 Total de métodos encontrados: ${allMethods.length}`)

    // 2. Agrupar por usuário e tipo
    const userGroups = {}
    allMethods.forEach(method => {
      const key = `${method.user_id}_${method.type}`
      if (!userGroups[key]) {
        userGroups[key] = []
      }
      userGroups[key].push(method)
    })

    // 3. Identificar duplicatas
    let duplicatesFound = 0
    let methodsToDelete = []

    for (const [key, methods] of Object.entries(userGroups)) {
      if (methods.length > 1) {
        duplicatesFound += methods.length - 1
        console.log(`🔍 Duplicatas encontradas para ${key}: ${methods.length} métodos`)
        
        // Manter apenas o mais recente ou o padrão
        const sortedMethods = methods.sort((a, b) => {
          if (a.is_default && !b.is_default) return -1
          if (!a.is_default && b.is_default) return 1
          return new Date(b.created_at) - new Date(a.created_at)
        })

        // Marcar os outros para exclusão
        const toDelete = sortedMethods.slice(1)
        methodsToDelete.push(...toDelete)
        
        console.log(`  ✅ Mantendo: ${sortedMethods[0].id} (${sortedMethods[0].created_at})`)
        toDelete.forEach(method => {
          console.log(`  ❌ Removendo: ${method.id} (${method.created_at})`)
        })
      }
    }

    console.log(`\n📊 RESUMO:`)
    console.log(`🔍 Duplicatas encontradas: ${duplicatesFound}`)
    console.log(`🗑️ Métodos para remover: ${methodsToDelete.length}`)

    if (methodsToDelete.length === 0) {
      console.log('✅ Nenhuma duplicata encontrada!')
      return
    }

    // 4. Confirmar limpeza
    console.log('\n⚠️ ATENÇÃO: Esta operação irá remover métodos duplicados!')
    console.log('Pressione Ctrl+C para cancelar ou aguarde 5 segundos...')
    
    await new Promise(resolve => setTimeout(resolve, 5000))

    // 5. Remover duplicatas
    console.log('\n🗑️ Removendo métodos duplicados...')
    
    for (const method of methodsToDelete) {
      const { error: deleteError } = await supabase
        .from('payment_methods')
        .update({ is_active: false })
        .eq('id', method.id)

      if (deleteError) {
        console.error(`❌ Erro ao remover ${method.id}:`, deleteError)
      } else {
        console.log(`✅ Removido: ${method.id}`)
      }
    }

    console.log('\n🎉 LIMPEZA CONCLUÍDA!')
    console.log(`✅ ${methodsToDelete.length} métodos duplicados removidos`)

    // 6. Verificar resultado
    const { data: finalMethods } = await supabase
      .from('payment_methods')
      .select('*')
      .eq('is_active', true)

    console.log(`📊 Métodos restantes: ${finalMethods.length}`)

  } catch (error) {
    console.error('💥 Erro durante limpeza:', error)
  }
}

// Executar limpeza
cleanupDuplicatePayments()
  .then(() => {
    console.log('\n🏁 SCRIPT DE LIMPEZA FINALIZADO')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
