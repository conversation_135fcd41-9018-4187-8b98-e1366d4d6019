// =====================================================
// SCRIPT PARA CORRIGIR TABELA PAYMENT_METHODS NO SUPABASE
// Executa via Node.js usando o cliente Supabase
// =====================================================

import { createClient } from '@supabase/supabase-js'

// Configuração do Supabase
const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function fixPaymentMethodsTable() {
  console.log('🔧 Iniciando correção da tabela payment_methods...')

  try {
    // 1. Verificar estrutura atual da tabela
    console.log('\n📋 Verificando estrutura atual da tabela...')
    const { data: columns, error: columnsError } = await supabase
      .rpc('get_table_columns', { table_name: 'payment_methods' })
    
    if (columnsError) {
      console.log('⚠️ Não foi possível verificar colunas via RPC, continuando...')
    } else {
      console.log('📊 Colunas atuais:', columns)
    }

    // 2. Executar SQL para adicionar colunas
    console.log('\n🔨 Adicionando colunas necessárias...')
    
    const alterTableSQL = `
      -- Adicionar colunas que estão faltando
      ALTER TABLE payment_methods 
      ADD COLUMN IF NOT EXISTS name TEXT,
      ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      ADD COLUMN IF NOT EXISTS card_holder_name TEXT,
      ADD COLUMN IF NOT EXISTS card_expiry_month TEXT,
      ADD COLUMN IF NOT EXISTS card_expiry_year TEXT,
      ADD COLUMN IF NOT EXISTS pix_key TEXT,
      ADD COLUMN IF NOT EXISTS cash_enabled BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS cash_change_limit DECIMAL(10,2) DEFAULT 50.00,
      ADD COLUMN IF NOT EXISTS cash_notes TEXT;
    `

    const { data: alterResult, error: alterError } = await supabase
      .rpc('exec_sql', { sql: alterTableSQL })

    if (alterError) {
      console.error('❌ Erro ao adicionar colunas:', alterError)
      // Tentar abordagem alternativa
      console.log('🔄 Tentando abordagem alternativa...')
      await addColumnsAlternative()
    } else {
      console.log('✅ Colunas adicionadas com sucesso!')
    }

    // 3. Atualizar registros existentes
    console.log('\n📝 Atualizando registros existentes...')
    
    // Atualizar name baseado em display_name
    const { error: updateNameError } = await supabase
      .from('payment_methods')
      .update({ name: supabase.raw('display_name') })
      .is('name', null)
      .not('display_name', 'is', null)

    if (updateNameError) {
      console.log('⚠️ Erro ao atualizar name:', updateNameError.message)
    } else {
      console.log('✅ Campo name atualizado')
    }

    // Atualizar is_active para true onde for null
    const { error: updateActiveError } = await supabase
      .from('payment_methods')
      .update({ is_active: true })
      .is('is_active', null)

    if (updateActiveError) {
      console.log('⚠️ Erro ao atualizar is_active:', updateActiveError.message)
    } else {
      console.log('✅ Campo is_active atualizado')
    }

    // Atualizar campos cash para métodos de dinheiro
    const { error: updateCashError } = await supabase
      .from('payment_methods')
      .update({ 
        cash_enabled: true,
        cash_change_limit: 50.00,
        cash_notes: 'Tenha o valor exato ou próximo. Motoristas podem não ter troco para valores altos.'
      })
      .eq('type', 'cash')
      .is('cash_enabled', null)

    if (updateCashError) {
      console.log('⚠️ Erro ao atualizar campos cash:', updateCashError.message)
    } else {
      console.log('✅ Campos cash atualizados')
    }

    // 4. Verificar resultado final
    console.log('\n🔍 Verificando dados atualizados...')
    const { data: finalData, error: finalError } = await supabase
      .from('payment_methods')
      .select('*')
      .limit(5)

    if (finalError) {
      console.error('❌ Erro ao verificar dados:', finalError)
    } else {
      console.log('📊 Dados atuais da tabela:')
      console.table(finalData)
    }

    console.log('\n🎉 Correção da tabela concluída!')

  } catch (error) {
    console.error('❌ Erro geral:', error)
  }
}

// Função alternativa para adicionar colunas uma por vez
async function addColumnsAlternative() {
  const columns = [
    { name: 'name', type: 'TEXT' },
    { name: 'is_active', type: 'BOOLEAN DEFAULT true' },
    { name: 'updated_at', type: 'TIMESTAMP WITH TIME ZONE DEFAULT NOW()' },
    { name: 'card_holder_name', type: 'TEXT' },
    { name: 'card_expiry_month', type: 'TEXT' },
    { name: 'card_expiry_year', type: 'TEXT' },
    { name: 'pix_key', type: 'TEXT' },
    { name: 'cash_enabled', type: 'BOOLEAN DEFAULT false' },
    { name: 'cash_change_limit', type: 'DECIMAL(10,2) DEFAULT 50.00' },
    { name: 'cash_notes', type: 'TEXT' }
  ]

  for (const column of columns) {
    try {
      const sql = `ALTER TABLE payment_methods ADD COLUMN IF NOT EXISTS ${column.name} ${column.type};`
      console.log(`🔨 Adicionando coluna: ${column.name}`)
      
      const { error } = await supabase.rpc('exec_sql', { sql })
      
      if (error) {
        console.log(`⚠️ Erro ao adicionar ${column.name}:`, error.message)
      } else {
        console.log(`✅ Coluna ${column.name} adicionada`)
      }
    } catch (error) {
      console.log(`❌ Erro na coluna ${column.name}:`, error.message)
    }
  }
}

// Executar o script
fixPaymentMethodsTable()
  .then(() => {
    console.log('\n🏁 Script finalizado!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Erro fatal:', error)
    process.exit(1)
  })
