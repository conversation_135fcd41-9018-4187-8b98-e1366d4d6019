import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  Play,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  RefreshCw,
  Activity,
  Database,
  Wifi,
  Bell,
  BarChart3,
  Smartphone,
  Settings,
  Users,
  Zap,
  Shield,
  Download
} from 'lucide-react';
import { GradientBackground } from '../components/GradientBackground';
import { useNoZoom } from '../hooks/useNoZoom';
import { comprehensiveValidator, ComprehensiveReport, ValidationResult } from '../utils/ComprehensiveValidator';
import { analyticsService } from '../services/AnalyticsService';
import '../styles/no-zoom.css';

export const ComprehensiveValidationMobile: React.FC = () => {
  const [report, setReport] = useState<ComprehensiveReport | null>(null);
  const [running, setRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');
  const [progress, setProgress] = useState(0);

  useNoZoom();

  useEffect(() => {
    analyticsService.trackPageView('comprehensive_validation', {
      user_type: 'admin'
    });
  }, []);

  const runComprehensiveValidation = async () => {
    setRunning(true);
    setReport(null);
    setProgress(0);
    
    try {
      analyticsService.trackUserAction('comprehensive_validation_started', 'validation');
      
      // Simular progresso
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 2, 95));
      }, 200);

      const validationReport = await comprehensiveValidator.runCompleteValidation();
      
      clearInterval(progressInterval);
      setProgress(100);
      setReport(validationReport);
      
      analyticsService.trackUserAction('comprehensive_validation_completed', 'validation', {
        overall_status: validationReport.overallStatus,
        total_tests: validationReport.totalTests,
        passed: validationReport.passed,
        failed: validationReport.failed,
        warnings: validationReport.warnings,
        deployment_ready: validationReport.deploymentReady
      });
      
    } catch (error) {
      console.error('Erro na validação:', error);
      analyticsService.trackError('comprehensive_validation_error', error);
    } finally {
      setRunning(false);
      setCurrentTest('');
    }
  };

  const downloadReport = () => {
    if (!report) return;
    
    const reportData = JSON.stringify(report, null, 2);
    const blob = new Blob([reportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `mobidrive-validation-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    analyticsService.trackUserAction('validation_report_downloaded', 'validation');
  };

  const getStatusIcon = (status: 'passed' | 'failed' | 'warning') => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    const iconMap = {
      systemHealth: Activity,
      integration: Wifi,
      userJourney: Users,
      database: Database,
      performance: Zap,
      pwa: Smartphone
    };
    
    const IconComponent = iconMap[category as keyof typeof iconMap] || Settings;
    return <IconComponent className="w-5 h-5" />;
  };

  const getOverallStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'from-green-500 to-green-600';
      case 'issues':
        return 'from-yellow-500 to-yellow-600';
      case 'critical':
        return 'from-red-500 to-red-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getOverallStatusText = (status: string) => {
    switch (status) {
      case 'healthy':
        return '✅ Sistema Saudável';
      case 'issues':
        return '⚠️ Problemas Detectados';
      case 'critical':
        return '❌ Problemas Críticos';
      default:
        return '⏳ Aguardando Validação';
    }
  };

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">
      <GradientBackground variant="static" opacity={0.7} />
      <div className="absolute inset-0 bg-black/20"></div>

      <div className="relative z-10 flex flex-col h-full min-h-screen">
        {/* Header */}
        <motion.div
          className="pt-8 pb-6"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="flex items-center justify-between px-4 mb-4">
            <motion.button
              onClick={() => window.location.href = '/dashboard'}
              className="p-2 text-white/80 hover:text-white transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowLeft className="w-6 h-6" />
            </motion.button>
            
            <div className="text-center">
              <h1 className="text-2xl font-bold text-white">
                🔍 Validação Completa
              </h1>
              <p className="text-xs text-white/70">Inspeção End-to-End</p>
            </div>

            <div className="w-10 h-10"></div>
          </div>
        </motion.div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto px-4 space-y-6">
          {/* Action Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
          >
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold text-white mb-2">🚀 Validação Completa do Sistema</h3>
              <p className="text-white/70 text-sm">
                Executa todos os testes de integração, performance, PWA e jornadas do usuário
              </p>
            </div>

            {running && (
              <div className="mb-4">
                <div className="bg-white/10 rounded-full h-2 mb-2">
                  <motion.div
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
                <p className="text-white/60 text-xs text-center">
                  {currentTest || `Progresso: ${progress}%`}
                </p>
              </div>
            )}

            <motion.button
              onClick={runComprehensiveValidation}
              disabled={running}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-4 px-6 rounded-xl font-semibold flex items-center justify-center space-x-2 disabled:opacity-50"
              whileHover={{ scale: running ? 1 : 1.02 }}
              whileTap={{ scale: running ? 1 : 0.98 }}
            >
              {running ? (
                <>
                  <RefreshCw className="w-5 h-5 animate-spin" />
                  <span>Executando Validação...</span>
                </>
              ) : (
                <>
                  <Play className="w-5 h-5" />
                  <span>Iniciar Validação Completa</span>
                </>
              )}
            </motion.button>
          </motion.div>

          {/* Results */}
          {report && (
            <>
              {/* Overall Status */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
              >
                <div className="text-center mb-4">
                  <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-gradient-to-r ${getOverallStatusColor(report.overallStatus)}`}>
                    <span className="text-white font-bold">
                      {getOverallStatusText(report.overallStatus)}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-3 mb-4">
                  <div className="text-center p-3 bg-green-500/20 rounded-xl border border-green-500/30">
                    <div className="text-2xl font-bold text-green-400">{report.passed}</div>
                    <div className="text-xs text-green-200">Passou</div>
                  </div>
                  <div className="text-center p-3 bg-red-500/20 rounded-xl border border-red-500/30">
                    <div className="text-2xl font-bold text-red-400">{report.failed}</div>
                    <div className="text-xs text-red-200">Falhou</div>
                  </div>
                  <div className="text-center p-3 bg-yellow-500/20 rounded-xl border border-yellow-500/30">
                    <div className="text-2xl font-bold text-yellow-400">{report.warnings}</div>
                    <div className="text-xs text-yellow-200">Avisos</div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    report.deploymentReady 
                      ? 'bg-green-500/20 text-green-200 border border-green-500/30'
                      : 'bg-red-500/20 text-red-200 border border-red-500/30'
                  }`}>
                    {report.deploymentReady ? '🚀 Pronto para Deploy' : '⚠️ Não Pronto para Deploy'}
                  </div>

                  <motion.button
                    onClick={downloadReport}
                    className="p-2 bg-blue-500/20 text-blue-200 rounded-lg border border-blue-500/30 hover:bg-blue-500/30 transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Download className="w-4 h-4" />
                  </motion.button>
                </div>
              </motion.div>

              {/* Categories */}
              {Object.entries(report.categories).map(([category, results]) => (
                results.length > 0 && (
                  <motion.div
                    key={category}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
                  >
                    <div className="flex items-center space-x-2 mb-4">
                      {getCategoryIcon(category)}
                      <h3 className="text-lg font-semibold text-white capitalize">
                        {category.replace(/([A-Z])/g, ' $1').trim()}
                      </h3>
                      <div className="text-white/60 text-sm">
                        ({results.length} testes)
                      </div>
                    </div>

                    <div className="space-y-2">
                      {results.map((result, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 bg-white/5 rounded-xl border border-white/10"
                        >
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(result.status)}
                            <div>
                              <div className="text-white font-medium text-sm">
                                {result.component}
                              </div>
                              <div className="text-white/60 text-xs">
                                {result.message}
                              </div>
                            </div>
                          </div>
                          <div className="text-white/50 text-xs">
                            {result.duration}ms
                          </div>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                )
              ))}

              {/* Critical Issues */}
              {report.criticalIssues.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-red-500/20 backdrop-blur-md rounded-2xl p-6 border border-red-500/30 shadow-2xl"
                >
                  <h3 className="text-lg font-semibold text-red-200 mb-4 flex items-center space-x-2">
                    <AlertTriangle className="w-5 h-5" />
                    <span>Problemas Críticos</span>
                  </h3>
                  <div className="space-y-2">
                    {report.criticalIssues.map((issue, index) => (
                      <div key={index} className="text-red-200 text-sm">
                        • {issue}
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Recommendations */}
              {report.recommendations.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-blue-500/20 backdrop-blur-md rounded-2xl p-6 border border-blue-500/30 shadow-2xl"
                >
                  <h3 className="text-lg font-semibold text-blue-200 mb-4 flex items-center space-x-2">
                    <Shield className="w-5 h-5" />
                    <span>Recomendações</span>
                  </h3>
                  <div className="space-y-2">
                    {report.recommendations.map((recommendation, index) => (
                      <div key={index} className="text-blue-200 text-sm">
                        • {recommendation}
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}
            </>
          )}
        </div>

        {/* Footer */}
        <motion.div
          className="pb-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <p className="text-white/50 text-xs">
            © 2024 MobiDrive. Sistema de Validação Completa.
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default ComprehensiveValidationMobile;
