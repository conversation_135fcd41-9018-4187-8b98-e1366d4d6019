import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  MapPin, 
  Clock, 
  Star, 
  CreditCard, 
  Download, 
  Filter,
  Calendar,
  Navigation,
  User,
  Receipt
} from 'lucide-react'

interface RideHistoryItem {
  id: string
  date: Date
  origin: string
  destination: string
  driverName: string
  driverRating: number
  vehicleModel: string
  vehiclePlate: string
  duration: number // em minutos
  distance: number // em km
  price: number
  paymentMethod: string
  status: 'completed' | 'cancelled' | 'refunded'
  rating?: number
  receipt?: string
}

interface RideHistoryProps {
  rides: RideHistoryItem[]
  onRequestReceipt: (rideId: string) => void
  onRateRide: (rideId: string) => void
  onRepeatRide: (ride: RideHistoryItem) => void
}

export const RideHistory: React.FC<RideHistoryProps> = ({
  rides,
  onRequestReceipt,
  onRateRide,
  onRepeatRide
}) => {
  const [filter, setFilter] = useState<'all' | 'completed' | 'cancelled'>('all')
  const [sortBy, setSortBy] = useState<'date' | 'price'>('date')
  const [showFilters, setShowFilters] = useState(false)

  const filteredRides = rides
    .filter(ride => filter === 'all' || ride.status === filter)
    .sort((a, b) => {
      if (sortBy === 'date') {
        return b.date.getTime() - a.date.getTime()
      }
      return b.price - a.price
    })

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}min`
    }
    return `${mins}min`
  }

  const getStatusColor = (status: RideHistoryItem['status']) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100'
      case 'cancelled': return 'text-red-600 bg-red-100'
      case 'refunded': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: RideHistoryItem['status']) => {
    switch (status) {
      case 'completed': return 'Concluída'
      case 'cancelled': return 'Cancelada'
      case 'refunded': return 'Reembolsada'
      default: return 'Desconhecido'
    }
  }

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-800">Histórico de Corridas</h2>
          <motion.button
            onClick={() => setShowFilters(!showFilters)}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Filter className="w-5 h-5 text-gray-600" />
          </motion.button>
        </div>

        {/* Filters */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-4"
          >
            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium text-gray-700">Status:</span>
              {['all', 'completed', 'cancelled'].map((status) => (
                <motion.button
                  key={status}
                  onClick={() => setFilter(status as any)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    filter === status 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {status === 'all' ? 'Todas' : 
                   status === 'completed' ? 'Concluídas' : 'Canceladas'}
                </motion.button>
              ))}
            </div>

            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium text-gray-700">Ordenar por:</span>
              {['date', 'price'].map((sort) => (
                <motion.button
                  key={sort}
                  onClick={() => setSortBy(sort as any)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    sortBy === sort 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {sort === 'date' ? 'Data' : 'Preço'}
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{rides.length}</p>
            <p className="text-sm text-gray-600">Total de corridas</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              R$ {rides.reduce((sum, ride) => sum + ride.price, 0).toFixed(2)}
            </p>
            <p className="text-sm text-gray-600">Total gasto</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">
              {Math.round(rides.reduce((sum, ride) => sum + ride.distance, 0))} km
            </p>
            <p className="text-sm text-gray-600">Distância total</p>
          </div>
        </div>
      </div>

      {/* Rides List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredRides.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-gray-500">
            <Calendar className="w-12 h-12 mb-4 opacity-50" />
            <p className="text-lg font-medium">Nenhuma corrida encontrada</p>
            <p className="text-sm">Tente ajustar os filtros</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredRides.map((ride) => (
              <motion.div
                key={ride.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-6 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ride.status)}`}>
                        {getStatusText(ride.status)}
                      </span>
                      <span className="text-sm text-gray-500">{formatDate(ride.date)}</span>
                    </div>
                    
                    {/* Route */}
                    <div className="space-y-2 mb-3">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full" />
                        <span className="text-sm text-gray-700 font-medium">{ride.origin}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full" />
                        <span className="text-sm text-gray-700 font-medium">{ride.destination}</span>
                      </div>
                    </div>

                    {/* Driver Info */}
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center space-x-1">
                        <User className="w-4 h-4" />
                        <span>{ride.driverName}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span>{ride.driverRating}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <span>{ride.vehicleModel} • {ride.vehiclePlate}</span>
                      </div>
                    </div>

                    {/* Trip Details */}
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{formatDuration(ride.duration)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Navigation className="w-4 h-4" />
                        <span>{ride.distance.toFixed(1)} km</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <CreditCard className="w-4 h-4" />
                        <span>{ride.paymentMethod}</span>
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <p className="text-xl font-bold text-gray-800">R$ {ride.price.toFixed(2)}</p>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-3">
                  <motion.button
                    onClick={() => onRepeatRide(ride)}
                    className="flex items-center space-x-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Navigation className="w-4 h-4" />
                    <span>Repetir</span>
                  </motion.button>

                  {ride.status === 'completed' && !ride.rating && (
                    <motion.button
                      onClick={() => onRateRide(ride.id)}
                      className="flex items-center space-x-1 px-3 py-1 bg-yellow-100 text-yellow-700 rounded-lg text-sm font-medium hover:bg-yellow-200 transition-colors"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Star className="w-4 h-4" />
                      <span>Avaliar</span>
                    </motion.button>
                  )}

                  <motion.button
                    onClick={() => onRequestReceipt(ride.id)}
                    className="flex items-center space-x-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Receipt className="w-4 h-4" />
                    <span>Recibo</span>
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default RideHistory
