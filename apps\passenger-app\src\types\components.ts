// Tipos para componentes React
import { ReactNode } from 'react';

export interface ComponentProps {
  children?: ReactNode;
  className?: string;
}

export interface ButtonProps extends ComponentProps {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface InputProps extends ComponentProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel';
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
}

export interface ModalProps extends ComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
}

export interface MapProps {
  center?: Location;
  zoom?: number;
  markers?: MapMarker[];
  onLocationSelect?: (location: Location) => void;
}

export interface MapMarker {
  id: string;
  location: Location;
  type: 'pickup' | 'destination' | 'driver' | 'passenger';
  icon?: string;
}
