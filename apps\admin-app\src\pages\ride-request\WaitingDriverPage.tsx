import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Navigate, useNavigate } from 'react-router-dom'
import {
  ArrowLeft,
  MapPin,
  Star,
  Phone,
  MessageCircle,
  Shield,
  Clock,
  Car,
  Loader2
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContextSimple'
import { useNoZoom } from '../../hooks/useNoZoom'
import { rideService } from '../../services/RideService'
import WaitingDriverMap from '../../components/WaitingDriverMap'
import useRideTracking from '../../hooks/useRideTracking'

// Types
interface Driver {
  id: string
  name: string
  rating: number
  vehicle: {
    model: string
    color: string
    plate: string
  }
  photo: string
  eta: number
  phone: string
}

// Mock driver data
const MOCK_DRIVER: Driver = {
  id: 'driver-123',
  name: '<PERSON>',
  rating: 4.8,
  vehicle: {
    model: 'Honda Civic',
    color: 'Prata',
    plate: 'ABC-1234'
  },
  photo: '👨‍💼',
  eta: 5,
  phone: '+55 11 99999-9999'
}

export const WaitingDriverPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  
  useNoZoom()

  // Redirect if not logged in
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // State management
  const [tripData, setTripData] = useState<any>(null)
  const [rideId, setRideId] = useState<string | null>(null)
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null)
  const [destination, setDestination] = useState<[number, number] | null>(null)

  // Use ride tracking hook
  const {
    ride,
    driver: trackingDriver,
    isLoading: loading,
    error: rideError,
    searchPhase,
    searchPhases,
    createRide,
    cancelRide
  } = useRideTracking({ rideId: rideId || undefined })

  // Local driver state for UI compatibility
  const [driver, setDriver] = useState<Driver | null>(null)
  const [eta, setEta] = useState(5)

  // Load trip data from sessionStorage
  useEffect(() => {
    const savedTripData = sessionStorage.getItem('tripData')
    const savedOrigin = sessionStorage.getItem('rideOrigin')
    const savedDestination = sessionStorage.getItem('rideDestination')

    console.log('🔍 Loading saved data:', { savedTripData, savedOrigin, savedDestination })

    if (savedTripData) {
      const tripData = JSON.parse(savedTripData)
      console.log('📋 Trip data loaded:', tripData)
      setTripData(tripData)
      setRideId(tripData.rideId || null)
    } else {
      // Create mock trip data for testing
      const mockTripData = {
        vehicle: { name: 'Econômico' },
        payment: { name: 'Dinheiro' },
        price: 15.50,
        estimatedDuration: 600,
        estimatedDistance: 5000,
        estimatedPrice: 15.50
      }
      console.log('🎭 Using mock trip data:', mockTripData)
      setTripData(mockTripData)
    }

    if (savedOrigin) {
      const origin = JSON.parse(savedOrigin)
      console.log('📍 Origin loaded:', origin)
      setUserLocation(origin.center)
    } else {
      // Use São Paulo as default
      const defaultLocation: [number, number] = [-46.6333, -23.5505]
      console.log('🏙️ Using default location (São Paulo):', defaultLocation)
      setUserLocation(defaultLocation)
    }

    if (savedDestination) {
      const dest = JSON.parse(savedDestination)
      console.log('🎯 Destination loaded:', dest)
      setDestination(dest.center)
    } else {
      // Use nearby location as default destination
      const defaultDestination: [number, number] = [-46.6234, -23.5456]
      console.log('🎯 Using default destination:', defaultDestination)
      setDestination(defaultDestination)
    }
  }, [navigate])

  // Update local driver state when tracking driver changes
  useEffect(() => {
    if (trackingDriver) {
      setDriver({
        id: trackingDriver.id,
        name: trackingDriver.name,
        rating: trackingDriver.rating,
        vehicle: trackingDriver.vehicle,
        photo: '👨‍💼',
        eta: trackingDriver.eta,
        phone: trackingDriver.phone
      })
      setEta(trackingDriver.eta)
    }
  }, [trackingDriver])

  // Create ride if not exists
  useEffect(() => {
    if (tripData && userLocation && destination && !rideId && !ride) {
      console.log('🚗 Creating new ride request...')

      const origin = {
        latitude: userLocation[1],
        longitude: userLocation[0],
        address: tripData.origin?.place_name || 'Origem'
      }

      const dest = {
        latitude: destination[1],
        longitude: destination[0],
        address: tripData.destination?.place_name || 'Destino'
      }

      createRide(
        origin,
        dest,
        tripData.estimatedDuration || 600, // 10 minutes default
        tripData.estimatedDistance || 5000, // 5km default
        tripData.estimatedPrice || 15.00, // R$ 15.00 default
        'cash'
      ).then((newRideId) => {
        if (newRideId) {
          setRideId(newRideId)
          console.log('✅ Ride created with ID:', newRideId)
        }
      })
    }
  }, [tripData, userLocation, destination, rideId, ride, createRide])

  // Update ETA countdown
  useEffect(() => {
    if (driver && eta > 0) {
      const etaInterval = setInterval(() => {
        setEta(prev => {
          if (prev <= 1) {
            // Driver arrived, go to riding page
            clearInterval(etaInterval)
            navigate('/ride-request/riding')
            return 0
          }
          return prev - 1
        })
      }, 60000) // Update every minute

      return () => clearInterval(etaInterval)
    }
  }, [driver, eta, navigate])

  // Event handlers
  const handleBack = useCallback(() => {
    navigate('/ride-request/details')
  }, [navigate])

  const handleCall = useCallback(() => {
    if (driver?.phone) {
      window.open(`tel:${driver.phone}`)
    }
  }, [driver])

  const handleMessage = useCallback(() => {
    if (driver?.phone) {
      window.open(`sms:${driver.phone}`)
    }
  }, [driver])

  const handleEmergency = useCallback(() => {
    // In a real app, this would contact emergency services
    alert('Emergência ativada! Contatos de emergência foram notificados.')
  }, [])

  const handleStartRide = useCallback(() => {
    // Store driver data for next page
    const rideData = {
      ...tripData,
      driver,
      startTime: new Date().toISOString()
    }
    sessionStorage.setItem('rideData', JSON.stringify(rideData))
    navigate('/ride-request/riding')
  }, [tripData, driver, navigate])

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }



  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-black/40"></div>
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

      <div className="relative z-10 flex flex-col h-full min-h-screen">
        {/* Header */}
        <motion.div
          className="pt-8 pb-6 px-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="flex items-center justify-between">
            <motion.button
              onClick={handleBack}
              className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowLeft className="w-5 h-5" />
            </motion.button>

            <div className="flex-1 text-center">
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-sm text-white/70">
                {loading ? 'Procurando motorista' : 'Motorista a caminho'}
              </p>
            </div>

            {/* Emergency Button */}
            <motion.button
              onClick={handleEmergency}
              className="p-2 rounded-xl bg-red-500/20 backdrop-blur-sm border border-red-500/30 text-red-400"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Shield className="w-5 h-5" />
            </motion.button>
          </div>
        </motion.div>

        {/* Content */}
        <div className="flex-1 px-4 pb-8">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            <AnimatePresence mode="wait">
              {loading ? (
                /* Driver Search Status */
                <motion.div
                  key="searching"
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                  exit="hidden"
                  className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 shadow-2xl text-center"
                >
                  <div className="mb-6">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      className="w-20 h-20 mx-auto mb-4"
                    >
                      <Car className="w-full h-full text-blue-400" />
                    </motion.div>
                    
                    <h2 className="text-2xl font-bold text-white mb-2">
                      {searchPhases[searchPhase]}
                    </h2>
                    
                    <div className="flex justify-center space-x-1 mb-4">
                      {[0, 1, 2].map((i) => (
                        <motion.div
                          key={i}
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 1, 0.5]
                          }}
                          transition={{
                            duration: 1,
                            repeat: Infinity,
                            delay: i * 0.2
                          }}
                          className="w-2 h-2 bg-blue-400 rounded-full"
                        />
                      ))}
                    </div>
                    
                    <p className="text-white/70">
                      Estamos encontrando o melhor motorista para você
                    </p>
                  </div>
                </motion.div>
              ) : (
                /* Driver Information */
                <motion.div
                  key="driver-info"
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                  className="space-y-6"
                >
                  {/* Driver Card */}
                  <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-2xl">
                        {driver?.photo}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-white">{driver?.name}</h3>
                        <div className="flex items-center space-x-1">
                          <Star className="w-4 h-4 text-yellow-400 fill-current" />
                          <span className="text-white/70">{driver?.rating}</span>
                        </div>
                        <p className="text-white/60 text-sm">
                          {driver?.vehicle.color} {driver?.vehicle.model}
                        </p>
                        <p className="text-white/60 text-sm font-mono">
                          {driver?.vehicle.plate}
                        </p>
                      </div>
                    </div>

                    {/* Contact Buttons */}
                    <div className="flex space-x-3">
                      <button
                        onClick={handleCall}
                        className="flex-1 bg-green-500/20 border border-green-500/30 text-green-400 py-3 px-4 rounded-xl flex items-center justify-center space-x-2 hover:bg-green-500/30 transition-colors"
                      >
                        <Phone className="w-4 h-4" />
                        <span>Ligar</span>
                      </button>
                      <button
                        onClick={handleMessage}
                        className="flex-1 bg-blue-500/20 border border-blue-500/30 text-blue-400 py-3 px-4 rounded-xl flex items-center justify-center space-x-2 hover:bg-blue-500/30 transition-colors"
                      >
                        <MessageCircle className="w-4 h-4" />
                        <span>Mensagem</span>
                      </button>
                    </div>
                  </div>

                  {/* ETA Card */}
                  <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl text-center">
                    <Clock className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                    <h3 className="text-2xl font-bold text-white mb-2">
                      {eta} minutos
                    </h3>
                    <p className="text-white/70">Tempo estimado de chegada</p>
                  </div>

                  {/* Map with Driver Location */}
                  <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl overflow-hidden">
                    {userLocation && destination ? (
                      <WaitingDriverMap
                        rideId={rideId || undefined}
                        userLocation={userLocation}
                        destination={destination}
                        onDriverUpdate={(driverData) => {
                          if (driverData) {
                            setDriver({
                              id: driverData.id,
                              name: driverData.name,
                              rating: 4.8,
                              vehicle: driverData.vehicle,
                              photo: '👨‍💼',
                              eta: driverData.eta,
                              phone: '+55 11 99999-9999'
                            })
                            setEta(driverData.eta)
                          }
                        }}
                        className="h-64"
                      />
                    ) : (
                      <div className="h-64 bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center relative">
                        <div className="text-center text-white/70">
                          <MapPin className="w-12 h-12 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">Carregando localização...</p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Trip Summary */}
                  {tripData && (
                    <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl">
                      <h3 className="text-lg font-bold text-white mb-4">Resumo da Corrida</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-white/70">Veículo:</span>
                          <span className="text-white">{tripData.vehicle?.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-white/70">Pagamento:</span>
                          <span className="text-white">{tripData.payment?.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-white/70">Valor estimado:</span>
                          <span className="text-white font-bold">R$ {tripData.price?.toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Start Ride Button (appears when ETA is very low) */}
                  {eta <= 1 && (
                    <motion.button
                      onClick={handleStartRide}
                      className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                    >
                      Motorista chegou - Iniciar corrida
                    </motion.button>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default WaitingDriverPage
