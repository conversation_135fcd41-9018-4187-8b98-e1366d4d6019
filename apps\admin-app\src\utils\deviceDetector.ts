// 📱 DEVICE DETECTOR - DETECTA TIPO DE DISPOSITIVO
// Determina se deve usar mockup ou renderização nativa

export type ExperienceType = 'mobile' | 'desktop'

export const getExperienceType = (): ExperienceType => {
  // Se estamos no servidor (SSR), assumir desktop
  if (typeof window === 'undefined') {
    return 'desktop'
  }

  // Detectar mobile através de múltiplos métodos
  const userAgent = navigator.userAgent.toLowerCase()
  const isMobileUA = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
  
  // Detectar através de touch
  const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  
  // Detectar através de tamanho da tela
  const isSmallScreen = window.innerWidth <= 768
  
  // Detectar através de orientação
  const hasOrientation = 'orientation' in window
  
  // Combinação de fatores para determinar se é mobile
  const isMobile = isMobileUA || (hasTouch && isSmallScreen) || hasOrientation
  
  console.log('📱 Device Detection:', {
    userAgent: userAgent.substring(0, 50) + '...',
    isMobileUA,
    hasTouch,
    isSmallScreen,
    hasOrientation,
    finalDecision: isMobile ? 'mobile' : 'desktop',
    screenSize: `${window.innerWidth}x${window.innerHeight}`
  })
  
  return isMobile ? 'mobile' : 'desktop'
}

export const isMobileDevice = (): boolean => {
  return getExperienceType() === 'mobile'
}

export const isDesktopDevice = (): boolean => {
  return getExperienceType() === 'desktop'
}
