import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Search, 
  Users, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Car,
  MapPin,
  Star
} from 'lucide-react'

interface RideMatchingStatusProps {
  isSearching: boolean
  driversFound: number
  estimatedWaitTime: number
  matchedDriver?: {
    name: string
    rating: number
    vehicle: string
    eta: number
    photo?: string
  }
  onCancel?: () => void
}

const RideMatchingStatus: React.FC<RideMatchingStatusProps> = ({
  isSearching,
  driversFound,
  estimatedWaitTime,
  matchedDriver,
  onCancel
}) => {
  const [searchPhase, setSearchPhase] = useState(0)
  const [timeElapsed, setTimeElapsed] = useState(0)

  const searchPhases = [
    { text: 'Procurando motoristas próximos...', icon: Search },
    { text: 'Analisando disponibilidade...', icon: Users },
    { text: 'Calculando melhor rota...', icon: MapPin },
    { text: 'Enviando solicitação...', icon: Car }
  ]

  // Simulate search phases
  useEffect(() => {
    if (!isSearching || matchedDriver) return

    const interval = setInterval(() => {
      setSearchPhase(prev => (prev + 1) % searchPhases.length)
    }, 2000)

    return () => clearInterval(interval)
  }, [isSearching, matchedDriver])

  // Track elapsed time
  useEffect(() => {
    if (!isSearching) {
      setTimeElapsed(0)
      return
    }

    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1)
    }, 1000)

    return () => clearInterval(interval)
  }, [isSearching])

  if (!isSearching && !matchedDriver) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="fixed inset-x-4 top-20 z-50"
    >
      <div className="bg-white/95 backdrop-blur-md rounded-3xl border border-white/20 shadow-2xl overflow-hidden">
        <AnimatePresence mode="wait">
          {matchedDriver ? (
            // Driver Found
            <motion.div
              key="matched"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="p-6"
            >
              <div className="flex items-center space-x-4">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring" }}
                  className="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center"
                >
                  <CheckCircle className="w-8 h-8 text-white" />
                </motion.div>
                
                <div className="flex-1">
                  <motion.h3
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-lg font-bold text-gray-900"
                  >
                    Motorista encontrado!
                  </motion.h3>
                  
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                    className="space-y-1"
                  >
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-gray-800">{matchedDriver.name}</span>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-sm text-gray-600">{matchedDriver.rating.toFixed(1)}</span>
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600">{matchedDriver.vehicle}</p>
                    
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4 text-blue-500" />
                      <span className="text-sm font-medium text-blue-600">
                        Chegará em {matchedDriver.eta} minutos
                      </span>
                    </div>
                  </motion.div>
                </div>

                {matchedDriver.photo && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.5 }}
                    className="w-12 h-12 rounded-full overflow-hidden border-2 border-white shadow-lg"
                  >
                    <img 
                      src={matchedDriver.photo} 
                      alt={matchedDriver.name}
                      className="w-full h-full object-cover"
                    />
                  </motion.div>
                )}
              </div>
            </motion.div>
          ) : (
            // Searching
            <motion.div
              key="searching"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="p-6"
            >
              <div className="flex items-center space-x-4">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center"
                >
                  {React.createElement(searchPhases[searchPhase].icon, {
                    className: "w-8 h-8 text-white"
                  })}
                </motion.div>
                
                <div className="flex-1">
                  <motion.h3
                    key={searchPhase}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-lg font-bold text-gray-900"
                  >
                    {searchPhases[searchPhase].text}
                  </motion.h3>
                  
                  <div className="space-y-2 mt-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">
                        {driversFound > 0 ? `${driversFound} motoristas encontrados` : 'Buscando motoristas...'}
                      </span>
                      <span className="text-gray-500">
                        {Math.floor(timeElapsed / 60)}:{(timeElapsed % 60).toString().padStart(2, '0')}
                      </span>
                    </div>
                    
                    {estimatedWaitTime > 0 && (
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4 text-orange-500" />
                        <span className="text-sm text-orange-600">
                          Tempo estimado: {estimatedWaitTime} min
                        </span>
                      </div>
                    )}
                    
                    {timeElapsed > 30 && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="flex items-center space-x-1"
                      >
                        <AlertCircle className="w-4 h-4 text-amber-500" />
                        <span className="text-sm text-amber-600">
                          Demanda alta na região
                        </span>
                      </motion.div>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Progress Bar */}
              <div className="mt-4">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                    initial={{ width: "0%" }}
                    animate={{ 
                      width: matchedDriver ? "100%" : `${Math.min((timeElapsed / 60) * 100, 90)}%`
                    }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
              </div>
              
              {/* Cancel Button */}
              {onCancel && timeElapsed > 10 && (
                <motion.button
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  onClick={onCancel}
                  className="w-full mt-4 py-3 px-4 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-2xl transition-colors duration-200"
                >
                  Cancelar Busca
                </motion.button>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  )
}

export default RideMatchingStatus
