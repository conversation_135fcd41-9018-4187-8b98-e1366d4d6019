import { supabase } from '../lib/supabase'

export interface UserLocation {
  id?: string
  user_id: string
  latitude: number
  longitude: number
  accuracy?: number
  heading?: number
  speed?: number
  altitude?: number
  timestamp: string
  address?: string
  is_active: boolean
  created_at?: string
  updated_at?: string
}

export interface LocationUpdate {
  latitude: number
  longitude: number
  accuracy?: number
  heading?: number
  speed?: number
  altitude?: number
  address?: string
}

class LocationService {
  private static instance: LocationService
  private currentLocationId: string | null = null
  private isTracking = false
  private watchId: number | null = null
  private updateInterval: NodeJS.Timeout | null = null
  private lastUpdate = 0
  private readonly UPDATE_INTERVAL = 30000 // 30 segundos
  private readonly MIN_ACCURACY = 100 // metros
  private tableExists: boolean | null = null // Cache para verificação de tabela
  private tableCheckTime = 0 // Timestamp da última verificação

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService()
    }
    return LocationService.instance
  }

  /**
   * Verificar se a tabela user_locations existe (com cache)
   */
  private async checkTableExists(): Promise<boolean> {
    const now = Date.now()
    const CACHE_DURATION = 5 * 60 * 1000 // 5 minutos

    // Se já verificamos recentemente, usar cache
    if (this.tableExists !== null && (now - this.tableCheckTime) < CACHE_DURATION) {
      return this.tableExists
    }

    try {
      const { error } = await supabase
        .from('user_locations')
        .select('id')
        .limit(1)

      this.tableExists = !error
      this.tableCheckTime = now

      if (!this.tableExists) {
        console.log('📋 Tabela user_locations não existe - usando localStorage')
      } else {
        console.log('📋 Tabela user_locations confirmada - usando Supabase')
      }

      return this.tableExists
    } catch (error) {
      this.tableExists = false
      this.tableCheckTime = now
      console.log('📋 Erro ao verificar tabela - usando localStorage')
      return false
    }
  }

  /**
   * Iniciar tracking de localização
   */
  async startLocationTracking(userId: string): Promise<void> {
    if (this.isTracking) {
      console.log('📍 Location tracking já está ativo')
      return
    }

    try {
      console.log('📍 Iniciando location tracking para usuário:', userId)
      this.isTracking = true

      // Verificar se geolocation está disponível
      if (!navigator.geolocation) {
        throw new Error('Geolocation não suportado neste navegador')
      }

      // Obter localização inicial
      const initialPosition = await this.getCurrentPosition()
      await this.saveLocationToSupabase(userId, {
        latitude: initialPosition.coords.latitude,
        longitude: initialPosition.coords.longitude,
        accuracy: initialPosition.coords.accuracy,
        heading: initialPosition.coords.heading || undefined,
        speed: initialPosition.coords.speed || undefined,
        altitude: initialPosition.coords.altitude || undefined
      })

      // Iniciar watch position
      this.watchId = navigator.geolocation.watchPosition(
        async (position) => {
          await this.handlePositionUpdate(userId, position)
        },
        (error) => {
          console.error('❌ Erro no watch position:', error)
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      )

      // Configurar update interval
      this.updateInterval = setInterval(async () => {
        try {
          const position = await this.getCurrentPosition()
          await this.handlePositionUpdate(userId, position)
        } catch (error) {
          console.error('❌ Erro no update interval:', error)
        }
      }, this.UPDATE_INTERVAL)

      console.log('✅ Location tracking iniciado com sucesso')
    } catch (error) {
      console.error('❌ Erro ao iniciar location tracking:', error)
      this.isTracking = false
      throw error
    }
  }

  /**
   * Parar tracking de localização
   */
  async stopLocationTracking(userId: string): Promise<void> {
    console.log('📍 Parando location tracking')

    try {
      this.isTracking = false

      // Limpar watch position
      if (this.watchId !== null) {
        navigator.geolocation.clearWatch(this.watchId)
        this.watchId = null
      }

      // Limpar interval
      if (this.updateInterval) {
        clearInterval(this.updateInterval)
        this.updateInterval = null
      }

      // Marcar localização atual como inativa
      if (this.currentLocationId) {
        await this.deactivateCurrentLocation(userId)
      }

      console.log('✅ Location tracking parado')
    } catch (error) {
      console.error('❌ Erro ao parar location tracking:', error)
    }
  }

  /**
   * Obter posição atual
   */
  private getCurrentPosition(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        resolve,
        reject,
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      )
    })
  }

  /**
   * Lidar com update de posição
   */
  private async handlePositionUpdate(userId: string, position: GeolocationPosition): Promise<void> {
    const now = Date.now()

    // Evitar updates muito frequentes
    if (now - this.lastUpdate < 10000) { // 10 segundos
      return
    }

    // Verificar precisão
    if (position.coords.accuracy > this.MIN_ACCURACY) {
      console.log('📍 Precisão insuficiente, ignorando update:', position.coords.accuracy)
      return
    }

    try {
      await this.saveLocationToSupabase(userId, {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        heading: position.coords.heading || undefined,
        speed: position.coords.speed || undefined,
        altitude: position.coords.altitude || undefined
      })

      this.lastUpdate = now
    } catch (error) {
      console.error('❌ Erro ao salvar localização:', error)
    }
  }

  /**
   * Salvar localização no Supabase
   */
  private async saveLocationToSupabase(userId: string, location: LocationUpdate): Promise<void> {
    try {
      // Verificar se a tabela existe usando cache
      const tableExists = await this.checkTableExists()

      if (!tableExists) {
        this.saveLocationLocally(userId, location)
        return
      }

      // Obter endereço via reverse geocoding (opcional)
      let address: string | undefined
      try {
        address = await this.reverseGeocode(location.latitude, location.longitude)
      } catch (error) {
        console.warn('⚠️ Erro ao obter endereço:', error)
      }

      // Desativar localização anterior
      if (this.currentLocationId) {
        await this.deactivateCurrentLocation(userId)
      }

      // Inserir nova localização
      const locationData: Omit<UserLocation, 'id' | 'created_at' | 'updated_at'> = {
        user_id: userId,
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy,
        heading: location.heading,
        speed: location.speed,
        altitude: location.altitude,
        timestamp: new Date().toISOString(),
        address: address,
        is_active: true
      }

      const { data, error } = await supabase
        .from('user_locations')
        .insert(locationData)
        .select()
        .single()

      if (error) {
        throw error
      }

      this.currentLocationId = data.id
      console.log('✅ Localização salva no Supabase:', {
        lat: location.latitude.toFixed(6),
        lng: location.longitude.toFixed(6),
        accuracy: location.accuracy,
        address: address?.substring(0, 50) + '...'
      })

    } catch (error) {
      console.error('❌ Erro ao salvar no Supabase:', error)
      // Fallback para armazenamento local
      console.log('📱 Usando fallback local...')
      this.saveLocationLocally(userId, location)
    }
  }

  /**
   * Salvar localização localmente como fallback
   */
  private saveLocationLocally(userId: string, location: LocationUpdate): void {
    try {
      const locationData = {
        user_id: userId,
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy,
        heading: location.heading,
        speed: location.speed,
        altitude: location.altitude,
        timestamp: new Date().toISOString(),
        is_active: true
      }

      // Salvar no localStorage
      const key = `user_location_${userId}`
      localStorage.setItem(key, JSON.stringify(locationData))

      console.log('💾 Localização salva localmente:', {
        lat: location.latitude.toFixed(6),
        lng: location.longitude.toFixed(6),
        accuracy: location.accuracy
      })

    } catch (error) {
      console.error('❌ Erro ao salvar localmente:', error)
    }
  }

  /**
   * Desativar localização atual
   */
  private async deactivateCurrentLocation(userId: string): Promise<void> {
    if (!this.currentLocationId) return

    try {
      const { error } = await supabase
        .from('user_locations')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', this.currentLocationId)
        .eq('user_id', userId)

      if (error) {
        console.error('❌ Erro ao desativar localização:', error)
      }
    } catch (error) {
      console.error('❌ Erro ao desativar localização:', error)
    }
  }

  /**
   * Reverse geocoding para obter endereço
   */
  private async reverseGeocode(lat: number, lng: number): Promise<string> {
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${import.meta.env.VITE_MAPBOX_ACCESS_TOKEN}&language=pt`
      )

      const data = await response.json()

      if (data.features && data.features.length > 0) {
        return data.features[0].place_name
      }

      return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
    } catch (error) {
      console.error('❌ Erro no reverse geocoding:', error)
      return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
    }
  }

  /**
   * Obter localização atual do usuário
   */
  async getCurrentUserLocation(userId: string): Promise<UserLocation | null> {
    try {
      // Verificar se a tabela existe usando cache
      const tableExists = await this.checkTableExists()

      if (!tableExists) {
        return this.getLocationFromLocalStorage(userId)
      }

      const { data, error } = await supabase
        .from('user_locations')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (error && error.code !== 'PGRST116') {
        throw error
      }

      return data || null
    } catch (error) {
      console.error('❌ Erro ao obter localização atual:', error)
      // Fallback para localStorage
      return this.getLocationFromLocalStorage(userId)
    }
  }

  /**
   * Obter localização do localStorage
   */
  private getLocationFromLocalStorage(userId: string): UserLocation | null {
    try {
      const key = `user_location_${userId}`
      const stored = localStorage.getItem(key)
      if (stored) {
        const data = JSON.parse(stored)
        console.log('💾 Localização obtida do localStorage')
        return data
      }
      return null
    } catch (error) {
      console.error('❌ Erro ao obter do localStorage:', error)
      return null
    }
  }

  /**
   * Obter histórico de localizações
   */
  async getLocationHistory(userId: string, limit = 50): Promise<UserLocation[]> {
    try {
      const { data, error } = await supabase
        .from('user_locations')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        throw error
      }

      return data || []
    } catch (error) {
      console.error('❌ Erro ao obter histórico:', error)
      return []
    }
  }

  /**
   * Verificar se está trackando
   */
  isLocationTracking(): boolean {
    return this.isTracking
  }
}

export const locationService = LocationService.getInstance()
export default locationService
