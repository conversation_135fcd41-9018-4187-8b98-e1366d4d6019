import React, { ReactNode, useEffect } from 'react'
import { MobileMockup } from './MobileMockup'
import { useMobileForcer, logMobileForcer } from '../utils/mobileForcer'

// 📱 WRAPPER QUE FORÇA EXPERIÊNCIA MOBILE
// - Desktop: Mostra mockup de celular
// - Mobile: Mostra app normal em tela cheia

interface MobileWrapperProps {
  children: ReactNode
  className?: string
}

export const MobileWrapper: React.FC<MobileWrapperProps> = ({ children, className = "" }) => {
  const shouldUseMockup = useMobileForcer()

  // Log das informações na primeira renderização
  useEffect(() => {
    logMobileForcer()
  }, [])

  // Se é desktop, usar mockup
  if (shouldUseMockup) {
    console.log('🖥️ Desktop detectado - Usando mockup de celular')
    return (
      <MobileMockup className={className}>
        {children}
      </MobileMockup>
    )
  }

  // Se é mobile real, usar tela cheia
  console.log('📱 Mobile detectado - Usando tela cheia')
  return (
    <div className={`w-full h-full ${className}`}>
      {children}
    </div>
  )
}

export default MobileWrapper
