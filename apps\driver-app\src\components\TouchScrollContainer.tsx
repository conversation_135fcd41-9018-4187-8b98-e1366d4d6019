import React, { ReactNode } from 'react'

interface TouchScrollContainerProps {
  children: ReactNode
  enableVerticalScroll?: boolean
  enableHorizontalScroll?: boolean
  bounceStiffness?: number
  bounceDamping?: number
  className?: string
}

export const TouchScrollContainer: React.FC<TouchScrollContainerProps> = ({
  children,
  enableVerticalScroll = true,
  enableHorizontalScroll = false,
  className = ''
}) => {
  return (
    <div 
      className={`w-full h-full ${enableVerticalScroll ? 'overflow-y-auto' : 'overflow-y-hidden'} ${enableHorizontalScroll ? 'overflow-x-auto' : 'overflow-x-hidden'} ${className}`}
      style={{
        WebkitOverflowScrolling: 'touch',
        scrollBehavior: 'smooth'
      }}
    >
      {children}
    </div>
  )
}
