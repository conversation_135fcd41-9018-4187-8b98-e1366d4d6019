// =====================================================
// MONITOR DE CONSOLE DO CHROME COM DEBUGGING PROTOCOL
// Conecta ao Chrome existente e monitora console em tempo real
// =====================================================

import puppeteer from 'puppeteer'
import { WebSocket } from 'ws'

class ChromeConsoleMonitor {
  constructor() {
    this.browser = null
    this.page = null
    this.isMonitoring = false
    this.logs = []
    this.errors = []
    this.warnings = []
    this.networkRequests = []
    this.startTime = Date.now()
  }

  async connectToExistingChrome() {
    console.log('🔍 Procurando instância do Chrome existente...')
    
    try {
      // Tentar conectar ao Chrome existente na porta padrão de debugging
      const browserURL = 'http://localhost:9222'
      
      this.browser = await puppeteer.connect({
        browserURL: browserURL,
        defaultViewport: null
      })
      
      console.log('✅ Conectado ao Chrome existente!')
      return true
    } catch (error) {
      console.log('⚠️ Não foi possível conectar ao Chrome existente')
      console.log('💡 Iniciando nova instância com debugging habilitado...')
      return false
    }
  }

  async startChromeWithDebugging() {
    console.log('🚀 Iniciando Chrome com debugging habilitado...')
    
    this.browser = await puppeteer.launch({
      headless: false,
      devtools: false,
      args: [
        '--remote-debugging-port=9222',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    })
    
    console.log('✅ Chrome iniciado com debugging na porta 9222')
  }

  async init() {
    console.log('🎯 INICIANDO CHROME CONSOLE MONITOR')
    console.log('=' .repeat(50))
    
    // Tentar conectar ao Chrome existente primeiro
    const connected = await this.connectToExistingChrome()
    
    if (!connected) {
      await this.startChromeWithDebugging()
    }

    // Obter todas as páginas abertas
    const pages = await this.browser.pages()
    
    if (pages.length === 0) {
      console.log('📄 Criando nova página...')
      this.page = await this.browser.newPage()
    } else {
      console.log(`📄 Encontradas ${pages.length} páginas abertas`)
      // Usar a primeira página ou a que contém localhost
      this.page = pages.find(page => 
        page.url().includes('localhost') || 
        page.url().includes('127.0.0.1')
      ) || pages[0]
      
      console.log(`📍 Usando página: ${this.page.url()}`)
    }

    await this.setupMonitoring()
    console.log('✅ Monitor configurado e ativo!')
  }

  async setupMonitoring() {
    console.log('🔧 Configurando monitoramento do console...')
    
    // Capturar logs do console
    this.page.on('console', (msg) => {
      const logEntry = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString(),
        timeFromStart: Date.now() - this.startTime,
        location: msg.location(),
        args: msg.args()
      }

      this.logs.push(logEntry)

      // Exibir em tempo real com cores
      const timeStr = `[${Math.floor(logEntry.timeFromStart / 1000)}s]`
      
      switch (msg.type()) {
        case 'error':
          this.errors.push(logEntry)
          console.log(`${timeStr} ❌ ERROR: ${msg.text()}`)
          break
        case 'warning':
          this.warnings.push(logEntry)
          console.log(`${timeStr} ⚠️ WARNING: ${msg.text()}`)
          break
        case 'log':
          console.log(`${timeStr} 📝 LOG: ${msg.text()}`)
          break
        case 'info':
          console.log(`${timeStr} ℹ️ INFO: ${msg.text()}`)
          break
        case 'debug':
          console.log(`${timeStr} 🐛 DEBUG: ${msg.text()}`)
          break
        default:
          console.log(`${timeStr} 🔍 ${msg.type().toUpperCase()}: ${msg.text()}`)
      }
    })

    // Capturar erros de página
    this.page.on('pageerror', (error) => {
      const errorEntry = {
        type: 'pageerror',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        timeFromStart: Date.now() - this.startTime
      }
      
      this.errors.push(errorEntry)
      const timeStr = `[${Math.floor(errorEntry.timeFromStart / 1000)}s]`
      console.log(`${timeStr} 💥 PAGE ERROR: ${error.message}`)
      if (error.stack) {
        console.log(`${timeStr} 📚 STACK: ${error.stack.split('\n')[0]}`)
      }
    })

    // Capturar falhas de requisições
    this.page.on('requestfailed', (request) => {
      const failedRequest = {
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText,
        timestamp: new Date().toISOString(),
        timeFromStart: Date.now() - this.startTime
      }
      
      this.networkRequests.push(failedRequest)
      const timeStr = `[${Math.floor(failedRequest.timeFromStart / 1000)}s]`
      console.log(`${timeStr} 🌐 REQUEST FAILED: ${request.method()} ${request.url()}`)
      console.log(`${timeStr} 📋 Reason: ${request.failure()?.errorText}`)
    })

    // Capturar respostas com erro
    this.page.on('response', (response) => {
      if (response.status() >= 400) {
        const errorResponse = {
          url: response.url(),
          status: response.status(),
          statusText: response.statusText(),
          timestamp: new Date().toISOString(),
          timeFromStart: Date.now() - this.startTime
        }
        
        this.networkRequests.push(errorResponse)
        const timeStr = `[${Math.floor(errorResponse.timeFromStart / 1000)}s]`
        console.log(`${timeStr} 🔴 HTTP ${response.status()}: ${response.url()}`)
      }
    })

    this.isMonitoring = true
  }

  async navigateToUrl(url) {
    if (!this.page) {
      throw new Error('Monitor não foi inicializado')
    }

    console.log(`\n🌐 Navegando para: ${url}`)
    console.log('=' .repeat(60))
    
    try {
      await this.page.goto(url, { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })
      
      console.log('✅ Página carregada, monitorando console...')
      
      // Aguardar um pouco para capturar logs iniciais
      await this.page.waitForTimeout(2000)
      
    } catch (error) {
      console.error(`💥 Erro ao navegar: ${error.message}`)
    }
  }

  startContinuousMonitoring(intervalSeconds = 10) {
    console.log(`\n⏰ Iniciando monitoramento contínuo (relatório a cada ${intervalSeconds}s)`)
    console.log('=' .repeat(60))
    
    setInterval(() => {
      this.printSummary()
    }, intervalSeconds * 1000)
  }

  printSummary() {
    const runtime = Math.floor((Date.now() - this.startTime) / 1000)
    
    console.log(`\n📊 RESUMO [${runtime}s de monitoramento]`)
    console.log('-' .repeat(40))
    console.log(`📝 Total de logs: ${this.logs.length}`)
    console.log(`❌ Erros: ${this.errors.length}`)
    console.log(`⚠️ Warnings: ${this.warnings.length}`)
    console.log(`🌐 Problemas de rede: ${this.networkRequests.length}`)
    
    // Mostrar últimos erros se houver
    if (this.errors.length > 0) {
      const recentErrors = this.errors.slice(-3)
      console.log('\n🔥 Últimos erros:')
      recentErrors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error.text || error.message}`)
      })
    }
  }

  async close() {
    if (this.browser) {
      console.log('\n🔒 Fechando monitor...')
      await this.browser.disconnect()
      console.log('✅ Monitor fechado')
    }
  }
}

// Função principal para iniciar o monitor
async function startMonitoring(url = null, continuous = true) {
  const monitor = new ChromeConsoleMonitor()
  
  try {
    await monitor.init()
    
    if (url) {
      await monitor.navigateToUrl(url)
    }
    
    if (continuous) {
      monitor.startContinuousMonitoring(10)
      
      // Manter o processo rodando
      console.log('\n⌨️ Pressione Ctrl+C para parar o monitoramento')
      
      process.on('SIGINT', async () => {
        console.log('\n\n🛑 Parando monitoramento...')
        await monitor.close()
        process.exit(0)
      })
      
      // Manter vivo
      setInterval(() => {}, 1000)
    }
    
    return monitor
    
  } catch (error) {
    console.error('💥 Erro ao iniciar monitor:', error)
    await monitor.close()
    throw error
  }
}

// Exportar para uso
export { ChromeConsoleMonitor, startMonitoring }

// Se executado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const url = process.argv[2] || null
  
  console.log('🎯 CHROME CONSOLE MONITOR')
  console.log('=' .repeat(30))
  
  if (url) {
    console.log(`🌐 URL alvo: ${url}`)
  } else {
    console.log('🌐 Monitorando página atual do Chrome')
  }
  
  startMonitoring(url, true)
    .catch((error) => {
      console.error('💥 Erro fatal:', error)
      process.exit(1)
    })
}
