import { supabase } from '../lib/supabase'

export interface RideRating {
  id: string
  ride_id: string
  passenger_id: string
  driver_id: string
  overall_rating: number
  categories: {
    punctuality: number
    vehicle_condition: number
    driving_quality: number
    communication: number
    route_efficiency: number
  }
  feedback: string
  tags: string[]
  would_recommend: boolean
  created_at: string
}

export interface DriverRating {
  id: string
  driver_id: string
  overall_rating: number
  total_rides: number
  rating_breakdown: {
    5: number
    4: number
    3: number
    2: number
    1: number
  }
  category_averages: {
    punctuality: number
    vehicle_condition: number
    driving_quality: number
    communication: number
    route_efficiency: number
  }
  recent_feedback: string[]
  common_tags: { tag: string; count: number }[]
  recommendation_rate: number
  updated_at: string
}

export interface RatingAnalytics {
  period: 'week' | 'month' | 'quarter' | 'year'
  average_rating: number
  total_ratings: number
  rating_trend: { date: string; rating: number }[]
  improvement_areas: string[]
  top_compliments: string[]
  satisfaction_score: number
}

class RatingService {
  /**
   * Submit a ride rating
   */
  async submitRating(rating: Omit<RideRating, 'id' | 'created_at'>): Promise<RideRating> {
    console.log('⭐ Submitting ride rating:', rating.overall_rating)

    try {
      // Validate rating data
      this.validateRating(rating)

      // Insert rating
      const { data, error } = await supabase
        .from('ride_ratings')
        .insert({
          ...rating,
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        console.error('❌ Failed to submit rating:', error)
        throw error
      }

      // Update driver's overall rating
      await this.updateDriverRating(rating.driver_id)

      // Update ride status
      await supabase
        .from('ride_requests')
        .update({ 
          status: 'rated',
          passenger_rating: rating.overall_rating,
          passenger_feedback: rating.feedback
        })
        .eq('id', rating.ride_id)

      console.log('✅ Rating submitted successfully')
      return data as RideRating

    } catch (error) {
      console.error('❌ Error submitting rating:', error)
      throw error
    }
  }

  /**
   * Get driver's current rating
   */
  async getDriverRating(driverId: string): Promise<DriverRating | null> {
    try {
      const { data, error } = await supabase
        .from('driver_ratings')
        .select('*')
        .eq('driver_id', driverId)
        .single()

      if (error && error.code !== 'PGRST116') { // Not found is OK
        console.error('❌ Failed to get driver rating:', error)
        throw error
      }

      return data as DriverRating || null
    } catch (error) {
      console.error('❌ Error getting driver rating:', error)
      return null
    }
  }

  /**
   * Update driver's overall rating
   */
  private async updateDriverRating(driverId: string): Promise<void> {
    try {
      // Get all ratings for this driver
      const { data: ratings, error: ratingsError } = await supabase
        .from('ride_ratings')
        .select('*')
        .eq('driver_id', driverId)
        .order('created_at', { ascending: false })

      if (ratingsError) {
        throw ratingsError
      }

      if (!ratings || ratings.length === 0) {
        return
      }

      // Calculate overall statistics
      const totalRides = ratings.length
      const overallRating = ratings.reduce((sum, r) => sum + r.overall_rating, 0) / totalRides

      // Calculate rating breakdown
      const ratingBreakdown = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
      ratings.forEach(r => {
        const rounded = Math.round(r.overall_rating)
        ratingBreakdown[rounded as keyof typeof ratingBreakdown]++
      })

      // Calculate category averages
      const categoryAverages = {
        punctuality: this.calculateCategoryAverage(ratings, 'punctuality'),
        vehicle_condition: this.calculateCategoryAverage(ratings, 'vehicle_condition'),
        driving_quality: this.calculateCategoryAverage(ratings, 'driving_quality'),
        communication: this.calculateCategoryAverage(ratings, 'communication'),
        route_efficiency: this.calculateCategoryAverage(ratings, 'route_efficiency')
      }

      // Get recent feedback (last 10 ratings)
      const recentFeedback = ratings
        .slice(0, 10)
        .map(r => r.feedback)
        .filter(f => f && f.trim().length > 0)

      // Calculate common tags
      const allTags = ratings.flatMap(r => r.tags || [])
      const tagCounts = allTags.reduce((acc, tag) => {
        acc[tag] = (acc[tag] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const commonTags = Object.entries(tagCounts)
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)

      // Calculate recommendation rate
      const recommendationRate = ratings.filter(r => r.would_recommend).length / totalRides

      // Upsert driver rating
      const driverRating: Omit<DriverRating, 'id'> = {
        driver_id: driverId,
        overall_rating: overallRating,
        total_rides: totalRides,
        rating_breakdown: ratingBreakdown,
        category_averages: categoryAverages,
        recent_feedback: recentFeedback,
        common_tags: commonTags,
        recommendation_rate: recommendationRate,
        updated_at: new Date().toISOString()
      }

      const { error: upsertError } = await supabase
        .from('driver_ratings')
        .upsert(driverRating, {
          onConflict: 'driver_id'
        })

      if (upsertError) {
        throw upsertError
      }

      console.log(`✅ Updated driver rating: ${overallRating.toFixed(2)} (${totalRides} rides)`)

    } catch (error) {
      console.error('❌ Error updating driver rating:', error)
      throw error
    }
  }

  /**
   * Calculate category average
   */
  private calculateCategoryAverage(ratings: any[], category: string): number {
    const validRatings = ratings
      .map(r => r.categories?.[category])
      .filter(rating => rating !== undefined && rating !== null)

    if (validRatings.length === 0) return 0

    return validRatings.reduce((sum, rating) => sum + rating, 0) / validRatings.length
  }

  /**
   * Get rating analytics for a driver
   */
  async getDriverAnalytics(driverId: string, period: RatingAnalytics['period'] = 'month'): Promise<RatingAnalytics> {
    try {
      const periodDays = {
        week: 7,
        month: 30,
        quarter: 90,
        year: 365
      }

      const startDate = new Date()
      startDate.setDate(startDate.getDate() - periodDays[period])

      // Get ratings for the period
      const { data: ratings, error } = await supabase
        .from('ride_ratings')
        .select('*')
        .eq('driver_id', driverId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true })

      if (error) {
        throw error
      }

      if (!ratings || ratings.length === 0) {
        return {
          period,
          average_rating: 0,
          total_ratings: 0,
          rating_trend: [],
          improvement_areas: [],
          top_compliments: [],
          satisfaction_score: 0
        }
      }

      // Calculate average rating
      const averageRating = ratings.reduce((sum, r) => sum + r.overall_rating, 0) / ratings.length

      // Calculate rating trend (daily averages)
      const ratingTrend = this.calculateRatingTrend(ratings, period)

      // Identify improvement areas
      const improvementAreas = this.identifyImprovementAreas(ratings)

      // Get top compliments
      const topCompliments = this.getTopCompliments(ratings)

      // Calculate satisfaction score (percentage of 4+ star ratings)
      const satisfactionScore = ratings.filter(r => r.overall_rating >= 4).length / ratings.length

      return {
        period,
        average_rating: averageRating,
        total_ratings: ratings.length,
        rating_trend: ratingTrend,
        improvement_areas: improvementAreas,
        top_compliments: topCompliments,
        satisfaction_score: satisfactionScore
      }

    } catch (error) {
      console.error('❌ Error getting driver analytics:', error)
      throw error
    }
  }

  /**
   * Calculate rating trend over time
   */
  private calculateRatingTrend(ratings: any[], period: RatingAnalytics['period']): { date: string; rating: number }[] {
    const groupBy = period === 'week' ? 'day' : period === 'month' ? 'day' : 'week'
    const groups = new Map<string, number[]>()

    ratings.forEach(rating => {
      const date = new Date(rating.created_at)
      let key: string

      if (groupBy === 'day') {
        key = date.toISOString().split('T')[0]
      } else {
        // Week grouping
        const weekStart = new Date(date)
        weekStart.setDate(date.getDate() - date.getDay())
        key = weekStart.toISOString().split('T')[0]
      }

      if (!groups.has(key)) {
        groups.set(key, [])
      }
      groups.get(key)!.push(rating.overall_rating)
    })

    return Array.from(groups.entries())
      .map(([date, ratings]) => ({
        date,
        rating: ratings.reduce((sum, r) => sum + r, 0) / ratings.length
      }))
      .sort((a, b) => a.date.localeCompare(b.date))
  }

  /**
   * Identify areas for improvement
   */
  private identifyImprovementAreas(ratings: any[]): string[] {
    const categories = ['punctuality', 'vehicle_condition', 'driving_quality', 'communication', 'route_efficiency']
    const categoryAverages = categories.map(category => ({
      category,
      average: this.calculateCategoryAverage(ratings, category)
    }))

    // Find categories with ratings below 4.0
    const improvementAreas = categoryAverages
      .filter(cat => cat.average < 4.0 && cat.average > 0)
      .sort((a, b) => a.average - b.average)
      .map(cat => {
        switch (cat.category) {
          case 'punctuality': return 'Pontualidade'
          case 'vehicle_condition': return 'Condição do veículo'
          case 'driving_quality': return 'Qualidade da direção'
          case 'communication': return 'Comunicação'
          case 'route_efficiency': return 'Eficiência da rota'
          default: return cat.category
        }
      })

    return improvementAreas.slice(0, 3) // Top 3 areas
  }

  /**
   * Get top compliments from tags
   */
  private getTopCompliments(ratings: any[]): string[] {
    const allTags = ratings.flatMap(r => r.tags || [])
    const positiveTagsMap = new Map<string, number>()

    // Define positive tags
    const positiveTags = [
      'pontual', 'educado', 'carro limpo', 'direção segura', 'rota eficiente',
      'comunicativo', 'prestativo', 'profissional', 'carro confortável', 'música boa'
    ]

    allTags.forEach(tag => {
      const lowerTag = tag.toLowerCase()
      if (positiveTags.some(positive => lowerTag.includes(positive))) {
        positiveTagsMap.set(tag, (positiveTagsMap.get(tag) || 0) + 1)
      }
    })

    return Array.from(positiveTagsMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([tag]) => tag)
  }

  /**
   * Validate rating data
   */
  private validateRating(rating: Omit<RideRating, 'id' | 'created_at'>): void {
    if (rating.overall_rating < 1 || rating.overall_rating > 5) {
      throw new Error('Overall rating must be between 1 and 5')
    }

    if (rating.categories) {
      Object.values(rating.categories).forEach(categoryRating => {
        if (categoryRating < 1 || categoryRating > 5) {
          throw new Error('Category ratings must be between 1 and 5')
        }
      })
    }

    if (rating.feedback && rating.feedback.length > 1000) {
      throw new Error('Feedback must be less than 1000 characters')
    }

    if (rating.tags && rating.tags.length > 10) {
      throw new Error('Maximum 10 tags allowed')
    }
  }

  /**
   * Get passenger's rating history
   */
  async getPassengerRatingHistory(passengerId: string, limit: number = 20): Promise<RideRating[]> {
    try {
      const { data, error } = await supabase
        .from('ride_ratings')
        .select(`
          *,
          ride_requests!inner(
            pickup_address,
            destination_address,
            completed_at
          ),
          profiles!driver_id(
            full_name,
            avatar_url
          )
        `)
        .eq('passenger_id', passengerId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        throw error
      }

      return data as RideRating[]
    } catch (error) {
      console.error('❌ Error getting passenger rating history:', error)
      throw error
    }
  }

  /**
   * Get suggested tags for rating
   */
  getSuggestedTags(rating: number): string[] {
    if (rating >= 4.5) {
      return [
        'Excelente motorista', 'Muito pontual', 'Carro impecável', 
        'Direção suave', 'Super educado', 'Recomendo'
      ]
    } else if (rating >= 3.5) {
      return [
        'Bom motorista', 'Pontual', 'Carro limpo', 
        'Direção segura', 'Educado', 'Profissional'
      ]
    } else if (rating >= 2.5) {
      return [
        'Razoável', 'Pode melhorar', 'Carro ok', 
        'Direção normal', 'Comunicação básica'
      ]
    } else {
      return [
        'Atrasado', 'Carro sujo', 'Direção ruim', 
        'Pouco educado', 'Rota ineficiente', 'Não recomendo'
      ]
    }
  }

  /**
   * Generate rating summary for display
   */
  generateRatingSummary(driverRating: DriverRating): string {
    const rating = driverRating.overall_rating
    const rides = driverRating.total_rides

    if (rating >= 4.8) {
      return `⭐ Motorista excepcional (${rating.toFixed(1)}/5.0 • ${rides} corridas)`
    } else if (rating >= 4.5) {
      return `⭐ Motorista excelente (${rating.toFixed(1)}/5.0 • ${rides} corridas)`
    } else if (rating >= 4.0) {
      return `⭐ Bom motorista (${rating.toFixed(1)}/5.0 • ${rides} corridas)`
    } else if (rating >= 3.5) {
      return `⭐ Motorista regular (${rating.toFixed(1)}/5.0 • ${rides} corridas)`
    } else {
      return `⭐ Motorista em treinamento (${rating.toFixed(1)}/5.0 • ${rides} corridas)`
    }
  }
}

export const ratingService = new RatingService()
export default ratingService
