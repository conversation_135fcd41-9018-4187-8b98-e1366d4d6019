import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';

// 📱 TOUCH SCROLL CONTAINER - VERSÃO SIMPLIFICADA
// Simula interações touch móveis para desktop usando Framer Motion

interface TouchScrollContainerProps {
  children: ReactNode;
  className?: string;
  enableVerticalScroll?: boolean;
  enableHorizontalScroll?: boolean;
}

export const TouchScrollContainer: React.FC<TouchScrollContainerProps> = ({
  children,
  className = '',
  enableVerticalScroll = true,
  enableHorizontalScroll = false
}) => {
  return (
    <div
      className={`touch-scroll-container ${className}`}
      style={{
        overflow: 'hidden',
        position: 'relative',
        height: '100%',
        width: '100%'
      }}
    >
      <motion.div
        drag={enableVerticalScroll && enableHorizontalScroll ? true :
              enableVerticalScroll ? "y" :
              enableHorizontalScroll ? "x" : false}
        dragElastic={0.2}
        dragMomentum={true}
        dragTransition={{
          bounceStiffness: 300,
          bounceDamping: 40,
          power: 0.3,
          timeConstant: 750
        }}
        style={{
          width: '100%',
          minHeight: '100%',
          cursor: 'grab'
        }}
        whileDrag={{
          cursor: 'grabbing',
          scale: 0.99
        }}
        className="touch-scroll-content"
      >
        {children}
      </motion.div>
    </div>
  );
};

export default TouchScrollContainer;
