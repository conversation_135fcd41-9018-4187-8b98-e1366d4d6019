// =====================================================
// SOLICITAÇÃO DE CORRIDA AUTOMATIZADA
// Destino: Rua Boa Vista, Ibotirama
// Veículo: Carro normal | Pagamento: Dinheiro
// =====================================================

import puppeteer from 'puppeteer'

class RideRequestAutomation {
  constructor() {
    this.browser = null
    this.page = null
    this.rideDetails = {
      destination: 'Rua Boa Vista, Ibotirama',
      vehicleType: 'normal',
      paymentMethod: 'dinheiro',
      status: 'pending'
    }
  }

  async init() {
    console.log('🚗 INICIANDO SOLICITAÇÃO DE CORRIDA AUTOMATIZADA')
    console.log('=' .repeat(60))
    console.log('📍 Destino: Rua Boa Vista, Ibotirama')
    console.log('🚗 Veículo: Carro normal')
    console.log('💰 Pagamento: Dinheiro')
    console.log('=' .repeat(60))
    
    this.browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--start-maximized',
        '--disable-dev-shm-usage'
      ]
    })
    
    this.page = await this.browser.newPage()
    await this.page.setViewport({ width: 1280, height: 720 })
    
    this.setupMonitoring()
    console.log('✅ Sistema iniciado')
  }

  setupMonitoring() {
    this.page.on('console', (msg) => {
      const time = new Date().toLocaleTimeString()
      
      if (msg.type() === 'error') {
        console.log(`[${time}] ❌ ${msg.text()}`)
      } else if (msg.text().includes('✅') || msg.text().includes('SUCCESS')) {
        console.log(`[${time}] 🟢 ${msg.text()}`)
      } else if (msg.text().includes('🚗') || msg.text().includes('💰') || msg.text().includes('📍')) {
        console.log(`[${time}] 🎯 ${msg.text()}`)
      } else if (msg.text().includes('❌') || msg.text().includes('ERROR')) {
        console.log(`[${time}] 🔴 ${msg.text()}`)
      }
    })

    this.page.on('pageerror', (error) => {
      console.log(`💥 PAGE ERROR: ${error.message}`)
    })
  }

  async performLogin() {
    console.log('\n🔐 ETAPA 1: FAZENDO LOGIN')
    console.log('-' .repeat(40))
    
    try {
      await this.page.goto('http://localhost:3000/login', { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })
      
      console.log('✅ Página de login carregada')
      await new Promise(resolve => setTimeout(resolve, 3000))

      const emailField = await this.page.$('input[type="email"]')
      const passwordField = await this.page.$('input[type="password"]')

      if (emailField && passwordField) {
        console.log('📝 Preenchendo credenciais...')
        
        await emailField.click({ clickCount: 3 })
        await emailField.type('<EMAIL>', { delay: 50 })
        
        await passwordField.click({ clickCount: 3 })
        await passwordField.type('test123456', { delay: 50 })
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const submitButton = await this.page.$('button[type="submit"]')
        if (submitButton) {
          console.log('🖱️ Fazendo login...')
          await submitButton.click()
          
          await new Promise(resolve => setTimeout(resolve, 5000))
          
          const currentUrl = this.page.url()
          if (!currentUrl.includes('/login')) {
            console.log('✅ Login realizado com sucesso!')
            console.log(`🌐 Redirecionado para: ${currentUrl}`)
            return true
          } else {
            console.log('❌ Login falhou')
            return false
          }
        }
      }
      
      return false
    } catch (error) {
      console.error('❌ Erro durante login:', error.message)
      return false
    }
  }

  async navigateToMapSelection() {
    console.log('\n🗺️ ETAPA 2: SELEÇÃO DE DESTINO NO MAPA')
    console.log('-' .repeat(40))
    
    try {
      console.log('🌐 Navegando para seleção de mapa...')
      await this.page.goto('http://localhost:3000/ride-request/map', { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })
      
      console.log('✅ Página de mapa carregada')
      await new Promise(resolve => setTimeout(resolve, 8000))
      
      // Verificar se o mapa carregou
      const mapContainer = await this.page.$('.mapboxgl-map, #map, [class*="map"]')
      if (mapContainer) {
        console.log('✅ Mapa detectado na página')
      } else {
        console.log('⚠️ Mapa não detectado, tentando localizar...')
      }

      // Procurar campo de busca para inserir o destino
      console.log('🔍 Procurando campo de busca...')
      
      const searchSelectors = [
        'input[placeholder*="destino" i]',
        'input[placeholder*="onde" i]',
        'input[placeholder*="endereço" i]',
        'input[type="search"]',
        'input[class*="search"]',
        '.search-input',
        '#destination-input'
      ]
      
      let searchField = null
      for (const selector of searchSelectors) {
        searchField = await this.page.$(selector)
        if (searchField) {
          console.log(`✅ Campo de busca encontrado: ${selector}`)
          break
        }
      }
      
      if (searchField) {
        console.log('📝 Inserindo destino: Rua Boa Vista, Ibotirama')
        await searchField.click()
        await searchField.type('Rua Boa Vista, Ibotirama', { delay: 100 })
        
        // Aguardar sugestões aparecerem
        await new Promise(resolve => setTimeout(resolve, 3000))
        
        // Tentar selecionar primeira sugestão ou pressionar Enter
        const suggestions = await this.page.$$('.suggestion, .autocomplete-item, [class*="suggestion"]')
        if (suggestions.length > 0) {
          console.log(`🎯 Selecionando primeira sugestão (${suggestions.length} encontradas)`)
          await suggestions[0].click()
        } else {
          console.log('⌨️ Pressionando Enter para confirmar')
          await searchField.press('Enter')
        }
        
        await new Promise(resolve => setTimeout(resolve, 2000))
        
      } else {
        console.log('⚠️ Campo de busca não encontrado, tentando clicar no mapa...')
        
        // Tentar clicar em um ponto do mapa
        if (mapContainer) {
          console.log('🖱️ Clicando no centro do mapa...')
          const mapBox = await mapContainer.boundingBox()
          if (mapBox) {
            await this.page.click(mapBox.x + mapBox.width / 2, mapBox.y + mapBox.height / 2)
            await new Promise(resolve => setTimeout(resolve, 2000))
          }
        }
      }
      
      // Procurar botão de confirmação
      console.log('🔍 Procurando botão de confirmação...')
      
      const confirmSelectors = [
        'button:contains("Confirmar")',
        'button:contains("Continuar")',
        'button:contains("Próximo")',
        'button[type="submit"]',
        '.confirm-button',
        '.next-button'
      ]
      
      let confirmButton = null
      
      // Tentar XPath para botões com texto
      const confirmButtons = await this.page.$x('//button[contains(text(), "Confirmar") or contains(text(), "Continuar") or contains(text(), "Próximo")]')
      if (confirmButtons.length > 0) {
        confirmButton = confirmButtons[0]
        console.log('✅ Botão de confirmação encontrado via XPath')
      } else {
        // Tentar seletores CSS
        for (const selector of confirmSelectors) {
          if (!selector.includes(':contains')) {
            confirmButton = await this.page.$(selector)
            if (confirmButton) {
              console.log(`✅ Botão encontrado: ${selector}`)
              break
            }
          }
        }
      }
      
      if (confirmButton) {
        console.log('🖱️ Confirmando destino...')
        await confirmButton.click()
        await new Promise(resolve => setTimeout(resolve, 3000))
        console.log('✅ Destino confirmado')
        return true
      } else {
        console.log('⚠️ Botão de confirmação não encontrado, continuando...')
        return true // Continuar mesmo sem confirmação explícita
      }
      
    } catch (error) {
      console.error('❌ Erro na seleção do mapa:', error.message)
      return false
    }
  }

  async selectVehicleAndPayment() {
    console.log('\n🚗 ETAPA 3: SELEÇÃO DE VEÍCULO E PAGAMENTO')
    console.log('-' .repeat(40))
    
    try {
      console.log('🌐 Navegando para detalhes da viagem...')
      await this.page.goto('http://localhost:3000/ride-request/details', { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })
      
      console.log('✅ Página de detalhes carregada')
      await new Promise(resolve => setTimeout(resolve, 10000))
      
      // Selecionar veículo normal
      console.log('🚗 Procurando opção de carro normal...')
      
      const vehicleSelectors = [
        'button:contains("Normal")',
        'button:contains("Carro")',
        '.vehicle-normal',
        '.car-normal',
        '[data-vehicle="normal"]'
      ]
      
      let vehicleButton = null
      
      // Tentar XPath primeiro
      const vehicleButtons = await this.page.$x('//button[contains(text(), "Normal") or contains(text(), "Carro") or contains(text(), "Econômico")]')
      if (vehicleButtons.length > 0) {
        vehicleButton = vehicleButtons[0]
        console.log('✅ Veículo normal encontrado via XPath')
      } else {
        // Tentar seletores CSS
        for (const selector of vehicleSelectors) {
          if (!selector.includes(':contains')) {
            vehicleButton = await this.page.$(selector)
            if (vehicleButton) {
              console.log(`✅ Veículo encontrado: ${selector}`)
              break
            }
          }
        }
      }
      
      if (vehicleButton) {
        console.log('🖱️ Selecionando carro normal...')
        await vehicleButton.click()
        await new Promise(resolve => setTimeout(resolve, 2000))
        console.log('✅ Carro normal selecionado')
      } else {
        console.log('⚠️ Opção de carro normal não encontrada, usando padrão')
      }
      
      // Selecionar pagamento em dinheiro
      console.log('💰 Procurando opção de pagamento em dinheiro...')
      
      const paymentSelectors = [
        'button:contains("Dinheiro")',
        'button:contains("Cash")',
        '.payment-cash',
        '.payment-money',
        '[data-payment="cash"]'
      ]
      
      let paymentButton = null
      
      // Tentar XPath primeiro
      const paymentButtons = await this.page.$x('//button[contains(text(), "Dinheiro") or contains(text(), "Cash") or contains(text(), "Espécie")]')
      if (paymentButtons.length > 0) {
        paymentButton = paymentButtons[0]
        console.log('✅ Pagamento em dinheiro encontrado via XPath')
      } else {
        // Tentar seletores CSS
        for (const selector of paymentSelectors) {
          if (!selector.includes(':contains')) {
            paymentButton = await this.page.$(selector)
            if (paymentButton) {
              console.log(`✅ Pagamento encontrado: ${selector}`)
              break
            }
          }
        }
      }
      
      if (paymentButton) {
        console.log('🖱️ Selecionando pagamento em dinheiro...')
        await paymentButton.click()
        await new Promise(resolve => setTimeout(resolve, 2000))
        console.log('✅ Pagamento em dinheiro selecionado')
      } else {
        console.log('⚠️ Opção de dinheiro não encontrada, usando padrão')
      }
      
      return true
      
    } catch (error) {
      console.error('❌ Erro na seleção de veículo/pagamento:', error.message)
      return false
    }
  }

  async requestRide() {
    console.log('\n🚀 ETAPA 4: SOLICITANDO CORRIDA')
    console.log('-' .repeat(40))
    
    try {
      // Procurar botão de solicitar corrida
      console.log('🔍 Procurando botão de solicitar corrida...')
      
      const requestSelectors = [
        'button:contains("Solicitar")',
        'button:contains("Confirmar")',
        'button:contains("Pedir")',
        '.request-ride',
        '.confirm-ride',
        '[data-action="request"]'
      ]
      
      let requestButton = null
      
      // Tentar XPath primeiro
      const requestButtons = await this.page.$x('//button[contains(text(), "Solicitar") or contains(text(), "Confirmar") or contains(text(), "Pedir")]')
      if (requestButtons.length > 0) {
        requestButton = requestButtons[0]
        console.log('✅ Botão de solicitação encontrado via XPath')
      } else {
        // Tentar seletores CSS
        for (const selector of requestSelectors) {
          if (!selector.includes(':contains')) {
            requestButton = await this.page.$(selector)
            if (requestButton) {
              console.log(`✅ Botão encontrado: ${selector}`)
              break
            }
          }
        }
      }
      
      if (requestButton) {
        console.log('🚗 SOLICITANDO CORRIDA...')
        console.log('📍 Destino: Rua Boa Vista, Ibotirama')
        console.log('🚗 Veículo: Carro normal')
        console.log('💰 Pagamento: Dinheiro')
        
        await requestButton.click()
        await new Promise(resolve => setTimeout(resolve, 8000))
        
        console.log('✅ CORRIDA SOLICITADA COM SUCESSO!')
        this.rideDetails.status = 'requested'
        
        // Verificar se foi redirecionado para página de acompanhamento
        const currentUrl = this.page.url()
        console.log(`🌐 URL atual: ${currentUrl}`)
        
        if (currentUrl.includes('tracking') || currentUrl.includes('ride') || currentUrl.includes('trip')) {
          console.log('✅ Redirecionado para acompanhamento da corrida')
        }
        
        return true
        
      } else {
        console.log('❌ Botão de solicitação não encontrado')
        
        // Listar todos os botões disponíveis para debug
        const allButtons = await this.page.$$('button')
        console.log(`🔍 Encontrados ${allButtons.length} botões na página`)
        
        for (let i = 0; i < Math.min(allButtons.length, 5); i++) {
          const buttonText = await allButtons[i].evaluate(el => el.textContent?.trim())
          console.log(`  Botão ${i + 1}: "${buttonText}"`)
        }
        
        return false
      }
      
    } catch (error) {
      console.error('❌ Erro ao solicitar corrida:', error.message)
      return false
    }
  }

  async generateRideReport() {
    console.log('\n' + '=' .repeat(60))
    console.log('📊 RELATÓRIO DA SOLICITAÇÃO DE CORRIDA')
    console.log('=' .repeat(60))
    
    console.log('📍 DETALHES DA CORRIDA:')
    console.log(`   Destino: ${this.rideDetails.destination}`)
    console.log(`   Veículo: ${this.rideDetails.vehicleType}`)
    console.log(`   Pagamento: ${this.rideDetails.paymentMethod}`)
    console.log(`   Status: ${this.rideDetails.status}`)
    
    if (this.rideDetails.status === 'requested') {
      console.log('\n🎉 CORRIDA SOLICITADA COM SUCESSO!')
      console.log('✅ Aguardando motorista aceitar a corrida')
      console.log('📱 Você pode acompanhar o status na aplicação')
    } else {
      console.log('\n⚠️ Houve problemas na solicitação')
      console.log('💡 Verifique os detalhes acima e tente novamente')
    }
    
    return this.rideDetails
  }

  async close() {
    if (this.browser) {
      console.log('\n🔒 Mantendo navegador aberto para acompanhamento...')
      console.log('💡 Feche manualmente quando terminar')
      // Não fechar o navegador para permitir acompanhamento
      // await this.browser.close()
    }
  }

  async runRideRequest() {
    try {
      await this.init()
      
      const loginSuccess = await this.performLogin()
      if (!loginSuccess) {
        console.log('❌ Falha no login, abortando solicitação')
        return
      }
      
      const mapSuccess = await this.navigateToMapSelection()
      if (!mapSuccess) {
        console.log('⚠️ Problemas na seleção do mapa, continuando...')
      }
      
      const selectionSuccess = await this.selectVehicleAndPayment()
      if (!selectionSuccess) {
        console.log('⚠️ Problemas na seleção, continuando...')
      }
      
      const requestSuccess = await this.requestRide()
      
      const report = await this.generateRideReport()
      
      return report
      
    } finally {
      await this.close()
    }
  }
}

// Executar solicitação de corrida
const rideRequest = new RideRequestAutomation()

rideRequest.runRideRequest()
  .then((report) => {
    console.log('\n🏁 PROCESSO DE SOLICITAÇÃO CONCLUÍDO!')
    process.exit(report?.status === 'requested' ? 0 : 1)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
