#!/usr/bin/env node

/**
 * Script para configurar domínios do MobiDrive no Vercel
 * Executa: node configure-domains.js
 */

const { execSync } = require('child_process');
const path = require('path');

// Configuração dos domínios
const DOMAIN_CONFIG = {
  'mobidrive-passenger': ['mobdrive.com.br', 'www.mobdrive.com.br'],
  'driver-app': ['driver.mobdrive.com.br'],
  'admin-app': ['admin.mobdrive.com.br']
};

console.log('🚀 Configurando domínios MobiDrive...\n');

// Função para executar comando Vercel
function runVercelCommand(command, cwd = process.cwd()) {
  try {
    console.log(`📋 Executando: ${command}`);
    const result = execSync(command, { 
      cwd, 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    console.log(`✅ Sucesso: ${result.trim()}\n`);
    return result;
  } catch (error) {
    console.log(`❌ Erro: ${error.message}\n`);
    return null;
  }
}

// Função para configurar domínios de um projeto
function configureDomains(projectName, domains, projectPath) {
  console.log(`🔧 Configurando projeto: ${projectName}`);
  console.log(`📁 Caminho: ${projectPath}`);
  
  domains.forEach(domain => {
    console.log(`🌐 Adicionando domínio: ${domain}`);
    runVercelCommand(`npx vercel domains add ${domain}`, projectPath);
  });
}

// Caminhos dos projetos
const APPS_DIR = path.join(__dirname, '..');
const PROJECT_PATHS = {
  'mobidrive-passenger': path.join(APPS_DIR, 'passenger-app'),
  'driver-app': path.join(APPS_DIR, 'driver-app'),
  'admin-app': path.join(APPS_DIR, 'admin-app')
};

// Executar configuração
async function main() {
  try {
    // Verificar autenticação
    console.log('🔐 Verificando autenticação Vercel...');
    runVercelCommand('npx vercel whoami');
    
    // Configurar cada projeto
    for (const [projectName, domains] of Object.entries(DOMAIN_CONFIG)) {
      const projectPath = PROJECT_PATHS[projectName];
      
      if (!projectPath) {
        console.log(`❌ Caminho não encontrado para: ${projectName}`);
        continue;
      }
      
      configureDomains(projectName, domains, projectPath);
    }
    
    console.log('🎊 Configuração concluída!');
    console.log('\n📊 Verificando status dos domínios...');
    
    // Verificar status final
    Object.keys(PROJECT_PATHS).forEach(projectName => {
      const projectPath = PROJECT_PATHS[projectName];
      console.log(`\n🔍 Domínios do ${projectName}:`);
      runVercelCommand('npx vercel domains ls', projectPath);
    });
    
  } catch (error) {
    console.error('💥 Erro na configuração:', error.message);
    process.exit(1);
  }
}

// Executar
main();
