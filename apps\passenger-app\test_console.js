// =====================================================
// TESTE RÁPIDO DO CONSOLE DO NAVEGADOR
// Script simples para testar páginas específicas
// =====================================================

import { testUrl, testMultipleUrls } from './browser_console_monitor.js'

async function testRideRequestApp() {
  console.log('🎯 TESTANDO APLICAÇÃO RIDE REQUEST')
  console.log('=' .repeat(50))

  const urlsToTest = [
    'http://localhost:3000',
    'http://localhost:3000/ride-request/map',
    'http://localhost:3000/ride-request/details'
  ]

  try {
    const reports = await testMultipleUrls(urlsToTest)
    
    console.log('\n📊 RESUMO GERAL:')
    console.log('=' .repeat(30))
    
    let totalErrors = 0
    let totalWarnings = 0
    let totalNetworkIssues = 0
    
    reports.forEach((report, index) => {
      console.log(`\n📄 Página ${index + 1}: ${urlsToTest[index]}`)
      console.log(`  ❌ Erros: ${report.summary.errors}`)
      console.log(`  ⚠️ Warnings: ${report.summary.warnings}`)
      console.log(`  🌐 Problemas de rede: ${report.summary.networkIssues}`)
      
      totalErrors += report.summary.errors
      totalWarnings += report.summary.warnings
      totalNetworkIssues += report.summary.networkIssues
    })
    
    console.log('\n🎯 TOTAL GERAL:')
    console.log(`❌ Total de erros: ${totalErrors}`)
    console.log(`⚠️ Total de warnings: ${totalWarnings}`)
    console.log(`🌐 Total de problemas de rede: ${totalNetworkIssues}`)
    
    if (totalErrors === 0) {
      console.log('\n🎉 SUCESSO! Nenhum erro encontrado!')
    } else {
      console.log('\n⚠️ Foram encontrados erros que precisam ser corrigidos.')
    }
    
    return {
      totalErrors,
      totalWarnings,
      totalNetworkIssues,
      reports
    }
    
  } catch (error) {
    console.error('💥 Erro no teste:', error)
    throw error
  }
}

// Executar teste
testRideRequestApp()
  .then((results) => {
    console.log('\n🏁 Teste do console concluído!')
    process.exit(results.totalErrors > 0 ? 1 : 0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
