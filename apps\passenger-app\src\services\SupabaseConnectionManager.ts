import { supabase } from '../lib/supabase'

/**
 * Gerenciador de conexões Supabase para prevenir vazamentos de memória
 * e stack overflow em subscriptions realtime
 */
class SupabaseConnectionManager {
  private activeChannels = new Map<string, any>()
  private reconnectAttempts = new Map<string, number>()
  private maxReconnectAttempts = 3
  private isConnected = false

  constructor() {
    this.setupConnectionMonitoring()
  }

  /**
   * Configurar monitoramento de conexão
   */
  private setupConnectionMonitoring() {
    // Monitor connection status
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        console.log('🌐 Conexão restaurada - reconectando Supabase...')
        this.reconnectAll()
      })

      window.addEventListener('offline', () => {
        console.log('🌐 Conexão perdida - pausando Supabase...')
        this.disconnectAll()
      })
    }
  }

  /**
   * Conectar ao realtime de forma segura
   */
  async connect(): Promise<boolean> {
    try {
      if (!this.isConnected) {
        await supabase.realtime.connect()
        this.isConnected = true
        console.log('✅ Supabase realtime conectado')
      }
      return true
    } catch (error) {
      console.error('❌ Erro ao conectar Supabase realtime:', error)
      return false
    }
  }

  /**
   * Desconectar do realtime
   */
  disconnect() {
    try {
      if (this.isConnected) {
        supabase.realtime.disconnect()
        this.isConnected = false
        console.log('🔌 Supabase realtime desconectado')
      }
    } catch (error) {
      console.error('❌ Erro ao desconectar Supabase realtime:', error)
    }
  }

  /**
   * Criar canal com cleanup automático
   */
  createChannel(channelName: string, config?: any): any {
    try {
      // Remove canal existente se houver
      this.removeChannel(channelName)

      // Criar novo canal
      const channel = supabase.channel(channelName, config)
      
      // Armazenar referência
      this.activeChannels.set(channelName, channel)
      this.reconnectAttempts.set(channelName, 0)

      console.log(`📡 Canal criado: ${channelName}`)
      return channel
    } catch (error) {
      console.error(`❌ Erro ao criar canal ${channelName}:`, error)
      return null
    }
  }

  /**
   * Remover canal com cleanup seguro
   */
  removeChannel(channelName: string) {
    try {
      const channel = this.activeChannels.get(channelName)
      if (channel) {
        channel.unsubscribe()
        this.activeChannels.delete(channelName)
        this.reconnectAttempts.delete(channelName)
        console.log(`🗑️ Canal removido: ${channelName}`)
      }
    } catch (error) {
      console.error(`❌ Erro ao remover canal ${channelName}:`, error)
      // Force cleanup
      this.activeChannels.delete(channelName)
      this.reconnectAttempts.delete(channelName)
    }
  }

  /**
   * Reconectar canal específico
   */
  async reconnectChannel(channelName: string): Promise<boolean> {
    const attempts = this.reconnectAttempts.get(channelName) || 0
    
    if (attempts >= this.maxReconnectAttempts) {
      console.warn(`⚠️ Máximo de tentativas atingido para canal: ${channelName}`)
      return false
    }

    try {
      this.reconnectAttempts.set(channelName, attempts + 1)
      
      // Aguardar antes de reconectar (backoff exponencial)
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempts)))
      
      const channel = this.activeChannels.get(channelName)
      if (channel) {
        await channel.subscribe()
        console.log(`🔄 Canal reconectado: ${channelName}`)
        this.reconnectAttempts.set(channelName, 0) // Reset counter on success
        return true
      }
    } catch (error) {
      console.error(`❌ Erro ao reconectar canal ${channelName}:`, error)
    }
    
    return false
  }

  /**
   * Reconectar todos os canais
   */
  async reconnectAll() {
    console.log('🔄 Reconectando todos os canais...')
    
    // Primeiro, reconectar o realtime
    await this.connect()
    
    // Depois, reconectar cada canal
    const channelNames = Array.from(this.activeChannels.keys())
    for (const channelName of channelNames) {
      await this.reconnectChannel(channelName)
    }
  }

  /**
   * Desconectar todos os canais
   */
  disconnectAll() {
    console.log('🔌 Desconectando todos os canais...')
    
    const channelNames = Array.from(this.activeChannels.keys())
    for (const channelName of channelNames) {
      this.removeChannel(channelName)
    }
    
    this.disconnect()
  }

  /**
   * Obter status da conexão
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      activeChannels: this.activeChannels.size,
      channelNames: Array.from(this.activeChannels.keys())
    }
  }

  /**
   * Cleanup completo - usar ao desmontar componentes
   */
  cleanup() {
    this.disconnectAll()
    this.activeChannels.clear()
    this.reconnectAttempts.clear()
    console.log('🧹 Cleanup completo do SupabaseConnectionManager')
  }
}

// Singleton instance
export const supabaseConnectionManager = new SupabaseConnectionManager()

// Export para uso em componentes
export default supabaseConnectionManager
