import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Trophy, 
  Star, 
  Gift, 
  Crown, 
  Zap, 
  Target,
  TrendingUp,
  Users,
  Award,
  Sparkles,
  ChevronRight,
  Coins
} from 'lucide-react'
import { rewardsService } from '../services/RewardsService'
import { useAuth } from '../contexts/AuthContextSimple'

interface RewardsCenterProps {
  isVisible: boolean
  onClose: () => void
}

const RewardsCenter: React.FC<RewardsCenterProps> = ({ isVisible, onClose }) => {
  const { user } = useAuth()
  const [userRewards, setUserRewards] = useState<any>(null)
  const [achievements, setAchievements] = useState<any[]>([])
  const [availableRewards, setAvailableRewards] = useState<any[]>([])
  const [leaderboard, setLeaderboard] = useState<any[]>([])
  const [activeTab, setActiveTab] = useState<'overview' | 'achievements' | 'rewards' | 'leaderboard'>('overview')
  const [levelProgress, setLevelProgress] = useState<any>(null)

  useEffect(() => {
    if (isVisible && user) {
      loadRewardsData()
    }
  }, [isVisible, user])

  const loadRewardsData = async () => {
    try {
      if (!user) return

      const [rewards, allAchievements, rewards_available, leaderboard_data] = await Promise.all([
        rewardsService.getUserRewards(user.id),
        rewardsService.getAllAchievements(),
        rewardsService.getAvailableRewards(user.id),
        rewardsService.getLeaderboard('month')
      ])

      setUserRewards(rewards)
      setAchievements(allAchievements)
      setAvailableRewards(rewards_available)
      setLeaderboard(leaderboard_data)

      const progress = rewardsService.getLevelProgress(rewards.total_points)
      setLevelProgress(progress)

    } catch (error) {
      console.error('Error loading rewards data:', error)
    }
  }

  const handleRedeemReward = async (rewardId: string) => {
    try {
      if (!user) return
      
      await rewardsService.redeemReward(user.id, rewardId)
      await loadRewardsData() // Refresh data
      
    } catch (error) {
      console.error('Error redeeming reward:', error)
    }
  }

  if (!isVisible || !userRewards) return null

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Level Progress */}
      <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-3xl p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="text-4xl">{levelProgress?.current.badge}</div>
            <div>
              <h3 className="text-xl font-bold">{levelProgress?.current.name}</h3>
              <p className="text-purple-100">Nível {levelProgress?.current.level}</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold">{userRewards.total_points}</p>
            <p className="text-purple-100 text-sm">pontos</p>
          </div>
        </div>

        {levelProgress?.next && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progresso para {levelProgress.next.name}</span>
              <span>{Math.round(levelProgress.progress * 100)}%</span>
            </div>
            <div className="w-full bg-purple-400/30 rounded-full h-2">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${levelProgress.progress * 100}%` }}
                className="bg-white h-2 rounded-full"
              />
            </div>
            <p className="text-purple-100 text-xs">
              {levelProgress.next.minPoints - userRewards.total_points} pontos para o próximo nível
            </p>
          </div>
        )}
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-blue-50 rounded-2xl p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Target className="w-5 h-5 text-blue-600" />
            <span className="text-blue-800 font-medium text-sm">Corridas</span>
          </div>
          <p className="text-2xl font-bold text-blue-600">{userRewards.lifetime_rides}</p>
        </div>

        <div className="bg-green-50 rounded-2xl p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Zap className="w-5 h-5 text-green-600" />
            <span className="text-green-800 font-medium text-sm">Sequência</span>
          </div>
          <p className="text-2xl font-bold text-green-600">{userRewards.streak_days} dias</p>
        </div>

        <div className="bg-yellow-50 rounded-2xl p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Trophy className="w-5 h-5 text-yellow-600" />
            <span className="text-yellow-800 font-medium text-sm">Conquistas</span>
          </div>
          <p className="text-2xl font-bold text-yellow-600">{userRewards.achievements.length}</p>
        </div>

        <div className="bg-purple-50 rounded-2xl p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Users className="w-5 h-5 text-purple-600" />
            <span className="text-purple-800 font-medium text-sm">Indicações</span>
          </div>
          <p className="text-2xl font-bold text-purple-600">{userRewards.referrals_count}</p>
        </div>
      </div>

      {/* Recent Achievements */}
      <div className="space-y-3">
        <h3 className="font-semibold text-gray-800">Conquistas Recentes</h3>
        <div className="space-y-2">
          {achievements
            .filter(a => userRewards.achievements.includes(a.id))
            .slice(0, 3)
            .map((achievement) => (
              <motion.div
                key={achievement.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-4 border border-yellow-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-800">{achievement.name}</h4>
                    <p className="text-gray-600 text-sm">{achievement.description}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-yellow-600 font-bold">+{achievement.points}</p>
                    <p className="text-yellow-500 text-xs">pontos</p>
                  </div>
                </div>
              </motion.div>
            ))}
        </div>
      </div>
    </div>
  )

  const renderAchievements = () => (
    <div className="space-y-4">
      {achievements.map((achievement) => {
        const isUnlocked = userRewards.achievements.includes(achievement.id)
        const rarityColors = {
          common: 'from-gray-400 to-gray-500',
          rare: 'from-blue-400 to-blue-500',
          epic: 'from-purple-400 to-purple-500',
          legendary: 'from-yellow-400 to-yellow-500'
        }

        return (
          <motion.div
            key={achievement.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`rounded-xl p-4 border-2 ${
              isUnlocked 
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' 
                : 'bg-gray-50 border-gray-200'
            }`}
          >
            <div className="flex items-center space-x-4">
              <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${rarityColors[achievement.rarity]} flex items-center justify-center text-2xl ${!isUnlocked && 'grayscale'}`}>
                {achievement.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h4 className={`font-medium ${isUnlocked ? 'text-gray-800' : 'text-gray-500'}`}>
                    {achievement.name}
                  </h4>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    achievement.rarity === 'legendary' ? 'bg-yellow-100 text-yellow-800' :
                    achievement.rarity === 'epic' ? 'bg-purple-100 text-purple-800' :
                    achievement.rarity === 'rare' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {achievement.rarity}
                  </span>
                </div>
                <p className={`text-sm ${isUnlocked ? 'text-gray-600' : 'text-gray-400'}`}>
                  {achievement.description}
                </p>
                <div className="flex items-center justify-between mt-2">
                  <span className={`text-sm font-medium ${isUnlocked ? 'text-green-600' : 'text-gray-500'}`}>
                    +{achievement.points} pontos
                  </span>
                  {isUnlocked && (
                    <div className="flex items-center space-x-1 text-green-600">
                      <Award className="w-4 h-4" />
                      <span className="text-xs">Desbloqueado</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )
      })}
    </div>
  )

  const renderRewards = () => (
    <div className="space-y-4">
      {availableRewards.map((reward) => {
        const canAfford = userRewards.total_points >= reward.cost_points

        return (
          <motion.div
            key={reward.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`rounded-xl p-4 border-2 ${
              canAfford ? 'bg-white border-blue-200' : 'bg-gray-50 border-gray-200'
            }`}
          >
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-400 rounded-xl flex items-center justify-center text-2xl">
                🎁
              </div>
              <div className="flex-1">
                <h4 className={`font-medium ${canAfford ? 'text-gray-800' : 'text-gray-500'}`}>
                  {reward.name}
                </h4>
                <p className={`text-sm ${canAfford ? 'text-gray-600' : 'text-gray-400'}`}>
                  {reward.description}
                </p>
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center space-x-1">
                    <Coins className={`w-4 h-4 ${canAfford ? 'text-blue-600' : 'text-gray-400'}`} />
                    <span className={`text-sm font-medium ${canAfford ? 'text-blue-600' : 'text-gray-400'}`}>
                      {reward.cost_points} pontos
                    </span>
                  </div>
                  <button
                    onClick={() => handleRedeemReward(reward.id)}
                    disabled={!canAfford}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      canAfford 
                        ? 'bg-blue-600 text-white hover:bg-blue-700' 
                        : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    Resgatar
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )
      })}
    </div>
  )

  const renderLeaderboard = () => (
    <div className="space-y-4">
      {leaderboard.map((entry, index) => (
        <motion.div
          key={entry.user_id}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1 }}
          className={`rounded-xl p-4 ${
            entry.user_id === user?.id 
              ? 'bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200' 
              : 'bg-gray-50'
          }`}
        >
          <div className="flex items-center space-x-4">
            <div className={`w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold ${
              index === 0 ? 'bg-yellow-400 text-yellow-900' :
              index === 1 ? 'bg-gray-300 text-gray-700' :
              index === 2 ? 'bg-orange-400 text-orange-900' :
              'bg-blue-100 text-blue-600'
            }`}>
              {index < 3 ? ['🥇', '🥈', '🥉'][index] : index + 1}
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-800">
                {entry.profiles?.full_name || 'Usuário Anônimo'}
              </h4>
              <p className="text-gray-600 text-sm">Nível {entry.current_level}</p>
            </div>
            <div className="text-right">
              <p className="text-lg font-bold text-blue-600">{entry.total_points}</p>
              <p className="text-gray-500 text-xs">pontos</p>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-3xl max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Trophy className="w-8 h-8 text-white" />
              <div>
                <h2 className="text-xl font-bold text-white">Centro de Recompensas</h2>
                <p className="text-purple-100 text-sm">Ganhe pontos e desbloqueie prêmios</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center text-white hover:bg-white/30 transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          {[
            { id: 'overview', label: 'Visão Geral', icon: Star },
            { id: 'achievements', label: 'Conquistas', icon: Trophy },
            { id: 'rewards', label: 'Prêmios', icon: Gift },
            { id: 'leaderboard', label: 'Ranking', icon: Crown }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 p-3 text-xs font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-purple-600 border-b-2 border-purple-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <tab.icon className="w-4 h-4 mx-auto mb-1" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              {activeTab === 'overview' && renderOverview()}
              {activeTab === 'achievements' && renderAchievements()}
              {activeTab === 'rewards' && renderRewards()}
              {activeTab === 'leaderboard' && renderLeaderboard()}
            </motion.div>
          </AnimatePresence>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default RewardsCenter
