import React from 'react';

export interface MobiDriveLogoProps {
  variant?: 'default' | 'compact' | 'icon';
  size?: 'sm' | 'md' | 'lg' | 'xl' | number;
  className?: string;
  appType?: 'admin' | 'driver' | 'passenger';
  isDarkMode?: boolean;
}

/**
 * MobiDrive Logo Component
 *
 * A standardized logo component that can be used across all MobiDrive apps.
 * It supports different variants, sizes, and app types.
 */
export const MobiDriveLogo: React.FC<MobiDriveLogoProps> = ({
  variant = 'default',
  size = 'md',
  className = '',
  appType,
  isDarkMode = false,
}) => {
  // No longer using useTheme hook

  // Define size values in pixels
  const sizeMap = {
    sm: 24,
    md: 32,
    lg: 48,
    xl: 64,
  };

  // Calculate actual size
  const actualSize = typeof size === 'number' ? size : sizeMap[size];

  // Define colors based on theme and app type
  const primaryColor = isDarkMode ? '#3b82f6' : '#2563eb'; // Blue
  const secondaryColor = '#22c55e'; // Green
  const textColor = isDarkMode ? '#f8fafc' : '#0f172a';

  // App-specific accent colors
  const appColors = {
    admin: '#4f46e5', // Indigo
    driver: '#22c55e', // Green
    passenger: '#3b82f6', // Blue
  };

  const accentColor = appType ? appColors[appType] : secondaryColor;

  // Icon-only variant
  if (variant === 'icon') {
    return (
      <svg
        width={actualSize}
        height={actualSize}
        viewBox="0 0 512 512"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <rect width="512" height="512" rx="64" fill={isDarkMode ? '#0F172A' : '#F8FAFC'} />
        <path
          d="M256 102.4C163.84 102.4 87.04 171.52 87.04 256C87.04 340.48 163.84 409.6 256 409.6C348.16 409.6 424.96 340.48 424.96 256C424.96 171.52 348.16 102.4 256 102.4ZM256 399.36C172.544 399.36 102.4 339.456 102.4 256C102.4 172.544 172.544 112.64 256 112.64C339.456 112.64 409.6 172.544 409.6 256C409.6 339.456 339.456 399.36 256 399.36Z"
          fill={accentColor}
        />
        <path
          d="M256 153.6C194.56 153.6 143.36 204.8 143.36 256C143.36 307.2 194.56 358.4 256 358.4C317.44 358.4 368.64 307.2 368.64 256C368.64 204.8 317.44 153.6 256 153.6ZM256 348.16C199.68 348.16 153.6 302.08 153.6 256C153.6 209.92 199.68 163.84 256 163.84C312.32 163.84 358.4 209.92 358.4 256C358.4 302.08 312.32 348.16 256 348.16Z"
          fill={primaryColor}
        />
        <circle cx="256" cy="256" r="76.8" fill={isDarkMode ? '#1E293B' : '#FFFFFF'} />
        <circle cx="256" cy="256" r="40.96" fill={accentColor} />
      </svg>
    );
  }

  // Compact variant (logo with just "MD")
  if (variant === 'compact') {
    return (
      <svg
        width={actualSize * 2}
        height={actualSize}
        viewBox="0 0 200 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
      >
        <g>
          {/* Icon */}
          <svg x="0" y="0" width="100" height="100" viewBox="0 0 512 512">
            <rect width="512" height="512" rx="64" fill={isDarkMode ? '#0F172A' : '#F8FAFC'} />
            <path
              d="M256 102.4C163.84 102.4 87.04 171.52 87.04 256C87.04 340.48 163.84 409.6 256 409.6C348.16 409.6 424.96 340.48 424.96 256C424.96 171.52 348.16 102.4 256 102.4ZM256 399.36C172.544 399.36 102.4 339.456 102.4 256C102.4 172.544 172.544 112.64 256 112.64C339.456 112.64 409.6 172.544 409.6 256C409.6 339.456 339.456 399.36 256 399.36Z"
              fill={accentColor}
            />
            <path
              d="M256 153.6C194.56 153.6 143.36 204.8 143.36 256C143.36 307.2 194.56 358.4 256 358.4C317.44 358.4 368.64 307.2 368.64 256C368.64 204.8 317.44 153.6 256 153.6ZM256 348.16C199.68 348.16 153.6 302.08 153.6 256C153.6 209.92 199.68 163.84 256 163.84C312.32 163.84 358.4 209.92 358.4 256C358.4 302.08 312.32 348.16 256 348.16Z"
              fill={primaryColor}
            />
            <circle cx="256" cy="256" r="76.8" fill={isDarkMode ? '#1E293B' : '#FFFFFF'} />
            <circle cx="256" cy="256" r="40.96" fill={accentColor} />
          </svg>

          {/* Text "MD" */}
          <text
            x="120"
            y="65"
            fontFamily="Inter, sans-serif"
            fontSize="40"
            fontWeight="700"
            fill={textColor}
          >
            MD
          </text>
        </g>
      </svg>
    );
  }

  // Default variant (full logo with "MobiDrive" and optional app type)
  return (
    <svg
      width={actualSize * 4}
      height={actualSize}
      viewBox="0 0 400 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g>
        {/* Icon */}
        <svg x="0" y="0" width="100" height="100" viewBox="0 0 512 512">
          <rect width="512" height="512" rx="64" fill={isDarkMode ? '#0F172A' : '#F8FAFC'} />
          <path
            d="M256 102.4C163.84 102.4 87.04 171.52 87.04 256C87.04 340.48 163.84 409.6 256 409.6C348.16 409.6 424.96 340.48 424.96 256C424.96 171.52 348.16 102.4 256 102.4ZM256 399.36C172.544 399.36 102.4 339.456 102.4 256C102.4 172.544 172.544 112.64 256 112.64C339.456 112.64 409.6 172.544 409.6 256C409.6 339.456 339.456 399.36 256 399.36Z"
            fill={accentColor}
          />
          <path
            d="M256 153.6C194.56 153.6 143.36 204.8 143.36 256C143.36 307.2 194.56 358.4 256 358.4C317.44 358.4 368.64 307.2 368.64 256C368.64 204.8 317.44 153.6 256 153.6ZM256 348.16C199.68 348.16 153.6 302.08 153.6 256C153.6 209.92 199.68 163.84 256 163.84C312.32 163.84 358.4 209.92 358.4 256C358.4 302.08 312.32 348.16 256 348.16Z"
            fill={primaryColor}
          />
          <circle cx="256" cy="256" r="76.8" fill={isDarkMode ? '#1E293B' : '#FFFFFF'} />
          <circle cx="256" cy="256" r="40.96" fill={accentColor} />
        </svg>

        {/* Text "MobiDrive" */}
        <text
          x="120"
          y="50"
          fontFamily="Inter, sans-serif"
          fontSize="32"
          fontWeight="700"
          fill={textColor}
        >
          MobiDrive
        </text>

        {/* App type text */}
        {appType && (
          <text
            x="120"
            y="80"
            fontFamily="Inter, sans-serif"
            fontSize="20"
            fontWeight="500"
            fill={appColors[appType]}
          >
            {appType === 'admin' ? 'Admin' : appType === 'driver' ? 'Driver' : 'Passenger'}
          </text>
        )}
      </g>
    </svg>
  );
};

export default MobiDriveLogo;
