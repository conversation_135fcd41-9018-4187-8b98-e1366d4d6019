// =====================================================
// PÁGINA DE CONFIGURAÇÃO DO SISTEMA
// Setup inicial do pagamento em dinheiro
// DESIGN ORIGINAL MANTIDO + CONVERSÃO ANDROID NATIVA
// =====================================================

import React from 'react'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { ModernLayout, ModernCard } from '../components/ModernLayout'
import { PaymentSetup } from '../components/PaymentSetup'

// ⚙️ SETUP - DESIGN ORIGINAL MANTIDO
// Conversão Android nativa aplicada via wrapper adaptativo

export const Setup: React.FC = () => {
  const navigate = useNavigate()

  // Animações simples e limpas (MANTENDO ORIGINAIS)
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <ModernLayout
      title="Configuração do Sistema"
      subtitle="Configure suas preferências"
      pageIcon="⚙️"
      animatedSymbol="🔧"
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >
        {/* Back Button (MANTENDO DESIGN ORIGINAL) */}
        <motion.div variants={itemVariants}>
          <ModernCard glass>
            <motion.button
              onClick={() => navigate(-1)}
              className="flex items-center text-white/80 hover:text-white transition-colors"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Voltar
            </motion.button>
          </ModernCard>
        </motion.div>

        {/* Payment Setup (MANTENDO DESIGN ORIGINAL) */}
        <motion.div
          variants={itemVariants}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <ModernCard title="Configuração de Pagamento" icon="💳">
            <PaymentSetup />
          </ModernCard>
        </motion.div>
      </motion.div>
    </ModernLayout>
  )
}
