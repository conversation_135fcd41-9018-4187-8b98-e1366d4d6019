import { supabase } from '../lib/supabase'
import { RealtimeChannel } from '@supabase/supabase-js'

// 🚗 SERVIÇO DE REALTIME PARA CORRIDAS
// Gerencia comunicação em tempo real para solicitações de corrida

export interface RideUpdate {
  id: string
  status: 'pending' | 'accepted' | 'driver_assigned' | 'in_progress' | 'completed' | 'cancelled'
  driver_id?: string
  driver_name?: string
  driver_phone?: string
  driver_location?: [number, number]
  estimated_arrival?: number
  updated_at: string
}

export interface DriverResponse {
  ride_id: string
  driver_id: string
  driver_name: string
  driver_phone: string
  driver_location: [number, number]
  estimated_arrival: number
  accepted: boolean
}

class RideRealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map()
  private isConnected = false

  constructor() {
    this.initializeConnection()
  }

  private async initializeConnection() {
    try {
      console.log('🔄 Inicializando conexão Realtime...')

      // Na nova API do Supabase, o Realtime se conecta automaticamente
      // quando um canal é criado, não precisamos conectar manualmente
      this.isConnected = true
      console.log('✅ Realtime inicializado com sucesso')

    } catch (error) {
      console.error('❌ Erro ao inicializar Realtime:', error)
      this.isConnected = false
    }
  }

  /**
   * Escutar atualizações de uma corrida específica
   */
  async subscribeToRide(
    rideId: string, 
    onUpdate: (update: RideUpdate) => void,
    onDriverResponse: (response: DriverResponse) => void
  ): Promise<boolean> {
    try {
      console.log('🎧 Inscrevendo-se para atualizações da corrida:', rideId)

      // Remover canal anterior se existir
      if (this.channels.has(rideId)) {
        await this.unsubscribeFromRide(rideId)
      }

      // Criar canal para a corrida
      const channel = supabase
        .channel(`ride-${rideId}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'ride_requests',
            filter: `id=eq.${rideId}`
          },
          (payload) => {
            console.log('📦 Atualização da corrida recebida:', payload)
            const rideData = payload.new as any
            
            const update: RideUpdate = {
              id: rideData.id,
              status: rideData.status,
              driver_id: rideData.driver_id,
              driver_name: rideData.driver_name,
              driver_phone: rideData.driver_phone,
              driver_location: rideData.driver_location ? 
                [rideData.driver_location.lng, rideData.driver_location.lat] : undefined,
              estimated_arrival: rideData.estimated_arrival,
              updated_at: rideData.updated_at
            }

            onUpdate(update)
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'driver_responses',
            filter: `ride_id=eq.${rideId}`
          },
          (payload) => {
            console.log('🚗 Resposta de motorista recebida:', payload)
            const responseData = payload.new as any
            
            const response: DriverResponse = {
              ride_id: responseData.ride_id,
              driver_id: responseData.driver_id,
              driver_name: responseData.driver_name,
              driver_phone: responseData.driver_phone,
              driver_location: [responseData.driver_location.lng, responseData.driver_location.lat],
              estimated_arrival: responseData.estimated_arrival,
              accepted: responseData.accepted
            }

            onDriverResponse(response)
          }
        )
        .subscribe((status) => {
          console.log('📡 Status da inscrição:', status)
          if (status === 'SUBSCRIBED') {
            console.log('✅ Inscrito com sucesso na corrida:', rideId)
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Erro na inscrição da corrida:', rideId)
          }
        })

      this.channels.set(rideId, channel)
      return true

    } catch (error) {
      console.error('❌ Erro ao se inscrever na corrida:', error)
      return false
    }
  }

  /**
   * Parar de escutar atualizações de uma corrida
   */
  async unsubscribeFromRide(rideId: string): Promise<void> {
    try {
      const channel = this.channels.get(rideId)
      if (channel) {
        console.log('🔇 Cancelando inscrição da corrida:', rideId)
        await supabase.removeChannel(channel)
        this.channels.delete(rideId)
      }
    } catch (error) {
      console.error('❌ Erro ao cancelar inscrição:', error)
    }
  }

  /**
   * Notificar motoristas sobre nova corrida
   */
  async notifyDriversNewRide(rideId: string, pickupLocation: [number, number]): Promise<boolean> {
    try {
      console.log('📢 Notificando motoristas sobre nova corrida:', rideId)

      // Buscar motoristas ativos próximos
      const { data: nearbyDrivers, error } = await supabase
        .rpc('get_nearby_active_drivers', {
          passenger_lat: pickupLocation[1],
          passenger_lng: pickupLocation[0],
          radius_km: 10
        })

      if (error) {
        console.error('❌ Erro ao buscar motoristas:', error)
        return false
      }

      console.log(`📍 Encontrados ${nearbyDrivers?.length || 0} motoristas próximos`)

      if (!nearbyDrivers || nearbyDrivers.length === 0) {
        console.warn('⚠️ Nenhum motorista ativo encontrado')
        return false
      }

      // Enviar notificação via broadcast para motoristas
      const channel = supabase.channel('driver-notifications')
      
      await channel.send({
        type: 'broadcast',
        event: 'new-ride-request',
        payload: {
          ride_id: rideId,
          pickup_location: pickupLocation,
          timestamp: new Date().toISOString()
        }
      })

      console.log('✅ Notificação enviada para motoristas')
      return true

    } catch (error) {
      console.error('❌ Erro ao notificar motoristas:', error)
      return false
    }
  }

  /**
   * Verificar status da conexão
   */
  isRealtimeConnected(): boolean {
    return this.isConnected
  }

  /**
   * Reconectar se necessário
   */
  async reconnect(): Promise<boolean> {
    try {
      if (!this.isConnected) {
        await this.initializeConnection()
      }
      return this.isConnected
    } catch (error) {
      console.error('❌ Erro ao reconectar:', error)
      return false
    }
  }

  /**
   * Limpar todas as inscrições
   */
  async cleanup(): Promise<void> {
    try {
      console.log('🧹 Limpando inscrições Realtime...')
      
      for (const [rideId, channel] of this.channels) {
        await supabase.removeChannel(channel)
      }
      
      this.channels.clear()
      console.log('✅ Limpeza concluída')
    } catch (error) {
      console.error('❌ Erro na limpeza:', error)
    }
  }
}

// Instância singleton
export const rideRealtimeService = new RideRealtimeService()

// Limpeza automática ao sair da página
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    rideRealtimeService.cleanup()
  })
}
