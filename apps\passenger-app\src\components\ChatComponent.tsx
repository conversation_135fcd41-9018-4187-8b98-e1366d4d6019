import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MessageCircle, 
  Send, 
  Phone, 
  X, 
  Smile,
  Paperclip,
  Mic,
  MicOff
} from 'lucide-react'

interface Message {
  id: string
  text: string
  sender: 'user' | 'driver'
  timestamp: Date
  type: 'text' | 'audio' | 'location'
}

interface ChatComponentProps {
  isOpen: boolean
  onClose: () => void
  driverName: string
  driverPhoto?: string
}

export const ChatComponent: React.FC<ChatComponentProps> = ({
  isOpen,
  onClose,
  driverName,
  driverPhoto
}) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Ol<PERSON>! Sou <PERSON> João, seu motorista. Estou a caminho!',
      sender: 'driver',
      timestamp: new Date(Date.now() - 60000),
      type: 'text'
    },
    {
      id: '2',
      text: 'Oi <PERSON>! Estarei esperando na frente do prédio.',
      sender: 'user',
      timestamp: new Date(Date.now() - 30000),
      type: 'text'
    }
  ])
  const [newMessage, setNewMessage] = useState('')
  const [isRecording, setIsRecording] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = () => {
    if (!newMessage.trim()) return

    const message: Message = {
      id: Date.now().toString(),
      text: newMessage,
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    }

    setMessages(prev => [...prev, message])
    setNewMessage('')

    // Simulate driver response
    setTimeout(() => {
      const responses = [
        'Perfeito! Chego em 2 minutos.',
        'Ok, obrigado pela informação!',
        'Estou chegando, já consigo ver o prédio.',
        'Tudo bem, aguarde mais um pouquinho.'
      ]
      
      const driverResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: responses[Math.floor(Math.random() * responses.length)],
        sender: 'driver',
        timestamp: new Date(),
        type: 'text'
      }
      
      setMessages(prev => [...prev, driverResponse])
    }, 1000 + Math.random() * 2000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const toggleRecording = () => {
    setIsRecording(!isRecording)
    // Here you would implement actual voice recording
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('pt-BR', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const quickMessages = [
    'Estou chegando',
    'Aguarde mais um pouco',
    'Onde você está?',
    'Obrigado!'
  ]

  if (!isOpen) return null

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-end justify-center p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Chat Container */}
      <motion.div
        className="relative w-full max-w-md h-[600px] glass-card flex flex-col"
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 100, opacity: 0 }}
        transition={{ type: "spring", damping: 25, stiffness: 300 }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/20">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold">
                {driverName.charAt(0)}
              </span>
            </div>
            <div>
              <h3 className="font-semibold text-white">{driverName}</h3>
              <p className="text-xs text-white/70">Motorista • Online</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <motion.button
              className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Phone className="w-5 h-5" />
            </motion.button>
            <motion.button
              className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
              onClick={onClose}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <X className="w-5 h-5" />
            </motion.button>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          <AnimatePresence>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <div className={`max-w-[80%] ${
                  message.sender === 'user' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-white/20 text-white'
                } rounded-2xl px-4 py-2`}>
                  <p className="text-sm">{message.text}</p>
                  <p className={`text-xs mt-1 ${
                    message.sender === 'user' 
                      ? 'text-blue-100' 
                      : 'text-white/60'
                  }`}>
                    {formatTime(message.timestamp)}
                  </p>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          <div ref={messagesEndRef} />
        </div>

        {/* Quick Messages */}
        <div className="px-4 py-2">
          <div className="flex flex-wrap gap-2">
            {quickMessages.map((msg, index) => (
              <motion.button
                key={index}
                className="px-3 py-1 bg-white/10 text-white/80 text-xs rounded-full hover:bg-white/20 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  setNewMessage(msg)
                  setTimeout(handleSendMessage, 100)
                }}
              >
                {msg}
              </motion.button>
            ))}
          </div>
        </div>

        {/* Input */}
        <div className="p-4 border-t border-white/20">
          <div className="flex items-center space-x-2">
            <motion.button
              className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Paperclip className="w-5 h-5" />
            </motion.button>
            
            <div className="flex-1 relative">
              <input
                ref={inputRef}
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Digite sua mensagem..."
                className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
              />
              <motion.button
                className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-white/70 hover:text-white"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Smile className="w-4 h-4" />
              </motion.button>
            </div>

            <motion.button
              className={`p-2 rounded-lg transition-colors ${
                isRecording 
                  ? 'bg-red-500 text-white' 
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
              onClick={toggleRecording}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {isRecording ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
            </motion.button>

            <motion.button
              className="p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors disabled:opacity-50"
              disabled={!newMessage.trim()}
              onClick={handleSendMessage}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Send className="w-5 h-5" />
            </motion.button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default ChatComponent
