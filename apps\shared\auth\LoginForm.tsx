import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Loader2 } from 'lucide-react';

import { useAuth } from './AuthContext';

// Login form schema
const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  rememberMe: z.boolean().optional(),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export interface LoginFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
  title?: string;
  description?: string;
  showRegisterLink?: boolean;
  showForgotPassword?: boolean;
  showRememberMe?: boolean;
  appType?: 'admin' | 'driver' | 'passenger';
  logoComponent?: React.ReactNode;
  className?: string;
  formClassName?: string;
  buttonClassName?: string;
  inputClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
  linkClassName?: string;
  registerPath?: string;
  forgotPasswordPath?: string;
  demoCredentials?: {
    email: string;
    password: string;
  };
}

/**
 * Shared Login Form Component
 * 
 * This component provides a standardized login form for all MobiDrive apps.
 * It handles form validation, submission, and error handling.
 */
export const LoginForm: React.FC<LoginFormProps> = ({
  onSuccess,
  redirectTo = '/dashboard',
  title = 'Login',
  description = 'Enter your email and password to access your account.',
  showRegisterLink = true,
  showForgotPassword = true,
  showRememberMe = true,
  appType = 'passenger',
  logoComponent,
  className = 'w-full max-w-md p-8 bg-white dark:bg-gray-800 rounded-lg shadow-md',
  formClassName = 'space-y-6',
  buttonClassName = 'w-full py-2 px-4 bg-primary hover:bg-primary/90 text-white font-medium rounded-md transition-colors',
  inputClassName = 'w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary',
  labelClassName = 'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1',
  errorClassName = 'text-red-500 text-xs mt-1',
  linkClassName = 'text-primary hover:text-primary/80 text-sm',
  registerPath = '/register',
  forgotPasswordPath = '/forgot-password',
  demoCredentials,
}) => {
  const { signIn } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: demoCredentials?.email || '',
      password: demoCredentials?.password || '',
      rememberMe: false,
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    try {
      setIsLoading(true);
      setAuthError(null);

      // Use demo credentials if provided and form is empty
      const email = data.email || demoCredentials?.email || '';
      const password = data.password || demoCredentials?.password || '';

      const { data: authData, error } = await signIn(email, password);

      if (error) {
        setAuthError(error.message || 'Failed to sign in. Please check your credentials.');
        setIsLoading(false);
        return;
      }

      // Handle successful login
      if (onSuccess) {
        onSuccess();
      } else {
        // Navigate to the redirect path
        navigate(redirectTo);
      }

      setIsLoading(false);
    } catch (error: any) {
      setAuthError(error.message || 'An unexpected error occurred');
      setIsLoading(false);
    }
  };

  return (
    <div className={className}>
      {/* Logo and Header */}
      <div className="text-center mb-6">
        {logoComponent}
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mt-4">{title}</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">{description}</p>
      </div>

      {/* Error Message */}
      {authError && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
          <p className="text-red-600 dark:text-red-400 text-sm">{authError}</p>
        </div>
      )}

      {/* Login Form */}
      <form onSubmit={handleSubmit(onSubmit)} className={formClassName}>
        {/* Email Field */}
        <div>
          <label htmlFor="email" className={labelClassName}>
            Email
          </label>
          <input
            id="email"
            type="email"
            className={inputClassName}
            placeholder="<EMAIL>"
            {...register('email')}
          />
          {errors.email && (
            <p className={errorClassName}>{errors.email.message}</p>
          )}
        </div>

        {/* Password Field */}
        <div>
          <div className="flex justify-between items-center mb-1">
            <label htmlFor="password" className={labelClassName}>
              Password
            </label>
            {showForgotPassword && (
              <Link to={forgotPasswordPath} className={linkClassName}>
                Forgot password?
              </Link>
            )}
          </div>
          <div className="relative">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              className={inputClassName}
              placeholder="••••••••"
              {...register('password')}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none"
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5" />
              ) : (
                <Eye className="h-5 w-5" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className={errorClassName}>{errors.password.message}</p>
          )}
        </div>

        {/* Remember Me Checkbox */}
        {showRememberMe && (
          <div className="flex items-center">
            <input
              id="rememberMe"
              type="checkbox"
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              {...register('rememberMe')}
            />
            <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Remember me
            </label>
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isLoading}
          className={buttonClassName}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              <span>Signing in...</span>
            </div>
          ) : (
            'Sign in'
          )}
        </button>

        {/* Register Link */}
        {showRegisterLink && (
          <div className="text-center mt-4">
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Don't have an account?{' '}
              <Link to={registerPath} className={linkClassName}>
                Register
              </Link>
            </p>
          </div>
        )}
      </form>
    </div>
  );
};

export default LoginForm;
