<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Teste de Rota Mapbox</title>
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no">
    <link href="https://api.mapbox.com/mapbox-gl-js/v3.1.2/mapbox-gl.css" rel="stylesheet">
    <script src="https://api.mapbox.com/mapbox-gl-js/v3.1.2/mapbox-gl.js"></script>
    <style>
        body { margin: 0; padding: 0; }
        #map { position: absolute; top: 0; bottom: 0; width: 100%; }
        .controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
        button {
            margin: 5px;
            padding: 10px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="controls">
        <button onclick="testRoute()">Testar Rota</button>
        <button onclick="clearRoute()">Limpar Rota</button>
        <div id="status">Clique em "Testar Rota" para começar</div>
    </div>

    <script>
        // SUBSTITUA PELO SEU TOKEN MAPBOX
        mapboxgl.accessToken = 'pk.eyJ1IjoibW9iaWRyaXZlYnIiLCJhIjoiY20zZGNqZGNhMGNhZzJqcHpqZGNqZGNqZCJ9.example';

        const map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/streets-v12',
            center: [-46.6333, -23.5505], // São Paulo
            zoom: 12
        });

        const statusDiv = document.getElementById('status');

        function updateStatus(message) {
            statusDiv.innerHTML = message;
            console.log(message);
        }

        async function testRoute() {
            updateStatus('🚀 Iniciando teste de rota...');

            // Coordenadas de teste em São Paulo
            const origin = [-46.6333, -23.5505]; // Centro de SP
            const destination = [-46.6433, -23.5605]; // Próximo ao centro

            try {
                // URL da API Directions do Mapbox
                const url = `https://api.mapbox.com/directions/v5/mapbox/driving/${origin[0]},${origin[1]};${destination[0]},${destination[1]}?steps=true&geometries=geojson&access_token=${mapboxgl.accessToken}`;

                updateStatus('🌐 Chamando API Directions...');
                console.log('URL:', url.replace(mapboxgl.accessToken, 'TOKEN_HIDDEN'));

                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Resposta da API:', data);

                if (data.code === 'Ok' && data.routes && data.routes.length > 0) {
                    const route = data.routes[0];
                    
                    updateStatus(`✅ Rota encontrada: ${(route.distance / 1000).toFixed(1)}km, ${Math.round(route.duration / 60)}min`);

                    // Limpar rota existente
                    if (map.getSource('route')) {
                        if (map.getLayer('route')) {
                            map.removeLayer('route');
                        }
                        map.removeSource('route');
                    }

                    // Adicionar nova rota
                    map.addSource('route', {
                        type: 'geojson',
                        data: {
                            type: 'Feature',
                            geometry: route.geometry
                        }
                    });

                    map.addLayer({
                        id: 'route',
                        type: 'line',
                        source: 'route',
                        layout: {
                            'line-join': 'round',
                            'line-cap': 'round'
                        },
                        paint: {
                            'line-color': '#3b9ddd',
                            'line-width': 6
                        }
                    });

                    // Ajustar mapa para mostrar a rota
                    const coordinates = route.geometry.coordinates;
                    const bounds = coordinates.reduce(
                        (bounds, coord) => bounds.extend(coord),
                        new mapboxgl.LngLatBounds()
                    );

                    map.fitBounds(bounds, {
                        padding: 50
                    });

                    updateStatus('🎯 Rota adicionada ao mapa com sucesso!');

                } else {
                    updateStatus('❌ Nenhuma rota encontrada na resposta da API');
                    console.error('Resposta da API:', data);
                }

            } catch (error) {
                updateStatus(`❌ Erro: ${error.message}`);
                console.error('Erro completo:', error);
            }
        }

        function clearRoute() {
            if (map.getSource('route')) {
                if (map.getLayer('route')) {
                    map.removeLayer('route');
                }
                map.removeSource('route');
            }
            updateStatus('🗑️ Rota removida');
        }

        map.on('load', () => {
            updateStatus('✅ Mapa carregado. Pronto para testar!');
        });
    </script>
</body>
</html>
