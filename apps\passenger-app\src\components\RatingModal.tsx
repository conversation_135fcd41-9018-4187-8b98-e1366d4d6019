import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Star, X, ThumbsUp, MessageCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface RatingModalProps {
  isOpen: boolean
  onClose: () => void
  rideId: string
  driverId: string
  driverName: string
  onRatingSubmitted: () => void
}

export const RatingModal: React.FC<RatingModalProps> = ({
  isOpen,
  onClose,
  rideId,
  driverId,
  driverName,
  onRatingSubmitted
}) => {
  const [rating, setRating] = useState(0)
  const [hoveredRating, setHoveredRating] = useState(0)
  const [comment, setComment] = useState('')
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  const ratingTags = [
    'Pontual',
    'Educado',
    'Direção segura',
    'Carro limpo',
    'Boa conversa',
    '<PERSON><PERSON><PERSON><PERSON>',
    'Música boa',
    'Ar condicionado',
    'Rota eficiente'
  ]

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  const submitRating = async () => {
    if (rating === 0) return

    setLoading(true)
    try {
      // Salvar avaliação
      const { error: ratingError } = await supabase
        .from('ride_ratings')
        .insert({
          ride_id: rideId,
          driver_id: driverId,
          rating,
          comment: comment.trim() || null,
          tags: selectedTags
        })

      if (ratingError) throw ratingError

      // Atualizar rating médio do motorista
      const { data: ratings, error: fetchError } = await supabase
        .from('ride_ratings')
        .select('rating')
        .eq('driver_id', driverId)

      if (fetchError) throw fetchError

      const avgRating = ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length

      const { error: updateError } = await supabase
        .from('drivers')
        .update({ 
          rating: avgRating,
          total_rides: ratings.length
        })
        .eq('user_id', driverId)

      if (updateError) throw updateError

      onRatingSubmitted()
      onClose()
    } catch (error) {
      console.error('Erro ao enviar avaliação:', error)
    } finally {
      setLoading(false)
    }
  }

  const getRatingText = (stars: number) => {
    switch (stars) {
      case 1: return 'Muito ruim'
      case 2: return 'Ruim'
      case 3: return 'Regular'
      case 4: return 'Bom'
      case 5: return 'Excelente'
      default: return 'Avalie sua experiência'
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-2xl w-full max-w-md p-6"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Avaliar Corrida</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Driver Info */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gray-300 rounded-full mx-auto mb-3 flex items-center justify-center">
              <span className="text-2xl">🚗</span>
            </div>
            <h3 className="font-semibold text-gray-900">{driverName}</h3>
            <p className="text-sm text-gray-500">Como foi sua experiência?</p>
          </div>

          {/* Rating Stars */}
          <div className="text-center mb-6">
            <div className="flex justify-center space-x-2 mb-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  className="p-1 transition-transform hover:scale-110"
                >
                  <Star
                    className={`w-8 h-8 ${
                      star <= (hoveredRating || rating)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
            </div>
            <p className="text-sm text-gray-600">
              {getRatingText(hoveredRating || rating)}
            </p>
          </div>

          {/* Tags */}
          {rating > 0 && (
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-900 mb-3">
                O que você mais gostou? (opcional)
              </h4>
              <div className="flex flex-wrap gap-2">
                {ratingTags.map((tag) => (
                  <button
                    key={tag}
                    onClick={() => handleTagToggle(tag)}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      selectedTags.includes(tag)
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Comment */}
          {rating > 0 && (
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                Comentário (opcional)
              </h4>
              <textarea
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                placeholder="Conte-nos mais sobre sua experiência..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={3}
                maxLength={500}
              />
              <p className="text-xs text-gray-500 mt-1">
                {comment.length}/500 caracteres
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Pular
            </button>
            <button
              onClick={submitRating}
              disabled={rating === 0 || loading}
              className="flex-1 py-3 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <>
                  <ThumbsUp className="w-4 h-4" />
                  <span>Enviar</span>
                </>
              )}
            </button>
          </div>

          {/* Footer */}
          <p className="text-xs text-gray-500 text-center mt-4">
            Sua avaliação ajuda a melhorar o serviço para todos
          </p>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default RatingModal
