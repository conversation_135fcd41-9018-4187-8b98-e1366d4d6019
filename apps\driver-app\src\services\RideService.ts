import { supabase } from '../lib/supabase';
import { mapboxService } from './MapboxService';
import { simpleNotificationService } from './SimpleNotificationService';
import { analyticsService } from './AnalyticsService';

export interface RideRequest {
  id?: string;
  user_id: string;
  driver_id?: string;
  origin_address: string;
  origin_coords: [number, number];
  destination_address: string;
  destination_coords: [number, number];
  distance: number;
  duration: number;
  estimated_price: number;
  final_price?: number;
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  vehicle_type: string;
  payment_method: string;
  created_at?: string;
  accepted_at?: string;
  started_at?: string;
  completed_at?: string;
  cancelled_at?: string;
}

export interface Driver {
  id: string;
  name: string;
  phone: string;
  rating: number;
  vehicle: {
    model: string;
    plate: string;
    color: string;
  };
  location: [number, number];
  distance: number;
  eta: number;
}

export class RideService {
  private static instance: RideService;

  static getInstance(): RideService {
    if (!RideService.instance) {
      RideService.instance = new RideService();
    }
    return RideService.instance;
  }

  // Criar uma nova solicitação de corrida (melhorado)
  async createRideRequest(
    originAddress: string,
    originCoords: [number, number],
    destinationAddress: string,
    destinationCoords: [number, number],
    vehicleType: string = 'economy',
    paymentMethod: string = 'cash'
  ): Promise<RideRequest | null> {
    try {
      // Obter usuário atual
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Usuário não autenticado');
      }

      // Calcular rota e preço com tipo de veículo
      const estimate = await mapboxService.calculateRideEstimate(
        originCoords,
        destinationCoords,
        vehicleType
      );
      if (!estimate) {
        throw new Error('Não foi possível calcular a rota');
      }

      // Usar função do banco para criar corrida
      const { data: rideId, error: createError } = await supabase
        .rpc('create_ride_request', {
          p_user_id: user.id,
          p_origin_address: originAddress,
          p_origin_lat: originCoords[1],
          p_origin_lng: originCoords[0],
          p_destination_address: destinationAddress,
          p_destination_lat: destinationCoords[1],
          p_destination_lng: destinationCoords[0],
          p_distance: estimate.distance / 1000, // Convert to km
          p_duration: estimate.duration / 60, // Convert to minutes
          p_estimated_price: estimate.price,
          p_vehicle_type: vehicleType,
          p_payment_method: paymentMethod
        });

      if (createError) {
        console.error('Erro ao criar solicitação:', createError);
        return null;
      }

      // Tentar atribuir motorista automaticamente
      const { data: driverAssigned, error: assignError } = await supabase
        .rpc('assign_driver_to_ride', {
          p_ride_id: rideId,
          p_max_distance_km: 10
        });

      if (assignError) {
        console.warn('Erro ao atribuir motorista:', assignError);
      }

      // Buscar dados completos da corrida criada
      const { data: rideData, error: fetchError } = await supabase
        .from('ride_requests')
        .select(`
          *,
          driver:profiles!ride_requests_driver_id_fkey(full_name, phone),
          passenger:profiles!ride_requests_user_id_fkey(full_name, phone)
        `)
        .eq('id', rideId)
        .single();

      if (fetchError) {
        console.error('Erro ao buscar dados da corrida:', fetchError);
        return null;
      }

      // Converter dados para formato esperado
      const rideRequest: RideRequest = {
        id: rideData.id,
        user_id: rideData.user_id,
        driver_id: rideData.driver_id,
        origin_address: rideData.origin_address,
        origin_coords: [
          rideData.origin_coords?.x || rideData.origin_coords?.coordinates?.[0] || originCoords[0],
          rideData.origin_coords?.y || rideData.origin_coords?.coordinates?.[1] || originCoords[1]
        ],
        destination_address: rideData.destination_address,
        destination_coords: [
          rideData.destination_coords?.x || rideData.destination_coords?.coordinates?.[0] || destinationCoords[0],
          rideData.destination_coords?.y || rideData.destination_coords?.coordinates?.[1] || destinationCoords[1]
        ],
        distance: rideData.distance,
        duration: rideData.duration,
        estimated_price: rideData.estimated_price,
        final_price: rideData.final_price,
        status: rideData.status,
        vehicle_type: rideData.vehicle_type,
        payment_method: rideData.payment_method,
        created_at: rideData.created_at,
        accepted_at: rideData.accepted_at,
        started_at: rideData.started_at,
        completed_at: rideData.completed_at,
        cancelled_at: rideData.cancelled_at
      };

      // Registrar evento no analytics
      analyticsService.trackRideEvent('request_created', {
        ride_id: rideId,
        origin_address: originAddress,
        destination_address: destinationAddress,
        distance: estimate.distance,
        estimated_price: estimate.price,
        vehicle_type: vehicleType,
        payment_method: paymentMethod,
        driver_assigned: !!driverAssigned
      });

      // Notificar usuário sobre criação da solicitação
      await simpleNotificationService.createNotification(
        user.id,
        '🚗 Solicitação Criada',
        driverAssigned
          ? 'Motorista encontrado! Aguarde a confirmação.'
          : 'Sua solicitação foi criada. Procurando motoristas próximos...',
        'ride_request',
        { ride_id: rideId },
        'normal'
      );

      console.log('✅ Corrida criada com sucesso:', rideRequest);
      return rideRequest;

    } catch (error) {
      console.error('Erro ao criar corrida:', error);
      return null;
    }
  }

  // Buscar motoristas próximos (melhorado)
  async findNearbyDrivers(userLocation: [number, number], radiusKm: number = 5): Promise<Driver[]> {
    try {
      // Usar função SQL melhorada para buscar motoristas próximos
      const { data, error } = await supabase
        .rpc('find_nearby_drivers', {
          user_lat: userLocation[1],
          user_lng: userLocation[0],
          radius_km: radiusKm
        });

      if (error) {
        console.error('Erro ao buscar motoristas:', error);
        return [];
      }

      if (!data || data.length === 0) {
        console.log('Nenhum motorista encontrado na região');
        return [];
      }

      return data.map((driver: any) => ({
        id: driver.driver_id,
        name: driver.driver_name || 'Motorista',
        phone: driver.driver_phone || '',
        rating: driver.rating || 4.5,
        vehicle: {
          model: driver.vehicle_type === 'moto' ? 'Honda CG 160' : 'Honda Civic',
          plate: 'ABC-1234', // TODO: Adicionar placa real do banco
          color: 'Prata',
          type: driver.vehicle_type || 'economy'
        },
        location: [driver.location_lng, driver.location_lat],
        distance: Math.round(driver.distance_km * 100) / 100, // Arredondar para 2 casas decimais
        eta: Math.max(Math.round(driver.distance_km * 2), 2), // Mínimo 2 min
        heading: driver.heading || 0,
        speed: driver.speed || 0,
        isAvailable: driver.is_available
      }));

    } catch (error) {
      console.error('Erro ao buscar motoristas:', error);
      return [];
    }
  }

  // Notificar motoristas próximos sobre nova corrida
  private async notifyNearbyDrivers(location: [number, number], rideId: string) {
    try {
      const drivers = await this.findNearbyDrivers(location);
      
      // Enviar notificação para cada motorista
      for (const driver of drivers) {
        await this.sendDriverNotification(driver.id, rideId);
      }

    } catch (error) {
      console.error('Erro ao notificar motoristas:', error);
    }
  }

  // Enviar notificação para motorista
  private async sendDriverNotification(driverId: string, rideId: string) {
    try {
      await supabase
        .from('notifications')
        .insert({
          user_id: driverId,
          title: 'Nova Corrida Disponível',
          message: 'Uma nova corrida está disponível próxima a você',
          type: 'ride_request',
          data: { ride_id: rideId }
        });

    } catch (error) {
      console.error('Erro ao enviar notificação:', error);
    }
  }

  // Obter status da corrida
  async getRideStatus(rideId: string): Promise<RideRequest | null> {
    try {
      const { data, error } = await supabase
        .from('ride_requests')
        .select(`
          *,
          driver:profiles!ride_requests_driver_id_fkey(
            id,
            full_name,
            phone
          )
        `)
        .eq('id', rideId)
        .single();

      if (error) {
        console.error('Erro ao obter status:', error);
        return null;
      }

      return data;

    } catch (error) {
      console.error('Erro ao obter status da corrida:', error);
      return null;
    }
  }

  // Cancelar corrida
  async cancelRide(rideId: string, reason?: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('ride_requests')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString()
        })
        .eq('id', rideId);

      if (error) {
        console.error('Erro ao cancelar corrida:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('Erro ao cancelar corrida:', error);
      return false;
    }
  }

  // Obter histórico de corridas do usuário
  async getUserRideHistory(limit: number = 10): Promise<RideRequest[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data, error } = await supabase
        .from('ride_requests')
        .select(`
          *,
          driver:profiles!ride_requests_driver_id_fkey(
            id,
            full_name,
            phone
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Erro ao obter histórico:', error);
        return [];
      }

      return data || [];

    } catch (error) {
      console.error('Erro ao obter histórico de corridas:', error);
      return [];
    }
  }

  // Avaliar corrida
  async rateRide(rideId: string, rating: number, comment?: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return false;

      // Obter dados da corrida
      const ride = await this.getRideStatus(rideId);
      if (!ride || !ride.driver_id) return false;

      // Inserir avaliação
      const { error } = await supabase
        .from('ride_ratings')
        .insert({
          ride_id: rideId,
          rater_id: user.id,
          rated_id: ride.driver_id,
          rating,
          comment
        });

      if (error) {
        console.error('Erro ao avaliar corrida:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('Erro ao avaliar corrida:', error);
      return false;
    }
  }

  // Calcular preço estimado
  calculateEstimatedPrice(distance: number, duration: number, vehicleType: string = 'economy'): number {
    const basePrices = {
      economy: { base: 5.00, perKm: 2.50, perMin: 0.30 },
      comfort: { base: 8.00, perKm: 3.50, perMin: 0.40 },
      premium: { base: 12.00, perKm: 5.00, perMin: 0.60 },
      moto: { base: 3.00, perKm: 1.80, perMin: 0.20 }
    };

    const pricing = basePrices[vehicleType as keyof typeof basePrices] || basePrices.economy;
    const distanceKm = distance / 1000;
    const durationMin = duration / 60;
    
    const price = pricing.base + (distanceKm * pricing.perKm) + (durationMin * pricing.perMin);
    
    // Preço mínimo de R$ 5,00
    return Math.max(price, 5.00);
  }

  // Subscrever a atualizações de corrida em tempo real (habilitado)
  subscribeToRideUpdates(rideId: string, callback: (ride: RideRequest) => void) {
    console.log('🔄 Iniciando subscription para ride updates:', rideId);

    try {
      const channel = supabase
        .channel(`ride-updates-${rideId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'ride_requests',
            filter: `id=eq.${rideId}`
          },
          (payload) => {
            console.log('📡 Ride update recebido:', payload);

            if (payload.new) {
              const rideData = payload.new as any;

              // Converter para formato esperado
              const rideUpdate: RideRequest = {
                id: rideData.id,
                user_id: rideData.user_id,
                driver_id: rideData.driver_id,
                origin_address: rideData.origin_address,
                origin_coords: [
                  rideData.origin_coords?.x || rideData.origin_coords?.coordinates?.[0] || 0,
                  rideData.origin_coords?.y || rideData.origin_coords?.coordinates?.[1] || 0
                ],
                destination_address: rideData.destination_address,
                destination_coords: [
                  rideData.destination_coords?.x || rideData.destination_coords?.coordinates?.[0] || 0,
                  rideData.destination_coords?.y || rideData.destination_coords?.coordinates?.[1] || 0
                ],
                distance: rideData.distance,
                duration: rideData.duration,
                estimated_price: rideData.estimated_price,
                final_price: rideData.final_price,
                status: rideData.status,
                vehicle_type: rideData.vehicle_type,
                payment_method: rideData.payment_method,
                created_at: rideData.created_at,
                accepted_at: rideData.accepted_at,
                started_at: rideData.started_at,
                completed_at: rideData.completed_at,
                cancelled_at: rideData.cancelled_at
              };

              callback(rideUpdate);
            }
          }
        )
        .subscribe();

      return {
        unsubscribe: () => {
          console.log('🛑 Unsubscribing from ride updates:', rideId);
          supabase.removeChannel(channel);
        }
      };

    } catch (error) {
      console.error('❌ Erro ao criar subscription:', error);
      return {
        unsubscribe: () => console.log('Mock unsubscribe due to error')
      };
    }
  }
}

export const rideService = RideService.getInstance();
