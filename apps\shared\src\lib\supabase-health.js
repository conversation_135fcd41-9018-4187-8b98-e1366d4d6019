/**
 * Sistema de monitoramento de saúde da conexão com o Supabase
 * 
 * Este módulo monitora a saúde da conexão com o Supabase e fornece
 * informações sobre o status da conexão, latência e disponibilidade.
 */

// Detectar se estamos no navegador ou no servidor
const isBrowser = typeof window !== 'undefined';

// Configurações padrão
const DEFAULT_CONFIG = {
  enabled: true,
  checkInterval: 60000, // 1 minuto
  timeoutThreshold: 5000, // 5 segundos
  healthyLatencyThreshold: 500, // 500ms
  warningLatencyThreshold: 1000, // 1000ms
  maxHistorySize: 10,
  debug: false
};

// Status de saúde
const HEALTH_STATUS = {
  UNKNOWN: 'unknown',
  HEALTHY: 'healthy',
  DEGRADED: 'degraded',
  UNHEALTHY: 'unhealthy',
  OFFLINE: 'offline'
};

/**
 * Classe para monitorar a saúde da conexão com o Supabase
 */
class SupabaseHealthMonitor {
  constructor(supabaseClient, config = {}) {
    this.supabase = supabaseClient;
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.status = HEALTH_STATUS.UNKNOWN;
    this.latency = null;
    this.lastCheck = null;
    this.history = [];
    this.checkIntervalId = null;
    this.listeners = [];
    this.isOnline = isBrowser ? navigator.onLine : true;
    
    // Inicializar
    this._init();
  }
  
  /**
   * Inicializa o monitor de saúde
   * @private
   */
  _init() {
    if (!this.config.enabled) return;
    
    // Adicionar event listeners para status de conexão
    if (isBrowser) {
      window.addEventListener('online', this._handleOnline.bind(this));
      window.addEventListener('offline', this._handleOffline.bind(this));
    }
    
    // Iniciar verificações periódicas
    this._startChecks();
    
    this._log('Monitor de saúde inicializado');
  }
  
  /**
   * Inicia as verificações periódicas
   * @private
   */
  _startChecks() {
    if (this.checkIntervalId) return;
    
    // Fazer verificação inicial
    this.check();
    
    // Configurar verificações periódicas
    this.checkIntervalId = setInterval(() => {
      this.check();
    }, this.config.checkInterval);
    
    this._log(`Verificações periódicas iniciadas: ${this.config.checkInterval}ms`);
  }
  
  /**
   * Para as verificações periódicas
   * @private
   */
  _stopChecks() {
    if (this.checkIntervalId) {
      clearInterval(this.checkIntervalId);
      this.checkIntervalId = null;
      this._log('Verificações periódicas paradas');
    }
  }
  
  /**
   * Manipula evento de conexão online
   * @private
   */
  _handleOnline() {
    this.isOnline = true;
    this._log('Conexão online detectada');
    
    // Fazer verificação imediata
    this.check();
    
    // Iniciar verificações periódicas
    this._startChecks();
  }
  
  /**
   * Manipula evento de conexão offline
   * @private
   */
  _handleOffline() {
    this.isOnline = false;
    this._log('Conexão offline detectada');
    
    // Atualizar status
    this._updateStatus(HEALTH_STATUS.OFFLINE, null);
    
    // Parar verificações periódicas
    this._stopChecks();
  }
  
  /**
   * Atualiza o status de saúde
   * @private
   * @param {string} status Novo status
   * @param {number} latency Latência em milissegundos
   */
  _updateStatus(status, latency) {
    const oldStatus = this.status;
    const timestamp = Date.now();
    
    // Atualizar status
    this.status = status;
    this.latency = latency;
    this.lastCheck = timestamp;
    
    // Adicionar ao histórico
    this.history.unshift({
      timestamp,
      status,
      latency
    });
    
    // Limitar tamanho do histórico
    if (this.history.length > this.config.maxHistorySize) {
      this.history = this.history.slice(0, this.config.maxHistorySize);
    }
    
    // Notificar listeners se o status mudou
    if (oldStatus !== status) {
      this._notifyListeners();
    }
    
    this._log(`Status atualizado: ${status}`, { latency });
  }
  
  /**
   * Notifica os listeners sobre mudanças de status
   * @private
   */
  _notifyListeners() {
    const healthInfo = this.getHealthInfo();
    
    this.listeners.forEach(listener => {
      try {
        listener(healthInfo);
      } catch (error) {
        console.error('Erro ao notificar listener:', error);
      }
    });
  }
  
  /**
   * Registra mensagens de debug se o modo debug estiver ativado
   * @private
   * @param {string} message Mensagem de debug
   * @param {any} data Dados adicionais (opcional)
   */
  _log(message, data) {
    if (this.config.debug) {
      console.log(`[SupabaseHealth] ${message}`, data || '');
    }
  }
  
  /**
   * Verifica a saúde da conexão com o Supabase
   * @returns {Promise<Object>} Informações de saúde
   */
  async check() {
    // Verificar se está online
    if (!this.isOnline) {
      this._updateStatus(HEALTH_STATUS.OFFLINE, null);
      return this.getHealthInfo();
    }
    
    const startTime = Date.now();
    let status = HEALTH_STATUS.UNKNOWN;
    let latency = null;
    
    try {
      // Configurar timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Timeout')), this.config.timeoutThreshold);
      });
      
      // Fazer requisição de teste
      const checkPromise = this.supabase
        .from('app_settings')
        .select('key')
        .limit(1)
        .then(response => {
          // Calcular latência
          latency = Date.now() - startTime;
          
          // Verificar se houve erro
          if (response.error) {
            status = HEALTH_STATUS.UNHEALTHY;
            throw new Error(response.error.message);
          }
          
          // Determinar status com base na latência
          if (latency <= this.config.healthyLatencyThreshold) {
            status = HEALTH_STATUS.HEALTHY;
          } else if (latency <= this.config.warningLatencyThreshold) {
            status = HEALTH_STATUS.DEGRADED;
          } else {
            status = HEALTH_STATUS.UNHEALTHY;
          }
          
          return { status, latency };
        });
      
      // Executar com timeout
      await Promise.race([checkPromise, timeoutPromise]);
    } catch (error) {
      // Se for timeout, definir status como unhealthy
      if (error.message === 'Timeout') {
        status = HEALTH_STATUS.UNHEALTHY;
        latency = this.config.timeoutThreshold;
      } else {
        status = HEALTH_STATUS.UNHEALTHY;
        latency = Date.now() - startTime;
      }
      
      this._log(`Erro ao verificar saúde: ${error.message}`);
    }
    
    // Atualizar status
    this._updateStatus(status, latency);
    
    return this.getHealthInfo();
  }
  
  /**
   * Obtém informações de saúde
   * @returns {Object} Informações de saúde
   */
  getHealthInfo() {
    return {
      status: this.status,
      latency: this.latency,
      lastCheck: this.lastCheck,
      isOnline: this.isOnline,
      history: this.history
    };
  }
  
  /**
   * Adiciona um listener para mudanças de status
   * @param {Function} listener Função a ser chamada quando o status mudar
   * @returns {Function} Função para remover o listener
   */
  addListener(listener) {
    if (typeof listener !== 'function') {
      throw new Error('Listener deve ser uma função');
    }
    
    this.listeners.push(listener);
    
    // Retornar função para remover o listener
    return () => {
      this.removeListener(listener);
    };
  }
  
  /**
   * Remove um listener
   * @param {Function} listener Listener a ser removido
   */
  removeListener(listener) {
    this.listeners = this.listeners.filter(l => l !== listener);
  }
  
  /**
   * Para o monitor de saúde
   */
  stop() {
    this._stopChecks();
    
    if (isBrowser) {
      window.removeEventListener('online', this._handleOnline);
      window.removeEventListener('offline', this._handleOffline);
    }
    
    this.listeners = [];
    this._log('Monitor de saúde parado');
  }
}

// Exportar classe
export default SupabaseHealthMonitor;
