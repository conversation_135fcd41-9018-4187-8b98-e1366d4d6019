/**
 * Sistema de Notificações Avançado para MobiDrive
 * Suporte a push notifications, in-app notifications e notificações de emergência
 */

import { supabase } from '../lib/supabase'

interface NotificationConfig {
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error' | 'emergency'
  priority: 'low' | 'normal' | 'high' | 'critical'
  actions?: NotificationAction[]
  data?: Record<string, any>
  sound?: boolean
  vibrate?: boolean
  persistent?: boolean
}

interface NotificationAction {
  action: string
  title: string
  icon?: string
}

interface PushSubscription {
  endpoint: string
  keys: {
    p256dh: string
    auth: string
  }
}

class NotificationService {
  private registration: ServiceWorkerRegistration | null = null
  private subscription: PushSubscription | null = null
  private userId?: string
  private isSupported = 'Notification' in window && 'serviceWorker' in navigator
  private permission: NotificationPermission = 'default'

  constructor() {
    this.init()
  }

  // Inicializar serviço
  private async init(): Promise<void> {
    if (!this.isSupported) {
      console.warn('Notificações não suportadas neste navegador')
      return
    }

    this.permission = Notification.permission

    // Service Worker temporariamente desabilitado para evitar erros
    // try {
    //   // Registrar service worker
    //   if ('serviceWorker' in navigator) {
    //     this.registration = await navigator.serviceWorker.register('/sw.js')
    //     console.log('Service Worker registrado:', this.registration)
    //   }
    // } catch (error) {
    //   console.error('Erro ao registrar Service Worker:', error)
    // }
  }

  // Configurar usuário
  setUser(userId: string): void {
    this.userId = userId
    this.subscribeToUserNotifications()
  }

  // Solicitar permissão
  async requestPermission(): Promise<boolean> {
    if (!this.isSupported) return false

    if (this.permission === 'granted') return true

    const permission = await Notification.requestPermission()
    this.permission = permission

    if (permission === 'granted') {
      await this.subscribeToPush()
      return true
    }

    return false
  }

  // Inscrever-se para push notifications
  private async subscribeToPush(): Promise<void> {
    if (!this.registration) return

    try {
      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(
          import.meta.env.VITE_VAPID_PUBLIC_KEY || ''
        )
      })

      this.subscription = subscription as any

      // Salvar subscription no Supabase
      if (this.userId) {
        await supabase
          .from('push_subscriptions')
          .upsert({
            user_id: this.userId,
            endpoint: subscription.endpoint,
            p256dh: (subscription as any).keys.p256dh,
            auth: (subscription as any).keys.auth,
            created_at: new Date().toISOString()
          })
      }

      console.log('Push subscription criada:', subscription)
    } catch (error) {
      console.error('Erro ao criar push subscription:', error)
    }
  }

  // Inscrever-se para notificações do usuário via Supabase Realtime (desabilitado)
  private subscribeToUserNotifications(): void {
    if (!this.userId) return

    console.log('⚠️ Realtime notifications desabilitado para estabilidade');
    // Implementar polling como alternativa se necessário
  }

  // Mostrar notificação
  async showNotification(config: NotificationConfig): Promise<void> {
    // Notificação in-app sempre
    this.showInAppNotification(config)

    // Push notification se permitido
    if (this.permission === 'granted' && this.registration) {
      await this.showPushNotification(config)
    }

    // Efeitos especiais para emergência
    if (config.type === 'emergency') {
      this.handleEmergencyNotification(config)
    }
  }

  // Notificação in-app
  private showInAppNotification(config: NotificationConfig): void {
    // Criar elemento de notificação
    const notification = document.createElement('div')
    notification.className = `notification notification-${config.type} ${config.priority === 'critical' ? 'critical' : ''}`
    
    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-icon">
          ${this.getNotificationIcon(config.type)}
        </div>
        <div class="notification-text">
          <div class="notification-title">${config.title}</div>
          <div class="notification-message">${config.message}</div>
        </div>
        <button class="notification-close">&times;</button>
      </div>
    `

    // Adicionar estilos
    this.addNotificationStyles()

    // Adicionar ao DOM
    const container = this.getNotificationContainer()
    container.appendChild(notification)

    // Auto-remover (exceto para críticas)
    if (config.priority !== 'critical') {
      setTimeout(() => {
        this.removeNotification(notification)
      }, this.getNotificationDuration(config.type))
    }

    // Event listeners
    notification.querySelector('.notification-close')?.addEventListener('click', () => {
      this.removeNotification(notification)
    })

    // Som
    if (config.sound) {
      this.playNotificationSound(config.type)
    }

    // Vibração
    if (config.vibrate && 'vibrate' in navigator) {
      navigator.vibrate(this.getVibrationPattern(config.type))
    }
  }

  // Push notification
  private async showPushNotification(config: NotificationConfig): Promise<void> {
    if (!this.registration) return

    const options: NotificationOptions = {
      body: config.message,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/icon-72x72.png',
      tag: `mobidrive-${config.type}`,
      requireInteraction: config.priority === 'critical',
      silent: !config.sound,
      data: config.data,
      actions: config.actions
    }

    await this.registration.showNotification(config.title, options)
  }

  // Notificação de emergência
  private handleEmergencyNotification(config: NotificationConfig): void {
    // Som de emergência contínuo
    this.playEmergencySound()

    // Vibração intensa
    if ('vibrate' in navigator) {
      const pattern = [200, 100, 200, 100, 200, 100, 200]
      navigator.vibrate(pattern)
    }

    // Piscar tela
    this.flashScreen()

    // Notificação persistente
    document.body.classList.add('emergency-mode')
  }

  // Notificações específicas do MobiDrive
  notifyRideAccepted(driverName: string, eta: number, vehicleInfo: string): void {
    this.showNotification({
      title: '🚗 Motorista encontrado!',
      message: `${driverName} está chegando em ${eta} minutos (${vehicleInfo})`,
      type: 'success',
      priority: 'high',
      sound: true,
      vibrate: true,
      actions: [
        { action: 'call', title: 'Ligar', icon: '/icons/phone.png' },
        { action: 'message', title: 'Mensagem', icon: '/icons/message.png' }
      ]
    })
  }

  notifyDriverArrived(): void {
    this.showNotification({
      title: '📍 Motorista chegou!',
      message: 'Seu motorista está te esperando',
      type: 'info',
      priority: 'high',
      sound: true,
      vibrate: true
    })
  }

  notifyEmergencyActivated(): void {
    this.showNotification({
      title: '🚨 EMERGÊNCIA ATIVADA',
      message: 'Autoridades foram notificadas. Sua localização está sendo compartilhada.',
      type: 'emergency',
      priority: 'critical',
      sound: true,
      vibrate: true,
      persistent: true
    })
  }

  notifyRideCompleted(price: number): void {
    this.showNotification({
      title: '✅ Corrida finalizada',
      message: `Obrigado por usar o MobiDrive! Valor: R$ ${price.toFixed(2)}`,
      type: 'success',
      priority: 'normal',
      sound: true
    })
  }

  // Utilitários
  private getNotificationIcon(type: string): string {
    const icons = {
      info: '💬',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      emergency: '🚨'
    }
    return icons[type as keyof typeof icons] || '💬'
  }

  private getNotificationDuration(type: string): number {
    const durations = {
      info: 4000,
      success: 5000,
      warning: 7000,
      error: 8000,
      emergency: 0 // Não remove automaticamente
    }
    return durations[type as keyof typeof durations] || 4000
  }

  private getVibrationPattern(type: string): number[] {
    const patterns = {
      info: [100],
      success: [100, 50, 100],
      warning: [200, 100, 200],
      error: [300, 100, 300],
      emergency: [500, 200, 500, 200, 500]
    }
    return patterns[type as keyof typeof patterns] || [100]
  }

  private playNotificationSound(type: string): void {
    try {
      const audio = new Audio(`/sounds/notification-${type}.mp3`)
      audio.volume = 0.5
      audio.play().catch(() => {
        // Fallback para som padrão
        const fallback = new Audio('/sounds/notification.mp3')
        fallback.volume = 0.3
        fallback.play().catch(() => console.log('Som não disponível'))
      })
    } catch (error) {
      console.log('Erro ao tocar som:', error)
    }
  }

  private playEmergencySound(): void {
    try {
      const audio = new Audio('/sounds/emergency.mp3')
      audio.loop = true
      audio.volume = 0.8
      audio.play()

      // Parar após 30 segundos
      setTimeout(() => {
        audio.pause()
        audio.currentTime = 0
      }, 30000)
    } catch (error) {
      console.log('Som de emergência não disponível:', error)
    }
  }

  private flashScreen(): void {
    const overlay = document.createElement('div')
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: red;
      opacity: 0.3;
      z-index: 9999;
      pointer-events: none;
      animation: flash 0.5s ease-in-out 3;
    `

    document.body.appendChild(overlay)

    setTimeout(() => {
      document.body.removeChild(overlay)
    }, 1500)
  }

  private getNotificationContainer(): HTMLElement {
    let container = document.getElementById('notification-container')
    if (!container) {
      container = document.createElement('div')
      container.id = 'notification-container'
      container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
      `
      document.body.appendChild(container)
    }
    return container
  }

  private removeNotification(notification: HTMLElement): void {
    notification.style.animation = 'slideOut 0.3s ease-in-out'
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification)
      }
    }, 300)
  }

  private addNotificationStyles(): void {
    if (document.getElementById('notification-styles')) return

    const styles = document.createElement('style')
    styles.id = 'notification-styles'
    styles.textContent = `
      .notification {
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        margin-bottom: 12px;
        animation: slideIn 0.3s ease-out;
        border-left: 4px solid #3B82F6;
      }
      .notification-success { border-left-color: #10B981; }
      .notification-warning { border-left-color: #F59E0B; }
      .notification-error { border-left-color: #EF4444; }
      .notification-emergency { 
        border-left-color: #DC2626; 
        animation: pulse 1s infinite;
      }
      .notification-content {
        display: flex;
        align-items: center;
        padding: 16px;
      }
      .notification-icon {
        font-size: 24px;
        margin-right: 12px;
      }
      .notification-text {
        flex: 1;
      }
      .notification-title {
        font-weight: 600;
        margin-bottom: 4px;
      }
      .notification-message {
        color: #6B7280;
        font-size: 14px;
      }
      .notification-close {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #9CA3AF;
      }
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
      @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
      @keyframes flash {
        0%, 100% { opacity: 0; }
        50% { opacity: 0.3; }
      }
    `
    document.head.appendChild(styles)
  }

  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  // Status do serviço
  getStatus(): {
    isSupported: boolean
    permission: NotificationPermission
    hasSubscription: boolean
    userId?: string
  } {
    return {
      isSupported: this.isSupported,
      permission: this.permission,
      hasSubscription: !!this.subscription,
      userId: this.userId
    }
  }
}

// Singleton instance
export const notificationService = new NotificationService()

export default notificationService
