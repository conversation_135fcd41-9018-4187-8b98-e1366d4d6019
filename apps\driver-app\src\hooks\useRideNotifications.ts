import { useEffect, useState, useCallback } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContextSimple'

// 🚗 TIPOS DE NOTIFICAÇÃO DE CORRIDA
export interface RideNotification {
  id: string
  passenger_id: string
  passenger_name: string
  pickup_address: string
  destination_address: string
  pickup_lat: number
  pickup_lng: number
  destination_lat: number
  destination_lng: number
  estimated_price: number
  estimated_distance: number
  estimated_duration: number
  created_at: string
  expires_at: string
}

export interface NotificationState {
  currentRide: RideNotification | null
  isListening: boolean
  error: string | null
  timeRemaining: number
}

// 🔔 HOOK PARA ESCUTAR NOTIFICAÇÕES DE CORRIDAS EM TEMPO REAL
export const useRideNotifications = (isOnline: boolean = false) => {
  const { user } = useAuth()
  const [state, setState] = useState<NotificationState>({
    currentRide: null,
    isListening: false,
    error: null,
    timeRemaining: 0
  })

  // ⏰ TIMER PARA CONTAGEM REGRESSIVA
  const [timer, setTimer] = useState<NodeJS.Timeout | null>(null)

  // 🎯 FUNÇÃO PARA ACEITAR CORRIDA
  const acceptRide = useCallback(async (rideId: string) => {
    try {
      console.log('🚗 Aceitando corrida:', rideId)
      
      // 1. Atualizar status da corrida no banco
      const { error: updateError } = await supabase
        .from('rides')
        .update({ 
          status: 'accepted',
          driver_id: user?.id,
          accepted_at: new Date().toISOString()
        })
        .eq('id', rideId)

      if (updateError) {
        throw updateError
      }

      // 2. Notificar o passageiro via broadcast
      await supabase.channel('passenger-notifications').send({
        type: 'broadcast',
        event: 'ride_accepted',
        payload: {
          ride_id: rideId,
          driver_id: user?.id,
          driver_name: user?.user_metadata?.full_name || 'Motorista',
          message: 'Sua corrida foi aceita! O motorista está a caminho.'
        }
      })

      // 3. Limpar notificação atual
      setState(prev => ({ ...prev, currentRide: null, timeRemaining: 0 }))
      
      // 4. Limpar timer
      if (timer) {
        clearInterval(timer)
        setTimer(null)
      }

      console.log('✅ Corrida aceita com sucesso!')
      return true

    } catch (error) {
      console.error('❌ Erro ao aceitar corrida:', error)
      setState(prev => ({ ...prev, error: 'Erro ao aceitar corrida' }))
      return false
    }
  }, [user, timer])

  // 🚫 FUNÇÃO PARA RECUSAR CORRIDA
  const rejectRide = useCallback(async (rideId: string) => {
    try {
      console.log('🚫 Recusando corrida:', rideId)
      
      // 1. Atualizar status da corrida no banco
      const { error: updateError } = await supabase
        .from('rides')
        .update({ 
          status: 'rejected',
          rejected_at: new Date().toISOString()
        })
        .eq('id', rideId)

      if (updateError) {
        throw updateError
      }

      // 2. Limpar notificação atual
      setState(prev => ({ ...prev, currentRide: null, timeRemaining: 0 }))
      
      // 3. Limpar timer
      if (timer) {
        clearInterval(timer)
        setTimer(null)
      }

      console.log('✅ Corrida recusada')
      return true

    } catch (error) {
      console.error('❌ Erro ao recusar corrida:', error)
      setState(prev => ({ ...prev, error: 'Erro ao recusar corrida' }))
      return false
    }
  }, [timer])

  // 🔔 CONFIGURAR LISTENER DE NOTIFICAÇÕES
  useEffect(() => {
    if (!user || !isOnline) {
      setState(prev => ({ ...prev, isListening: false, currentRide: null }))
      return
    }

    console.log('🔔 Iniciando listener de notificações para motorista:', user.id)
    
    setState(prev => ({ ...prev, isListening: true, error: null }))

    // Criar canal para escutar notificações
    const channel = supabase.channel('driver-notifications')
      .on('broadcast', { event: 'new-ride-request' }, (payload) => {
        console.log('🚗 Nova solicitação de corrida recebida:', payload)
        
        const rideData = payload.payload as RideNotification
        
        // Verificar se a corrida ainda está válida
        const expiresAt = new Date(rideData.expires_at)
        const now = new Date()
        
        if (expiresAt > now) {
          setState(prev => ({ 
            ...prev, 
            currentRide: rideData,
            timeRemaining: Math.floor((expiresAt.getTime() - now.getTime()) / 1000)
          }))

          // Iniciar contagem regressiva
          const countdownTimer = setInterval(() => {
            setState(prev => {
              const newTime = prev.timeRemaining - 1
              
              if (newTime <= 0) {
                // Tempo esgotado - auto-rejeitar
                clearInterval(countdownTimer)
                rejectRide(rideData.id)
                return { ...prev, currentRide: null, timeRemaining: 0 }
              }
              
              return { ...prev, timeRemaining: newTime }
            })
          }, 1000)

          setTimer(countdownTimer)

          // Tocar som de notificação (se disponível)
          try {
            const audio = new Audio('/notification-sound.mp3')
            audio.play().catch(e => console.log('Som de notificação não disponível'))
          } catch (e) {
            console.log('Som de notificação não disponível')
          }

          // Vibrar (se disponível)
          if ('vibrate' in navigator) {
            navigator.vibrate([200, 100, 200])
          }
        }
      })
      .subscribe((status) => {
        console.log('📡 Status do canal de notificações:', status)
        if (status === 'SUBSCRIBED') {
          console.log('✅ Conectado ao canal de notificações!')
        }
      })

    // Cleanup
    return () => {
      console.log('🔌 Desconectando do canal de notificações')
      channel.unsubscribe()
      setState(prev => ({ ...prev, isListening: false, currentRide: null }))
      
      if (timer) {
        clearInterval(timer)
        setTimer(null)
      }
    }
  }, [user, isOnline, rejectRide, timer])

  return {
    ...state,
    acceptRide,
    rejectRide
  }
}
