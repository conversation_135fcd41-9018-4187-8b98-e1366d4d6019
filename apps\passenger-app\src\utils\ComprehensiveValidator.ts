import { supabase } from '../lib/supabase';
import { mapboxService } from '../services/MapboxService';
import { rideService } from '../services/RideService';
import { chatService } from '../services/ChatService';
import { simpleNotificationService } from '../services/SimpleNotificationService';
import { analyticsService } from '../services/AnalyticsService';
import { integrationTestService } from '../services/IntegrationTestService';
import { systemHealthService } from '../services/SystemHealthService';

export interface ValidationResult {
  component: string;
  status: 'passed' | 'failed' | 'warning';
  message: string;
  details?: any;
  timestamp: string;
  duration: number;
}

export interface ComprehensiveReport {
  timestamp: string;
  overallStatus: 'healthy' | 'issues' | 'critical';
  totalTests: number;
  passed: number;
  failed: number;
  warnings: number;
  categories: {
    systemHealth: ValidationResult[];
    integration: ValidationResult[];
    userJourney: ValidationResult[];
    database: ValidationResult[];
    performance: ValidationResult[];
    pwa: ValidationResult[];
  };
  criticalIssues: string[];
  recommendations: string[];
  deploymentReady: boolean;
}

export class ComprehensiveValidator {
  private results: ValidationResult[] = [];
  private startTime: number = Date.now();

  async runCompleteValidation(): Promise<ComprehensiveReport> {
    console.log('🔍 Iniciando validação completa do MobiDrive...');

    // 1. Testes de Saúde do Sistema
    await this.validateSystemHealth();

    // 2. Testes de Integração
    await this.validateIntegration();

    // 3. Testes de Jornada do Usuário
    await this.validateUserJourney();

    // 4. Testes de Banco de Dados
    await this.validateDatabase();

    // 5. Testes de Performance
    await this.validatePerformance();

    // 6. Testes de PWA
    await this.validatePWA();

    return this.generateComprehensiveReport();
  }

  // 1. VALIDAÇÃO DE SAÚDE DO SISTEMA
  async validateSystemHealth(): Promise<void> {
    console.log('🏥 Validando saúde do sistema...');

    try {
      const healthReport = await systemHealthService.fullHealthCheck();
      
      for (const component of healthReport.components) {
        this.addResult(
          'systemHealth',
          `System Health - ${component.component}`,
          component.status === 'healthy' ? 'passed' : 
          component.status === 'degraded' ? 'warning' : 'failed',
          `${component.component}: ${component.status} (${component.responseTime}ms)`,
          component
        );
      }

      // Verificar problemas críticos
      if (healthReport.criticalIssues.length > 0) {
        this.addResult(
          'systemHealth',
          'Critical Issues Check',
          'failed',
          `${healthReport.criticalIssues.length} problemas críticos encontrados`,
          healthReport.criticalIssues
        );
      } else {
        this.addResult(
          'systemHealth',
          'Critical Issues Check',
          'passed',
          'Nenhum problema crítico encontrado'
        );
      }

    } catch (error) {
      this.addResult(
        'systemHealth',
        'System Health Check',
        'failed',
        `Erro na verificação de saúde: ${error}`,
        error
      );
    }
  }

  // 2. VALIDAÇÃO DE INTEGRAÇÃO
  async validateIntegration(): Promise<void> {
    console.log('🔗 Validando integrações...');

    try {
      const integrationReport = await integrationTestService.runFullIntegrationTest();
      
      for (const test of integrationReport.tests) {
        this.addResult(
          'integration',
          test.testName,
          test.status,
          test.message,
          test.details
        );
      }

      // Verificar integrações específicas
      await this.validateRideChatIntegration();
      await this.validateNotificationIntegration();
      await this.validateAnalyticsIntegration();

    } catch (error) {
      this.addResult(
        'integration',
        'Integration Tests',
        'failed',
        `Erro nos testes de integração: ${error}`,
        error
      );
    }
  }

  // 3. VALIDAÇÃO DE JORNADA DO USUÁRIO
  async validateUserJourney(): Promise<void> {
    console.log('👤 Validando jornadas do usuário...');

    // Verificar autenticação
    await this.validateAuthenticationFlow();
    
    // Verificar dashboard
    await this.validateDashboardAccess();
    
    // Verificar solicitação de corrida
    await this.validateRideRequestFlow();
    
    // Verificar tracking
    await this.validateRideTrackingFlow();
  }

  // 4. VALIDAÇÃO DE BANCO DE DADOS
  async validateDatabase(): Promise<void> {
    console.log('🗄️ Validando banco de dados...');

    try {
      // Verificar tabelas principais
      const tables = [
        'profiles', 'ride_requests', 'driver_locations', 
        'notifications', 'chat_messages', 'notification_settings',
        'push_subscriptions', 'analytics_sessions', 'analytics_events'
      ];

      for (const table of tables) {
        try {
          const { data, error } = await supabase.from(table).select('count').limit(1);
          
          if (error) {
            this.addResult(
              'database',
              `Table Check - ${table}`,
              'failed',
              `Tabela ${table} não acessível: ${error.message}`,
              error
            );
          } else {
            this.addResult(
              'database',
              `Table Check - ${table}`,
              'passed',
              `Tabela ${table} acessível`
            );
          }
        } catch (err) {
          this.addResult(
            'database',
            `Table Check - ${table}`,
            'failed',
            `Erro ao acessar tabela ${table}: ${err}`,
            err
          );
        }
      }

      // Verificar RLS
      await this.validateRLS();

      // Verificar relacionamentos
      await this.validateRelationships();

    } catch (error) {
      this.addResult(
        'database',
        'Database Validation',
        'failed',
        `Erro na validação do banco: ${error}`,
        error
      );
    }
  }

  // 5. VALIDAÇÃO DE PERFORMANCE
  async validatePerformance(): Promise<void> {
    console.log('⚡ Validando performance...');

    try {
      // Verificar Web Vitals
      if ('performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        const metrics = {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
          firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
        };

        // Avaliar métricas
        if (metrics.loadTime < 3000) {
          this.addResult('performance', 'Load Time', 'passed', `Tempo de carregamento: ${metrics.loadTime}ms`, metrics);
        } else if (metrics.loadTime < 5000) {
          this.addResult('performance', 'Load Time', 'warning', `Tempo de carregamento alto: ${metrics.loadTime}ms`, metrics);
        } else {
          this.addResult('performance', 'Load Time', 'failed', `Tempo de carregamento crítico: ${metrics.loadTime}ms`, metrics);
        }

        if (metrics.domContentLoaded < 2000) {
          this.addResult('performance', 'DOM Content Loaded', 'passed', `DOM carregado em: ${metrics.domContentLoaded}ms`, metrics);
        } else {
          this.addResult('performance', 'DOM Content Loaded', 'warning', `DOM carregamento lento: ${metrics.domContentLoaded}ms`, metrics);
        }
      }

      // Verificar tamanho do bundle
      await this.validateBundleSize();

    } catch (error) {
      this.addResult(
        'performance',
        'Performance Validation',
        'failed',
        `Erro na validação de performance: ${error}`,
        error
      );
    }
  }

  // 6. VALIDAÇÃO DE PWA
  async validatePWA(): Promise<void> {
    console.log('📱 Validando PWA...');

    try {
      // Service Worker
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.getRegistration();
        if (registration) {
          this.addResult('pwa', 'Service Worker', 'passed', 'Service Worker registrado e ativo');
        } else {
          this.addResult('pwa', 'Service Worker', 'warning', 'Service Worker não registrado');
        }
      } else {
        this.addResult('pwa', 'Service Worker', 'failed', 'Service Worker não suportado');
      }

      // Manifest
      const manifestLink = document.querySelector('link[rel="manifest"]');
      if (manifestLink) {
        this.addResult('pwa', 'Web App Manifest', 'passed', 'Manifest encontrado');
      } else {
        this.addResult('pwa', 'Web App Manifest', 'warning', 'Manifest não encontrado');
      }

      // Notificações
      if ('Notification' in window) {
        const permission = Notification.permission;
        if (permission === 'granted') {
          this.addResult('pwa', 'Push Notifications', 'passed', 'Notificações permitidas');
        } else if (permission === 'default') {
          this.addResult('pwa', 'Push Notifications', 'warning', 'Permissão de notificações não solicitada');
        } else {
          this.addResult('pwa', 'Push Notifications', 'failed', 'Notificações negadas');
        }
      } else {
        this.addResult('pwa', 'Push Notifications', 'failed', 'API de notificações não suportada');
      }

      // Cache
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        if (cacheNames.length > 0) {
          this.addResult('pwa', 'Cache Storage', 'passed', `${cacheNames.length} caches encontrados`);
        } else {
          this.addResult('pwa', 'Cache Storage', 'warning', 'Nenhum cache encontrado');
        }
      } else {
        this.addResult('pwa', 'Cache Storage', 'failed', 'Cache API não suportada');
      }

    } catch (error) {
      this.addResult(
        'pwa',
        'PWA Validation',
        'failed',
        `Erro na validação PWA: ${error}`,
        error
      );
    }
  }

  // MÉTODOS AUXILIARES DE VALIDAÇÃO

  private async validateRideChatIntegration(): Promise<void> {
    try {
      // Verificar se chat pode ser associado a corridas
      const { data: rides } = await supabase.from('ride_requests').select('id').limit(1);
      const { data: messages } = await supabase.from('chat_messages').select('ride_id').limit(1);
      
      this.addResult(
        'integration',
        'Ride-Chat Integration',
        'passed',
        'Integração ride-chat verificada'
      );
    } catch (error) {
      this.addResult(
        'integration',
        'Ride-Chat Integration',
        'failed',
        `Erro na integração ride-chat: ${error}`
      );
    }
  }

  private async validateNotificationIntegration(): Promise<void> {
    try {
      await simpleNotificationService.requestPermission();
      this.addResult(
        'integration',
        'Notification Integration',
        'passed',
        'Sistema de notificações integrado'
      );
    } catch (error) {
      this.addResult(
        'integration',
        'Notification Integration',
        'warning',
        `Notificações com limitações: ${error}`
      );
    }
  }

  private async validateAnalyticsIntegration(): Promise<void> {
    try {
      analyticsService.track('validation_test', { test: true });
      const stats = analyticsService.getSessionStats();
      
      this.addResult(
        'integration',
        'Analytics Integration',
        'passed',
        'Analytics funcionando',
        stats
      );
    } catch (error) {
      this.addResult(
        'integration',
        'Analytics Integration',
        'failed',
        `Erro no analytics: ${error}`
      );
    }
  }

  private async validateAuthenticationFlow(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        this.addResult('userJourney', 'Authentication Flow', 'passed', 'Usuário autenticado');
      } else {
        this.addResult('userJourney', 'Authentication Flow', 'warning', 'Usuário não autenticado');
      }
    } catch (error) {
      this.addResult('userJourney', 'Authentication Flow', 'failed', `Erro na autenticação: ${error}`);
    }
  }

  private async validateDashboardAccess(): Promise<void> {
    try {
      // Verificar se elementos do dashboard estão presentes
      const dashboardElements = document.querySelectorAll('[data-testid="dashboard"]');
      
      if (dashboardElements.length > 0 || window.location.pathname.includes('dashboard')) {
        this.addResult('userJourney', 'Dashboard Access', 'passed', 'Dashboard acessível');
      } else {
        this.addResult('userJourney', 'Dashboard Access', 'warning', 'Dashboard não detectado na página atual');
      }
    } catch (error) {
      this.addResult('userJourney', 'Dashboard Access', 'failed', `Erro no acesso ao dashboard: ${error}`);
    }
  }

  private async validateRideRequestFlow(): Promise<void> {
    try {
      // Verificar se o serviço de corridas está funcionando
      const estimate = await mapboxService.calculateRideEstimate(
        { lat: -23.5505, lng: -46.6333 },
        { lat: -23.5489, lng: -46.6388 }
      );
      
      if (estimate) {
        this.addResult('userJourney', 'Ride Request Flow', 'passed', 'Fluxo de solicitação funcional');
      } else {
        this.addResult('userJourney', 'Ride Request Flow', 'warning', 'Estimativa de corrida não calculada');
      }
    } catch (error) {
      this.addResult('userJourney', 'Ride Request Flow', 'failed', `Erro no fluxo de corrida: ${error}`);
    }
  }

  private async validateRideTrackingFlow(): Promise<void> {
    try {
      // Verificar se o tracking está configurado
      const { data: rides } = await supabase.from('ride_requests').select('id').limit(1);
      
      this.addResult('userJourney', 'Ride Tracking Flow', 'passed', 'Sistema de tracking configurado');
    } catch (error) {
      this.addResult('userJourney', 'Ride Tracking Flow', 'failed', `Erro no tracking: ${error}`);
    }
  }

  private async validateRLS(): Promise<void> {
    try {
      // Tentar acessar dados sem autenticação adequada
      const { error } = await supabase.from('profiles').select('*').limit(1);
      
      if (error && error.message.includes('RLS')) {
        this.addResult('database', 'RLS Validation', 'passed', 'RLS funcionando corretamente');
      } else {
        this.addResult('database', 'RLS Validation', 'warning', 'RLS pode não estar configurado adequadamente');
      }
    } catch (error) {
      this.addResult('database', 'RLS Validation', 'failed', `Erro na validação RLS: ${error}`);
    }
  }

  private async validateRelationships(): Promise<void> {
    try {
      // Verificar relacionamentos entre tabelas
      const { data, error } = await supabase
        .from('ride_requests')
        .select(`
          id,
          user_id,
          profiles!inner(id, email)
        `)
        .limit(1);

      if (!error) {
        this.addResult('database', 'Relationships', 'passed', 'Relacionamentos funcionando');
      } else {
        this.addResult('database', 'Relationships', 'warning', 'Alguns relacionamentos podem ter problemas');
      }
    } catch (error) {
      this.addResult('database', 'Relationships', 'failed', `Erro nos relacionamentos: ${error}`);
    }
  }

  private async validateBundleSize(): Promise<void> {
    try {
      // Verificar tamanho dos recursos carregados
      const resources = performance.getEntriesByType('resource');
      const totalSize = resources.reduce((sum, resource: any) => {
        return sum + (resource.transferSize || 0);
      }, 0);

      const totalSizeMB = totalSize / (1024 * 1024);

      if (totalSizeMB < 5) {
        this.addResult('performance', 'Bundle Size', 'passed', `Tamanho total: ${totalSizeMB.toFixed(2)}MB`);
      } else if (totalSizeMB < 10) {
        this.addResult('performance', 'Bundle Size', 'warning', `Tamanho alto: ${totalSizeMB.toFixed(2)}MB`);
      } else {
        this.addResult('performance', 'Bundle Size', 'failed', `Tamanho crítico: ${totalSizeMB.toFixed(2)}MB`);
      }
    } catch (error) {
      this.addResult('performance', 'Bundle Size', 'warning', 'Não foi possível medir o tamanho do bundle');
    }
  }

  private addResult(
    category: keyof ComprehensiveReport['categories'],
    component: string,
    status: 'passed' | 'failed' | 'warning',
    message: string,
    details?: any
  ): void {
    const result: ValidationResult = {
      component,
      status,
      message,
      details,
      timestamp: new Date().toISOString(),
      duration: Date.now() - this.startTime
    };

    this.results.push(result);
    
    const emoji = status === 'passed' ? '✅' : status === 'failed' ? '❌' : '⚠️';
    console.log(`${emoji} [${category}] ${component}: ${message}`);
  }

  private generateComprehensiveReport(): ComprehensiveReport {
    const passed = this.results.filter(r => r.status === 'passed').length;
    const failed = this.results.filter(r => r.status === 'failed').length;
    const warnings = this.results.filter(r => r.status === 'warning').length;

    const overallStatus = failed > 0 ? 'critical' : warnings > 3 ? 'issues' : 'healthy';
    const deploymentReady = failed === 0 && warnings < 5;

    const criticalIssues = this.results
      .filter(r => r.status === 'failed')
      .map(r => `${r.component}: ${r.message}`);

    const recommendations = [];
    if (warnings > 0) {
      recommendations.push(`Resolver ${warnings} avisos para melhorar a qualidade`);
    }
    if (failed > 0) {
      recommendations.push(`Corrigir ${failed} problemas críticos antes do deploy`);
    }
    if (deploymentReady) {
      recommendations.push('Sistema pronto para deploy em produção');
    }

    return {
      timestamp: new Date().toISOString(),
      overallStatus,
      totalTests: this.results.length,
      passed,
      failed,
      warnings,
      categories: {
        systemHealth: this.results.filter(r => r.component.includes('System Health') || r.component.includes('Critical Issues')),
        integration: this.results.filter(r => r.component.includes('Integration') || r.component.includes('Test')),
        userJourney: this.results.filter(r => r.component.includes('Authentication') || r.component.includes('Dashboard') || r.component.includes('Ride')),
        database: this.results.filter(r => r.component.includes('Table') || r.component.includes('RLS') || r.component.includes('Relationships')),
        performance: this.results.filter(r => r.component.includes('Load') || r.component.includes('DOM') || r.component.includes('Bundle')),
        pwa: this.results.filter(r => r.component.includes('Service Worker') || r.component.includes('Manifest') || r.component.includes('Notifications') || r.component.includes('Cache'))
      },
      criticalIssues,
      recommendations,
      deploymentReady
    };
  }
}

export const comprehensiveValidator = new ComprehensiveValidator();
