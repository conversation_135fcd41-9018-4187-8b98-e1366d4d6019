/* 📱 ESTILOS MOBILE NATIVOS - ANTI-ZOOM TOTAL */
/* Garante que a página mobile seja 100% nativa sem interferência do zoom do browser */

/* RESET GLOBAL PARA MOBILE */
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* ANTI-ZOOM TOTAL - MÁXIMA PRIORIDADE */
html, body {
  zoom: 1 !important;
  transform: scale(1) !important;
  transform-origin: top left !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
  
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  
  /* Previne zoom por CSS */
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
  
  /* Previne zoom por touch */
  touch-action: manipulation !important;
  -ms-touch-action: manipulation !important;
}

/* VIEWPORT META FORÇADO VIA CSS */
@viewport {
  width: device-width;
  initial-scale: 1.0;
  maximum-scale: 1.0;
  minimum-scale: 1.0;
  user-scalable: no;
}

/* WEBKIT VIEWPORT */
@-webkit-viewport {
  width: device-width;
  initial-scale: 1.0;
  maximum-scale: 1.0;
  minimum-scale: 1.0;
  user-scalable: no;
}

/* MOZ VIEWPORT */
@-moz-viewport {
  width: device-width;
  initial-scale: 1.0;
  maximum-scale: 1.0;
  minimum-scale: 1.0;
  user-scalable: no;
}

/* MS VIEWPORT */
@-ms-viewport {
  width: device-width;
  initial-scale: 1.0;
  maximum-scale: 1.0;
  minimum-scale: 1.0;
  user-scalable: no;
}

/* O VIEWPORT */
@-o-viewport {
  width: device-width;
  initial-scale: 1.0;
  maximum-scale: 1.0;
  minimum-scale: 1.0;
  user-scalable: no;
}

/* CONTAINER MOBILE NATIVO */
.mobile-native-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
  
  /* Anti-zoom específico */
  zoom: 1 !important;
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  -moz-transform: scale(1) !important;
  -ms-transform: scale(1) !important;
  -o-transform: scale(1) !important;
  
  /* Previne interações que podem causar zoom */
  touch-action: manipulation !important;
  -ms-touch-action: manipulation !important;
  
  /* Font rendering otimizado para mobile */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* INPUTS MOBILE NATIVOS */
.mobile-native-container input,
.mobile-native-container textarea,
.mobile-native-container select {
  /* Previne zoom automático em inputs no iOS */
  font-size: 16px !important;
  
  /* Estilo mobile nativo */
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 12px 16px;
  background-color: #fff;
  
  /* Anti-zoom */
  zoom: 1 !important;
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  
  /* Touch otimizado */
  touch-action: manipulation !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* BOTÕES MOBILE NATIVOS */
.mobile-native-container button {
  /* Estilo mobile nativo */
  border-radius: 12px;
  border: none;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  
  /* Anti-zoom */
  zoom: 1 !important;
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
  
  /* Touch otimizado */
  touch-action: manipulation !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  
  /* Feedback visual */
  transition: all 0.2s ease;
}

.mobile-native-container button:active {
  transform: scale(0.98) !important;
  -webkit-transform: scale(0.98) !important;
}

/* LINKS MOBILE NATIVOS */
.mobile-native-container a {
  color: #00ff88;
  text-decoration: none;
  font-weight: 500;
  
  /* Touch otimizado */
  touch-action: manipulation !important;
  
  /* Anti-zoom */
  zoom: 1 !important;
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
}

.mobile-native-container a:active {
  opacity: 0.7;
}

/* SCROLLING MOBILE NATIVO */
.mobile-native-container .scrollable {
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  
  /* Anti-zoom durante scroll */
  zoom: 1 !important;
  transform: scale(1) !important;
  -webkit-transform: scale(1) !important;
}

/* MEDIA QUERIES PARA DIFERENTES TAMANHOS */
@media screen and (max-width: 480px) {
  .mobile-native-container {
    font-size: 14px;
  }
  
  .mobile-native-container input,
  .mobile-native-container button {
    font-size: 16px !important; /* Previne zoom no iOS */
  }
}

@media screen and (min-width: 481px) and (max-width: 768px) {
  .mobile-native-container {
    font-size: 15px;
  }
}

/* ORIENTAÇÃO LANDSCAPE */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .mobile-native-container .logo-section {
    margin: 20px 0 !important;
  }
  
  .mobile-native-container .logo-container {
    width: 60px !important;
    height: 60px !important;
  }
}

/* DARK MODE SUPPORT */
@media (prefers-color-scheme: dark) {
  .mobile-native-container {
    background-color: #1a1a1a;
    color: #fff;
  }
  
  .mobile-native-container .login-card {
    background-color: #2a2a2a;
    color: #fff;
  }
  
  .mobile-native-container input {
    background-color: #333;
    color: #fff;
    border-color: #555;
  }
}

/* HIGH DPI DISPLAYS */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-native-container {
    /* Otimizações para telas de alta densidade */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
