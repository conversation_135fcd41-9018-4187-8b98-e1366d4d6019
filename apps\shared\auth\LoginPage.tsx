import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from './AuthContext';
import { LoginForm, LoginFormProps } from './LoginForm';
import { MobiDriveLogo } from '../components/MobiDriveLogo';

export interface LoginPageProps extends Omit<LoginFormProps, 'logoComponent'> {
  backgroundClassName?: string;
  containerClassName?: string;
  logoSize?: 'sm' | 'md' | 'lg' | 'xl' | number;
  logoVariant?: 'default' | 'compact' | 'icon';
  showLogo?: boolean;
  redirectIfAuthenticated?: string;
  animationDuration?: number;
}

/**
 * Shared Login Page Component
 *
 * This component provides a standardized login page for all MobiDrive apps.
 * It includes animations, responsive design, and automatic redirection for authenticated users.
 */
export const LoginPage: React.FC<LoginPageProps> = ({
  backgroundClassName = "min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4",
  containerClassName = "w-full max-w-md",
  logoSize = 'lg',
  logoVariant = 'default',
  showLogo = true,
  redirectIfAuthenticated = '/dashboard',
  animationDuration = 0.5,
  appType = 'passenger',
  ...loginFormProps
}) => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();

  // Redirect if already authenticated
  useEffect(() => {
    if (user && !isLoading) {
      navigate(redirectIfAuthenticated);
    }
  }, [user, isLoading, navigate, redirectIfAuthenticated]);

  // Create logo component
  const logoComponent = showLogo ? (
    <div className="flex justify-center">
      <MobiDriveLogo
        variant={logoVariant}
        size={logoSize}
        appType={appType as 'admin' | 'driver' | 'passenger'}
        isDarkMode={appType === 'admin' ? true : appType === 'driver' ? true : false}
      />
    </div>
  ) : null;

  // Determine title and description based on app type
  const getAppSpecificProps = () => {
    switch (appType) {
      case 'admin':
        return {
          title: loginFormProps.title || 'Admin Access',
          description: loginFormProps.description || 'Sign in with your administrator credentials',
          demoCredentials: loginFormProps.demoCredentials || {
            email: '<EMAIL>',
            password: 'admin123',
          },
        };
      case 'driver':
        return {
          title: loginFormProps.title || 'Driver Login',
          description: loginFormProps.description || 'Sign in to your driver account',
          demoCredentials: loginFormProps.demoCredentials || {
            email: '<EMAIL>',
            password: 'driver123',
          },
        };
      case 'passenger':
      default:
        return {
          title: loginFormProps.title || 'Passenger Login',
          description: loginFormProps.description || 'Sign in to your passenger account',
          demoCredentials: loginFormProps.demoCredentials || {
            email: '<EMAIL>',
            password: 'passenger123',
          },
        };
    }
  };

  const appSpecificProps = getAppSpecificProps();

  return (
    <div className={backgroundClassName}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: animationDuration }}
        className={containerClassName}
      >
        <LoginForm
          {...loginFormProps}
          {...appSpecificProps}
          logoComponent={logoComponent}
          appType={appType}
        />
      </motion.div>
    </div>
  );
};

export default LoginPage;
