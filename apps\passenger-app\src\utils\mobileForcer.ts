// 📱 MOBILE FORCER
// Força a experiência mobile mesmo em desktop usando mockup

export const isDesktopDevice = (): boolean => {
  if (typeof window === 'undefined') return false
  
  const userAgent = navigator.userAgent.toLowerCase()
  const screenWidth = window.screen.width
  const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  
  // Detectar se é mobile real
  const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i
  const isMobileUA = mobileRegex.test(userAgent)
  const isMobileScreen = screenWidth <= 768
  
  // Se não é mobile real E tem tela grande = desktop
  const isDesktop = !isMobileUA && !isMobileScreen && screenWidth > 1024
  
  return isDesktop
}

export const shouldUseMockup = (): boolean => {
  return isDesktopDevice()
}

// Hook para usar o detector
import { useState, useEffect } from 'react'

export const useMobileForcer = () => {
  const [shouldMockup, setShouldMockup] = useState(false)

  useEffect(() => {
    setShouldMockup(shouldUseMockup())
    
    const handleResize = () => {
      setShouldMockup(shouldUseMockup())
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return shouldMockup
}

// Log de informações
export const logMobileForcer = () => {
  const isDesktop = isDesktopDevice()
  const shouldMockup = shouldUseMockup()
  
  console.log('📱 MOBILE FORCER:', {
    isDesktop,
    shouldMockup,
    screenWidth: window.screen.width,
    userAgent: navigator.userAgent.substring(0, 50) + '...',
    touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0
  })
}
