// =====================================================
// DEBUG DOS MÉTODOS DE PAGAMENTO
// Analisa detalhadamente o que está acontecendo
// =====================================================

import puppeteer from 'puppeteer'

async function debugPaymentMethods() {
  console.log('🔍 DEBUG: MÉTODOS DE PAGAMENTO')
  console.log('=' .repeat(50))

  let browser = null
  
  try {
    browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })

    const page = await browser.newPage()
    await page.setViewport({ width: 1280, height: 720 })

    // Monitorar console para logs específicos
    page.on('console', (msg) => {
      const text = msg.text()
      if (text.includes('Payment methods loaded:') || 
          text.includes('unique payment methods') ||
          text.includes('PAYMENT_METHODS') ||
          text.includes('realPaymentMethods')) {
        console.log(`📱 ${text}`)
      }
    })

    console.log('\n🔐 Fazendo login primeiro...')
    await page.goto('http://localhost:3000/login', { 
      waitUntil: 'domcontentloaded',
      timeout: 15000 
    })
    
    // Fazer login
    await page.type('input[type="email"]', '<EMAIL>')
    await page.type('input[type="password"]', 'Test123!')
    await page.click('button[type="submit"]')
    
    console.log('⏱️ Aguardando redirecionamento...')
    await page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 10000 })
    
    console.log('🗺️ Navegando para detalhes...')
    await page.goto('http://localhost:3000/ride-request/details', { 
      waitUntil: 'domcontentloaded',
      timeout: 10000 
    })
    
    console.log('⏱️ Aguardando carregamento dos métodos...')
    await new Promise(resolve => setTimeout(resolve, 10000))
    
    // Analisar estrutura da página
    const pageAnalysis = await page.evaluate(() => {
      // Procurar por seções de pagamento
      const paymentSections = Array.from(document.querySelectorAll('*')).filter(el => {
        const text = el.textContent || ''
        return text.includes('Forma de Pagamento') || 
               text.includes('Payment') ||
               text.includes('Método')
      })
      
      // Procurar por todos os botões
      const allButtons = Array.from(document.querySelectorAll('button'))
      
      // Procurar por elementos com texto de pagamento
      const paymentElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const text = el.textContent || ''
        return text.includes('Dinheiro') || 
               text.includes('Cartão') || 
               text.includes('PIX') ||
               text.includes('💵') ||
               text.includes('💳') ||
               text.includes('📱')
      })
      
      return {
        paymentSections: paymentSections.length,
        totalButtons: allButtons.length,
        paymentElements: paymentElements.length,
        paymentTexts: paymentElements.map(el => el.textContent?.trim()).slice(0, 10)
      }
    })
    
    console.log('\n📊 ANÁLISE DA PÁGINA:')
    console.log(`🔍 Seções de pagamento: ${pageAnalysis.paymentSections}`)
    console.log(`🔘 Total de botões: ${pageAnalysis.totalButtons}`)
    console.log(`💳 Elementos de pagamento: ${pageAnalysis.paymentElements}`)
    console.log(`📝 Textos encontrados:`, pageAnalysis.paymentTexts)
    
    // Verificar se há erros de carregamento
    const hasErrors = await page.evaluate(() => {
      const errorElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const text = el.textContent || ''
        return text.includes('erro') || 
               text.includes('Error') ||
               text.includes('failed') ||
               text.includes('loading')
      })
      return errorElements.map(el => el.textContent?.trim())
    })
    
    if (hasErrors.length > 0) {
      console.log('\n❌ ERROS ENCONTRADOS:')
      hasErrors.forEach(error => console.log(`  - ${error}`))
    }
    
    // Aguardar mais um pouco e verificar novamente
    console.log('\n⏱️ Aguardando mais 5 segundos...')
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    const finalCheck = await page.evaluate(() => {
      const paymentButtons = Array.from(document.querySelectorAll('button')).filter(btn => {
        const text = btn.textContent || ''
        return text.includes('Dinheiro') || 
               text.includes('Cartão') || 
               text.includes('PIX')
      })
      
      return {
        found: paymentButtons.length,
        texts: paymentButtons.map(btn => btn.textContent?.trim())
      }
    })
    
    console.log('\n🎯 VERIFICAÇÃO FINAL:')
    console.log(`💳 Métodos encontrados: ${finalCheck.found}`)
    console.log(`📝 Textos:`, finalCheck.texts)
    
    return finalCheck.found >= 3

  } catch (error) {
    console.error('💥 Erro no debug:', error.message)
    return false
  } finally {
    // Manter navegador aberto para inspeção manual
    console.log('\n🔍 Navegador mantido aberto para inspeção manual...')
    console.log('Pressione Ctrl+C para fechar')
    await new Promise(resolve => setTimeout(resolve, 60000))
    
    if (browser) {
      await browser.close()
    }
  }
}

debugPaymentMethods()
  .then((success) => {
    console.log('\n🏁 DEBUG CONCLUÍDO!')
    console.log(success ? '✅ MÉTODOS ENCONTRADOS' : '❌ PROBLEMA IDENTIFICADO')
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
  })
