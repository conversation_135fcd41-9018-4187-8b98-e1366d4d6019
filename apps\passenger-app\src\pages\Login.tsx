import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Navigate } from 'react-router-dom'
import { Mail, Lock, Eye, EyeOff, ArrowRight } from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { GradientBackground } from '../components/GradientBackground'
import { Car3DLogin } from '../components/Car3DLogin'
import { useNoZoom } from '../hooks/useNoZoom'
import '../styles/no-zoom.css'

// 📱 LOGIN MOBILE-FIRST - ZOOM COMPLETAMENTE DESABILITADO
// Experiência mobile nativa com scroll apenas vertical
// DESIGN ORIGINAL MANTIDO + CONVERSÃO ANDROID NATIVA

export const Login: React.FC = () => {
  const { signIn, loading, error, user } = useAuth()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  // 🚫 DESABILITA ZOOM COMPLETAMENTE
  useNoZoom()

  // Redirect se já logado
  if (user) {
    return <Navigate to="/dashboard" replace />
  }

  // Submit do formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await signIn(email, password)
  }

  // Animações simples e limpas (MANTENDO ORIGINAIS)
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">

      {/* Background Gradient Sutil (MANTIDO ORIGINAL) */}
      <GradientBackground
        variant="static"
        opacity={0.7}
      />

      {/* Overlay muito sutil para legibilidade (MANTIDO ORIGINAL) */}
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Conteúdo Principal (MANTIDO ORIGINAL) */}
      <div className="relative z-10 flex flex-col h-full min-h-screen">

        {/* Header com Logo (MANTIDO ORIGINAL) */}
        <motion.div
          className="pt-8 pb-6 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <img
                src="/icons/icon-48x48.png"
                alt="MobiDrive"
                className="w-6 h-6"
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-xs text-white/70">Sua jornada começa aqui</p>
            </div>
          </div>
        </motion.div>

        {/* Formulário Central (MANTIDO ORIGINAL) */}
        <div className="flex-1 flex items-center justify-center px-4">
          <motion.div
            className="w-full max-w-xs"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.div
              className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl"
              variants={itemVariants}
            >
              {/* Título do Formulário (MANTIDO ORIGINAL) */}
              <motion.div
                className="text-center mb-6"
                variants={itemVariants}
              >
                <h2 className="text-xl font-semibold text-white mb-2">
                  Bem-vindo de volta
                </h2>
                <p className="text-white/70 text-sm">
                  Entre na sua conta para continuar
                </p>
              </motion.div>

              {/* Formulário (MANTIDO ORIGINAL) */}
              <motion.form
                onSubmit={handleSubmit}
                className="space-y-4"
                variants={itemVariants}
              >
                {/* Campo Email (MANTIDO ORIGINAL) */}
                <motion.div variants={itemVariants}>
                  <label htmlFor="email" className="block text-sm font-medium text-white/90 mb-2">
                    Email
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </motion.div>

                {/* Campo Senha (MANTIDO ORIGINAL) */}
                <motion.div variants={itemVariants}>
                  <label htmlFor="password" className="block text-sm font-medium text-white/90 mb-2">
                    Senha
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                    <input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      placeholder="••••••••"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                    >
                      {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </motion.div>

                {/* Mensagem de Erro (MANTIDO ORIGINAL) */}
                <AnimatePresence>
                  {error && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="bg-red-500/20 border border-red-400/30 rounded-xl p-3"
                    >
                      <p className="text-red-200 text-sm">{error}</p>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Botão de Submit (MANTIDO ORIGINAL) */}
                <motion.button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {loading ? (
                    <motion.div
                      className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                  ) : (
                    <>
                      <span>Entrar</span>
                      <ArrowRight className="w-4 h-4" />
                    </>
                  )}
                </motion.button>
              </motion.form>

              {/* Link de Cadastro (MANTIDO ORIGINAL) */}
              <motion.div
                className="mt-6 text-center"
                variants={itemVariants}
              >
                <p className="text-white/70 text-sm">
                  Não tem uma conta?{' '}
                  <motion.a
                    href="/register"
                    className="text-blue-400 font-medium hover:text-blue-300 transition-colors"
                    whileHover={{ scale: 1.05 }}
                  >
                    Cadastre-se
                  </motion.a>
                </p>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>

        {/* Footer Simples (MANTIDO ORIGINAL) */}
        <motion.div
          className="pb-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <p className="text-white/50 text-xs">
            © 2024 MobiDrive. Todos os direitos reservados.
          </p>
        </motion.div>
      </div>

      {/* Componente 3D (MANTIDO ORIGINAL) */}
      <Car3DLogin />
    </div>
  )
}

export default Login
