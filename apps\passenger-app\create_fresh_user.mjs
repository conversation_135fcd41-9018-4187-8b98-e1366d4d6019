// =====================================================
// CRIAR USUÁRIO NOVO E TESTAR LOGIN IMEDIATAMENTE
// Cria usuário com email único e testa login
// =====================================================

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function createFreshUser() {
  console.log('👤 CRIANDO USUÁRIO NOVO PARA TESTE')
  console.log('=' .repeat(50))
  
  // Gerar email único baseado no timestamp
  const timestamp = Date.now()
  const email = `test${timestamp}@mobidrive.com`
  const password = 'test123456'
  
  console.log(`📧 Email: ${email}`)
  console.log(`🔒 Senha: ${password}`)
  
  try {
    console.log('\n🔄 Criando usuário...')
    
    const { data, error } = await supabase.auth.signUp({
      email: email,
      password: password,
      options: {
        data: {
          full_name: 'Usuário de Teste',
          user_type: 'passenger'
        }
      }
    })
    
    if (error) {
      console.log('❌ Erro ao criar usuário:', error.message)
      return null
    }
    
    console.log('✅ Usuário criado com sucesso!')
    console.log(`👤 ID: ${data.user?.id}`)
    console.log(`📧 Email: ${data.user?.email}`)
    console.log(`📅 Criado em: ${data.user?.created_at}`)
    console.log(`✉️ Email confirmado: ${data.user?.email_confirmed_at ? 'SIM' : 'NÃO'}`)
    
    return { email, password, user: data.user }
    
  } catch (error) {
    console.error('💥 Erro inesperado:', error.message)
    return null
  }
}

async function testLoginDirectly(email, password) {
  console.log('\n🧪 TESTANDO LOGIN DIRETO VIA API')
  console.log('-' .repeat(40))
  
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email,
      password: password
    })
    
    if (error) {
      console.log('❌ Erro no login direto:', error.message)
      return false
    }
    
    console.log('✅ Login direto bem-sucedido!')
    console.log(`👤 Usuário: ${data.user?.email}`)
    console.log(`🔑 Token: ${data.session?.access_token ? 'Presente' : 'Ausente'}`)
    console.log(`⏰ Expira em: ${data.session?.expires_at}`)
    
    // Fazer logout
    await supabase.auth.signOut()
    console.log('🚪 Logout realizado')
    
    return true
    
  } catch (error) {
    console.error('💥 Erro no teste direto:', error.message)
    return false
  }
}

async function testLoginInBrowser(email, password) {
  console.log('\n🌐 TESTANDO LOGIN NO NAVEGADOR')
  console.log('-' .repeat(40))
  
  const puppeteer = await import('puppeteer')
  
  let browser = null
  try {
    browser = await puppeteer.default.launch({
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })
    
    const page = await browser.newPage()
    await page.setViewport({ width: 1280, height: 720 })
    
    // Monitorar console
    let loginSuccess = false
    let authError = null
    
    page.on('console', (msg) => {
      if (msg.text().includes('✅') && msg.text().includes('login')) {
        loginSuccess = true
        console.log(`🎯 ${msg.text()}`)
      } else if (msg.text().includes('❌') && msg.text().includes('login')) {
        console.log(`❌ ${msg.text()}`)
      }
    })
    
    page.on('response', (response) => {
      if (response.url().includes('auth') && response.status() === 400) {
        response.text().then(body => {
          authError = body
          console.log(`🔴 Auth error: ${body}`)
        }).catch(() => {})
      } else if (response.url().includes('auth') && response.status() === 200) {
        console.log(`🟢 Auth success: ${response.status()}`)
      }
    })
    
    // Navegar para login
    console.log('🌐 Navegando para página de login...')
    await page.goto('http://localhost:3000/login', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    })
    
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // Preencher e submeter
    const emailField = await page.$('input[type="email"]')
    const passwordField = await page.$('input[type="password"]')
    
    if (emailField && passwordField) {
      console.log('📝 Preenchendo campos...')
      
      await emailField.click({ clickCount: 3 })
      await emailField.type(email, { delay: 50 })
      
      await passwordField.click({ clickCount: 3 })
      await passwordField.type(password, { delay: 50 })
      
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const submitButton = await page.$('button[type="submit"]')
      if (submitButton) {
        console.log('🖱️ Clicando em submit...')
        await submitButton.click()
        
        // Aguardar resposta
        await new Promise(resolve => setTimeout(resolve, 8000))
        
        // Verificar redirecionamento
        const currentUrl = page.url()
        const isLoggedIn = !currentUrl.includes('/login')
        
        if (isLoggedIn) {
          console.log('✅ LOGIN NO NAVEGADOR SUCESSO!')
          console.log(`🌐 Redirecionado para: ${currentUrl}`)
          return true
        } else {
          console.log('❌ Login no navegador falhou')
          if (authError) {
            console.log(`📄 Erro: ${authError}`)
          }
          return false
        }
      } else {
        console.log('❌ Botão de submit não encontrado')
        return false
      }
    } else {
      console.log('❌ Campos de login não encontrados')
      return false
    }
    
  } catch (error) {
    console.error('💥 Erro no teste do navegador:', error.message)
    return false
  } finally {
    if (browser) {
      await browser.close()
    }
  }
}

async function main() {
  console.log('🎯 CRIAÇÃO E TESTE DE USUÁRIO NOVO')
  console.log('=' .repeat(60))
  
  // Criar usuário
  const userInfo = await createFreshUser()
  
  if (!userInfo) {
    console.log('❌ Falha ao criar usuário')
    return
  }
  
  // Testar login direto
  const directLoginSuccess = await testLoginDirectly(userInfo.email, userInfo.password)
  
  // Testar login no navegador
  const browserLoginSuccess = await testLoginInBrowser(userInfo.email, userInfo.password)
  
  // Relatório final
  console.log('\n' + '=' .repeat(60))
  console.log('📊 RELATÓRIO FINAL')
  console.log('=' .repeat(60))
  
  console.log(`👤 Usuário criado: ✅`)
  console.log(`📧 Email: ${userInfo.email}`)
  console.log(`🔒 Senha: ${userInfo.password}`)
  console.log(`🔗 Login direto (API): ${directLoginSuccess ? '✅' : '❌'}`)
  console.log(`🌐 Login navegador: ${browserLoginSuccess ? '✅' : '❌'}`)
  
  if (directLoginSuccess && browserLoginSuccess) {
    console.log('\n🎉 SUCESSO TOTAL!')
    console.log('✅ Usuário funcional criado e testado!')
    console.log('\n🔑 CREDENCIAIS PARA USO:')
    console.log(`   📧 Email: ${userInfo.email}`)
    console.log(`   🔒 Senha: ${userInfo.password}`)
  } else if (directLoginSuccess && !browserLoginSuccess) {
    console.log('\n⚠️ PROBLEMA NO NAVEGADOR')
    console.log('✅ API funciona, mas navegador tem problema')
    console.log('💡 Pode ser problema na interface ou JavaScript')
  } else if (!directLoginSuccess && !browserLoginSuccess) {
    console.log('\n❌ PROBLEMA GERAL')
    console.log('❌ Nem API nem navegador funcionam')
    console.log('💡 Problema nas configurações do Supabase')
  } else {
    console.log('\n🤔 SITUAÇÃO ESTRANHA')
    console.log('⚠️ Navegador funciona mas API não')
  }
}

main()
  .then(() => {
    console.log('\n🏁 Script concluído!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
