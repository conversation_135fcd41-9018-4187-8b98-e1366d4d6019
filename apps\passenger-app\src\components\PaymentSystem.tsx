import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CreditCard, Smartphone, DollarSign, Check, X, Plus, Trash2, Shield } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContextSimple'
import { analyticsService } from '../services/AnalyticsService'
import { notificationService } from '../services/NotificationService'

interface PaymentMethod {
  id: string
  type: 'credit_card' | 'debit_card' | 'pix' | 'digital_wallet'
  name: string
  last_four?: string
  brand?: string
  is_default: boolean
  expires_at?: string
}

interface PaymentSystemProps {
  rideId: string
  amount: number
  onPaymentComplete: (paymentId: string) => void
  onCancel: () => void
}

export const PaymentSystem: React.FC<PaymentSystemProps> = ({
  rideId,
  amount,
  onPaymentComplete,
  onCancel
}) => {
  const { user } = useAuth()
  const [currentStep, setCurrentStep] = useState<'methods' | 'new_card' | 'processing' | 'success' | 'error'>('methods')
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [newCardData, setNewCardData] = useState({
    number: '',
    name: '',
    expiry: '',
    cvv: '',
    save_card: true
  })

  // Carregar métodos de pagamento
  useEffect(() => {
    loadPaymentMethods()
  }, [user?.id])

  const loadPaymentMethods = async () => {
    if (!user?.id) return

    try {
      setIsLoading(true)
      const { data: methods, error } = await supabase
        .from('payment_methods')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('is_default', { ascending: false })

      if (error) throw error

      setPaymentMethods(methods || [])
      
      // Selecionar método padrão automaticamente
      const defaultMethod = methods?.find(m => m.is_default)
      if (defaultMethod) {
        setSelectedMethod(defaultMethod.id)
      }
    } catch (error) {
      console.error('Erro ao carregar métodos de pagamento:', error)
      setError('Erro ao carregar métodos de pagamento')
    } finally {
      setIsLoading(false)
    }
  }

  // Processar pagamento
  const processPayment = async () => {
    if (!selectedMethod && currentStep !== 'new_card') return

    setCurrentStep('processing')
    
    try {
      let paymentMethodId = selectedMethod

      // Se for cartão novo, criar método de pagamento primeiro
      if (currentStep === 'new_card' || !selectedMethod) {
        paymentMethodId = await createNewPaymentMethod()
      }

      // Criar transação de pagamento
      const { data: payment, error: paymentError } = await supabase
        .from('payments')
        .insert({
          ride_id: rideId,
          user_id: user?.id,
          payment_method_id: paymentMethodId,
          amount: amount,
          currency: 'BRL',
          status: 'processing',
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (paymentError) throw paymentError

      // Simular processamento do pagamento
      await simulatePaymentProcessing(payment.id)

      // Analytics
      analyticsService.track('payment_completed', {
        ride_id: rideId,
        payment_id: payment.id,
        amount: amount,
        payment_method_type: getPaymentMethodType(paymentMethodId)
      })

      setCurrentStep('success')
      
      // Notificar sucesso após 2 segundos
      setTimeout(() => {
        onPaymentComplete(payment.id)
      }, 2000)

    } catch (error) {
      console.error('Erro no pagamento:', error)
      setError('Falha no processamento do pagamento')
      setCurrentStep('error')
      
      analyticsService.track('payment_failed', {
        ride_id: rideId,
        amount: amount,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  // Criar novo método de pagamento
  const createNewPaymentMethod = async (): Promise<string> => {
    const { data: method, error } = await supabase
      .from('payment_methods')
      .insert({
        user_id: user?.id,
        type: 'credit_card',
        name: `**** ${newCardData.number.slice(-4)}`,
        last_four: newCardData.number.slice(-4),
        brand: detectCardBrand(newCardData.number),
        is_default: paymentMethods.length === 0,
        expires_at: `20${newCardData.expiry.slice(-2)}-${newCardData.expiry.slice(0, 2)}-01`,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error
    return method.id
  }

  // Simular processamento do pagamento
  const simulatePaymentProcessing = async (paymentId: string) => {
    // Simular delay de processamento
    await new Promise(resolve => setTimeout(resolve, 3000))

    // Atualizar status do pagamento
    const { error } = await supabase
      .from('payments')
      .update({
        status: 'completed',
        processed_at: new Date().toISOString()
      })
      .eq('id', paymentId)

    if (error) throw error
  }

  // Detectar bandeira do cartão
  const detectCardBrand = (number: string): string => {
    const cleaned = number.replace(/\s/g, '')
    if (cleaned.startsWith('4')) return 'visa'
    if (cleaned.startsWith('5') || cleaned.startsWith('2')) return 'mastercard'
    if (cleaned.startsWith('3')) return 'amex'
    return 'unknown'
  }

  // Obter tipo do método de pagamento
  const getPaymentMethodType = (methodId: string | null): string => {
    if (!methodId) return 'new_card'
    const method = paymentMethods.find(m => m.id === methodId)
    return method?.type || 'unknown'
  }

  // Formatar número do cartão
  const formatCardNumber = (value: string) => {
    const cleaned = value.replace(/\s/g, '')
    const match = cleaned.match(/.{1,4}/g)
    return match ? match.join(' ') : cleaned
  }

  // Formatar data de expiração
  const formatExpiry = (value: string) => {
    const cleaned = value.replace(/\D/g, '')
    if (cleaned.length >= 2) {
      return cleaned.slice(0, 2) + '/' + cleaned.slice(2, 4)
    }
    return cleaned
  }

  // Renderizar métodos de pagamento
  const renderPaymentMethods = () => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">Pagamento</h3>
        <p className="text-gray-600">Total: R$ {amount.toFixed(2)}</p>
      </div>

      {paymentMethods.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Métodos salvos</h4>
          {paymentMethods.map((method) => (
            <motion.button
              key={method.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setSelectedMethod(method.id)}
              className={`w-full p-4 border-2 rounded-xl transition-colors text-left ${
                selectedMethod === method.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded flex items-center justify-center">
                    <CreditCard className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{method.name}</p>
                    <p className="text-sm text-gray-500 capitalize">{method.brand} • {method.type.replace('_', ' ')}</p>
                  </div>
                </div>
                {method.is_default && (
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                    Padrão
                  </span>
                )}
              </div>
            </motion.button>
          ))}
        </div>
      )}

      <div className="border-t border-gray-200 pt-4">
        <button
          onClick={() => setCurrentStep('new_card')}
          className="w-full p-4 border-2 border-dashed border-gray-300 rounded-xl text-gray-600 hover:border-blue-500 hover:text-blue-600 transition-colors flex items-center justify-center space-x-2"
        >
          <Plus className="w-5 h-5" />
          <span>Adicionar novo cartão</span>
        </button>
      </div>

      {/* PIX Option */}
      <div className="border-t border-gray-200 pt-4">
        <button
          onClick={() => {
            // TODO: Implementar PIX
            notificationService.showNotification({
              title: '🚧 PIX em breve',
              message: 'Pagamento via PIX será disponibilizado em breve',
              type: 'info',
              priority: 'normal'
            })
          }}
          className="w-full p-4 border-2 border-gray-200 rounded-xl text-gray-600 hover:border-green-500 hover:text-green-600 transition-colors flex items-center justify-center space-x-2"
        >
          <Smartphone className="w-5 h-5" />
          <span>Pagar com PIX</span>
        </button>
      </div>
    </div>
  )

  // Renderizar formulário de novo cartão
  const renderNewCardForm = () => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">Novo Cartão</h3>
        <p className="text-gray-600">Total: R$ {amount.toFixed(2)}</p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Número do cartão
          </label>
          <input
            type="text"
            value={newCardData.number}
            onChange={(e) => {
              const formatted = formatCardNumber(e.target.value)
              if (formatted.replace(/\s/g, '').length <= 16) {
                setNewCardData(prev => ({ ...prev, number: formatted }))
              }
            }}
            placeholder="1234 5678 9012 3456"
            className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nome no cartão
          </label>
          <input
            type="text"
            value={newCardData.name}
            onChange={(e) => setNewCardData(prev => ({ ...prev, name: e.target.value.toUpperCase() }))}
            placeholder="NOME COMPLETO"
            className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Validade
            </label>
            <input
              type="text"
              value={newCardData.expiry}
              onChange={(e) => {
                const formatted = formatExpiry(e.target.value)
                if (formatted.length <= 5) {
                  setNewCardData(prev => ({ ...prev, expiry: formatted }))
                }
              }}
              placeholder="MM/AA"
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CVV
            </label>
            <input
              type="text"
              value={newCardData.cvv}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, '')
                if (value.length <= 4) {
                  setNewCardData(prev => ({ ...prev, cvv: value }))
                }
              }}
              placeholder="123"
              className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="save_card"
            checked={newCardData.save_card}
            onChange={(e) => setNewCardData(prev => ({ ...prev, save_card: e.target.checked }))}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
          <label htmlFor="save_card" className="text-sm text-gray-700">
            Salvar cartão para próximas compras
          </label>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 flex items-start space-x-3">
        <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
        <div className="text-sm text-blue-800">
          <p className="font-medium">Seus dados estão seguros</p>
          <p>Utilizamos criptografia de ponta para proteger suas informações</p>
        </div>
      </div>
    </div>
  )

  // Renderizar processamento
  const renderProcessing = () => (
    <div className="text-center space-y-6">
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto"
      />
      <div>
        <h3 className="text-xl font-bold text-gray-900 mb-2">Processando pagamento...</h3>
        <p className="text-gray-600">Aguarde enquanto confirmamos sua transação</p>
      </div>
    </div>
  )

  // Renderizar sucesso
  const renderSuccess = () => (
    <div className="text-center space-y-6">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className="w-20 h-20 bg-green-100 rounded-full mx-auto flex items-center justify-center"
      >
        <Check className="w-10 h-10 text-green-600" />
      </motion.div>
      
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Pagamento aprovado!</h3>
        <p className="text-gray-600">R$ {amount.toFixed(2)} • Transação concluída</p>
      </div>
    </div>
  )

  // Renderizar erro
  const renderError = () => (
    <div className="text-center space-y-6">
      <div className="w-20 h-20 bg-red-100 rounded-full mx-auto flex items-center justify-center">
        <X className="w-10 h-10 text-red-600" />
      </div>
      
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Pagamento recusado</h3>
        <p className="text-gray-600">{error || 'Tente novamente ou use outro método'}</p>
      </div>
    </div>
  )

  const isFormValid = () => {
    if (currentStep === 'new_card') {
      return newCardData.number.replace(/\s/g, '').length === 16 &&
             newCardData.name.length > 0 &&
             newCardData.expiry.length === 5 &&
             newCardData.cvv.length >= 3
    }
    return selectedMethod !== null
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="bg-white rounded-2xl w-full max-w-md max-h-[80vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              {currentStep === 'methods' && 'Pagamento'}
              {currentStep === 'new_card' && 'Novo Cartão'}
              {currentStep === 'processing' && 'Processando'}
              {currentStep === 'success' && 'Sucesso'}
              {currentStep === 'error' && 'Erro'}
            </h2>
            {!['processing', 'success'].includes(currentStep) && (
              <button
                onClick={onCancel}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <AnimatePresence mode="wait">
            {currentStep === 'methods' && (
              <motion.div
                key="methods"
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: 20, opacity: 0 }}
              >
                {renderPaymentMethods()}
              </motion.div>
            )}
            
            {currentStep === 'new_card' && (
              <motion.div
                key="new_card"
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: 20, opacity: 0 }}
              >
                {renderNewCardForm()}
              </motion.div>
            )}
            
            {currentStep === 'processing' && (
              <motion.div
                key="processing"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                {renderProcessing()}
              </motion.div>
            )}
            
            {currentStep === 'success' && (
              <motion.div
                key="success"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
              >
                {renderSuccess()}
              </motion.div>
            )}
            
            {currentStep === 'error' && (
              <motion.div
                key="error"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
              >
                {renderError()}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Footer */}
        {['methods', 'new_card'].includes(currentStep) && (
          <div className="p-6 border-t border-gray-100">
            <div className="flex space-x-3">
              {currentStep === 'new_card' && (
                <button
                  onClick={() => setCurrentStep('methods')}
                  className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-colors"
                >
                  Voltar
                </button>
              )}
              
              <button
                onClick={processPayment}
                disabled={!isFormValid()}
                className="flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white font-semibold py-3 px-4 rounded-xl transition-colors flex items-center justify-center space-x-2"
              >
                <DollarSign className="w-4 h-4" />
                <span>Pagar R$ {amount.toFixed(2)}</span>
              </button>
            </div>
          </div>
        )}

        {currentStep === 'error' && (
          <div className="p-6 border-t border-gray-100">
            <div className="flex space-x-3">
              <button
                onClick={() => setCurrentStep('methods')}
                className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-colors"
              >
                Tentar novamente
              </button>
              
              <button
                onClick={onCancel}
                className="flex-1 bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-xl transition-colors"
              >
                Cancelar
              </button>
            </div>
          </div>
        )}
      </motion.div>
    </motion.div>
  )
}

export default PaymentSystem
