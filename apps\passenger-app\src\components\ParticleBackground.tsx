import React, { useRef, useMemo } from 'react'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'

interface ParticleBackgroundProps {
  count?: number
  color?: string
  size?: number
  speed?: number
}

export const ParticleBackground: React.FC<ParticleBackgroundProps> = ({
  count = 100,
  color = '#ffffff',
  size = 0.02,
  speed = 0.5
}) => {
  const meshRef = useRef<THREE.Points>(null)
  
  // Gerar posições aleatórias das partículas
  const particles = useMemo(() => {
    const positions = new Float32Array(count * 3)
    const velocities = new Float32Array(count * 3)
    
    for (let i = 0; i < count; i++) {
      // Posições aleatórias em um cubo
      positions[i * 3] = (Math.random() - 0.5) * 20     // x
      positions[i * 3 + 1] = (Math.random() - 0.5) * 20 // y
      positions[i * 3 + 2] = (Math.random() - 0.5) * 20 // z
      
      // Velocidades aleatórias pequenas
      velocities[i * 3] = (Math.random() - 0.5) * 0.01     // vx
      velocities[i * 3 + 1] = (Math.random() - 0.5) * 0.01 // vy
      velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.01 // vz
    }
    
    return { positions, velocities }
  }, [count])
  
  // Animação das partículas
  useFrame((state) => {
    if (!meshRef.current) return
    
    const positions = meshRef.current.geometry.attributes.position.array as Float32Array
    const time = state.clock.elapsedTime * speed
    
    for (let i = 0; i < count; i++) {
      const i3 = i * 3
      
      // Movimento flutuante baseado no tempo
      positions[i3] += Math.sin(time + i * 0.1) * 0.001     // x
      positions[i3 + 1] += Math.cos(time + i * 0.1) * 0.001 // y
      positions[i3 + 2] += Math.sin(time * 0.5 + i * 0.05) * 0.001 // z
      
      // Resetar partículas que saem dos limites
      if (positions[i3] > 10) positions[i3] = -10
      if (positions[i3] < -10) positions[i3] = 10
      if (positions[i3 + 1] > 10) positions[i3 + 1] = -10
      if (positions[i3 + 1] < -10) positions[i3 + 1] = 10
      if (positions[i3 + 2] > 10) positions[i3 + 2] = -10
      if (positions[i3 + 2] < -10) positions[i3 + 2] = 10
    }
    
    meshRef.current.geometry.attributes.position.needsUpdate = true
  })
  
  return (
    <points ref={meshRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={count}
          array={particles.positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        color={color}
        size={size}
        transparent
        opacity={0.6}
        sizeAttenuation={true}
        blending={THREE.AdditiveBlending}
      />
    </points>
  )
}

// Componente de estrelas para o fundo
export const StarField: React.FC = () => {
  const starsRef = useRef<THREE.Points>(null)
  
  const starPositions = useMemo(() => {
    const positions = new Float32Array(200 * 3)
    
    for (let i = 0; i < 200; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 50     // x
      positions[i * 3 + 1] = (Math.random() - 0.5) * 50 // y
      positions[i * 3 + 2] = (Math.random() - 0.5) * 50 // z
    }
    
    return positions
  }, [])
  
  useFrame((state) => {
    if (!starsRef.current) return
    
    // Rotação lenta das estrelas
    starsRef.current.rotation.y = state.clock.elapsedTime * 0.05
    starsRef.current.rotation.x = state.clock.elapsedTime * 0.02
  })
  
  return (
    <points ref={starsRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={200}
          array={starPositions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        color="#ffffff"
        size={0.01}
        transparent
        opacity={0.8}
        sizeAttenuation={true}
      />
    </points>
  )
}

// Componente de névoa/fumaça ambiente
export const AmbientFog: React.FC = () => {
  const fogRef = useRef<THREE.Points>(null)
  
  const fogPositions = useMemo(() => {
    const positions = new Float32Array(50 * 3)
    
    for (let i = 0; i < 50; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 30     // x
      positions[i * 3 + 1] = (Math.random() - 0.5) * 10 // y (mais baixo)
      positions[i * 3 + 2] = (Math.random() - 0.5) * 30 // z
    }
    
    return positions
  }, [])
  
  useFrame((state) => {
    if (!fogRef.current) return
    
    const positions = fogRef.current.geometry.attributes.position.array as Float32Array
    const time = state.clock.elapsedTime * 0.2
    
    for (let i = 0; i < 50; i++) {
      const i3 = i * 3
      
      // Movimento lento e suave da névoa
      positions[i3] += Math.sin(time + i * 0.2) * 0.002
      positions[i3 + 1] += Math.cos(time + i * 0.15) * 0.001
      positions[i3 + 2] += Math.sin(time * 0.3 + i * 0.1) * 0.002
    }
    
    fogRef.current.geometry.attributes.position.needsUpdate = true
  })
  
  return (
    <points ref={fogRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={50}
          array={fogPositions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        color="#666666"
        size={0.1}
        transparent
        opacity={0.1}
        sizeAttenuation={true}
        blending={THREE.AdditiveBlending}
      />
    </points>
  )
}

export default ParticleBackground
