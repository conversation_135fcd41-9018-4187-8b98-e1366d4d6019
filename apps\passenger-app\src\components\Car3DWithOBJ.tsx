import React, { useRef, useState, useEffect, Suspense } from 'react'
import { Canvas, useFrame, useLoader } from '@react-three/fiber'
import { Environment, useTexture } from '@react-three/drei'
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader'
import * as THREE from 'three'

// Componente que carrega o modelo .obj real
const OBJCarModel: React.FC<{ manualRotation: [number, number, number] }> = ({ manualRotation }) => {
  const carRef = useRef<THREE.Group>(null)
  const [loadError, setLoadError] = useState<string | null>(null)

  // Tentar carregar o modelo .obj
  let carModel: THREE.Group | null = null
  let textures: any = null

  try {
    console.log('🚗 Tentando carregar modelo .obj...')
    carModel = useLoader(OBJLoader, '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/base.obj')
    console.log('✅ Modelo .obj carregado com sucesso:', carModel)

    // Carregar texturas PBR
    textures = useTexture({
      diffuse: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_diffuse.png',
      metallic: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_metallic.png',
      normal: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_normal.png',
      roughness: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_roughness.png'
    })
    console.log('✅ Texturas PBR carregadas com sucesso:', textures)
  } catch (error) {
    console.error('❌ Erro ao carregar modelo/texturas:', error)
    setLoadError(error instanceof Error ? error.message : 'Erro desconhecido')
    throw error // Re-throw para que o Suspense capture
  }

  useFrame((state) => {
    if (carRef.current) {
      // Rotação baseada no scroll da página
      const scrollY = window.scrollY || 0
      const maxScroll = Math.max(document.body.scrollHeight - window.innerHeight, 1)
      const scrollProgress = Math.min(scrollY / maxScroll, 1)

      // Combinar rotação do scroll + rotação manual + rotação automática lenta
      carRef.current.rotation.x = manualRotation[0] + Math.sin(scrollProgress * Math.PI) * 0.1
      carRef.current.rotation.y = manualRotation[1] + scrollProgress * Math.PI * 2 + state.clock.elapsedTime * 0.02
      carRef.current.rotation.z = manualRotation[2]

      // Movimento sutil para cima e para baixo
      carRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  // Aplicar materiais PBR ao modelo
  useEffect(() => {
    if (carModel && textures) {
      console.log('🎨 Aplicando materiais PBR ao modelo...')
      carModel.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          // Criar material PBR com as texturas carregadas
          const material = new THREE.MeshStandardMaterial({
            map: textures.diffuse,
            metalnessMap: textures.metallic,
            normalMap: textures.normal,
            roughnessMap: textures.roughness,
            metalness: 0.8,
            roughness: 0.2,
          })

          child.material = material
          child.castShadow = true
          child.receiveShadow = true
          console.log('✅ Material aplicado a:', child.name || 'mesh sem nome')
        }
      })
    }
  }, [carModel, textures])

  if (loadError) {
    console.error('❌ Erro no componente OBJCarModel:', loadError)
    return null
  }

  return (
    <group ref={carRef} position={[0, 0, 0]} scale={[5, 5, 5]}>
      {carModel && <primitive object={carModel} />}
    </group>
  )
}

// Componente de fallback melhorado
const FallbackCarModel: React.FC<{ manualRotation: [number, number, number] }> = ({ manualRotation }) => {
  const carRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (carRef.current) {
      const scrollY = window.scrollY || 0
      const maxScroll = Math.max(document.body.scrollHeight - window.innerHeight, 1)
      const scrollProgress = Math.min(scrollY / maxScroll, 1)

      carRef.current.rotation.x = manualRotation[0] + Math.sin(scrollProgress * Math.PI) * 0.1
      carRef.current.rotation.y = manualRotation[1] + scrollProgress * Math.PI * 2 + state.clock.elapsedTime * 0.02
      carRef.current.rotation.z = manualRotation[2]
      carRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={carRef} position={[0, 0, 0]} scale={[1.2, 1.2, 1.2]}>
      {/* Corpo principal do carro */}
      <mesh position={[0, 0.5, 0]} castShadow receiveShadow>
        <boxGeometry args={[4.5, 1.2, 2.2]} />
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.95}
          roughness={0.05}
          envMapIntensity={1.5}
        />
      </mesh>

      {/* Teto do carro */}
      <mesh position={[0.2, 1.4, 0]} castShadow>
        <boxGeometry args={[3.2, 0.9, 2]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* Rodas */}
      {[
        [1.6, -0.3, 1.3],
        [1.6, -0.3, -1.3],
        [-1.6, -0.3, 1.3],
        [-1.6, -0.3, -1.3]
      ].map((position, index) => (
        <group key={index} position={position as [number, number, number]}>
          <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
            <cylinderGeometry args={[0.5, 0.5, 0.4]} />
            <meshStandardMaterial color="#1a1a1a" metalness={0.1} roughness={0.9} />
          </mesh>
          <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0.15]} castShadow>
            <cylinderGeometry args={[0.35, 0.35, 0.1]} />
            <meshStandardMaterial color="#c0c0c0" metalness={0.9} roughness={0.1} />
          </mesh>
        </group>
      ))}

      {/* Faróis */}
      <mesh position={[2.3, 0.4, 0.8]} castShadow>
        <sphereGeometry args={[0.2]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.5}
        />
      </mesh>
      <mesh position={[2.3, 0.4, -0.8]} castShadow>
        <sphereGeometry args={[0.2]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.5}
        />
      </mesh>
    </group>
  )
}

// Componente de loading melhorado
const CarLoader: React.FC = () => {
  const loaderRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (loaderRef.current) {
      loaderRef.current.rotation.y = state.clock.elapsedTime * 0.5
    }
  })

  return (
    <group ref={loaderRef}>
      {/* Carro placeholder */}
      <mesh position={[0, 0.5, 0]} castShadow receiveShadow>
        <boxGeometry args={[4, 1, 2]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.1}
          transparent
          opacity={0.3}
        />
      </mesh>

      {/* Indicadores de loading animados */}
      {[0, 1, 2].map((i) => (
        <mesh key={i} position={[0, 2 + i * 0.3, 0]}>
          <sphereGeometry args={[0.1]} />
          <meshStandardMaterial
            color="#3B82F6"
            emissive="#3B82F6"
            emissiveIntensity={0.5 + Math.sin(Date.now() * 0.01 + i) * 0.3}
          />
        </mesh>
      ))}

      {/* Texto de loading */}
      <mesh position={[0, 1.5, 0]}>
        <boxGeometry args={[3, 0.1, 0.1]} />
        <meshStandardMaterial
          color="#3B82F6"
          emissive="#3B82F6"
          emissiveIntensity={0.3}
        />
      </mesh>

      {/* Anel de loading */}
      <mesh position={[0, 3.5, 0]} rotation={[0, 0, 0]}>
        <torusGeometry args={[0.5, 0.1, 8, 16]} />
        <meshStandardMaterial
          color="#3B82F6"
          emissive="#3B82F6"
          emissiveIntensity={0.4}
        />
      </mesh>
    </group>
  )
}

// Componente principal
interface Car3DWithOBJProps {
  className?: string
  useAdvancedModel?: boolean
}

export const Car3DWithOBJ: React.FC<Car3DWithOBJProps> = ({
  className = "",
  useAdvancedModel = true
}) => {
  const [rotation, setRotation] = useState<[number, number, number]>([0, 0, 0])
  const [isDragging, setIsDragging] = useState(false)
  const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 })
  const [modelError, setModelError] = useState(false)

  // Handlers para mouse
  const handleMouseDown = (event: React.MouseEvent) => {
    event.preventDefault()
    setIsDragging(true)
    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isDragging) return
    event.preventDefault()

    const deltaX = event.clientX - lastPosition.x
    const deltaY = event.clientY - lastPosition.y

    setRotation(prev => [
      prev[0] + deltaY * 0.005,
      prev[1] + deltaX * 0.005,
      prev[2]
    ])

    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Event listeners globais
  useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false)
    const handleGlobalMouseMove = (event: MouseEvent) => {
      if (!isDragging) return

      const deltaX = event.clientX - lastPosition.x
      const deltaY = event.clientY - lastPosition.y

      setRotation(prev => [
        prev[0] + deltaY * 0.005,
        prev[1] + deltaX * 0.005,
        prev[2]
      ])

      setLastPosition({ x: event.clientX, y: event.clientY })
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }
  }, [isDragging, lastPosition])

  return (
    <div
      className={`w-full h-full ${className} select-none`}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
        pointerEvents: 'auto',
        cursor: isDragging ? 'grabbing' : 'grab',
        touchAction: 'none'
      }}
    >
      <Canvas
        shadows
        camera={{ position: [6, 4, 6], fov: 50 }}
        style={{
          background: 'transparent',
          width: '100%',
          height: '100%'
        }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]}
        onError={(error) => {
          console.error('❌ Erro no Canvas:', error)
          setModelError(true)
        }}
      >
        {/* Iluminação otimizada para PBR */}
        <ambientLight intensity={0.4} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1.2}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
        <pointLight position={[-5, 5, -5]} intensity={0.4} />
        <pointLight position={[5, 5, 5]} intensity={0.4} />

        {/* Ambiente HDR */}
        <Environment preset="city" />

        {/* Carro com Suspense para loading */}
        <Suspense fallback={<CarLoader />}>
          {useAdvancedModel && !modelError ? (
            <OBJCarModel manualRotation={rotation} />
          ) : (
            <FallbackCarModel manualRotation={rotation} />
          )}
        </Suspense>

        {/* Chão reflexivo */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1, 0]} receiveShadow>
          <planeGeometry args={[30, 30]} />
          <meshStandardMaterial
            color="#1a1a1a"
            transparent
            opacity={0.2}
            metalness={0.8}
            roughness={0.2}
          />
        </mesh>
      </Canvas>

      {/* Debug info */}
      {modelError && (
        <div className="absolute top-4 left-4 bg-red-500/20 text-red-200 p-2 rounded text-xs">
          Erro ao carregar modelo 3D - usando fallback
        </div>
      )}
    </div>
  )
}

export default Car3DWithOBJ
