// =====================================================
// COMPONENTE SELETOR DE MÉTODOS DE PAGAMENTO
// Interface para escolher método de pagamento
// =====================================================

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Check, Plus, CreditCard, Smartphone, DollarSign, AlertCircle } from 'lucide-react'
import { PaymentMethod, PaymentMethodType } from '../../../../shared/lib/types'
import { PaymentMethodService } from '../services/PaymentMethodService'
import { useAuth } from '../contexts/AuthContextSimple'
import { useNotifications } from '../contexts/NotificationContext'

interface PaymentMethodSelectorProps {
  selectedMethodId?: string
  onMethodSelect: (method: PaymentMethod) => void
  onAddMethod?: () => void
  showAddButton?: boolean
  className?: string
}

export const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  selectedMethodId,
  onMethodSelect,
  onAddMethod,
  showAddButton = true,
  className = ''
}) => {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  const [methods, setMethods] = useState<PaymentMethod[]>([])
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Carregar métodos de pagamento
  useEffect(() => {
    if (user?.id) {
      loadPaymentMethods()
    }
  }, [user?.id])

  // Definir método selecionado
  useEffect(() => {
    if (selectedMethodId && methods.length > 0) {
      const method = methods.find(m => m.id === selectedMethodId)
      if (method) {
        setSelectedMethod(method)
      }
    } else if (methods.length > 0 && !selectedMethod) {
      // Selecionar método padrão (dinheiro primeiro)
      const defaultMethod = methods.find(m => m.is_default) || methods[0]
      setSelectedMethod(defaultMethod)
      onMethodSelect(defaultMethod)
    }
  }, [selectedMethodId, methods, selectedMethod, onMethodSelect])

  const loadPaymentMethods = async () => {
    try {
      setIsLoading(true)
      const userMethods = await PaymentMethodService.getUserPaymentMethods(user!.id)
      setMethods(userMethods)
    } catch (error) {
      console.error('Erro ao carregar métodos:', error)
      addNotification({
        type: 'error',
        title: 'Erro',
        message: 'Não foi possível carregar os métodos de pagamento'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleMethodSelect = async (method: PaymentMethod) => {
    setSelectedMethod(method)
    onMethodSelect(method)

    // Definir como padrão se não for
    if (!method.is_default && user?.id) {
      await PaymentMethodService.setDefaultPaymentMethod(user.id, method.id)
      await loadPaymentMethods() // Recarregar para atualizar estado
    }
  }

  const getMethodIcon = (type: PaymentMethodType): React.ReactNode => {
    switch (type) {
      case 'cash':
        return <DollarSign className="w-6 h-6 text-green-600" />
      case 'credit_card':
      case 'debit_card':
        return <CreditCard className="w-6 h-6 text-blue-600" />
      case 'pix':
        return <Smartphone className="w-6 h-6 text-purple-600" />
      default:
        return <CreditCard className="w-6 h-6 text-gray-600" />
    }
  }

  const getMethodDisplayName = (method: PaymentMethod): string => {
    if (method.display_info?.display_name) {
      return method.display_info.display_name
    }
    
    switch (method.type) {
      case 'cash':
        return 'Dinheiro'
      case 'credit_card':
        return 'Cartão de Crédito'
      case 'debit_card':
        return 'Cartão de Débito'
      case 'pix':
        return 'PIX'
      default:
        return method.name
    }
  }

  const getMethodDescription = (method: PaymentMethod): string => {
    if (method.display_info?.description) {
      return method.display_info.description
    }

    switch (method.type) {
      case 'cash':
        return 'Pagamento em espécie'
      case 'credit_card':
      case 'debit_card':
        return method.card_last_four ? `**** **** **** ${method.card_last_four}` : 'Cartão'
      case 'pix':
        return 'Pagamento instantâneo'
      default:
        return method.name
    }
  }

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded mb-3"></div>
        <div className="space-y-2">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-16 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <h3 className="text-lg font-semibold mb-3 flex items-center">
        <CreditCard className="w-5 h-5 mr-2" />
        Método de Pagamento
      </h3>

      <div className="space-y-2">
        <AnimatePresence>
          {methods.map((method) => (
            <motion.div
              key={method.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className={`
                relative p-4 border rounded-lg cursor-pointer transition-all duration-200
                ${selectedMethod?.id === method.id
                  ? 'border-blue-500 bg-blue-50 shadow-md'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }
              `}
              onClick={() => handleMethodSelect(method)}
            >
              <div className="flex items-center">
                <div className="mr-3">
                  {getMethodIcon(method.type)}
                </div>
                
                <div className="flex-1">
                  <div className="font-medium text-gray-900">
                    {getMethodDisplayName(method)}
                  </div>
                  <div className="text-sm text-gray-600">
                    {getMethodDescription(method)}
                  </div>
                </div>

                {method.is_default && (
                  <div className="mr-3">
                    <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                      Padrão
                    </span>
                  </div>
                )}

                {selectedMethod?.id === method.id && (
                  <div className="text-blue-600">
                    <Check className="w-5 h-5" />
                  </div>
                )}
              </div>

              {/* Informações específicas para dinheiro */}
              {method.type === 'cash' && selectedMethod?.id === method.id && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
                >
                  <div className="flex items-start">
                    <AlertCircle className="w-4 h-4 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-yellow-800">
                      <strong>Dica:</strong> {method.cash_notes || 'Tenha o valor exato ou próximo. Motoristas podem não ter troco para valores altos.'}
                      {method.cash_change_limit && (
                        <div className="mt-1">
                          <strong>Limite de troco:</strong> R$ {method.cash_change_limit.toFixed(2)}
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Botão para adicionar método */}
        {showAddButton && onAddMethod && (
          <motion.button
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            onClick={onAddMethod}
            className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors flex items-center justify-center"
          >
            <Plus className="w-5 h-5 mr-2" />
            Adicionar Método de Pagamento
          </motion.button>
        )}
      </div>

      {/* Método selecionado (resumo) */}
      {selectedMethod && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="mr-3">
                {getMethodIcon(selectedMethod.type)}
              </div>
              <div>
                <div className="font-medium text-blue-900">
                  Pagamento via {getMethodDisplayName(selectedMethod)}
                </div>
                <div className="text-sm text-blue-700">
                  {getMethodDescription(selectedMethod)}
                </div>
              </div>
            </div>
            <button
              onClick={() => {
                // Scroll para seletor
                const selector = document.querySelector('.space-y-2')
                selector?.scrollIntoView({ behavior: 'smooth' })
              }}
              className="text-blue-600 text-sm font-medium hover:underline"
            >
              Alterar
            </button>
          </div>
        </motion.div>
      )}
    </div>
  )
}
