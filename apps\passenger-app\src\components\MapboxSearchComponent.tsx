import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Search,
  MapPin,
  Navigation,
  Clock,
  DollarSign,
  X,
  Target,
  Route
} from 'lucide-react'

// ⚡ OPTIMIZED MAPBOX COMPONENT - LAZY LOADING & PERFORMANCE IMPROVEMENTS

// Lazy load Mapbox GL to improve initial bundle size
const loadMapbox = async () => {
  const mapboxgl = await import('mapbox-gl')
  await import('mapbox-gl/dist/mapbox-gl.css')
  return mapboxgl.default
}

// Memoized Car Icon component
const CarIcon = React.memo<{ className?: string }>(({ className = "w-6 h-6" }) => (
  <img
    src="/icons/icon-48x48.png"
    alt="MobiDrive Logo"
    className={className}
    loading="lazy"
  />
))

// Import the hook directly to avoid conditional hook usage
import { useMapboxSearch } from '../hooks/useMapboxSearch'

// Configure Mapbox token
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'

interface MapboxSearchComponentProps {
  onRideRequest?: (rideData: any) => void
  className?: string
}

const MapboxSearchComponent: React.FC<MapboxSearchComponentProps> = ({
  onRideRequest,
  className = ""
}) => {
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<any>(null)
  const [mapboxgl, setMapboxgl] = useState<any>(null)
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null)
  const [isMapLoaded, setIsMapLoaded] = useState(false)
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [searchMode, setSearchMode] = useState<'destination' | 'origin'>('destination')
  const [isLoading, setIsLoading] = useState(true)

  // Use the search hook directly
  const {
    searchQuery,
    searchResults,
    isSearching,
    searchError,
    origin,
    destination,
    rideEstimate,
    nearbyDrivers,
    isCalculatingRide,
    searchPlaces,
    selectResult,
    setOrigin,
    setDestination,
    clearSearch,
    getCurrentLocation
  } = useMapboxSearch({ userLocation: userLocation || undefined })

  // Load Mapbox and initialize map
  useEffect(() => {
    let isMounted = true

    const initializeMapbox = async () => {
      try {
        if (!isMounted) return
        setIsLoading(true)

        // Load Mapbox GL asynchronously
        const mapboxglModule = await loadMapbox()
        if (!isMounted) return

        mapboxglModule.accessToken = MAPBOX_TOKEN
        setMapboxgl(mapboxglModule)

        if (!mapContainer.current || map.current) return

        // Get user location first
        try {
          const coords = await getCurrentLocation()
          if (!isMounted) return
          setUserLocation(coords)
          initializeMap(coords, mapboxglModule)
        } catch {
          if (!isMounted) return
          // Fallback to São Paulo
          const fallbackCoords: [number, number] = [-46.6333, -23.5505]
          setUserLocation(fallbackCoords)
          initializeMap(fallbackCoords, mapboxglModule)
        }
      } catch (error) {
        console.warn('Failed to load Mapbox:', error)
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    initializeMapbox()

    return () => {
      isMounted = false
      // Cleanup map instance
      if (map.current) {
        map.current.remove()
        map.current = null
      }
    }
  }, [getCurrentLocation])

  const initializeMap = useCallback((coords: [number, number], mapboxglInstance: any) => {
    if (!mapContainer.current || !mapboxglInstance) return

    try {
      map.current = new mapboxglInstance.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/dark-v11',
        center: coords,
        zoom: 14,
        attributionControl: false,
        logoPosition: 'bottom-right'
      })

      // Add navigation controls
      map.current.addControl(new mapboxglInstance.NavigationControl(), 'top-right')

      // Add geolocate control
      const geolocate = new mapboxglInstance.GeolocateControl({
        positionOptions: { enableHighAccuracy: true },
        trackUserLocation: true,
        showUserHeading: true
      })
      map.current.addControl(geolocate, 'top-right')

      map.current.on('load', () => {
        setIsMapLoaded(true)
        setIsLoading(false)

        // Add user location marker
        if (mapboxglInstance && map.current) {
          new mapboxglInstance.Marker({
            color: '#3b82f6',
            scale: 1.2
          })
            .setLngLat(coords)
            .addTo(map.current)

          // Set origin to current location
          setOrigin(coords)
        }
      })

      map.current.on('error', (e: any) => {
        console.warn('Map error:', e)
        setIsLoading(false)
      })

    } catch (error) {
      console.warn('Map initialization failed:', error)
      setIsLoading(false)
    }
  }, [setOrigin])

  // Update map markers when locations change
  useEffect(() => {
    if (!map.current || !isMapLoaded) return

    // Clear existing markers (except user location)
    const markers = document.querySelectorAll('.mapboxgl-marker:not(.user-marker)')
    markers.forEach(marker => marker.remove())

    // Add destination marker
    if (destination) {
      new mapboxgl.Marker({
        color: '#ef4444',
        scale: 1.1
      })
        .setLngLat(destination.center)
        .addTo(map.current!)
    }

    // Note: Nearby drivers functionality is disabled

    // Fit bounds if we have both origin and destination
    if (origin && destination) {
      const bounds = new mapboxgl.LngLatBounds()
      bounds.extend(origin.center)
      bounds.extend(destination.center)
      map.current!.fitBounds(bounds, { padding: 50 })
    }
  }, [origin, destination, nearbyDrivers, isMapLoaded])

  // Memoized event handlers for better performance
  const handleSearchInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    searchPlaces(query)
    setShowSearchResults(query.length > 0)
  }, [searchPlaces])

  const handleResultSelect = useCallback((result: any) => {
    selectResult(result)
    if (searchMode === 'destination') {
      setDestination(result)
    } else {
      setOrigin(result)
    }
    setShowSearchResults(false)
  }, [selectResult, searchMode, setDestination, setOrigin])

  const handleRequestRide = useCallback(() => {
    if (rideEstimate && origin && destination) {
      onRideRequest?.({
        origin,
        destination,
        estimate: rideEstimate,
        nearbyDrivers
      })
    }
  }, [rideEstimate, origin, destination, nearbyDrivers, onRideRequest])

  const handleClearSearch = useCallback(() => {
    clearSearch()
    setShowSearchResults(false)
  }, [clearSearch])

  // Memoized search results to prevent unnecessary re-renders
  const memoizedSearchResults = useMemo(() => searchResults, [searchResults])

  // Show loading state while components are initializing
  if (isLoading) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full h-96 rounded-2xl overflow-hidden bg-gray-900 flex items-center justify-center">
          <div className="text-center text-white">
            <motion.div
              className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <p className="text-sm">Carregando mapa...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      {/* Map Container */}
      <div
        ref={mapContainer}
        className="w-full h-96 rounded-2xl overflow-hidden bg-gray-900"
      >
        {!isMapLoaded && (
          <div className="absolute inset-0 flex items-center justify-center text-white">
            <motion.div
              className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
          </div>
        )}
      </div>

      {/* Search Overlay */}
      <div className="absolute top-4 left-4 right-4 z-10">
        {/* Origin/Destination Toggle */}
        <div className="flex space-x-2 mb-3">
          <motion.button
            className={`px-4 py-2 rounded-xl text-sm font-medium transition-colors ${
              searchMode === 'origin'
                ? 'bg-blue-500 text-white'
                : 'bg-white/90 text-gray-700 hover:bg-white'
            }`}
            onClick={() => setSearchMode('origin')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <MapPin className="w-4 h-4 inline mr-1" />
            Origem
          </motion.button>
          <motion.button
            className={`px-4 py-2 rounded-xl text-sm font-medium transition-colors ${
              searchMode === 'destination'
                ? 'bg-red-500 text-white'
                : 'bg-white/90 text-gray-700 hover:bg-white'
            }`}
            onClick={() => setSearchMode('destination')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Target className="w-4 h-4 inline mr-1" />
            Destino
          </motion.button>
        </div>

        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearchInput}
            placeholder={searchMode === 'origin' ? 'De onde você está saindo?' : 'Para onde vamos?'}
            className="w-full pl-12 pr-12 py-4 bg-white/95 backdrop-blur-sm border border-white/20 rounded-2xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent transition-all shadow-lg"
          />
          {searchQuery && (
            <button
              onClick={handleClearSearch}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </button>
          )}
          {isSearching && (
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
              <motion.div
                className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
            </div>
          )}
        </div>

        {/* Search Results */}
        <AnimatePresence>
          {showSearchResults && memoizedSearchResults.length > 0 && (
            <motion.div
              className="mt-2 bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
            >
              {memoizedSearchResults.map((result) => (
                <motion.button
                  key={result.id}
                  className="w-full px-4 py-3 text-left hover:bg-blue-50 transition-colors border-b border-gray-100 last:border-b-0"
                  onClick={() => handleResultSelect(result)}
                  whileHover={{ backgroundColor: 'rgba(59, 130, 246, 0.05)' }}
                >
                  <div className="flex items-start space-x-3">
                    <MapPin className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-gray-900 font-medium truncate">
                        {result.place_name.split(',')[0]}
                      </p>
                      <p className="text-gray-500 text-sm truncate">
                        {result.place_name.split(',').slice(1).join(',')}
                      </p>
                    </div>
                  </div>
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Current Trip Info */}
      {origin && destination && (
        <div className="absolute bottom-4 left-4 right-4 z-10">
          <motion.div
            className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Detalhes da Viagem</h3>
              <Route className="w-5 h-5 text-blue-500" />
            </div>

            <div className="space-y-2 mb-4">
              <div className="flex items-center space-x-2 text-sm">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-gray-600 truncate">{origin.place_name}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-gray-600 truncate">{destination.place_name}</span>
              </div>
            </div>

            {isCalculatingRide ? (
              <div className="flex items-center justify-center py-4">
                <motion.div
                  className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                <span className="ml-2 text-gray-600">Calculando...</span>
              </div>
            ) : rideEstimate ? (
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1 text-sm text-gray-600">
                      <Clock className="w-4 h-4" />
                      <span>{Math.round(rideEstimate.duration / 60)} min</span>
                    </div>
                    <div className="flex items-center space-x-1 text-sm text-gray-600">
                      <Navigation className="w-4 h-4" />
                      <span>{(rideEstimate.distance / 1000).toFixed(1)} km</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1 text-lg font-bold text-green-600">
                    <DollarSign className="w-5 h-5" />
                    <span>R$ {rideEstimate.price.toFixed(2)}</span>
                  </div>
                </div>

                <motion.button
                  className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-xl font-semibold"
                  onClick={handleRequestRide}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <CarIcon className="w-5 h-5 inline mr-2" />
                  Solicitar Corrida
                </motion.button>
              </div>
            ) : null}
          </motion.div>
        </div>
      )}

      {/* DESABILITADO: Info de motoristas próximos desativada */}
      {/* Nearby Drivers Info */}
      {false && nearbyDrivers.length > 0 && !destination && (
        <div className="absolute top-20 right-4 z-10">
          <motion.div
            className="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-gray-900">
                {nearbyDrivers.length} motoristas próximos
              </span>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

// Add display name for debugging
MapboxSearchComponent.displayName = 'MapboxSearchComponent'

// Memoize the component for better performance
export default React.memo(MapboxSearchComponent)
