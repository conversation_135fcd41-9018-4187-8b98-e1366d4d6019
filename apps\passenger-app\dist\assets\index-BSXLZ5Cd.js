import{c as e,_ as t}from"./supabase-12kLBD3p.js";import{j as s,m as a,A as i}from"./motion-BBkHRuyi.js";import{r,R as n,d as o}from"./vendor-BXK_cukq.js";import{c as l}from"./three-CiZ2Medu.js";import{A as c,R as d,H as m,a as h,I as u,C as x,X as p,b,D as g,M as f,B as w,Z as v,c as y,L as j,E as N,d as _,e as k,U as S,P as C,f as E,S as R,g as I,h as T,i as M,j as D,k as P,l as A,m as L,n as $,o as O,p as q,T as F,q as H,r as z,s as U,t as B,N as W,u as J,v as V,w as K,x as G,y as Y,z as Z,F as X,G as Q,J as ee,K as te,O as se,Q as ae,V as ie,W as re,Y as ne,_ as oe,$ as le,a0 as ce}from"./icons-BBZsSRpR.js";import{m as de}from"./mapbox-KzVpRplj.js";
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function me(){return me=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e},me.apply(this,arguments)}var he;!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}(),function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(he||(he={}));const ue="popstate";function xe(e){return void 0===e&&(e={}),function(e,t,s,a){void 0===a&&(a={});let{window:i=document.defaultView,v5Compat:r=!1}=a,n=i.history,o=he.Pop,l=null,c=d();null==c&&(c=0,n.replaceState(me({},n.state,{idx:c}),""));function d(){return(n.state||{idx:null}).idx}function m(){o=he.Pop;let e=d(),t=null==e?null:e-c;c=e,l&&l({action:o,location:p.location,delta:t})}function h(e,t){o=he.Push;let s=fe(p.location,e,t);c=d()+1;let a=ge(s,c),m=p.createHref(s);try{n.pushState(a,"",m)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;i.location.assign(m)}r&&l&&l({action:o,location:p.location,delta:1})}function u(e,t){o=he.Replace;let s=fe(p.location,e,t);c=d();let a=ge(s,c),i=p.createHref(s);n.replaceState(a,"",i),r&&l&&l({action:o,location:p.location,delta:0})}function x(e){let t="null"!==i.location.origin?i.location.origin:i.location.href,s="string"==typeof e?e:we(e);return s=s.replace(/ $/,"%20"),pe(t,"No window.location.(origin|href) available to create URL for href: "+s),new URL(s,t)}let p={get action(){return o},get location(){return e(i,n)},listen(e){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(ue,m),l=e,()=>{i.removeEventListener(ue,m),l=null}},createHref:e=>t(i,e),createURL:x,encodeLocation(e){let t=x(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:h,replace:u,go:e=>n.go(e)};return p}((function(e,t){let{pathname:s,search:a,hash:i}=e.location;return fe("",{pathname:s,search:a,hash:i},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:we(t)}),0,e)}function pe(e,t){if(!1===e||null==e)throw new Error(t)}function be(e,t){if(!e)try{throw new Error(t)}catch(s){}}function ge(e,t){return{usr:e.state,key:e.key,idx:t}}function fe(e,t,s,a){return void 0===s&&(s=null),me({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?ve(t):t,{state:s,key:t&&t.key||a||Math.random().toString(36).substr(2,8)})}function we(e){let{pathname:t="/",search:s="",hash:a=""}=e;return s&&"?"!==s&&(t+="?"===s.charAt(0)?s:"?"+s),a&&"#"!==a&&(t+="#"===a.charAt(0)?a:"#"+a),t}function ve(e){let t={};if(e){let s=e.indexOf("#");s>=0&&(t.hash=e.substr(s),e=e.substr(0,s));let a=e.indexOf("?");a>=0&&(t.search=e.substr(a),e=e.substr(0,a)),e&&(t.pathname=e)}return t}var ye;function je(e,t,s){return void 0===s&&(s="/"),function(e,t,s){let a="string"==typeof t?ve(t):t,i=Le(a.pathname||"/",s);if(null==i)return null;let r=Ne(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let s=e.length===t.length&&e.slice(0,-1).every(((e,s)=>e===t[s]));return s?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(r);let n=null;for(let o=0;null==n&&o<r.length;++o){let e=Ae(i);n=De(r[o],e)}return n}(e,t,s)}function Ne(e,t,s,a){void 0===t&&(t=[]),void 0===s&&(s=[]),void 0===a&&(a="");let i=(e,i,r)=>{let n={relativePath:void 0===r?e.path||"":r,caseSensitive:!0===e.caseSensitive,childrenIndex:i,route:e};n.relativePath.startsWith("/")&&(pe(n.relativePath.startsWith(a),'Absolute route path "'+n.relativePath+'" nested under path "'+a+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),n.relativePath=n.relativePath.slice(a.length));let o=Fe([a,n.relativePath]),l=s.concat(n);e.children&&e.children.length>0&&(pe(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+o+'".'),Ne(e.children,t,l,o)),(null!=e.path||e.index)&&t.push({path:o,score:Me(o,e.index),routesMeta:l})};return e.forEach(((e,t)=>{var s;if(""!==e.path&&null!=(s=e.path)&&s.includes("?"))for(let a of _e(e.path))i(e,t,a);else i(e,t)})),t}function _e(e){let t=e.split("/");if(0===t.length)return[];let[s,...a]=t,i=s.endsWith("?"),r=s.replace(/\?$/,"");if(0===a.length)return i?[r,""]:[r];let n=_e(a.join("/")),o=[];return o.push(...n.map((e=>""===e?r:[r,e].join("/")))),i&&o.push(...n),o.map((t=>e.startsWith("/")&&""===t?"/":t))}!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(ye||(ye={}));const ke=/^:[\w-]+$/,Se=3,Ce=2,Ee=1,Re=10,Ie=-2,Te=e=>"*"===e;function Me(e,t){let s=e.split("/"),a=s.length;return s.some(Te)&&(a+=Ie),t&&(a+=Ce),s.filter((e=>!Te(e))).reduce(((e,t)=>e+(ke.test(t)?Se:""===t?Ee:Re)),a)}function De(e,t,s){let{routesMeta:a}=e,i={},r="/",n=[];for(let o=0;o<a.length;++o){let e=a[o],s=o===a.length-1,l="/"===r?t:t.slice(r.length)||"/",c=Pe({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},l),d=e.route;if(!c)return null;Object.assign(i,c.params),n.push({params:i,pathname:Fe([r,c.pathname]),pathnameBase:He(Fe([r,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(r=Fe([r,c.pathnameBase]))}return n}function Pe(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[s,a]=function(e,t,s){void 0===t&&(t=!1);void 0===s&&(s=!0);be("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let a=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,s)=>(a.push({paramName:t,isOptional:null!=s}),s?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(a.push({paramName:"*"}),i+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?i+="\\/*$":""!==e&&"/"!==e&&(i+="(?:(?=\\/|$))");let r=new RegExp(i,t?void 0:"i");return[r,a]}(e.path,e.caseSensitive,e.end),i=t.match(s);if(!i)return null;let r=i[0],n=r.replace(/(.)\/+$/,"$1"),o=i.slice(1);return{params:a.reduce(((e,t,s)=>{let{paramName:a,isOptional:i}=t;if("*"===a){let e=o[s]||"";n=r.slice(0,r.length-e.length).replace(/(.)\/+$/,"$1")}const l=o[s];return e[a]=i&&!l?void 0:(l||"").replace(/%2F/g,"/"),e}),{}),pathname:r,pathnameBase:n,pattern:e}}function Ae(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return be(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function Le(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let s=t.endsWith("/")?t.length-1:t.length,a=e.charAt(s);return a&&"/"!==a?null:e.slice(s)||"/"}function $e(e,t,s,a){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(a)+"].  Please separate it out to the `to."+s+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Oe(e,t){let s=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t?s.map(((e,t)=>t===s.length-1?e.pathname:e.pathnameBase)):s.map((e=>e.pathnameBase))}function qe(e,t,s,a){let i;void 0===a&&(a=!1),"string"==typeof e?i=ve(e):(i=me({},e),pe(!i.pathname||!i.pathname.includes("?"),$e("?","pathname","search",i)),pe(!i.pathname||!i.pathname.includes("#"),$e("#","pathname","hash",i)),pe(!i.search||!i.search.includes("#"),$e("#","search","hash",i)));let r,n=""===e||""===i.pathname,o=n?"/":i.pathname;if(null==o)r=s;else{let e=t.length-1;if(!a&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;i.pathname=t.join("/")}r=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:s,search:a="",hash:i=""}="string"==typeof e?ve(e):e,r=s?s.startsWith("/")?s:function(e,t){let s=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?s.length>1&&s.pop():"."!==e&&s.push(e)})),s.length>1?s.join("/"):"/"}(s,t):t;return{pathname:r,search:ze(a),hash:Ue(i)}}(i,r),c=o&&"/"!==o&&o.endsWith("/"),d=(n||"."===o)&&s.endsWith("/");return l.pathname.endsWith("/")||!c&&!d||(l.pathname+="/"),l}const Fe=e=>e.join("/").replace(/\/\/+/g,"/"),He=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),ze=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Ue=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const Be=["post","put","patch","delete"];new Set(Be);const We=["get",...Be];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function Je(){return Je=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e},Je.apply(this,arguments)}new Set(We);const Ve=r.createContext(null),Ke=r.createContext(null),Ge=r.createContext(null),Ye=r.createContext(null),Ze=r.createContext({outlet:null,matches:[],isDataRoute:!1}),Xe=r.createContext(null);function Qe(){return null!=r.useContext(Ye)}function et(){return Qe()||pe(!1),r.useContext(Ye).location}function tt(e){r.useContext(Ge).static||r.useLayoutEffect(e)}function st(){let{isDataRoute:e}=r.useContext(Ze);return e?function(){let{router:e}=function(){let e=r.useContext(Ve);return e||pe(!1),e}(ct.UseNavigateStable),t=mt(dt.UseNavigateStable),s=r.useRef(!1);return tt((()=>{s.current=!0})),r.useCallback((function(a,i){void 0===i&&(i={}),s.current&&("number"==typeof a?e.navigate(a):e.navigate(a,Je({fromRouteId:t},i)))}),[e,t])}():function(){Qe()||pe(!1);let e=r.useContext(Ve),{basename:t,future:s,navigator:a}=r.useContext(Ge),{matches:i}=r.useContext(Ze),{pathname:n}=et(),o=JSON.stringify(Oe(i,s.v7_relativeSplatPath)),l=r.useRef(!1);return tt((()=>{l.current=!0})),r.useCallback((function(s,i){if(void 0===i&&(i={}),!l.current)return;if("number"==typeof s)return void a.go(s);let r=qe(s,JSON.parse(o),n,"path"===i.relative);null==e&&"/"!==t&&(r.pathname="/"===r.pathname?t:Fe([t,r.pathname])),(i.replace?a.replace:a.push)(r,i.state,i)}),[t,a,o,n,e])}()}function at(e,t){let{relative:s}=void 0===t?{}:t,{future:a}=r.useContext(Ge),{matches:i}=r.useContext(Ze),{pathname:n}=et(),o=JSON.stringify(Oe(i,a.v7_relativeSplatPath));return r.useMemo((()=>qe(e,JSON.parse(o),n,"path"===s)),[e,o,n,s])}function it(e,t){return function(e,t,s,a){Qe()||pe(!1);let{navigator:i}=r.useContext(Ge),{matches:n}=r.useContext(Ze),o=n[n.length-1],l=o?o.params:{};!o||o.pathname;let c=o?o.pathnameBase:"/";o&&o.route;let d,m=et();if(t){var h;let e="string"==typeof t?ve(t):t;"/"===c||(null==(h=e.pathname)?void 0:h.startsWith(c))||pe(!1),d=e}else d=m;let u=d.pathname||"/",x=u;if("/"!==c){let e=c.replace(/^\//,"").split("/");x="/"+u.replace(/^\//,"").split("/").slice(e.length).join("/")}let p=je(e,{pathname:x}),b=function(e,t,s,a){var i;void 0===t&&(t=[]);void 0===s&&(s=null);void 0===a&&(a=null);if(null==e){var n;if(!s)return null;if(s.errors)e=s.matches;else{if(!(null!=(n=a)&&n.v7_partialHydration&&0===t.length&&!s.initialized&&s.matches.length>0))return null;e=s.matches}}let o=e,l=null==(i=s)?void 0:i.errors;if(null!=l){let e=o.findIndex((e=>e.route.id&&void 0!==(null==l?void 0:l[e.route.id])));e>=0||pe(!1),o=o.slice(0,Math.min(o.length,e+1))}let c=!1,d=-1;if(s&&a&&a.v7_partialHydration)for(let r=0;r<o.length;r++){let e=o[r];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(d=r),e.route.id){let{loaderData:t,errors:a}=s,i=e.route.loader&&void 0===t[e.route.id]&&(!a||void 0===a[e.route.id]);if(e.route.lazy||i){c=!0,o=d>=0?o.slice(0,d+1):[o[0]];break}}}return o.reduceRight(((e,a,i)=>{let n,m=!1,h=null,u=null;var x;s&&(n=l&&a.route.id?l[a.route.id]:void 0,h=a.route.errorElement||nt,c&&(d<0&&0===i?(ht[x="route-fallback"]||(ht[x]=!0),m=!0,u=null):d===i&&(m=!0,u=a.route.hydrateFallbackElement||null)));let p=t.concat(o.slice(0,i+1)),b=()=>{let t;return t=n?h:m?u:a.route.Component?r.createElement(a.route.Component,null):a.route.element?a.route.element:e,r.createElement(lt,{match:a,routeContext:{outlet:e,matches:p,isDataRoute:null!=s},children:t})};return s&&(a.route.ErrorBoundary||a.route.errorElement||0===i)?r.createElement(ot,{location:s.location,revalidation:s.revalidation,component:h,error:n,children:b(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):b()}),null)}(p&&p.map((e=>Object.assign({},e,{params:Object.assign({},l,e.params),pathname:Fe([c,i.encodeLocation?i.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:Fe([c,i.encodeLocation?i.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),n,s,a);if(t&&b)return r.createElement(Ye.Provider,{value:{location:Je({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:he.Pop}},b);return b}(e,t)}function rt(){let e=function(){var e;let t=r.useContext(Xe),s=function(){let e=r.useContext(Ke);return e||pe(!1),e}(),a=mt();if(void 0!==t)return t;return null==(e=s.errors)?void 0:e[a]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),s=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return r.createElement(r.Fragment,null,r.createElement("h2",null,"Unexpected Application Error!"),r.createElement("h3",{style:{fontStyle:"italic"}},t),s?r.createElement("pre",{style:a},s):null,null)}const nt=r.createElement(rt,null);class ot extends r.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){}render(){return void 0!==this.state.error?r.createElement(Ze.Provider,{value:this.props.routeContext},r.createElement(Xe.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function lt(e){let{routeContext:t,match:s,children:a}=e,i=r.useContext(Ve);return i&&i.static&&i.staticContext&&(s.route.errorElement||s.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=s.route.id),r.createElement(Ze.Provider,{value:t},a)}var ct=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ct||{}),dt=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(dt||{});function mt(e){let t=function(){let e=r.useContext(Ze);return e||pe(!1),e}(),s=t.matches[t.matches.length-1];return s.route.id||pe(!1),s.route.id}const ht={};function ut(e){let{to:t,replace:s,state:a,relative:i}=e;Qe()||pe(!1);let{future:n,static:o}=r.useContext(Ge),{matches:l}=r.useContext(Ze),{pathname:c}=et(),d=st(),m=qe(t,Oe(l,n.v7_relativeSplatPath),c,"path"===i),h=JSON.stringify(m);return r.useEffect((()=>d(JSON.parse(h),{replace:s,state:a,relative:i})),[d,h,i,s,a]),null}function xt(e){pe(!1)}function pt(e){let{basename:t="/",children:s=null,location:a,navigationType:i=he.Pop,navigator:n,static:o=!1,future:l}=e;Qe()&&pe(!1);let c=t.replace(/^\/*/,"/"),d=r.useMemo((()=>({basename:c,navigator:n,static:o,future:Je({v7_relativeSplatPath:!1},l)})),[c,l,n,o]);"string"==typeof a&&(a=ve(a));let{pathname:m="/",search:h="",hash:u="",state:x=null,key:p="default"}=a,b=r.useMemo((()=>{let e=Le(m,c);return null==e?null:{location:{pathname:e,search:h,hash:u,state:x,key:p},navigationType:i}}),[c,m,h,u,x,p,i]);return null==b?null:r.createElement(Ge.Provider,{value:d},r.createElement(Ye.Provider,{children:s,value:b}))}function bt(e){let{children:t,location:s}=e;return it(gt(t),s)}function gt(e,t){void 0===t&&(t=[]);let s=[];return r.Children.forEach(e,((e,a)=>{if(!r.isValidElement(e))return;let i=[...t,a];if(e.type===r.Fragment)return void s.push.apply(s,gt(e.props.children,i));e.type!==xt&&pe(!1),e.props.index&&e.props.children&&pe(!1);let n={id:e.props.id||i.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(n.children=gt(e.props.children,i)),s.push(n)})),s}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ft(){return ft=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e},ft.apply(this,arguments)}new Promise((()=>{}));const wt=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(pa){}const vt=n.startTransition;function yt(e){let{basename:t,children:s,future:a,window:i}=e,n=r.useRef();null==n.current&&(n.current=xe({window:i,v5Compat:!0}));let o=n.current,[l,c]=r.useState({action:o.action,location:o.location}),{v7_startTransition:d}=a||{},m=r.useCallback((e=>{d&&vt?vt((()=>c(e))):c(e)}),[c,d]);return r.useLayoutEffect((()=>o.listen(m)),[o,m]),r.useEffect((()=>{return null==(e=a)||e.v7_startTransition,void(null==e||e.v7_relativeSplatPath);var e}),[a]),r.createElement(pt,{basename:t,children:s,location:l.location,navigationType:l.action,navigator:o,future:a})}const jt="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,Nt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,_t=r.forwardRef((function(e,t){let s,{onClick:a,relative:i,reloadDocument:n,replace:o,state:l,target:c,to:d,preventScrollReset:m,viewTransition:h}=e,u=function(e,t){if(null==e)return{};var s,a,i={},r=Object.keys(e);for(a=0;a<r.length;a++)s=r[a],t.indexOf(s)>=0||(i[s]=e[s]);return i}(e,wt),{basename:x}=r.useContext(Ge),p=!1;if("string"==typeof d&&Nt.test(d)&&(s=d,jt))try{let e=new URL(window.location.href),t=d.startsWith("//")?new URL(e.protocol+d):new URL(d),s=Le(t.pathname,x);t.origin===e.origin&&null!=s?d=s+t.search+t.hash:p=!0}catch(pa){}let b=function(e,t){let{relative:s}=void 0===t?{}:t;Qe()||pe(!1);let{basename:a,navigator:i}=r.useContext(Ge),{hash:n,pathname:o,search:l}=at(e,{relative:s}),c=o;return"/"!==a&&(c="/"===o?a:Fe([a,o])),i.createHref({pathname:c,search:l,hash:n})}(d,{relative:i}),g=function(e,t){let{target:s,replace:a,state:i,preventScrollReset:n,relative:o,viewTransition:l}=void 0===t?{}:t,c=st(),d=et(),m=at(e,{relative:o});return r.useCallback((t=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(t,s)){t.preventDefault();let s=void 0!==a?a:we(d)===we(m);c(e,{replace:s,state:i,preventScrollReset:n,relative:o,viewTransition:l})}}),[d,c,m,a,i,s,e,n,o,l])}(d,{replace:o,state:l,target:c,preventScrollReset:m,relative:i,viewTransition:h});return r.createElement("a",ft({},u,{href:s||b,onClick:p||n?a:function(e){a&&a(e),e.defaultPrevented||g(e)},ref:t,target:c}))}));var kt,St,Ct,Et;(St=kt||(kt={})).UseScrollRestoration="useScrollRestoration",St.UseSubmit="useSubmit",St.UseSubmitFetcher="useSubmitFetcher",St.UseFetcher="useFetcher",St.useViewTransitionState="useViewTransitionState",(Et=Ct||(Ct={})).UseFetcher="useFetcher",Et.UseFetchers="useFetchers",Et.UseScrollRestoration="useScrollRestoration";const Rt=e("https://udquhavmgqtpkubrfzdm.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI",{auth:{autoRefreshToken:!0,persistSession:!0,storageKey:"mobidrive-passenger-auth",detectSessionInUrl:!1},realtime:{autoconnect:!0},global:{headers:{"X-Client-Info":"MobiDrive-Passenger/1.0.0"}}}),It=r.createContext(void 0),Tt=({children:e})=>{const[t,a]=r.useState({user:null,profile:null,loading:!0,error:null});r.useEffect((()=>{let e=!0;const t=t=>{e&&a((e=>({...e,loading:!1,...t})))},s=setTimeout((()=>{t()}),3e3);(async()=>{try{const{data:{session:a},error:i}=await Rt.auth.getSession();if(i)return void t({error:i.message});if(a?.user)try{const{data:e}=await Rt.from("profiles").select("*").eq("id",a.user.id).single();t({user:a.user,profile:e||{id:a.user.id,email:a.user.email||"",full_name:a.user.user_metadata?.full_name||a.user.email?.split("@")[0]||"Usuário",user_type:"passenger"}})}catch(e){t({user:a.user,profile:{id:a.user.id,email:a.user.email||"",full_name:a.user.user_metadata?.full_name||a.user.email?.split("@")[0]||"Usuário",user_type:"passenger"}})}else t();clearTimeout(s)}catch(a){t({error:a.message}),clearTimeout(s)}})();const{data:{subscription:i}}=Rt.auth.onAuthStateChange((async(e,s)=>{"SIGNED_IN"===e&&s?.user?t({user:s.user,profile:{id:s.user.id,email:s.user.email||"",full_name:s.user.user_metadata?.full_name||s.user.email?.split("@")[0]||"Usuário",user_type:"passenger"}}):"SIGNED_OUT"===e&&t({user:null,profile:null})}));return()=>{e=!1,clearTimeout(s),i.unsubscribe()}}),[]);return s.jsx(It.Provider,{value:{...t,signIn:async(e,t)=>{try{a((e=>({...e,loading:!0,error:null})));const{data:s,error:i}=await Rt.auth.signInWithPassword({email:e,password:t});return i?(a((e=>({...e,loading:!1,error:i.message}))),{error:i}):(setTimeout((()=>{window.location.href="/dashboard"}),500),{})}catch(s){return a((e=>({...e,loading:!1,error:s.message}))),{error:s}}},signUp:async(e,t,s)=>{try{a((e=>({...e,loading:!0,error:null})));const{error:i}=await Rt.auth.signUp({email:e,password:t,options:{data:{full_name:s.full_name,user_type:"passenger"}}});return i?(a((e=>({...e,loading:!1,error:i.message}))),{error:i}):(a((e=>({...e,loading:!1}))),{})}catch(i){return a((e=>({...e,loading:!1,error:i.message}))),{error:i}}},signOut:async()=>{try{a((e=>({...e,loading:!0}))),await Rt.auth.signOut()}catch(e){a((t=>({...t,loading:!1,error:e.message})))}},updateProfile:async e=>{try{if(!t.user)return{error:{message:"Usuário não autenticado"}};a((e=>({...e,loading:!0,error:null})));const{data:s,error:i}=await Rt.from("profiles").update(e).eq("id",t.user.id).select().single();return i?(a((e=>({...e,loading:!1,error:i.message}))),{error:i}):(a((e=>({...e,profile:s,loading:!1}))),{})}catch(s){return a((e=>({...e,loading:!1,error:s.message}))),{error:s}}}},children:e})},Mt=()=>{const e=r.useContext(It);if(void 0===e)throw new Error("useAuth deve ser usado dentro de um AuthProvider");return e},Dt=r.createContext(void 0),Pt=({children:e})=>{const[t,a]=r.useState([]),i=r.useCallback((e=>{const t=Math.random().toString(36).substr(2,9),s={...e,id:t};a((e=>[...e,s])),0!==e.duration&&setTimeout((()=>{n(t)}),e.duration||5e3)}),[]),n=r.useCallback((e=>{a((t=>t.filter((t=>t.id!==e))))}),[]),o=r.useCallback((()=>{a([])}),[]);return s.jsx(Dt.Provider,{value:{notifications:t,addNotification:i,removeNotification:n,clearAll:o},children:e})};class At extends r.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t});const s={message:e.message,stack:e.stack,componentStack:t.componentStack,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent,url:window.location.href};try{const e=JSON.parse(localStorage.getItem("app-errors")||"[]");e.push(s),e.length>10&&e.splice(0,e.length-10),localStorage.setItem("app-errors",JSON.stringify(e))}catch(pa){}}handleReload=()=>{window.location.reload()};handleGoHome=()=>{window.location.href="/"};render(){return this.state.hasError?this.props.fallback?this.props.fallback:s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4",children:s.jsxs(a.div,{className:"bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},children:[s.jsx(a.div,{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",initial:{rotate:0},animate:{rotate:360},transition:{duration:1,delay:.2},children:s.jsx(c,{className:"w-8 h-8 text-red-600"})}),s.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Oops! Algo deu errado"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"Encontramos um problema inesperado. Não se preocupe, nossa equipe foi notificada."}),!1,s.jsxs("div",{className:"space-y-3",children:[s.jsxs(a.button,{onClick:this.handleReload,className:"w-full bg-blue-600 text-white py-3 px-6 rounded-xl font-medium flex items-center justify-center space-x-2 hover:bg-blue-700 transition-colors",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(d,{className:"w-5 h-5"}),s.jsx("span",{children:"Tentar Novamente"})]}),s.jsxs(a.button,{onClick:this.handleGoHome,className:"w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-xl font-medium flex items-center justify-center space-x-2 hover:bg-gray-200 transition-colors",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(m,{className:"w-5 h-5"}),s.jsx("span",{children:"Voltar ao Início"})]})]}),s.jsx("p",{className:"text-xs text-gray-500 mt-6",children:"Se o problema persistir, entre em contato com o suporte."})]})}):this.props.children}}const Lt={success:x,error:h,info:u,warning:h},$t={success:{bg:"bg-green-50",border:"border-green-200",icon:"text-green-500",title:"text-green-800",message:"text-green-600"},error:{bg:"bg-red-50",border:"border-red-200",icon:"text-red-500",title:"text-red-800",message:"text-red-600"},info:{bg:"bg-blue-50",border:"border-blue-200",icon:"text-blue-500",title:"text-blue-800",message:"text-blue-600"},warning:{bg:"bg-yellow-50",border:"border-yellow-200",icon:"text-yellow-500",title:"text-yellow-800",message:"text-yellow-600"}},Ot=({notification:e,onClose:t})=>{const i=Lt[e.type],n=$t[e.type];return r.useEffect((()=>{if(0!==e.duration){const s=setTimeout((()=>{t(e.id)}),e.duration||5e3);return()=>clearTimeout(s)}}),[e.id,e.duration,t]),s.jsx(a.div,{className:`max-w-sm w-full ${n.bg} ${n.border} border rounded-lg shadow-lg p-4`,initial:{opacity:0,x:300,scale:.3},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:300,scale:.5,transition:{duration:.2}},whileHover:{scale:1.02},children:s.jsxs("div",{className:"flex items-start",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx(i,{className:`h-5 w-5 ${n.icon}`})}),s.jsxs("div",{className:"ml-3 w-0 flex-1",children:[s.jsx("p",{className:`text-sm font-medium ${n.title}`,children:e.title}),e.message&&s.jsx("p",{className:`mt-1 text-sm ${n.message}`,children:e.message}),e.action&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:e.action.onClick,className:`text-sm font-medium ${n.title} hover:underline`,children:e.action.label})})]}),s.jsx("div",{className:"ml-4 flex-shrink-0 flex",children:s.jsxs("button",{onClick:()=>t(e.id),className:`rounded-md inline-flex ${n.message} hover:${n.title} focus:outline-none focus:ring-2 focus:ring-offset-2`,children:[s.jsx("span",{className:"sr-only",children:"Fechar"}),s.jsx(p,{className:"h-4 w-4"})]})})]})})},qt=({notifications:e,onClose:t})=>s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.jsx(i,{children:e.map((e=>s.jsx(Ot,{notification:e,onClose:t},e.id)))})});const Ft=new class{sessionId;userId;eventQueue=[];performanceQueue=[];isOnline=navigator.onLine;constructor(){this.sessionId=this.generateSessionId(),this.setupEventListeners(),this.startPerformanceMonitoring(),setInterval((()=>this.flushQueues()),3e4)}setupEventListeners(){window.addEventListener("online",(()=>{this.isOnline=!0,this.flushQueues()})),window.addEventListener("offline",(()=>{this.isOnline=!1})),window.addEventListener("beforeunload",(()=>{this.flushQueues()})),window.addEventListener("error",(e=>{this.trackError("javascript_error",{message:e.message,filename:e.filename,lineno:e.lineno,colno:e.colno})})),window.addEventListener("unhandledrejection",(e=>{this.trackError("promise_rejection",{reason:e.reason?.toString()})}))}setUser(e){this.userId=e,this.track("user_session_start",{user_id:e,session_duration:0})}track(e,t={}){const s={event_type:e,user_id:this.userId,session_id:this.sessionId,properties:{...t,timestamp_client:Date.now()},timestamp:(new Date).toISOString(),page_url:window.location.href,user_agent:navigator.userAgent};this.eventQueue.push(s),this.isCriticalEvent(e)&&this.flushQueues()}trackRideRequest(e,t,s){this.track("ride_request_created",{origin:e,destination:t,vehicle_type:s,timestamp:Date.now()})}trackRideAccepted(e,t,s){this.track("ride_accepted",{ride_id:e,driver_id:t,eta_minutes:s})}trackEmergencyActivated(e){this.track("emergency_activated",{ride_id:e,activation_method:"sos_button",response_time:0})}trackMapInteraction(e,t){this.track("map_interaction",{action:e,...t})}trackSearchQuery(e,t,s){this.track("search_performed",{query:e,results_count:t,selected_result:s})}trackPageView(e,t){this.track("page_view",{page:e,load_time_ms:t,referrer:document.referrer})}trackUserBehavior(e){this.track("user_behavior",e)}trackPerformance(e,t,s,a){const i={metric_name:e,value:t,unit:s,timestamp:(new Date).toISOString(),context:a};this.performanceQueue.push(i)}trackError(e,t){this.track("error_occurred",{error_type:e,...t,stack_trace:t.stack?.toString()})}startPerformanceMonitoring(){"PerformanceObserver"in window&&(new PerformanceObserver((e=>{for(const t of e.getEntries())this.trackPerformance("lcp",t.startTime,"ms")})).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver((e=>{for(const t of e.getEntries())this.trackPerformance("fid",t.processingStart-t.startTime,"ms")})).observe({entryTypes:["first-input"]}),new PerformanceObserver((e=>{let t=0;for(const s of e.getEntries())s.hadRecentInput||(t+=s.value);this.trackPerformance("cls",t,"score")})).observe({entryTypes:["layout-shift"]})),setInterval((()=>{if("memory"in performance){const e=performance.memory;this.trackPerformance("memory_used",e.usedJSHeapSize,"bytes"),this.trackPerformance("memory_total",e.totalJSHeapSize,"bytes")}}),6e4)}isCriticalEvent(e){return["emergency_activated","ride_request_created","error_occurred","user_session_start"].includes(e)}async flushQueues(){this.eventQueue=[],this.performanceQueue=[]}generateSessionId(){return`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}getSessionStats(){return{sessionId:this.sessionId,userId:this.userId,eventsQueued:this.eventQueue.length,metricsQueued:this.performanceQueue.length,isOnline:this.isOnline}}async flush(){await this.flushQueues()}},Ht=Object.freeze(Object.defineProperty({__proto__:null,analyticsService:Ft},Symbol.toStringTag,{value:"Module"}));const zt=new class{registration=null;subscription=null;userId;isSupported="Notification"in window&&"serviceWorker"in navigator;permission="default";constructor(){this.init()}async init(){this.isSupported&&(this.permission=Notification.permission)}setUser(e){this.userId=e,this.subscribeToUserNotifications()}async requestPermission(){if(!this.isSupported)return!1;if("granted"===this.permission)return!0;const e=await Notification.requestPermission();return this.permission=e,"granted"===e&&(await this.subscribeToPush(),!0)}async subscribeToPush(){if(this.registration)try{const e=await this.registration.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:this.urlBase64ToUint8Array("")});this.subscription=e,this.userId&&await Rt.from("push_subscriptions").upsert({user_id:this.userId,endpoint:e.endpoint,p256dh:e.keys.p256dh,auth:e.keys.auth,created_at:(new Date).toISOString()})}catch(e){}}subscribeToUserNotifications(){this.userId}async showNotification(e){this.showInAppNotification(e),"granted"===this.permission&&this.registration&&await this.showPushNotification(e),"emergency"===e.type&&this.handleEmergencyNotification(e)}showInAppNotification(e){const t=document.createElement("div");t.className=`notification notification-${e.type} ${"critical"===e.priority?"critical":""}`,t.innerHTML=`\n      <div class="notification-content">\n        <div class="notification-icon">\n          ${this.getNotificationIcon(e.type)}\n        </div>\n        <div class="notification-text">\n          <div class="notification-title">${e.title}</div>\n          <div class="notification-message">${e.message}</div>\n        </div>\n        <button class="notification-close">&times;</button>\n      </div>\n    `,this.addNotificationStyles();this.getNotificationContainer().appendChild(t),"critical"!==e.priority&&setTimeout((()=>{this.removeNotification(t)}),this.getNotificationDuration(e.type)),t.querySelector(".notification-close")?.addEventListener("click",(()=>{this.removeNotification(t)})),e.sound&&this.playNotificationSound(e.type),e.vibrate&&"vibrate"in navigator&&navigator.vibrate(this.getVibrationPattern(e.type))}async showPushNotification(e){if(!this.registration)return;const t={body:e.message,icon:"/icons/icon-192x192.png",badge:"/icons/icon-72x72.png",tag:`mobidrive-${e.type}`,requireInteraction:"critical"===e.priority,silent:!e.sound,data:e.data,actions:e.actions};await this.registration.showNotification(e.title,t)}handleEmergencyNotification(e){if(this.playEmergencySound(),"vibrate"in navigator){const e=[200,100,200,100,200,100,200];navigator.vibrate(e)}this.flashScreen(),document.body.classList.add("emergency-mode")}notifyRideAccepted(e,t,s){this.showNotification({title:"🚗 Motorista encontrado!",message:`${e} está chegando em ${t} minutos (${s})`,type:"success",priority:"high",sound:!0,vibrate:!0,actions:[{action:"call",title:"Ligar",icon:"/icons/phone.png"},{action:"message",title:"Mensagem",icon:"/icons/message.png"}]})}notifyDriverArrived(){this.showNotification({title:"📍 Motorista chegou!",message:"Seu motorista está te esperando",type:"info",priority:"high",sound:!0,vibrate:!0})}notifyEmergencyActivated(){this.showNotification({title:"🚨 EMERGÊNCIA ATIVADA",message:"Autoridades foram notificadas. Sua localização está sendo compartilhada.",type:"emergency",priority:"critical",sound:!0,vibrate:!0,persistent:!0})}notifyRideCompleted(e){this.showNotification({title:"✅ Corrida finalizada",message:`Obrigado por usar o MobiDrive! Valor: R$ ${e.toFixed(2)}`,type:"success",priority:"normal",sound:!0})}getNotificationIcon(e){return{info:"💬",success:"✅",warning:"⚠️",error:"❌",emergency:"🚨"}[e]||"💬"}getNotificationDuration(e){return{info:4e3,success:5e3,warning:7e3,error:8e3,emergency:0}[e]||4e3}getVibrationPattern(e){return{info:[100],success:[100,50,100],warning:[200,100,200],error:[300,100,300],emergency:[500,200,500,200,500]}[e]||[100]}playNotificationSound(e){try{const t=new Audio(`/sounds/notification-${e}.mp3`);t.volume=.5,t.play().catch((()=>{const e=new Audio("/sounds/notification.mp3");e.volume=.3,e.play().catch((()=>{}))}))}catch(t){}}playEmergencySound(){try{const e=new Audio("/sounds/emergency.mp3");e.loop=!0,e.volume=.8,e.play(),setTimeout((()=>{e.pause(),e.currentTime=0}),3e4)}catch(e){}}flashScreen(){const e=document.createElement("div");e.style.cssText="\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background: red;\n      opacity: 0.3;\n      z-index: 9999;\n      pointer-events: none;\n      animation: flash 0.5s ease-in-out 3;\n    ",document.body.appendChild(e),setTimeout((()=>{document.body.removeChild(e)}),1500)}getNotificationContainer(){let e=document.getElementById("notification-container");return e||(e=document.createElement("div"),e.id="notification-container",e.style.cssText="\n        position: fixed;\n        top: 20px;\n        right: 20px;\n        z-index: 10000;\n        max-width: 400px;\n      ",document.body.appendChild(e)),e}removeNotification(e){e.style.animation="slideOut 0.3s ease-in-out",setTimeout((()=>{e.parentNode&&e.parentNode.removeChild(e)}),300)}addNotificationStyles(){if(document.getElementById("notification-styles"))return;const e=document.createElement("style");e.id="notification-styles",e.textContent="\n      .notification {\n        background: white;\n        border-radius: 12px;\n        box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n        margin-bottom: 12px;\n        animation: slideIn 0.3s ease-out;\n        border-left: 4px solid #3B82F6;\n      }\n      .notification-success { border-left-color: #10B981; }\n      .notification-warning { border-left-color: #F59E0B; }\n      .notification-error { border-left-color: #EF4444; }\n      .notification-emergency { \n        border-left-color: #DC2626; \n        animation: pulse 1s infinite;\n      }\n      .notification-content {\n        display: flex;\n        align-items: center;\n        padding: 16px;\n      }\n      .notification-icon {\n        font-size: 24px;\n        margin-right: 12px;\n      }\n      .notification-text {\n        flex: 1;\n      }\n      .notification-title {\n        font-weight: 600;\n        margin-bottom: 4px;\n      }\n      .notification-message {\n        color: #6B7280;\n        font-size: 14px;\n      }\n      .notification-close {\n        background: none;\n        border: none;\n        font-size: 20px;\n        cursor: pointer;\n        color: #9CA3AF;\n      }\n      @keyframes slideIn {\n        from { transform: translateX(100%); opacity: 0; }\n        to { transform: translateX(0); opacity: 1; }\n      }\n      @keyframes slideOut {\n        from { transform: translateX(0); opacity: 1; }\n        to { transform: translateX(100%); opacity: 0; }\n      }\n      @keyframes pulse {\n        0%, 100% { opacity: 1; }\n        50% { opacity: 0.7; }\n      }\n      @keyframes flash {\n        0%, 100% { opacity: 0; }\n        50% { opacity: 0.3; }\n      }\n    ",document.head.appendChild(e)}urlBase64ToUint8Array(e){const t=(e+"=".repeat((4-e.length%4)%4)).replace(/-/g,"+").replace(/_/g,"/"),s=window.atob(t),a=new Uint8Array(s.length);for(let i=0;i<s.length;++i)a[i]=s.charCodeAt(i);return a}getStatus(){return{isSupported:this.isSupported,permission:this.permission,hasSubscription:!!this.subscription,userId:this.userId}}};const Ut=new class{cache=new Map;locationCache=null;driversCache=null;set(e,t,s=5){const a=60*s*1e3;this.cache.set(e,{data:t,timestamp:Date.now(),ttl:a})}get(e){const t=this.cache.get(e);return t?Date.now()-t.timestamp>t.ttl?(this.cache.delete(e),null):t.data:null}setUserLocation(e,t,s){this.locationCache={lat:e,lng:t,address:s,timestamp:Date.now()}}getUserLocation(){return this.locationCache?Date.now()-this.locationCache.timestamp>12e4?(this.locationCache=null,null):this.locationCache:null}setNearbyDrivers(e,t){this.driversCache={drivers:e,location:t,timestamp:Date.now()}}getNearbyDrivers(e){if(!this.driversCache)return null;if(Date.now()-this.driversCache.timestamp>3e4)return this.driversCache=null,null;return this.calculateDistance(this.driversCache.location.lat,this.driversCache.location.lng,e.lat,e.lng)>.1?(this.driversCache=null,null):this.driversCache.drivers}setRoute(e,t,s){const a=`route_${e}_${t}`;this.set(a,s,10)}getRoute(e,t){const s=`route_${e}_${t}`;return this.get(s)}setGeocode(e,t){const s=`geocode_${e.toLowerCase()}`;this.set(s,t,60)}getGeocode(e){const t=`geocode_${e.toLowerCase()}`;return this.get(t)}setPriceEstimate(e,t,s){const a=`price_${e}_${t}`;this.set(a,s,5)}getPriceEstimate(e,t){const s=`price_${e}_${t}`;return this.get(s)}calculateDistance(e,t,s,a){const i=this.deg2rad(s-e),r=this.deg2rad(a-t),n=Math.sin(i/2)*Math.sin(i/2)+Math.cos(this.deg2rad(e))*Math.cos(this.deg2rad(s))*Math.sin(r/2)*Math.sin(r/2);return 6371*(2*Math.atan2(Math.sqrt(n),Math.sqrt(1-n)))}deg2rad(e){return e*(Math.PI/180)}clear(){this.cache.clear(),this.locationCache=null,this.driversCache=null}cleanup(){const e=Date.now();for(const[t,s]of this.cache.entries())e-s.timestamp>s.ttl&&this.cache.delete(t)}getStats(){return{totalItems:this.cache.size,locationCached:!!this.locationCache,driversCached:!!this.driversCache,cacheHitRate:0}}async preloadCommonData(){try{const e=["Aeroporto de Guarulhos, São Paulo","Aeroporto de Congonhas, São Paulo","Estação da Sé, São Paulo","Shopping Ibirapuera, São Paulo","Av. Paulista, São Paulo"];for(const t of e)this.getGeocode(t)}catch(e){}}};setInterval((()=>{Ut.cleanup()}),3e5);const Bt=Object.freeze(Object.defineProperty({__proto__:null,cacheService:Ut},Symbol.toStringTag,{value:"Module"})),Wt=()=>{const[e,t]=r.useState(!1),[n,o]=r.useState({supabase:"online",mapbox:"online",notifications:"enabled",cache:"active",analytics:"tracking",realtime:"connected"}),[l,d]=r.useState({loadTime:0,memoryUsage:0,cacheHitRate:0,apiLatency:0,errorRate:0}),[m,h]=r.useState([]);r.useEffect((()=>{const e=async()=>{try{const e=Date.now(),{error:t}=await Rt.from("ride_requests").select("id").limit(1),s=Date.now()-e;o((e=>({...e,supabase:t?"offline":s>2e3?"slow":"online"})));try{const e=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/test.json?access_token=pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g");o((t=>({...t,mapbox:e.ok?"online":"offline"})))}catch{o((e=>({...e,mapbox:"offline"})))}const a=zt.getStatus();o((e=>({...e,notifications:"granted"===a.permission?"enabled":"denied"===a.permission?"blocked":"disabled"})));const i=Ut.getStats();o((e=>({...e,cache:i.totalItems>0?"active":"inactive"})));const r=Ft.getSessionStats();o((e=>({...e,analytics:r.isOnline?"tracking":"paused"}))),d((e=>({...e,apiLatency:s,cacheHitRate:i.cacheHitRate||0}))),u(`Sistema verificado - Supabase: ${s}ms`)}catch(e){u(`Erro na verificação: ${e}`)}},t=setInterval(e,3e4);return e(),()=>clearInterval(t)}),[]),r.useEffect((()=>{const e=()=>{if(performance.timing){const e=performance.timing.loadEventEnd-performance.timing.navigationStart;d((t=>({...t,loadTime:e})))}if("memory"in performance){const e=performance.memory,t=e.usedJSHeapSize/e.totalJSHeapSize*100;d((e=>({...e,memoryUsage:t})))}};e();const t=setInterval(e,1e4);return()=>clearInterval(t)}),[]);const u=e=>{const t=(new Date).toLocaleTimeString();h((s=>[`[${t}] ${e}`,...s.slice(0,9)]))},p=e=>{switch(e){case"online":case"enabled":case"active":case"tracking":case"connected":return"text-green-500";case"slow":case"disabled":case"reconnecting":return"text-yellow-500";case"offline":case"blocked":case"inactive":case"paused":case"disconnected":return"text-red-500";default:return"text-gray-500"}},y=e=>{switch(e){case"online":case"enabled":case"active":case"tracking":case"connected":return s.jsx(x,{className:"w-4 h-4"});default:return s.jsx(c,{className:"w-4 h-4"})}};return e?s.jsx(i,{children:s.jsxs(a.div,{initial:{opacity:0,x:-300},animate:{opacity:1,x:0},exit:{opacity:0,x:-300},className:"fixed bottom-4 left-4 z-50 w-80 bg-gray-900 text-white rounded-2xl shadow-2xl overflow-hidden",children:[s.jsxs("div",{className:"bg-gray-800 p-4 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(b,{className:"w-5 h-5 text-blue-400"}),s.jsx("h3",{className:"font-semibold",children:"System Monitor"})]}),s.jsx("button",{onClick:()=>t(!1),className:"text-gray-400 hover:text-white",children:"×"})]}),s.jsxs("div",{className:"p-4 space-y-3",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(g,{className:"w-4 h-4 text-blue-400"}),s.jsx("span",{className:"text-sm",children:"Supabase"}),s.jsxs("div",{className:`ml-auto flex items-center space-x-1 ${p(n.supabase)}`,children:[y(n.supabase),s.jsx("span",{className:"text-xs",children:n.supabase})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(f,{className:"w-4 h-4 text-green-400"}),s.jsx("span",{className:"text-sm",children:"Mapbox"}),s.jsxs("div",{className:`ml-auto flex items-center space-x-1 ${p(n.mapbox)}`,children:[y(n.mapbox),s.jsx("span",{className:"text-xs",children:n.mapbox})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(w,{className:"w-4 h-4 text-purple-400"}),s.jsx("span",{className:"text-sm",children:"Notifications"}),s.jsxs("div",{className:`ml-auto flex items-center space-x-1 ${p(n.notifications)}`,children:[y(n.notifications),s.jsx("span",{className:"text-xs",children:n.notifications})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(v,{className:"w-4 h-4 text-yellow-400"}),s.jsx("span",{className:"text-sm",children:"Cache"}),s.jsxs("div",{className:`ml-auto flex items-center space-x-1 ${p(n.cache)}`,children:[y(n.cache),s.jsx("span",{className:"text-xs",children:n.cache})]})]})]}),s.jsxs("div",{className:"border-t border-gray-700 pt-3",children:[s.jsx("h4",{className:"text-sm font-medium mb-2",children:"Performance"}),s.jsxs("div",{className:"space-y-2 text-xs",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Load Time:"}),s.jsxs("span",{children:[l.loadTime,"ms"]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Memory:"}),s.jsxs("span",{children:[l.memoryUsage.toFixed(1),"%"]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"API Latency:"}),s.jsxs("span",{children:[l.apiLatency,"ms"]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Cache Hit:"}),s.jsxs("span",{children:[l.cacheHitRate.toFixed(1),"%"]})]})]})]}),s.jsxs("div",{className:"border-t border-gray-700 pt-3",children:[s.jsx("h4",{className:"text-sm font-medium mb-2",children:"System Logs"}),s.jsx("div",{className:"bg-gray-800 rounded p-2 max-h-32 overflow-y-auto",children:m.map(((e,t)=>s.jsx("div",{className:"text-xs text-gray-300 mb-1",children:e},t)))})]}),s.jsx("div",{className:"border-t border-gray-700 pt-3",children:s.jsx("button",{onClick:async()=>{u("Iniciando diagnósticos..."),Ut.clear(),u("Cache limpo"),await Ft.flush(),u("Analytics sincronizado");try{await Rt.from("ride_requests").select("id").limit(1),u("Conectividade Supabase: OK")}catch(e){u("Erro na conectividade Supabase")}u("Diagnósticos concluídos")},className:"w-full bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-3 rounded-lg transition-colors",children:"Run Diagnostics"})})]})]})}):s.jsx(a.button,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},onClick:()=>t(!0),className:"fixed bottom-4 left-4 z-50 w-12 h-12 bg-gray-800 hover:bg-gray-700 text-white rounded-full shadow-lg flex items-center justify-center",children:s.jsx(b,{className:"w-5 h-5"})})},Jt=({timeout:e=3e4,onTimeout:t,message:i="Carregando..."})=>{const[n,o]=r.useState(e/1e3),[l,h]=r.useState(!1);r.useEffect((()=>{const e=setInterval((()=>{o((e=>e<=1?(h(!0),t?.(),0):e-1))}),1e3);return()=>clearInterval(e)}),[t]);const u=()=>{window.location.reload()},x=()=>{window.location.href="/"};return l?s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4",children:s.jsxs(a.div,{className:"bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},children:[s.jsx(a.div,{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",initial:{rotate:0},animate:{rotate:360},transition:{duration:1},children:s.jsx(c,{className:"w-8 h-8 text-red-600"})}),s.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Tempo Limite Excedido"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"O carregamento está demorando mais que o esperado. Isso pode indicar um problema de conexão ou configuração."}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs(a.button,{onClick:u,className:"w-full bg-blue-600 text-white py-3 px-6 rounded-xl font-medium flex items-center justify-center space-x-2 hover:bg-blue-700 transition-colors",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(d,{className:"w-5 h-5"}),s.jsx("span",{children:"Tentar Novamente"})]}),s.jsxs(a.button,{onClick:x,className:"w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-xl font-medium flex items-center justify-center space-x-2 hover:bg-gray-200 transition-colors",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(m,{className:"w-5 h-5"}),s.jsx("span",{children:"Voltar ao Início"})]})]}),s.jsx("p",{className:"text-xs text-gray-500 mt-6",children:"Se o problema persistir, verifique sua conexão com a internet ou entre em contato com o suporte."})]})}):s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4",children:s.jsxs(a.div,{className:"bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 max-w-md w-full text-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[s.jsx(a.div,{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-6",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:i}),s.jsx("p",{className:"text-gray-600 mb-6",children:"Aguarde enquanto preparamos tudo para você..."}),s.jsxs("div",{className:"bg-gray-100 rounded-lg p-4 mb-4",children:[s.jsxs("div",{className:"flex justify-between items-center text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"Tempo restante:"}),s.jsxs("span",{className:"font-mono text-blue-600",children:[n,"s"]})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:s.jsx(a.div,{className:"bg-blue-500 h-2 rounded-full",initial:{width:"100%"},animate:{width:n/(e/1e3)*100+"%"},transition:{duration:1,ease:"linear"}})})]}),s.jsx("p",{className:"text-xs text-gray-500",children:"Se o carregamento demorar muito, a página será recarregada automaticamente."})]})})},Vt=({children:e})=>{const{user:t,loading:a}=Mt();return a?s.jsx(Jt,{message:"Verificando autenticação...",timeout:2e4,onTimeout:()=>{window.location.reload()}}):t?s.jsx(s.Fragment,{children:e}):s.jsx(ut,{to:"/login",replace:!0})},Kt=()=>{const e=(()=>{const e=navigator.userAgent||"",t=navigator.platform||"",s=window.screen.width,a=window.screen.height,i=window.innerWidth,r="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,n=/Android/i.test(e),o=/iPad|iPhone|iPod/.test(e),l=/Mobi|Android/i.test(e),c=i>1024,d=(n||o)&&r&&i<=768,m=i>768&&i<=1024&&r||/iPad/.test(e)||n&&!l;return{isMobile:d,isTablet:m,isDesktop:!d&&!m&&c,isTouchDevice:r,screenWidth:s,screenHeight:a,userAgent:e,platform:t,isAndroid:n,isIOS:o,isWebMobile:l}})();return e.isMobile||e.isTouchDevice&&e.screenWidth<=768||e.isWebMobile},Gt=()=>(()=>{if("undefined"==typeof window)return!1;const e=navigator.userAgent.toLowerCase(),t=window.screen.width;return!/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(e)&&!(t<=768)&&t>1024})(),Yt=()=>{const[e,t]=r.useState(!1);return r.useEffect((()=>{t(Gt());const e=()=>{t(Gt())};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]),e},Zt=()=>{const e=navigator.userAgent.toLowerCase(),t=["mobile","android","iphone","ipad","ipod","blackberry","windows phone"].some((t=>e.includes(t))),s=window.screen.width<=768||window.screen.height<=768,a="ontouchstart"in window||navigator.maxTouchPoints>0,i="orientation"in window;return t||s&&a||i},Xt=()=>{const e=r.useRef(1),t=r.useRef(!1),s=r.useRef(0);r.useEffect((()=>{if(Zt())return;const a=()=>{const i=Date.now();if(i-s.current<500)return;s.current=i;const r=(()=>{if(Zt())return 1;try{const e=window.outerWidth/window.innerWidth;if(e>=.5&&e<=3&&!isNaN(e))return Math.round(100*e)/100;const t=window.devicePixelRatio||1;return t>=.5&&t<=3?Math.round(100*t)/100:1}catch(e){return 1}})(),n=e.current;if(Math.abs(r-n)>.1){e.current=r;const s=1/r,i=document.querySelector(".device-wrapper-container");i?(i.style.transform=`scale(${s})`,i.style.transformOrigin="center center",i.style.width=100*r+"vw",i.style.height=100*r+"vh",i.style.position="fixed",i.style.top="0",i.style.left="0",i.style.zIndex="9999",i.style.zoom="1",i.style.webkitTransform=`scale(${s})`,i.style.mozTransform=`scale(${s})`,i.style.msTransform=`scale(${s})`):t.current||setTimeout(a,200);const n=document.querySelector(".device-iphone-container");n&&(n.style.transform="scale(0.75)",n.style.webkitTransform="scale(0.75)",n.style.mozTransform="scale(0.75)",n.style.msTransform="scale(0.75)",n.style.oTransform="scale(0.75)",n.style.transformOrigin="center center"),document.documentElement.style.setProperty("zoom","1","important"),document.body.style.setProperty("zoom","1","important"),document.documentElement.style.overflow="hidden",document.body.style.overflow="hidden"}};(()=>{let e=document.querySelector('meta[name="viewport"]');e||(e=document.createElement("meta"),e.setAttribute("name","viewport"),document.head.appendChild(e)),e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no")})(),a(),setTimeout((()=>{a(),t.current=!0}),100);const i=setInterval(a,5e3),r=()=>{a()},n=e=>{!e.ctrlKey||"+"!==e.key&&"-"!==e.key&&"0"!==e.key&&"="!==e.key&&"Equal"!==e.key||(s.current=0,setTimeout(a,100))};return window.addEventListener("resize",r,{passive:!0}),window.addEventListener("keydown",n,{passive:!0}),()=>{clearInterval(i),window.removeEventListener("resize",r),window.removeEventListener("keydown",n)}}),[])},Qt=({children:e,className:t="",enableVerticalScroll:i=!0,enableHorizontalScroll:r=!1})=>s.jsx("div",{className:`touch-scroll-container ${t}`,style:{overflow:"hidden",position:"relative",height:"100%",width:"100%"},children:s.jsx(a.div,{drag:!(!i||!r)||(i?"y":!!r&&"x"),dragElastic:.2,dragMomentum:!0,dragTransition:{bounceStiffness:300,bounceDamping:40,power:.3,timeConstant:750},style:{width:"100%",minHeight:"100%",cursor:"grab"},whileDrag:{cursor:"grabbing",scale:.99},className:"touch-scroll-content",children:e})}),es=({children:e})=>{const t=Yt(),a=(()=>{const[e,t]=r.useState("");return r.useEffect((()=>{const e=()=>{const e=new Date,s=new Intl.DateTimeFormat("pt-BR",{timeZone:"America/Sao_Paulo",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e);t(s)};e();const s=setInterval(e,1e3);return()=>clearInterval(s)}),[]),e})();return Xt(),r.useEffect((()=>{}),[t]),t?s.jsx("div",{className:"device-wrapper-container",children:s.jsxs("div",{className:"device-wrapper-background",children:[s.jsxs("div",{className:"device-iphone-container",children:[s.jsx("div",{className:"device-iphone-shadow"}),s.jsx("div",{className:"device-iphone-body",children:s.jsx("div",{className:"device-iphone-frame",children:s.jsxs("div",{className:"device-iphone-screen",children:[s.jsx("div",{className:"device-notch",children:s.jsx("div",{className:"device-notch-content",children:s.jsxs("div",{className:"device-notch-inner",children:[s.jsx("div",{className:"device-camera-left"}),s.jsx("div",{className:"device-speaker"}),s.jsx("div",{className:"device-camera-right"})]})})}),s.jsx("div",{className:"device-status-bar",children:s.jsxs("div",{className:"device-status-content",children:[s.jsx("div",{className:"device-status-left",children:s.jsx("span",{className:"device-time",children:a||"9:41"})}),s.jsxs("div",{className:"device-status-right",children:[s.jsxs("div",{className:"device-signal",children:[s.jsx("div",{className:"device-signal-bar device-signal-1"}),s.jsx("div",{className:"device-signal-bar device-signal-2"}),s.jsx("div",{className:"device-signal-bar device-signal-3"}),s.jsx("div",{className:"device-signal-bar device-signal-4"})]}),s.jsx("svg",{className:"device-wifi",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.24 0 1 1 0 01-1.415-1.414 5 5 0 017.07 0 1 1 0 01-1.415 1.414zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z",clipRule:"evenodd"})}),s.jsxs("div",{className:"device-battery",children:[s.jsx("div",{className:"device-battery-body",children:s.jsx("div",{className:"device-battery-level"})}),s.jsx("div",{className:"device-battery-tip"})]})]})]})}),s.jsx("div",{className:"device-content-area",children:s.jsx(Qt,{enableVerticalScroll:!0,enableHorizontalScroll:!1,bounceStiffness:400,bounceDamping:40,className:"device-touch-container",children:e})}),s.jsx("div",{className:"device-home-indicator",children:s.jsx("div",{className:"device-home-bar"})})]})})}),s.jsx("div",{className:"device-button-volume-1"}),s.jsx("div",{className:"device-button-volume-2"}),s.jsx("div",{className:"device-button-power"})]}),s.jsx("div",{className:"device-info-badge-left",children:s.jsxs("div",{className:"device-badge-content",children:[s.jsxs("div",{className:"device-badge-row",children:[s.jsx("div",{className:"device-badge-indicator device-badge-green"}),s.jsx("span",{children:"📱 Simulação Mobile"})]}),s.jsx("div",{className:"device-badge-subtitle",children:"Zoom fixo • Fundo estável"})]})}),s.jsx("div",{className:"device-info-badge-right",children:s.jsxs("div",{className:"device-badge-content",children:[s.jsx("div",{className:"device-badge-title",children:"DISPOSITIVO SIMULADO:"}),s.jsxs("div",{className:"device-badge-row",children:[s.jsx("div",{className:"device-badge-indicator device-badge-blue"}),s.jsx("span",{className:"device-badge-text",children:"iPhone 14 Pro"})]}),s.jsxs("div",{className:"device-badge-row",children:[s.jsx("div",{className:"device-badge-indicator device-badge-purple"}),s.jsx("span",{className:"device-badge-text",children:"375 × 812px"})]}),s.jsxs("div",{className:"device-badge-row",children:[s.jsx("div",{className:"device-badge-indicator device-badge-green"}),s.jsx("span",{className:"device-badge-text",children:"Zoom Fixo"})]})]})})]})}):s.jsx("div",{className:"device-content-mobile",children:e})},ts=({children:e})=>"mobile"===(Kt()?"mobile":"desktop")?s.jsx("div",{className:"mobile-native-container",children:e}):s.jsx(es,{children:e}),ss={subtle:"https://media.giphy.com/media/l0HlKlRMBvdzAFYYM/giphy.gif",particles:"https://media.giphy.com/media/xT0xepIsg7kK2ZZO0g/giphy.gif",minimal:"https://media.giphy.com/media/MtEbLYV2Rp1Ha/giphy.gif",elegant:"https://media.giphy.com/media/ddGvtRqYF75uDPzJb4/giphy.gif",static:""},as=({variant:e="subtle",className:t="",opacity:a=.6,brightness:i=.5,contrast:r=1.1})=>{const n=ss[e];return"static"===e?s.jsx("div",{className:`absolute inset-0 ${t}`,style:{background:"\n            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.08) 0%, transparent 50%),\n            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.05) 0%, transparent 50%),\n            linear-gradient(135deg, #000000 0%, #0a0a0a 25%, #111111 50%, #0a0a0a 75%, #000000 100%)\n          ",opacity:a}}):s.jsx("div",{className:`absolute inset-0 flex items-center justify-center ${t}`,children:s.jsx("img",{src:n,alt:"Gradient Background",className:"w-full h-full object-cover",style:{filter:`brightness(${i}) contrast(${r}) saturate(0.8)`,opacity:a},loading:"lazy",onError:e=>{const t=e.target;t.style.display="none";const s=t.parentElement;s&&(s.style.background="\n              radial-gradient(circle at 30% 70%, rgba(120, 119, 198, 0.08) 0%, transparent 50%),\n              linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%)\n            ")}})})},is=()=>{r.useEffect((()=>{const e=e=>{if(e.ctrlKey||e.metaKey){if(!e.target.closest('.mapbox-gl-canvas, .mapbox, .map-container, .leaflet-container, [data-allow-zoom="true"]'))return e.preventDefault(),e.stopPropagation(),!1}},t=e=>{const t=["+","-","=","0","Equal","Minus","Digit0"];if((e.ctrlKey||e.metaKey)&&(t.includes(e.key)||t.includes(e.code)||"+"===e.key||"-"===e.key||"="===e.key||"0"===e.key)){if(!e.target.closest('.mapbox-gl-canvas, .mapbox, .map-container, .leaflet-container, [data-allow-zoom="true"]'))return e.preventDefault(),e.stopPropagation(),!1}},s=e=>{if(e.touches.length>1){if(!e.target.closest('.mapbox-gl-canvas, .mapbox, .map-container, .leaflet-container, [data-allow-zoom="true"]'))return e.preventDefault(),e.stopPropagation(),!1}},a=e=>{if(!e.target.closest('.mapbox-gl-canvas, .mapbox, .map-container, .leaflet-container, [data-allow-zoom="true"]'))return e.preventDefault(),e.stopPropagation(),!1};let i=0;const r=e=>{const t=Date.now();if(t-i<=300){if(!e.target.closest('.mapbox-gl-canvas, .mapbox, .map-container, .leaflet-container, [data-allow-zoom="true"]'))return e.preventDefault(),e.stopPropagation(),!1}i=t};(()=>{let e=document.querySelector('meta[name="viewport"]');e||(e=document.createElement("meta"),e.setAttribute("name","viewport"),document.head.appendChild(e)),e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover")})(),document.addEventListener("wheel",e,{passive:!1}),document.addEventListener("keydown",t,{passive:!1}),document.addEventListener("touchstart",s,{passive:!1}),document.addEventListener("touchmove",s,{passive:!1}),document.addEventListener("touchend",r,{passive:!1}),document.addEventListener("gesturestart",a,{passive:!1}),document.addEventListener("gesturechange",a,{passive:!1}),document.addEventListener("gestureend",a,{passive:!1});const n=document.createElement("style");return n.textContent='\n      /* DESABILITA ZOOM EM TODA A APLICAÇÃO */\n      html, body {\n        touch-action: manipulation !important;\n        -webkit-touch-callout: none !important;\n        -webkit-tap-highlight-color: transparent !important;\n        overscroll-behavior: none !important;\n      }\n      \n      /* PERMITE SCROLL VERTICAL */\n      html, body {\n        overflow-x: hidden !important;\n        overflow-y: auto !important;\n      }\n      \n      /* EXCEÇÃO PARA MAPAS - PERMITE ZOOM */\n      .mapbox-gl-canvas,\n      .mapbox,\n      .map-container,\n      .leaflet-container,\n      [data-allow-zoom="true"] {\n        touch-action: auto !important;\n        -webkit-user-select: auto !important;\n        user-select: auto !important;\n      }\n      \n      /* PERMITE SELEÇÃO DE TEXTO EM INPUTS E BOTÕES */\n      input, textarea, [contenteditable], button, [role="button"], .btn {\n        -webkit-user-select: text !important;\n        user-select: text !important;\n        touch-action: manipulation !important;\n        pointer-events: auto !important;\n      }\n\n      /* GARANTE QUE BOTÕES FUNCIONEM CORRETAMENTE */\n      button, [role="button"], .btn, [type="button"], [type="submit"] {\n        -webkit-user-select: none !important;\n        user-select: none !important;\n        touch-action: manipulation !important;\n        pointer-events: auto !important;\n        cursor: pointer !important;\n      }\n\n      /* DESABILITA ZOOM EM IMAGENS */\n      img {\n        -webkit-user-select: none !important;\n        user-select: none !important;\n        touch-action: manipulation !important;\n      }\n    ',document.head.appendChild(n),()=>{document.removeEventListener("wheel",e),document.removeEventListener("keydown",t),document.removeEventListener("touchstart",s),document.removeEventListener("touchmove",s),document.removeEventListener("touchend",r),document.removeEventListener("gesturestart",a),document.removeEventListener("gesturechange",a),document.removeEventListener("gestureend",a),n.parentNode&&n.parentNode.removeChild(n)}}),[])},rs=({onLogin:e})=>{const{signIn:t,loading:n,error:o,user:l}=Mt(),[c,d]=r.useState(""),[m,h]=r.useState(""),[u,x]=r.useState(!1);if(is(),r.useEffect((()=>{let e=document.querySelector('meta[name="viewport"]');e||(e=document.createElement("meta"),e.setAttribute("name","viewport"),document.head.appendChild(e)),e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover"),document.body.style.margin="0",document.body.style.padding="0",document.body.style.overflow="hidden",document.documentElement.style.margin="0",document.documentElement.style.padding="0",document.documentElement.style.overflow="hidden"}),[]),l)return s.jsx(ut,{to:"/dashboard",replace:!0});const p={hidden:{opacity:0,y:10},visible:{opacity:1,y:0}};return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden",children:[s.jsx(as,{variant:"static",opacity:.7}),s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6 text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center",children:s.jsx("img",{src:"/icons/icon-48x48.png",alt:"MobiDrive",className:"w-6 h-6"})}),s.jsxs("div",{children:[s.jsxs("h1",{className:"text-2xl font-bold text-white",children:["MOBI",s.jsx("span",{className:"text-blue-400",children:"DRIVE"})]}),s.jsx("p",{className:"text-xs text-white/70",children:"Sua jornada começa aqui"})]})]})}),s.jsx("div",{className:"flex-1 flex items-center justify-center px-4",children:s.jsx(a.div,{className:"w-full max-w-xs",variants:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,staggerChildren:.1}}},initial:"hidden",animate:"visible",children:s.jsxs(a.div,{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",variants:p,children:[s.jsxs(a.div,{className:"text-center mb-6",variants:p,children:[s.jsx("h2",{className:"text-xl font-semibold text-white mb-2",children:"Bem-vindo de volta"}),s.jsx("p",{className:"text-white/70 text-sm",children:"Entre na sua conta para continuar"})]}),s.jsxs(a.form,{onSubmit:async s=>{s.preventDefault(),e?e(c,m):await t(c,m)},className:"space-y-4",variants:p,children:[s.jsxs(a.div,{variants:p,children:[s.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-white/90 mb-2",children:"Email"}),s.jsxs("div",{className:"relative",children:[s.jsx(y,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4"}),s.jsx("input",{id:"email",type:"email",value:c,onChange:e=>d(e.target.value),required:!0,autoComplete:"email",className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all",placeholder:"<EMAIL>"})]})]}),s.jsxs(a.div,{variants:p,children:[s.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-white/90 mb-2",children:"Senha"}),s.jsxs("div",{className:"relative",children:[s.jsx(j,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4"}),s.jsx("input",{id:"password",type:u?"text":"password",value:m,onChange:e=>h(e.target.value),required:!0,autoComplete:"current-password",className:"w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all",placeholder:"••••••••"}),s.jsx("button",{type:"button",onClick:()=>x(!u),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors",children:u?s.jsx(N,{className:"w-4 h-4"}):s.jsx(_,{className:"w-4 h-4"})})]})]}),s.jsx(i,{children:o&&s.jsx(a.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-red-500/20 border border-red-400/30 rounded-xl p-3",children:s.jsx("p",{className:"text-red-200 text-sm",children:o})})}),s.jsx(a.button,{type:"submit",disabled:n,className:"w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2",variants:p,whileHover:{scale:1.02},whileTap:{scale:.98},children:n?s.jsx(a.div,{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}):s.jsxs(s.Fragment,{children:[s.jsx("span",{children:"Entrar"}),s.jsx(k,{className:"w-4 h-4"})]})})]}),s.jsx(a.div,{className:"mt-6 text-center",variants:p,children:s.jsxs("p",{className:"text-white/70 text-sm",children:["Não tem uma conta?"," ",s.jsx(_t,{to:"/register",className:"text-blue-400 font-medium hover:text-blue-300 transition-colors",children:"Cadastre-se"})]})})]})})}),s.jsx(a.div,{className:"pb-4 text-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:.5},children:s.jsx("p",{className:"text-white/50 text-xs",children:"© 2024 MobiDrive. Todos os direitos reservados."})})]})]})},ns=({children:e,title:t="MobiDrive",subtitle:a="Transporte Inteligente",showHeader:i=!0,showFooter:n=!0,className:o="",pageIcon:l="🚗",animatedSymbol:c="⚡"})=>(r.useEffect((()=>{let e=document.querySelector('meta[name="viewport"]');return e||(e=document.createElement("meta"),e.setAttribute("name","viewport"),document.head.appendChild(e)),e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover"),document.body.style.margin="0",document.body.style.padding="0",document.body.style.overflow="hidden",document.documentElement.style.margin="0",document.documentElement.style.padding="0",document.documentElement.style.overflow="hidden",()=>{}}),[]),s.jsxs("div",{className:`modern-page ${o}`,children:[i&&s.jsx("div",{className:"modern-header",children:s.jsxs("div",{className:"modern-header-content",children:[s.jsxs("div",{className:"modern-brand-container",children:[s.jsxs("div",{className:"modern-brand-icon-container",children:[s.jsx("div",{className:"modern-brand-icon",children:l}),s.jsx("div",{className:"modern-animated-symbol",children:c})]}),s.jsx("h1",{className:"modern-header-title",children:t})]}),s.jsx("p",{className:"modern-header-subtitle",children:a})]})}),s.jsx("div",{className:"modern-content",children:e}),n&&s.jsx("div",{className:"modern-footer",children:s.jsx("p",{className:"modern-footer-text",children:"© 2024 MobiDrive. Todos os direitos reservados."})})]})),os=({children:e,className:t="",glass:a=!1,title:i,icon:r})=>{const n=a?"modern-glass-card":"modern-card";return s.jsxs("div",{className:`${n} ${t}`,children:[i&&s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px",marginBottom:"16px",paddingBottom:"12px",borderBottom:a?"1px solid rgba(255,255,255,0.2)":"1px solid rgba(0,0,0,0.1)"},children:[r&&s.jsx("span",{style:{fontSize:"20px"},children:r}),s.jsx("h3",{style:{margin:0,fontSize:"18px",fontWeight:"600",color:a?"#fff":"#1f2937"},children:i})]}),e]})},ls=({children:e,onClick:t,icon:a,loading:i=!1,disabled:r=!1,className:n="",variant:o="primary"})=>s.jsx("button",{onClick:t,disabled:r||i,className:`modern-button ${n}`,style:{...(()=>{switch(o){case"secondary":return{background:"linear-gradient(135deg, #6b7280 0%, #4b5563 100%)",boxShadow:"0 8px 24px rgba(107, 114, 128, 0.4)"};case"success":return{background:"linear-gradient(135deg, #10b981 0%, #059669 100%)",boxShadow:"0 8px 24px rgba(16, 185, 129, 0.4)"};case"danger":return{background:"linear-gradient(135deg, #ef4444 0%, #dc2626 100%)",boxShadow:"0 8px 24px rgba(239, 68, 68, 0.4)"};default:return{}}})(),opacity:r?.6:1,cursor:r?"not-allowed":"pointer",transform:i?"scale(0.98)":"scale(1)"},children:i?s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"modern-button-icon",children:"⏳"}),"Carregando..."]}):s.jsxs(s.Fragment,{children:[a&&s.jsx("span",{className:"modern-button-icon",children:a}),e]})}),cs={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.6,staggerChildren:.1}}},ds={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}},ms=({onRegister:e})=>{const{signUp:t,loading:n,error:o,user:l}=Mt(),c=st(),[d,m]=r.useState({email:"",password:"",confirmPassword:"",full_name:"",phone:""}),[h,u]=r.useState(!1),[x,p]=r.useState(!1),[b,g]=r.useState([]),[f,w]=r.useState(!1);if(is(),r.useEffect((()=>{let e=document.querySelector('meta[name="viewport"]');e||(e=document.createElement("meta"),e.setAttribute("name","viewport"),document.head.appendChild(e)),e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover"),document.body.style.margin="0",document.body.style.padding="0",document.body.style.overflow="hidden",document.documentElement.style.margin="0",document.documentElement.style.padding="0",document.documentElement.style.overflow="hidden"}),[]),l)return s.jsx(ut,{to:"/dashboard",replace:!0});const v=(e,t)=>{m((s=>({...s,[e]:t}))),"password"===e&&g((e=>{const t=[];return e.length<8&&t.push("Mínimo 8 caracteres"),/[A-Z]/.test(e)||t.push("Uma letra maiúscula"),/[a-z]/.test(e)||t.push("Uma letra minúscula"),/\d/.test(e)||t.push("Um número"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Um caractere especial"),t})(t))};return f?s.jsx(ns,{title:"Sucesso!",subtitle:"Conta criada com sucesso",pageIcon:"✅",animatedSymbol:"🎉",children:s.jsxs(os,{className:"text-center",children:[s.jsx("div",{style:{fontSize:"64px",marginBottom:"16px"},children:"✅"}),s.jsx("h2",{style:{fontSize:"24px",fontWeight:"700",marginBottom:"12px",color:"#1f2937"},children:"Conta Criada!"}),s.jsx("p",{style:{color:"#6b7280",marginBottom:"24px",lineHeight:"1.5"},children:"Verifique seu email para confirmar sua conta e fazer login."}),s.jsx(ls,{onClick:()=>c("/login"),icon:"🚀",variant:"success",children:"Ir para Login"})]})}):s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden",children:[s.jsx(as,{variant:"static",opacity:.7}),s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6 text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center",children:s.jsx("img",{src:"/icons/icon-48x48.png",alt:"MobiDrive",className:"w-6 h-6"})}),s.jsxs("div",{children:[s.jsxs("h1",{className:"text-2xl font-bold text-white",children:["MOBI",s.jsx("span",{className:"text-blue-400",children:"DRIVE"})]}),s.jsx("p",{className:"text-xs text-white/70",children:"Criar Nova Conta"})]})]})}),s.jsx("div",{className:"flex-1 flex items-center justify-center px-4",children:s.jsx(a.div,{className:"w-full max-w-xs",variants:cs,initial:"hidden",animate:"visible",children:s.jsxs(a.div,{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",variants:ds,children:[s.jsxs(a.div,{className:"text-center mb-6",variants:ds,children:[s.jsx("h2",{className:"text-xl font-semibold text-white mb-2",children:"Criar Conta"}),s.jsx("p",{className:"text-white/70 text-sm",children:"Preencha os dados para começar"})]}),s.jsxs(a.form,{onSubmit:async s=>{if(s.preventDefault(),!(b.length>0)&&d.password===d.confirmPassword)try{e?e(d):(await t(d.email,d.password,{full_name:d.full_name,phone:d.phone}),w(!0))}catch(a){}},className:"space-y-4",variants:ds,children:[s.jsxs(a.div,{variants:ds,children:[s.jsx("label",{htmlFor:"full_name",className:"block text-sm font-medium text-white/90 mb-2",children:"Nome Completo"}),s.jsxs("div",{className:"relative",children:[s.jsx(S,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4"}),s.jsx("input",{id:"full_name",type:"text",value:d.full_name,onChange:e=>v("full_name",e.target.value),required:!0,className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all",placeholder:"Seu nome completo"})]})]}),s.jsxs(a.div,{variants:ds,children:[s.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-white/90 mb-2",children:"Email"}),s.jsxs("div",{className:"relative",children:[s.jsx(y,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4"}),s.jsx("input",{id:"email",type:"email",value:d.email,onChange:e=>v("email",e.target.value),required:!0,className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all",placeholder:"<EMAIL>"})]})]}),s.jsxs(a.div,{variants:ds,children:[s.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-white/90 mb-2",children:"Telefone"}),s.jsxs("div",{className:"relative",children:[s.jsx(C,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4"}),s.jsx("input",{id:"phone",type:"tel",value:d.phone,onChange:e=>v("phone",e.target.value),required:!0,className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all",placeholder:"(11) 99999-9999"})]})]}),s.jsxs(a.div,{variants:ds,children:[s.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-white/90 mb-2",children:"Senha"}),s.jsxs("div",{className:"relative",children:[s.jsx(j,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4"}),s.jsx("input",{id:"password",type:h?"text":"password",value:d.password,onChange:e=>v("password",e.target.value),required:!0,className:"w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all",placeholder:"••••••••"}),s.jsx("button",{type:"button",onClick:()=>u(!h),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors",children:h?s.jsx(N,{className:"w-4 h-4"}):s.jsx(_,{className:"w-4 h-4"})})]}),d.password&&s.jsxs("div",{className:"mt-2 text-xs",children:[b.map(((e,t)=>s.jsxs("p",{className:"text-red-300 mb-1",children:["• ",e]},t))),0===b.length&&s.jsx("p",{className:"text-green-300",children:"✓ Senha válida"})]})]}),s.jsxs(a.div,{variants:ds,children:[s.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-white/90 mb-2",children:"Confirmar Senha"}),s.jsxs("div",{className:"relative",children:[s.jsx(j,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4"}),s.jsx("input",{id:"confirmPassword",type:x?"text":"password",value:d.confirmPassword,onChange:e=>v("confirmPassword",e.target.value),required:!0,className:"w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all",placeholder:"••••••••"}),s.jsx("button",{type:"button",onClick:()=>p(!x),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors",children:x?s.jsx(N,{className:"w-4 h-4"}):s.jsx(_,{className:"w-4 h-4"})})]}),d.confirmPassword&&s.jsx("div",{className:"mt-2 text-xs",children:d.password===d.confirmPassword?s.jsx("p",{className:"text-green-300",children:"✓ Senhas coincidem"}):s.jsx("p",{className:"text-red-300",children:"• Senhas não coincidem"})})]}),s.jsx(i,{children:o&&s.jsx(a.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-red-500/20 border border-red-400/30 rounded-xl p-3",children:s.jsx("p",{className:"text-red-200 text-sm",children:o})})}),s.jsx(a.button,{type:"submit",disabled:n||b.length>0||d.password!==d.confirmPassword,className:"w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2",variants:ds,whileHover:{scale:1.02},whileTap:{scale:.98},children:n?s.jsx(a.div,{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}):s.jsxs(s.Fragment,{children:[s.jsx("span",{children:"Criar Conta"}),s.jsx(k,{className:"w-4 h-4"})]})})]}),s.jsx(a.div,{className:"mt-6 text-center",variants:ds,children:s.jsxs("p",{className:"text-white/70 text-sm",children:["Já tem uma conta?"," ",s.jsx(_t,{to:"/login",className:"text-blue-400 font-medium hover:text-blue-300 transition-colors",children:"Faça login"})]})})]})})}),s.jsx(a.div,{className:"pb-4 text-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:.5},children:s.jsx("p",{className:"text-white/50 text-xs",children:"© 2024 MobiDrive. Todos os direitos reservados."})})]})]})},hs=()=>{const{user:e,signOut:t,loading:i}=Mt();if(is(),r.useEffect((()=>{let e=document.querySelector('meta[name="viewport"]');e||(e=document.createElement("meta"),e.setAttribute("name","viewport"),document.head.appendChild(e)),e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover"),document.body.style.margin="0",document.body.style.padding="0",document.body.style.overflow="hidden",document.documentElement.style.margin="0",document.documentElement.style.padding="0",document.documentElement.style.overflow="hidden"}),[]),!e&&!i)return s.jsx(ut,{to:"/login",replace:!0});const n={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden",children:[s.jsx(as,{variant:"static",opacity:.7}),s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6 text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center",children:s.jsx("img",{src:"/icons/icon-48x48.png",alt:"MobiDrive",className:"w-6 h-6"})}),s.jsxs("div",{children:[s.jsxs("h1",{className:"text-2xl font-bold text-white",children:["MOBI",s.jsx("span",{className:"text-blue-400",children:"DRIVE"})]}),s.jsxs("p",{className:"text-xs text-white/70",children:["Bem-vindo, ",e?.user_metadata?.full_name||e?.email||"Usuário","!"]})]})]})}),s.jsx("div",{className:"flex-1 flex flex-col justify-center px-4 space-y-6",children:s.jsxs(a.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.6,staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"space-y-6",children:[s.jsx(a.div,{variants:n,children:s.jsx("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:s.jsxs("div",{className:"text-center",children:[s.jsx(a.div,{className:"w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(S,{className:"w-10 h-10 text-white"})}),s.jsxs("h2",{className:"text-xl font-bold text-white mb-2",children:["Olá, ",e?.user_metadata?.full_name||"Usuário","!"]}),s.jsx("p",{className:"text-white/80 text-sm",children:"Pronto para sua próxima viagem?"})]})})}),s.jsx(a.div,{variants:n,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4 text-center",children:"⚡ Ações Rápidas"}),s.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[s.jsxs(a.button,{onClick:()=>window.location.href="/ride-request/map",className:"bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(E,{className:"w-6 h-6"}),s.jsx("span",{className:"text-sm font-medium",children:"Solicitar Corrida"})]}),s.jsxs(a.button,{onClick:()=>window.location.href="/ride-tracking",className:"bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(f,{className:"w-6 h-6"}),s.jsx("span",{className:"text-sm font-medium",children:"Acompanhar"})]}),s.jsxs(a.button,{onClick:()=>window.location.href="/setup",className:"bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(R,{className:"w-6 h-6"}),s.jsx("span",{className:"text-sm font-medium",children:"Configurações"})]}),s.jsxs(a.button,{onClick:()=>window.location.href="/ride-tracking",className:"bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(I,{className:"w-6 h-6"}),s.jsx("span",{className:"text-sm font-medium",children:"Histórico"})]}),s.jsxs(a.button,{onClick:()=>window.location.href="/free-ads",className:"bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200 relative overflow-hidden",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx("div",{className:"absolute top-0 right-0 bg-green-500 text-white text-xs px-2 py-1 rounded-bl-lg font-bold",children:"R$ 10"}),s.jsx(T,{className:"w-6 h-6"}),s.jsx("span",{className:"text-sm font-medium",children:"Ganhar Dinheiro"})]}),s.jsxs(a.button,{onClick:()=>window.location.href="/premium",className:"bg-gradient-to-r from-amber-500 to-orange-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200 relative overflow-hidden",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx("div",{className:"absolute top-0 right-0 bg-red-500 text-white text-xs px-2 py-1 rounded-bl-lg font-bold",children:"HOT"}),s.jsx(M,{className:"w-6 h-6"}),s.jsx("span",{className:"text-sm font-medium",children:"Premium"})]})]})]})}),s.jsx(a.div,{variants:n,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4 text-center",children:"📈 Suas Estatísticas"}),s.jsxs("div",{className:"grid grid-cols-3 gap-4 text-center",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-blue-400 mb-1",children:"12"}),s.jsx("div",{className:"text-xs text-white/70",children:"Corridas"})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-green-400 mb-1",children:"4.8"}),s.jsx("div",{className:"text-xs text-white/70",children:"Avaliação"})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-purple-400 mb-1",children:"R$ 240"}),s.jsx("div",{className:"text-xs text-white/70",children:"Economizado"})]})]})]})}),!1,s.jsx(a.div,{variants:n,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4 text-center",children:"👤 Minha Conta"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-white/70",children:"Email:"}),s.jsx("span",{className:"text-sm font-medium text-white",children:e?.email})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-white/70",children:"Nome:"}),s.jsx("span",{className:"text-sm font-medium text-white",children:e?.user_metadata?.full_name||"Não informado"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-white/70",children:"Telefone:"}),s.jsx("span",{className:"text-sm font-medium text-white",children:e?.user_metadata?.phone||"Não informado"})]})]})]})}),s.jsx(a.div,{variants:n,children:s.jsx("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:s.jsxs(a.button,{onClick:async()=>{await t()},className:"w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2 transition-all duration-200",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(D,{className:"w-4 h-4"}),s.jsx("span",{children:"Sair da Conta"})]})})})]})}),s.jsx(a.div,{className:"pb-4 text-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:.5},children:s.jsx("p",{className:"text-white/50 text-xs",children:"© 2024 MobiDrive. Todos os direitos reservados."})})]})]})};const us=new class{searchCache=new Map;routeCache=new Map;rideEstimateCache=new Map;config={searchTTL:6e5,routeTTL:9e5,rideEstimateTTL:9e5,maxItems:200};generateSearchKey(e,t){const s=t?JSON.stringify(t):"";return`search:${e.toLowerCase().trim()}:${s}`}generateRouteKey(e,t){return`route:${e[0]},${e[1]}:${t[0]},${t[1]}`}generateRideEstimateKey(e,t){return`estimate:${e[0].toFixed(4)},${e[1].toFixed(4)}:${t[0].toFixed(4)},${t[1].toFixed(4)}`}cleanExpired(e){const t=Date.now();for(const[s,a]of e.entries())t>a.expiresAt&&e.delete(s)}enforceMaxItems(e){if(e.size>this.config.maxItems){const t=Array.from(e.entries());t.sort(((e,t)=>e[1].timestamp-t[1].timestamp));const s=e.size-this.config.maxItems;for(let a=0;a<s;a++)e.delete(t[a][0])}}cacheSearchResults(e,t,s){const a=this.generateSearchKey(e,s),i=Date.now();this.searchCache.set(a,{data:t,timestamp:i,expiresAt:i+this.config.searchTTL}),this.cleanExpired(this.searchCache),this.enforceMaxItems(this.searchCache)}getCachedSearchResults(e,t){const s=this.generateSearchKey(e,t),a=this.searchCache.get(s);if(!a)return null;return Date.now()>a.expiresAt?(this.searchCache.delete(s),null):a.data}cacheRouteResults(e,t,s){const a=this.generateRouteKey(e,t),i=Date.now();this.routeCache.set(a,{data:s,timestamp:i,expiresAt:i+this.config.routeTTL}),this.cleanExpired(this.routeCache),this.enforceMaxItems(this.routeCache)}getCachedRouteResults(e,t){const s=this.generateRouteKey(e,t),a=this.routeCache.get(s);if(!a)return null;return Date.now()>a.expiresAt?(this.routeCache.delete(s),null):a.data}cacheRideEstimate(e,t,s){const a=this.generateRideEstimateKey(e,t),i=Date.now();this.rideEstimateCache.set(a,{data:s,timestamp:i,expiresAt:i+this.config.rideEstimateTTL}),this.cleanExpired(this.rideEstimateCache),this.enforceMaxItems(this.rideEstimateCache)}getCachedRideEstimate(e,t){const s=this.generateRideEstimateKey(e,t),a=this.rideEstimateCache.get(s);if(!a)return null;return Date.now()>a.expiresAt?(this.rideEstimateCache.delete(s),null):a.data}clearAll(){this.searchCache.clear(),this.routeCache.clear(),this.rideEstimateCache.clear()}cleanup(){this.cleanExpired(this.searchCache),this.cleanExpired(this.routeCache),this.cleanExpired(this.rideEstimateCache)}getStats(){return{searchCache:{size:this.searchCache.size,maxItems:this.config.maxItems,ttl:this.config.searchTTL},routeCache:{size:this.routeCache.size,maxItems:this.config.maxItems,ttl:this.config.routeTTL},rideEstimateCache:{size:this.rideEstimateCache.size,maxItems:this.config.maxItems,ttl:this.config.rideEstimateTTL}}}updateConfig(e){this.config={...this.config,...e}}};setInterval((()=>{us.cleanup()}),3e5);const xs=new class{events=[];sessionId;maxEvents=1e3;performanceMetrics={searchLatency:[],routeLatency:[],errorRate:0,cacheHitRate:0,apiCallsCount:0};constructor(){this.sessionId=this.generateSessionId(),this.startPeriodicReporting()}generateSessionId(){return`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}trackSearch(e,t,s,a=!1){this.addEvent({type:"search",timestamp:Date.now(),data:{query:e.substring(0,100),resultsCount:t.length,latency:s,fromCache:a,hasResults:t.length>0},sessionId:this.sessionId}),this.performanceMetrics.searchLatency.push(s),this.performanceMetrics.apiCallsCount+=a?0:1,this.updateCacheHitRate(a)}trackRoute(e,t,s,a,i=!1){const r=s[0];this.addEvent({type:"route",timestamp:Date.now(),data:{hasRoute:s.length>0,distance:r?.distance||0,duration:r?.duration||0,latency:a,fromCache:i,alternativesCount:s.length},sessionId:this.sessionId}),this.performanceMetrics.routeLatency.push(a),this.performanceMetrics.apiCallsCount+=i?0:1,this.updateCacheHitRate(i)}trackError(e,t,s){this.addEvent({type:"error",timestamp:Date.now(),data:{errorType:e,errorMessage:t.substring(0,200),context:s?JSON.stringify(s).substring(0,500):null},sessionId:this.sessionId}),this.updateErrorRate()}trackUserAction(e,t){this.addEvent({type:"user_action",timestamp:Date.now(),data:{action:e,...t},sessionId:this.sessionId})}trackPerformance(e,t,s){this.addEvent({type:"performance",timestamp:Date.now(),data:{metric:e,value:t,context:s},sessionId:this.sessionId})}addEvent(e){this.events.push(e),this.events.length>this.maxEvents&&(this.events=this.events.slice(-this.maxEvents))}updateCacheHitRate(e){const t=this.performanceMetrics.searchLatency.length+this.performanceMetrics.routeLatency.length,s=this.events.filter((e=>("search"===e.type||"route"===e.type)&&e.data.fromCache)).length;this.performanceMetrics.cacheHitRate=t>0?s/t*100:0}updateErrorRate(){const e=this.events.length,t=this.events.filter((e=>"error"===e.type)).length;this.performanceMetrics.errorRate=e>0?t/e*100:0}getSearchMetrics(){const e=this.events.filter((e=>"search"===e.type)),t=e.filter((e=>e.data.hasResults)),s=new Map;e.forEach((e=>{const t=e.data.query.toLowerCase();s.set(t,(s.get(t)||0)+1)}));const a=e.reduce(((e,t)=>e+t.data.resultsCount),0);return{totalSearches:e.length,successfulSearches:t.length,failedSearches:e.length-t.length,averageResultsCount:e.length>0?a/e.length:0,popularQueries:s}}getRouteMetrics(){const e=this.events.filter((e=>"route"===e.type)),t=e.filter((e=>e.data.hasRoute)),s=t.reduce(((e,t)=>e+t.data.distance),0),a=t.reduce(((e,t)=>e+t.data.duration),0);return{totalRoutes:e.length,successfulRoutes:t.length,failedRoutes:e.length-t.length,averageDistance:t.length>0?s/t.length:0,averageDuration:t.length>0?a/t.length:0,averagePrice:0}}getPerformanceSummary(){const e=this.performanceMetrics.searchLatency,t=this.performanceMetrics.routeLatency;return{searchPerformance:{averageLatency:e.length>0?e.reduce(((e,t)=>e+t),0)/e.length:0,minLatency:e.length>0?Math.min(...e):0,maxLatency:e.length>0?Math.max(...e):0,totalRequests:e.length},routePerformance:{averageLatency:t.length>0?t.reduce(((e,t)=>e+t),0)/t.length:0,minLatency:t.length>0?Math.min(...t):0,maxLatency:t.length>0?Math.max(...t):0,totalRequests:t.length},cacheHitRate:this.performanceMetrics.cacheHitRate,errorRate:this.performanceMetrics.errorRate,totalApiCalls:this.performanceMetrics.apiCallsCount,sessionDuration:Date.now()-parseInt(this.sessionId.split("_")[1])}}exportData(){return{sessionId:this.sessionId,events:this.events,searchMetrics:this.getSearchMetrics(),routeMetrics:this.getRouteMetrics(),performanceSummary:this.getPerformanceSummary(),exportedAt:(new Date).toISOString()}}clearData(){this.events=[],this.performanceMetrics={searchLatency:[],routeLatency:[],errorRate:0,cacheHitRate:0,apiCallsCount:0}}startPeriodicReporting(){setInterval((()=>{this.getPerformanceSummary()}),3e5)}};const ps=new class{FAVORITES_KEY="mapbox_favorites";RECENT_SEARCHES_KEY="mapbox_recent_searches";TRIP_HISTORY_KEY="mapbox_trip_history";MAX_RECENT_SEARCHES=20;MAX_TRIP_HISTORY=50;addFavorite(e){const t=this.getFavorites(),s={...e,id:this.generateId(),createdAt:Date.now(),lastUsed:Date.now(),useCount:1};return t.push(s),this.saveFavorites(t),s}removeFavorite(e){const t=this.getFavorites(),s=t.findIndex((t=>t.id===e));return-1!==s&&(t.splice(s,1),this.saveFavorites(t),!0)}updateFavorite(e,t){const s=this.getFavorites(),a=s.findIndex((t=>t.id===e));return-1===a?null:(s[a]={...s[a],...t},this.saveFavorites(s),s[a])}getFavorites(){try{const e=localStorage.getItem(this.FAVORITES_KEY);return e?JSON.parse(e):[]}catch(e){return[]}}getFavoritesByType(e){return this.getFavorites().filter((t=>t.type===e))}useFavorite(e){const t=this.getFavorites(),s=t.find((t=>t.id===e));return s?(s.useCount++,s.lastUsed=Date.now(),this.saveFavorites(t),s):null}addRecentSearch(e,t,s=!1){const a=this.getRecentSearches(),i=a.findIndex((s=>s.query.toLowerCase()===e.toLowerCase()&&s.result.id===t.id));-1!==i&&a.splice(i,1);const r={id:this.generateId(),query:e,result:t,timestamp:Date.now(),selected:s};a.unshift(r),a.length>this.MAX_RECENT_SEARCHES&&a.splice(this.MAX_RECENT_SEARCHES),this.saveRecentSearches(a)}getRecentSearches(){try{const e=localStorage.getItem(this.RECENT_SEARCHES_KEY);return e?JSON.parse(e):[]}catch(e){return[]}}clearRecentSearches(){localStorage.removeItem(this.RECENT_SEARCHES_KEY)}addTripHistory(e){const t=this.getTripHistory(),s={...e,id:this.generateId(),timestamp:Date.now()};return t.unshift(s),t.length>this.MAX_TRIP_HISTORY&&t.splice(this.MAX_TRIP_HISTORY),this.saveTripHistory(t),s}getTripHistory(){try{const e=localStorage.getItem(this.TRIP_HISTORY_KEY);return e?JSON.parse(e):[]}catch(e){return[]}}getFrequentDestinations(e=5){const t=this.getTripHistory(),s=new Map;return t.forEach((e=>{const t=`${e.destination.center[0]},${e.destination.center[1]}`,a=s.get(t);a?a.count++:s.set(t,{result:e.destination,count:1})})),Array.from(s.values()).sort(((e,t)=>t.count-e.count)).slice(0,e).map((e=>e.result))}getSuggestions(e="",t=8){const s=[],a=e.toLowerCase(),i=this.getFavorites().filter((t=>!e||t.name.toLowerCase().includes(a)||t.address.toLowerCase().includes(a))).sort(((e,t)=>t.useCount-e.useCount)).slice(0,3).map((e=>({id:e.id,place_name:e.address,center:e.coordinates,place_type:["favorite"],properties:{category:e.type},context:[],source:"favorite"})));if(s.push(...i),s.length<t){const i=this.getRecentSearches().filter((t=>!e||t.query.toLowerCase().includes(a)||t.result.place_name.toLowerCase().includes(a))).slice(0,t-s.length).map((e=>({...e.result,source:"recent"})));s.push(...i)}if(s.length<t){const i=this.getFrequentDestinations(t-s.length).filter((t=>!e||t.place_name.toLowerCase().includes(a))).map((e=>({...e,source:"frequent"})));s.push(...i)}return s.slice(0,t)}clearAllData(){localStorage.removeItem(this.FAVORITES_KEY),localStorage.removeItem(this.RECENT_SEARCHES_KEY),localStorage.removeItem(this.TRIP_HISTORY_KEY)}generateId(){return`${Date.now()}_${Math.random().toString(36).substr(2,9)}`}saveFavorites(e){try{localStorage.setItem(this.FAVORITES_KEY,JSON.stringify(e))}catch(t){}}saveRecentSearches(e){try{localStorage.setItem(this.RECENT_SEARCHES_KEY,JSON.stringify(e))}catch(t){}}saveTripHistory(e){try{localStorage.setItem(this.TRIP_HISTORY_KEY,JSON.stringify(e))}catch(t){}}};de.accessToken="pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g";const bs="https://api.mapbox.com",gs=`${bs}/geocoding/v5/mapbox.places`,fs=`${bs}/directions-matrix/v1/mapbox/driving`,ws=`${bs}/isochrone/v1/mapbox/driving`;class vs extends Error{constructor(e,t,s){super(e),this.code=t,this.details=s,this.name="MapboxError"}}const ys=new class{requests=[];maxRequests=30;timeWindow=6e4;lastRequestTime=0;minInterval=2e3;canMakeRequest(){const e=Date.now();return!(e-this.lastRequestTime<this.minInterval)&&(this.requests=this.requests.filter((t=>e-t<this.timeWindow)),this.requests.length<this.maxRequests)}recordRequest(){const e=Date.now();this.requests.push(e),this.lastRequestTime=e}getStats(){const e=Date.now(),t=this.requests.filter((t=>e-t<this.timeWindow));return{currentRequests:t.length,maxRequests:this.maxRequests,remainingRequests:this.maxRequests-t.length,resetTime:t.length>0?Math.max(...t)+this.timeWindow:e}}};const js=new class{accessToken;constructor(){if(this.accessToken="pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g",!this.accessToken)throw new Error("Mapbox access token is required")}async searchPlaces(e,t){if(!e.trim())return[];const s=Date.now(),a=us.getCachedSearchResults(e,t);if(a){const t=Date.now()-s;return xs.trackSearch(e,a,t,!0),ps.addRecentSearch(e,a[0],!1),a}if(!ys.canMakeRequest())throw xs.trackError("RATE_LIMIT","Rate limit exceeded",{query:e}),new vs("Rate limit exceeded. Please wait before making more requests.","RATE_LIMIT");const i=new URLSearchParams({access_token:this.accessToken,country:"br",language:"pt-BR",limit:(t?.limit||8).toString(),autocomplete:"true",fuzzyMatch:"true"});t?.proximity&&i.append("proximity",t.proximity.join(",")),t?.bbox&&i.append("bbox",t.bbox.join(",")),t?.types&&i.append("types",t.types.join(","));try{ys.recordRequest();const a=`${gs}/${encodeURIComponent(e)}.json?${i}`,r=await fetch(a,{method:"GET",headers:{Accept:"application/json"},signal:AbortSignal.timeout(1e4)});if(!r.ok){const e=await r.json().catch((()=>({})));throw new vs(`Geocoding API error: ${r.status}`,"API_ERROR",{status:r.status,...e})}const n=await r.json();if(!n.features)throw new vs("Invalid response format from Geocoding API","INVALID_RESPONSE",n);const o=n.features.filter((e=>e.center&&e.place_name)).map((e=>({id:e.id||`${e.center[0]}-${e.center[1]}`,place_name:e.place_name,center:e.center,place_type:e.place_type||[],properties:e.properties||{},context:e.context||[]}))).slice(0,t?.limit||8),l=Date.now()-s;return us.cacheSearchResults(e,o,t),xs.trackSearch(e,o,l,!1),o.length>0&&ps.addRecentSearch(e,o[0],!1),o}catch(r){const t=Date.now()-s;if(r instanceof vs)throw xs.trackError(r.code||"MAPBOX_ERROR",r.message,{query:e,latency:t}),r;if(r instanceof Error){if("AbortError"===r.name)throw xs.trackError("TIMEOUT","Request timeout",{query:e,latency:t}),new vs("Request timeout","TIMEOUT");throw xs.trackError("NETWORK_ERROR",r.message,{query:e,latency:t}),new vs(`Search failed: ${r.message}`,"NETWORK_ERROR",r)}throw xs.trackError("UNKNOWN_ERROR","Unknown error occurred",{query:e,latency:t,error:r}),new vs("Unknown error occurred during search","UNKNOWN_ERROR",r)}}async getDirections(e,t,s){if(!this.isValidCoordinate(e)||!this.isValidCoordinate(t))throw new vs("Invalid coordinates provided","INVALID_COORDINATES");if(!ys.canMakeRequest())throw new vs("Rate limit exceeded. Please wait before making more requests.","RATE_LIMIT");const a=s?.profile||"driving",i=new URLSearchParams({access_token:this.accessToken,alternatives:(!1!==s?.alternatives).toString(),steps:(!1!==s?.steps).toString(),geometries:s?.geometries||"geojson",overview:"full",language:"pt-BR",annotations:"distance,duration,speed,congestion"}),r=`${e.join(",")};${t.join(",")}`,n=`${bs}/directions/v5/mapbox/${a}`;try{ys.recordRequest();const e=await fetch(`${n}/${r}?${i}`,{method:"GET",headers:{Accept:"application/json"},signal:AbortSignal.timeout(15e3)});if(!e.ok){const t=await e.json().catch((()=>({})));if(429===e.status)throw new vs("Rate limit exceeded. Please wait before making more requests.","RATE_LIMIT",{status:e.status,...t});throw new vs(`Directions API error: ${e.status}`,"API_ERROR",{status:e.status,...t})}const t=await e.json();if(!t.routes)throw new vs("No routes found","NO_ROUTES",t);return t.routes.filter((e=>e.geometry&&e.distance&&e.duration)).map((e=>({distance:e.distance,duration:e.duration,geometry:e.geometry,legs:e.legs||[],weight:e.weight||e.duration,weight_name:e.weight_name||"duration",steps:e.legs?.[0]?.steps||[]})))}catch(o){if(o instanceof vs)throw o;if(o instanceof Error){if("AbortError"===o.name)throw new vs("Request timeout","TIMEOUT");throw new vs(`Directions failed: ${o.message}`,"NETWORK_ERROR",o)}throw new vs("Unknown error occurred getting directions","UNKNOWN_ERROR",o)}}isValidCoordinate(e){return Array.isArray(e)&&2===e.length&&"number"==typeof e[0]&&"number"==typeof e[1]&&e[0]>=-180&&e[0]<=180&&e[1]>=-90&&e[1]<=90}async calculateRideEstimate(e,t,s="economy"){e.join(","),t.join(",");const a=us.getCachedRideEstimate(e,t);if(a)return a;try{await new Promise((e=>setTimeout(e,300)));const a=await this.getDirections(e,t,{profile:"driving-traffic",alternatives:!1,steps:!1});if(!a.length)return null;const i=a[0],r=i.distance/1e3,n=i.duration/60,o=this.calculateDynamicPrice(r,n,s),l={distance:i.distance,duration:i.duration,price:o,route:i,vehicleType:s};return us.cacheRideEstimate(e,t,l),l}catch(i){return i instanceof vs&&"RATE_LIMIT"===i.code?this.getMockRideEstimate(e,t,s):null}}calculateDynamicPrice(e,t,s){const a={moto:{base:3,perKm:1.5,perMin:.2,surge:1},economy:{base:5,perKm:2,perMin:.3,surge:1},comfort:{base:8,perKm:3,perMin:.4,surge:1.1},premium:{base:12,perKm:4.5,perMin:.6,surge:1.2}},i=a[s]||a.economy;let r=i.base+e*i.perKm+t*i.perMin;const n=(new Date).getHours();(n>=7&&n<=9||n>=17&&n<=19)&&(r*=i.surge);const o={moto:4,economy:6,comfort:8,premium:12},l=o[s]||o.economy;return Math.max(r,l)}getMockRideEstimate(e,t,s="economy"){const a=this.calculateHaversineDistance(e,t),i=60*a;return{distance:1e3*a,duration:i,price:this.calculateDynamicPrice(a,i/60,s),route:{distance:1e3*a,duration:i,geometry:{coordinates:[e,t]},legs:[]},vehicleType:s}}async calculateRealTimeETA(e,t){try{const s=await this.getDirections(e,t,{profile:"driving-traffic",alternatives:!1});return s.length?Math.round(s[0].duration/60):null}catch(s){const a=this.calculateHaversineDistance(e,t);return Math.max(Math.round(a/30*60)+2,3)}}calculateHaversineDistance(e,t){const s=this.toRadians(t[1]-e[1]),a=this.toRadians(t[0]-e[0]),i=this.toRadians(e[1]),r=this.toRadians(t[1]),n=Math.sin(s/2)*Math.sin(s/2)+Math.sin(a/2)*Math.sin(a/2)*Math.cos(i)*Math.cos(r);return 6371*(2*Math.atan2(Math.sqrt(n),Math.sqrt(1-n)))}toRadians(e){return e*(Math.PI/180)}async getTravelTimeMatrix(e,t){const s=e.map((e=>e.join(","))).join(";"),a=t.map((e=>e.join(","))).join(";"),i=new URLSearchParams({access_token:this.accessToken,sources:Array.from({length:e.length},((e,t)=>t)).join(";"),destinations:Array.from({length:t.length},((e,t)=>t)).join(";")});try{const e=await fetch(`${fs}/${s};${a}?${i}`);return(await e.json()).durations||[]}catch(r){return[]}}async getIsochrone(e,t=[5,10,15]){const s=new URLSearchParams({access_token:this.accessToken,contours_minutes:t.join(","),polygons:"true",denoise:"1"}),a=e.join(",");try{const e=await fetch(`${ws}/${a}?${s}`);return await e.json()}catch(i){return null}}async findNearbyDrivers(e,t=5e3){const s=[{id:"1",name:"João Silva",coordinates:[e[0]+.01,e[1]+.005],eta:3,rating:4.8,vehicle:{model:"Honda Civic",plate:"ABC-1234",color:"Prata"}},{id:"2",name:"Maria Santos",coordinates:[e[0]-.008,e[1]-.003],eta:5,rating:4.9,vehicle:{model:"Toyota Corolla",plate:"XYZ-5678",color:"Branco"}},{id:"3",name:"Pedro Costa",coordinates:[e[0]+.005,e[1]-.008],eta:7,rating:4.7,vehicle:{model:"Hyundai HB20",plate:"DEF-9012",color:"Azul"}}];try{const t=s.map((e=>e.coordinates)),a=await this.getTravelTimeMatrix(t,[e]);return s.map(((e,t)=>({...e,eta:a[t]?Math.round(a[t][0]/60):e.eta})))}catch(a){return s}}async reverseGeocode(e){const t=new URLSearchParams({access_token:this.accessToken,language:"pt-BR",types:"address,poi"});try{const s=await fetch(`${gs}/${e.join(",")}.json?${t}`),a=await s.json();return a.features?.[0]?.place_name||"Endereço não encontrado"}catch(s){return"Erro ao obter endereço"}}getRateLimitStats(){return ys.getStats()}async getHealthStatus(){try{const e=this.getRateLimitStats();return{status:ys.canMakeRequest()?"healthy":"rate_limited",rateLimiting:e,timestamp:(new Date).toISOString()}}catch(e){return{status:"error",error:e instanceof Error?e.message:"Unknown error",timestamp:(new Date).toISOString()}}}},Ns=(e={})=>{const{userLocation:t,debounceMs:s=2500,minQueryLength:a=4}=e,[i,n]=r.useState({query:"",results:[],isLoading:!1,error:null,selectedResult:null}),[o,l]=r.useState({origin:null,destination:null,estimate:null,nearbyDrivers:[],isCalculating:!1}),c=r.useRef(),d=r.useRef(),m=r.useCallback((async e=>{c.current&&clearTimeout(c.current),d.current&&d.current.abort(),n((t=>({...t,query:e,error:null}))),e.length<a?n((e=>({...e,results:[],isLoading:!1}))):c.current=setTimeout((async()=>{n((e=>({...e,isLoading:!0})));try{d.current=new AbortController;const s=await js.searchPlaces(e,{proximity:t,types:["address","poi","place"],limit:8});n((e=>({...e,results:s,isLoading:!1})))}catch(s){if(s instanceof vs){let e="Erro ao buscar locais";switch(s.code){case"RATE_LIMIT":e="Muitas buscas. Aguarde um momento.";break;case"TIMEOUT":e="Busca demorou muito. Tente novamente.";break;case"NETWORK_ERROR":e="Erro de conexão. Verifique sua internet.";break;case"API_ERROR":e="Erro no serviço de busca.";break;default:e=s.message}n((t=>({...t,error:e,isLoading:!1,results:[]})))}else s instanceof Error&&"AbortError"!==s.name&&n((e=>({...e,error:"Erro inesperado ao buscar locais",isLoading:!1,results:[]})))}}),s)}),[t,s,a]),h=r.useCallback((e=>{n((t=>({...t,selectedResult:e,query:e.place_name,results:[]})))}),[]),u=r.useCallback((e=>{Array.isArray(e)?js.reverseGeocode(e).then((t=>{const s={id:"current-location",place_name:t,center:e,place_type:["address"],properties:{}};l((e=>({...e,origin:s})))})):l((t=>({...t,origin:e})))}),[]),x=r.useCallback((e=>{l((t=>({...t,destination:e})))}),[]),p=r.useCallback((async()=>{if(o.origin&&o.destination){l((e=>({...e,isCalculating:!0})));try{const e=await js.calculateRideEstimate(o.origin.center,o.destination.center);l((t=>({...t,estimate:e,isCalculating:!1})))}catch(e){l((e=>({...e,isCalculating:!1})))}}}),[o.origin,o.destination]),b=r.useCallback((async()=>{if(t)try{const e=await js.findNearbyDrivers(t);l((t=>({...t,nearbyDrivers:e})))}catch(e){}}),[t]),g=r.useCallback((()=>{c.current&&clearTimeout(c.current),d.current&&d.current.abort(),n({query:"",results:[],isLoading:!1,error:null,selectedResult:null})}),[]),f=r.useCallback((()=>{l({origin:null,destination:null,estimate:null,nearbyDrivers:[],isCalculating:!1})}),[]),w=r.useCallback((()=>new Promise(((e,t)=>{navigator.geolocation?navigator.geolocation.getCurrentPosition((t=>{e([t.coords.longitude,t.coords.latitude])}),(e=>{t(e)}),{enableHighAccuracy:!0,timeout:1e4,maximumAge:6e4}):t(new Error("Geolocation not supported"))}))),[]);return r.useEffect((()=>{if(o.origin&&o.destination&&!o.isCalculating){const e=setTimeout((()=>{p()}),3e3);return()=>clearTimeout(e)}}),[o.origin,o.destination,p,o.isCalculating]),r.useEffect((()=>{t&&b()}),[t,b]),r.useEffect((()=>()=>{c.current&&clearTimeout(c.current),d.current&&d.current.abort()}),[]),{searchQuery:i.query,searchResults:i.results,isSearching:i.isLoading,searchError:i.error,selectedResult:i.selectedResult,origin:o.origin,destination:o.destination,rideEstimate:o.estimate,nearbyDrivers:o.nearbyDrivers,isCalculatingRide:o.isCalculating,searchPlaces:m,selectResult:h,setOrigin:u,setDestination:x,calculateRideEstimate:p,findNearbyDrivers:b,clearSearch:g,clearRide:f,getCurrentLocation:w}},_s="pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g";de.accessToken=_s;const ks=[{id:"moto",name:"Moto",icon:"🏍️",description:"Rápido e econômico",basePrice:3,pricePerKm:1.5,estimatedTime:3,available:!0},{id:"carro",name:"Carro",icon:"🚗",description:"Conforto e segurança",basePrice:5,pricePerKm:2,estimatedTime:5,available:!0},{id:"suv",name:"SUV",icon:"🚙",description:"Espaço e luxo",basePrice:8,pricePerKm:3,estimatedTime:7,available:!0}],Ss=[{id:"card",name:"Cartão",icon:"💳",lastFour:"1234"},{id:"cash",name:"Dinheiro",icon:"💵"},{id:"pix",name:"PIX",icon:"📱"}],Cs={id:"driver-1",name:"João Silva",rating:4.9,vehicle:"Honda Civic Branco",plate:"ABC-1234",phone:"+5511999999999",estimatedArrival:5,location:[-46.6333,-23.5505]},Es=()=>{const{user:e}=Mt(),t=st(),[n,o]=r.useState("map"),[l,c]=r.useState(!1),[d,m]=r.useState(null),[u,p]=r.useState(null),[b,g]=r.useState(null),[w,v]=r.useState(null),[y,j]=r.useState(null),[N,_]=r.useState(null),[k,R]=r.useState(""),[T,M]=r.useState(null),[D,V]=r.useState(!1),[K,G]=r.useState(0),[Y,Z]=r.useState(""),[X,Q]=r.useState(!1),[ee,te]=r.useState(!1),[se,ae]=r.useState(!1),ie=r.useRef(null),re=r.useRef(null),[ne,oe]=r.useState(!1),le=r.useRef(null),ce=r.useRef(null),[me,he]=r.useState(!0),[ue,xe]=r.useState(!0),[pe,be]=r.useState([]);if(is(),!e)return s.jsx(ut,{to:"/login",replace:!0});const{searchQuery:ge,searchResults:fe,isSearching:we,searchPlaces:ve,selectResult:ye,getCurrentLocation:je}=Ns(),Ne=r.useCallback(((e,t)=>{const s=(t[1]-e[1])*Math.PI/180,a=(t[0]-e[0])*Math.PI/180,i=Math.sin(s/2)*Math.sin(s/2)+Math.cos(e[1]*Math.PI/180)*Math.cos(t[1]*Math.PI/180)*Math.sin(a/2)*Math.sin(a/2);return 6371*(2*Math.atan2(Math.sqrt(i),Math.sqrt(1-i)))}),[]),_e=r.useCallback((e=>b&&e.length?e.map((e=>({...e,distance:Ne(b,e.center)}))).sort(((e,t)=>e.distance-t.distance)):e),[b,Ne]),ke=r.useCallback((async e=>{try{const t=await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${e[0]},${e[1]}.json?access_token=${_s}&language=pt-BR&limit=1`),s=await t.json();if(s.features&&s.features.length>0)return s.features[0].place_name}catch(t){}return`${e[1].toFixed(4)}, ${e[0].toFixed(4)}`}),[]),Se=r.useCallback((e=>(le.current&&le.current.remove(),le.current=new de.Marker({color:"#ef4444",scale:1.2,draggable:!0}).setLngLat(e).addTo(re.current),le.current.on("dragstart",(()=>{re.current?.getSource("route")&&(re.current.removeLayer("route"),re.current.removeSource("route"))})),le.current.on("dragend",(async()=>{const e=le.current.getLngLat(),t=[e.lng,e.lat],s=await ke(t),a={id:`destination-${Date.now()}`,place_name:s,center:t,geometry:{type:"Point",coordinates:t},properties:{},context:[]};p(a),await Ce(b,t),v({distance:5e3,duration:900})})),le.current)),[ke,b]),Ce=r.useCallback((async(e,t)=>{if(re.current)try{const s=await fetch(`https://api.mapbox.com/directions/v5/mapbox/driving/${e[0]},${e[1]};${t[0]},${t[1]}?geometries=geojson&access_token=${_s}`),a=await s.json();if(a.routes&&a.routes.length>0){const e=a.routes[0];re.current.getSource("route")&&(re.current.removeLayer("route"),re.current.removeSource("route")),re.current.addSource("route",{type:"geojson",data:{type:"Feature",properties:{},geometry:e.geometry}}),re.current.addLayer({id:"route",type:"line",source:"route",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"#3b82f6","line-width":4,"line-opacity":.8}});const t=e.geometry.coordinates,s=t.reduce(((e,t)=>e.extend(t)),new de.LngLatBounds(t[0],t[0]));re.current.fitBounds(s,{padding:50,duration:1e3})}}catch(s){}}),[]),Ee=r.useCallback((e=>{ie.current&&!re.current&&(re.current=new de.Map({container:ie.current,style:"mapbox://styles/mapbox/dark-v11",center:e,zoom:14,attributionControl:!1,antialias:!0,pitch:0,bearing:0}),re.current.on("load",(()=>{oe(!0);const t=document.createElement("div");t.innerHTML='\n        <div style="\n          width: 20px;\n          height: 20px;\n          background: #3b82f6;\n          border: 3px solid white;\n          border-radius: 50%;\n          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);\n        "></div>\n      ',ce.current=new de.Marker({element:t}).setLngLat(e).addTo(re.current),re.current.on("click",(e=>{if(me){const t=[e.lngLat.lng,e.lngLat.lat];Re(t)}})),re.current.on("mouseenter",(()=>{me&&(re.current.getCanvas().style.cursor="crosshair")})),re.current.on("mouseleave",(()=>{re.current.getCanvas().style.cursor=""}))})),re.current.addControl(new de.NavigationControl,"bottom-right"))}),[me]),Re=r.useCallback((async e=>{Se(e);const t=await ke(e),s={id:`destination-${Date.now()}`,place_name:t,center:e,geometry:{type:"Point",coordinates:e},properties:{},context:[]};p(s),b&&await Ce(b,e),v({distance:5e3,duration:900})}),[Se,ke,b,Ce]);r.useEffect((()=>{(async()=>{try{const e=await je();g(e),Ee(e)}catch(e){const t=[-46.6333,-23.5505];g(t),Ee(t)}})()}),[je,Ee]),r.useEffect((()=>{("webkitSpeechRecognition"in window||"SpeechRecognition"in window)&&te(!0)}),[]),r.useEffect((()=>{const e=_e(fe);be(e)}),[fe,_e]),r.useEffect((()=>()=>{re.current&&re.current.remove()}),[]);const Ie=r.useCallback((e=>{if(!w)return e.basePrice;const t=w.distance/1e3;return e.basePrice+t*e.pricePerKm}),[w]),Te=r.useCallback((()=>{if(!ee)return;Q(!0);const e=new(window.webkitSpeechRecognition||window.SpeechRecognition);e.lang="pt-BR",e.continuous=!1,e.interimResults=!1,e.onresult=e=>{const t=e.results[0][0].transcript;ve(t),Q(!1)},e.onerror=()=>{Q(!1),m("Erro no reconhecimento de voz")},e.onend=()=>{Q(!1)},e.start()}),[ee,ve]),Me=r.useCallback((async e=>{p(e),ye(e),re.current&&e.center&&(Se(e.center),b&&await Ce(b,e.center),re.current.flyTo({center:e.center,zoom:16,duration:1e3,essential:!0})),v({distance:5e3,duration:900})}),[ye,Se,b,Ce]),De=r.useCallback((()=>{u&&(xe(!1),he(!1),o("details"))}),[u]),Pe=r.useCallback((e=>{j(e)}),[]),Ae=r.useCallback((e=>{_(e)}),[]),Le=r.useCallback((async()=>{y&&N&&u&&(c(!0),o("waiting"),setTimeout((()=>{M(Cs),c(!1)}),3e3))}),[y,N,u]),$e=r.useCallback((()=>{V(!0),o("riding")}),[]),Oe=r.useCallback((()=>{o("rating")}),[]),qe=r.useCallback((e=>{G(e)}),[]),Fe=r.useCallback((()=>{t("/dashboard")}),[t]),He=r.useCallback((()=>{ae(!0),alert("Emergência ativada! Contatos de emergência foram notificados.")}),[]),ze=r.useCallback((()=>{switch(n){case"details":o("map"),j(null),_(null),R("");break;case"waiting":o("details"),M(null);break;case"riding":case"rating":break;default:t("/dashboard")}}),[n,t]),Ue={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden",children:[s.jsx("div",{className:"absolute inset-0 bg-black/40"}),s.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6 px-4",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"flex items-center justify-between",children:["map"!==n&&"riding"!==n&&"rating"!==n&&s.jsx(a.button,{onClick:ze,className:"p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(P,{className:"w-5 h-5"})}),s.jsxs("div",{className:"flex-1 text-center",children:[s.jsxs("h1",{className:"text-2xl font-bold text-white",children:["MOBI",s.jsx("span",{className:"text-blue-400",children:"DRIVE"})]}),s.jsxs("p",{className:"text-sm text-white/70",children:["map"===n&&"Escolha o destino","details"===n&&"Detalhes da corrida","waiting"===n&&"Aguardando motorista","riding"===n&&"Corrida em andamento","rating"===n&&"Avalie sua corrida"]})]}),("waiting"===n||"riding"===n)&&s.jsx(a.button,{onClick:He,className:"p-2 rounded-xl bg-red-500/20 backdrop-blur-sm border border-red-500/30 text-red-400",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(A,{className:"w-5 h-5"})}),"map"===n&&s.jsx("div",{className:"w-9"})]})}),s.jsx(i,{children:d&&s.jsx(a.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"mx-4 mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-xl backdrop-blur-sm",children:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(h,{className:"w-5 h-5 text-red-400"}),s.jsx("p",{className:"text-red-200 text-sm",children:d})]})})}),s.jsx("div",{className:"flex-1 px-4 pb-8",children:s.jsxs(a.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.6,staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"space-y-6",children:["map"===n&&s.jsxs(a.div,{variants:Ue,className:""+(ue?"fixed inset-0 z-50":"space-y-4"),children:[s.jsxs("div",{className:`relative ${ue?"h-screen":"h-[70vh] rounded-2xl border border-white/20 shadow-2xl"} overflow-hidden bg-gray-900 ${me?"selection-mode":""}`,children:[s.jsx("div",{ref:ie,className:"w-full h-full"}),ue&&s.jsx("div",{className:"absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/50 to-transparent p-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("button",{onClick:()=>t("/dashboard"),className:"flex items-center space-x-2 text-white/80 hover:text-white transition-colors",children:[s.jsx(P,{className:"w-5 h-5"}),s.jsx("span",{className:"text-sm font-medium",children:"Voltar"})]}),s.jsx("div",{className:"text-white/80 text-sm font-medium",children:"Selecione o destino"})]})}),s.jsx("div",{className:"absolute top-20 left-4 right-4 z-10",children:s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-3 border border-white/20 shadow-lg",children:[s.jsxs("div",{className:"relative",children:[s.jsx(L,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4"}),s.jsx("input",{type:"text",value:ge,onChange:e=>ve(e.target.value),placeholder:"Para onde vamos?",className:"w-full pl-10 pr-16 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"}),ee&&s.jsx("button",{onClick:Te,disabled:X,className:"absolute right-8 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors disabled:opacity-50",children:X?s.jsx($,{className:"w-4 h-4 text-red-400 animate-pulse"}):s.jsx(O,{className:"w-4 h-4"})}),we&&s.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:s.jsx(q,{className:"w-4 h-4 animate-spin text-blue-400"})})]}),pe.length>0&&s.jsx("div",{className:"mt-3 bg-white/10 rounded-lg border border-white/20 overflow-hidden max-h-48 overflow-y-auto",children:pe.map(((e,t)=>s.jsx("button",{onClick:()=>Me(e),className:"w-full text-left px-3 py-2 text-white hover:bg-white/10 transition-colors border-b border-white/10 last:border-b-0",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2 flex-1",children:[s.jsx(f,{className:"w-3 h-3 text-white/60 flex-shrink-0"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-sm font-medium",children:e.text}),s.jsx("p",{className:"text-xs text-white/60 truncate",children:e.place_name})]})]}),e.distance&&s.jsx("div",{className:"text-xs text-white/50 ml-2",children:e.distance<1?`${Math.round(1e3*e.distance)}m`:`${e.distance.toFixed(1)}km`})]})},t)))})]})}),me&&!u&&s.jsx("div",{className:"absolute bottom-32 left-1/2 transform -translate-x-1/2 z-10",children:s.jsx("div",{className:"bg-black/60 backdrop-blur-sm rounded-full px-4 py-2",children:s.jsx("span",{className:"text-white text-sm",children:"Toque no mapa"})})}),!ne&&s.jsx("div",{className:"absolute inset-0 bg-gray-900/80 flex items-center justify-center z-30",children:s.jsxs("div",{className:"text-center text-white",children:[s.jsx(q,{className:"w-12 h-12 animate-spin mx-auto mb-4"}),s.jsx("p",{className:"text-lg font-medium",children:"Carregando mapa..."}),s.jsx("p",{className:"text-sm text-white/70",children:"Aguarde um momento"})]})})]}),u&&s.jsx(a.div,{initial:{opacity:0,y:100},animate:{opacity:1,y:0},className:(ue?"absolute bottom-0 left-0 right-0":"")+" z-20",children:s.jsxs("div",{className:"bg-gradient-to-t from-black/90 via-black/60 to-transparent p-4 pt-8",children:[s.jsx("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-3 mb-3",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center",children:s.jsx(f,{className:"w-4 h-4 text-white"})}),s.jsx("div",{className:"flex-1",children:s.jsx("p",{className:"text-white text-sm font-medium line-clamp-1",children:u.place_name})})]}),s.jsx("button",{onClick:()=>{p(null),le.current&&(le.current.remove(),le.current=null),re.current?.getSource("route")&&(re.current.removeLayer("route"),re.current.removeSource("route"))},className:"text-white/60 hover:text-white transition-colors p-1",children:"✕"})]})}),s.jsx(a.button,{onClick:De,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200",whileHover:{scale:1.01},whileTap:{scale:.99},children:"Confirmar destino"})]})})]}),"details"===n&&s.jsxs(a.div,{variants:Ue,className:"space-y-6",children:[s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h3",{className:"text-xl font-semibold text-white mb-6 text-center",children:"📋 Detalhes da corrida"}),s.jsxs("div",{className:"space-y-4 mb-6",children:[s.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-white/5 rounded-xl",children:[s.jsx("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:s.jsx(f,{className:"w-4 h-4 text-white"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-sm font-medium text-white",children:"Origem"}),s.jsx("p",{className:"text-xs text-white/70",children:"Localização atual"})]})]}),s.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-white/5 rounded-xl",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center",children:s.jsx(F,{className:"w-4 h-4 text-white"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-sm font-medium text-white",children:"Destino"}),s.jsx("p",{className:"text-xs text-white/70",children:u?.place_name||"Destino selecionado"})]})]}),w&&s.jsxs("div",{className:"flex items-center justify-between p-3 bg-blue-500/10 rounded-xl border border-blue-500/30",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(H,{className:"w-4 h-4 text-blue-400"}),s.jsxs("span",{className:"text-sm text-white",children:[(w.distance/1e3).toFixed(1)," km"]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(I,{className:"w-4 h-4 text-blue-400"}),s.jsxs("span",{className:"text-sm text-white",children:[Math.round(w.duration/60)," min"]})]})]})]})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"🚗 Escolha o veículo"}),s.jsx("div",{className:"grid grid-cols-3 gap-3",children:ks.map((e=>{const t=Ie(e),i=y?.id===e.id;return s.jsx(a.button,{onClick:()=>Pe(e),className:"p-4 rounded-xl border transition-all "+(i?"bg-blue-500/20 border-blue-500/50 ring-2 ring-blue-500/30":"bg-white/5 border-white/20 hover:bg-white/10"),whileHover:{scale:1.02},whileTap:{scale:.98},children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl mb-2",children:e.icon}),s.jsx("h5",{className:"font-semibold text-white text-sm",children:e.name}),s.jsx("p",{className:"text-xs text-white/60 mb-2",children:e.description}),s.jsxs("div",{className:"text-sm font-bold text-white",children:["R$ ",t.toFixed(2)]}),s.jsxs("div",{className:"flex items-center justify-center space-x-1 mt-1",children:[s.jsx(I,{className:"w-3 h-3 text-white/60"}),s.jsxs("span",{className:"text-xs text-white/60",children:[e.estimatedTime,"min"]})]})]})},e.id)}))})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"💳 Forma de pagamento"}),s.jsx("div",{className:"space-y-3",children:Ss.map((e=>{const t=N?.id===e.id;return s.jsx(a.button,{onClick:()=>Ae(e),className:"w-full p-4 rounded-xl border transition-all "+(t?"bg-green-500/20 border-green-500/50 ring-2 ring-green-500/30":"bg-white/5 border-white/20 hover:bg-white/10"),whileHover:{scale:1.02},whileTap:{scale:.98},children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("span",{className:"text-xl",children:e.icon}),s.jsxs("div",{className:"text-left",children:[s.jsx("h5",{className:"font-semibold text-white",children:e.name}),e.lastFour&&s.jsxs("p",{className:"text-sm text-white/70",children:["•••• ",e.lastFour]})]})]}),t&&s.jsx(x,{className:"w-5 h-5 text-green-400"})]})},e.id)}))})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"📝 Observações (opcional)"}),s.jsx("textarea",{value:k,onChange:e=>R(e.target.value),placeholder:"Alguma observação para o motorista?",className:"w-full p-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none",rows:3})]}),y&&N&&s.jsx(a.button,{onClick:Le,disabled:l,className:"w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 disabled:opacity-50 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},initial:{opacity:0,y:20},animate:{opacity:1,y:0},children:l?s.jsx(q,{className:"w-5 h-5 animate-spin"}):s.jsxs(s.Fragment,{children:[s.jsx(z,{className:"w-5 h-5"}),s.jsx("span",{children:"Solicitar motorista"})]})})]}),"waiting"===n&&s.jsxs(a.div,{variants:Ue,className:"space-y-6",children:[s.jsx("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 shadow-2xl text-center",children:T?s.jsxs(s.Fragment,{children:[s.jsx(a.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",duration:.6},className:"w-16 h-16 mx-auto mb-6 bg-green-500 rounded-full flex items-center justify-center",children:s.jsx(x,{className:"w-8 h-8 text-white"})}),s.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"✅ Motorista encontrado!"}),s.jsx("p",{className:"text-white/70 mb-6",children:"Seu motorista está a caminho"})]}):s.jsxs(s.Fragment,{children:[s.jsx(a.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-16 h-16 mx-auto mb-6",children:s.jsx("div",{className:"w-full h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center",children:s.jsx(E,{className:"w-8 h-8 text-white"})})}),s.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"🔍 Procurando motorista"}),s.jsx("p",{className:"text-white/70 mb-6",children:"Estamos encontrando o melhor motorista para você..."}),s.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-bounce"}),s.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),s.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})}),T&&s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[s.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center",children:s.jsx(S,{className:"w-8 h-8 text-white"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"text-lg font-semibold text-white",children:T.name}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(U,{className:"w-4 h-4 text-yellow-400"}),s.jsx("span",{className:"text-sm text-white/70",children:T.rating}),s.jsx("span",{className:"text-white/50",children:"•"}),s.jsx("span",{className:"text-sm text-white/70",children:T.vehicle})]}),s.jsx("p",{className:"text-sm text-white/60",children:T.plate})]})]}),s.jsx("div",{className:"bg-blue-500/10 rounded-xl p-4 border border-blue-500/30",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(I,{className:"w-4 h-4 text-blue-400"}),s.jsx("span",{className:"text-white",children:"Tempo estimado"})]}),s.jsxs("span",{className:"text-lg font-bold text-white",children:[T.estimatedArrival," min"]})]})}),s.jsxs("div",{className:"flex space-x-3 mt-4",children:[s.jsxs(a.button,{onClick:()=>window.open(`tel:${T.phone}`),className:"flex-1 bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(C,{className:"w-4 h-4"}),s.jsx("span",{children:"Ligar"})]}),s.jsxs(a.button,{onClick:()=>window.open(`sms:${T.phone}`),className:"flex-1 bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(B,{className:"w-4 h-4"}),s.jsx("span",{children:"Mensagem"})]})]})]}),s.jsx("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl overflow-hidden",children:s.jsxs("div",{className:"h-64 bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center relative",children:[s.jsxs("div",{className:"text-center text-white/70",children:[s.jsx(f,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),s.jsx("p",{className:"text-sm",children:"Mapa com motorista aproximando"})]}),T&&s.jsx("div",{className:"absolute top-1/4 left-1/3 transform -translate-x-1/2 -translate-y-1/2",children:s.jsx(a.div,{animate:{scale:[1,1.2,1]},transition:{duration:2,repeat:1/0},className:"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center shadow-lg",children:s.jsx(E,{className:"w-3 h-3 text-white"})})})]})}),T&&s.jsxs(a.button,{onClick:$e,className:"w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2},children:[s.jsx(W,{className:"w-5 h-5"}),s.jsx("span",{children:"Motorista chegou - Iniciar corrida"})]})]}),"riding"===n&&s.jsxs(a.div,{variants:Ue,className:"space-y-6",children:[s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsxs("div",{className:"text-center mb-4",children:[s.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"🚗 Corrida em andamento"}),s.jsx("p",{className:"text-white/70",children:"Você está a caminho do destino"})]}),T&&s.jsxs("div",{className:"flex items-center space-x-4 p-4 bg-white/5 rounded-xl",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center",children:s.jsx(S,{className:"w-6 h-6 text-white"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-semibold text-white",children:T.name}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(U,{className:"w-4 h-4 text-yellow-400"}),s.jsx("span",{className:"text-sm text-white/70",children:T.rating}),s.jsx("span",{className:"text-white/50",children:"•"}),s.jsx("span",{className:"text-sm text-white/70",children:T.vehicle})]})]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx(a.button,{onClick:()=>window.open(`tel:${T.phone}`),className:"p-2 bg-green-500 hover:bg-green-600 rounded-lg transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(C,{className:"w-4 h-4 text-white"})}),s.jsx(a.button,{onClick:()=>window.open(`sms:${T.phone}`),className:"p-2 bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(B,{className:"w-4 h-4 text-white"})})]})]})]}),s.jsx("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl overflow-hidden",children:s.jsxs("div",{className:"h-80 bg-gradient-to-br from-green-500/20 to-blue-500/20 flex items-center justify-center relative",children:[s.jsxs("div",{className:"text-center text-white/70",children:[s.jsx(H,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),s.jsx("p",{className:"text-sm",children:"Rota em tempo real"}),s.jsx("p",{className:"text-xs",children:"Acompanhe sua viagem"})]}),s.jsx("div",{className:"absolute inset-4 border-2 border-dashed border-blue-400/50 rounded-lg"}),s.jsx("div",{className:"absolute top-1/2 left-1/4 transform -translate-x-1/2 -translate-y-1/2",children:s.jsx(a.div,{animate:{scale:[1,1.2,1]},transition:{duration:2,repeat:1/0},className:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg",children:s.jsx(E,{className:"w-3 h-3 text-white"})})}),s.jsx("div",{className:"absolute top-1/2 right-1/4 transform translate-x-1/2 -translate-y-1/2",children:s.jsx("div",{className:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center shadow-lg",children:s.jsx(F,{className:"w-3 h-3 text-white"})})})]})}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"📊 Progresso da viagem"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-white/70",children:"Distância percorrida"}),s.jsx("span",{className:"text-white font-semibold",children:"2.3 km"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-white/70",children:"Tempo decorrido"}),s.jsx("span",{className:"text-white font-semibold",children:"8 min"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-white/70",children:"Tempo restante"}),s.jsx("span",{className:"text-white font-semibold",children:"7 min"})]}),s.jsx("div",{className:"w-full bg-white/20 rounded-full h-2",children:s.jsx(a.div,{className:"bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full",initial:{width:0},animate:{width:"60%"},transition:{duration:2}})})]})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs(a.button,{onClick:()=>{navigator.share?.({title:"Compartilhar trajeto",text:"Estou em uma corrida MobiDrive",url:window.location.href})},className:"w-full bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/50 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(J,{className:"w-5 h-5"}),s.jsx("span",{children:"Compartilhar trajeto"})]}),s.jsxs(a.button,{onClick:Oe,className:"w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:3},children:[s.jsx(x,{className:"w-5 h-5"}),s.jsx("span",{children:"Chegamos ao destino - Finalizar corrida"})]})]})]}),"rating"===n&&s.jsxs(a.div,{variants:Ue,className:"space-y-6",children:[s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 shadow-2xl text-center",children:[s.jsx(a.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",duration:.6},className:"w-16 h-16 mx-auto mb-6 bg-green-500 rounded-full flex items-center justify-center",children:s.jsx(x,{className:"w-8 h-8 text-white"})}),s.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"✅ Corrida finalizada!"}),s.jsx("p",{className:"text-white/70 mb-6",children:"Você chegou ao seu destino com segurança"}),s.jsx("div",{className:"bg-white/5 rounded-xl p-4 mb-6",children:s.jsxs("div",{className:"grid grid-cols-3 gap-4 text-center",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-2xl font-bold text-white",children:"5.0"}),s.jsx("p",{className:"text-xs text-white/60",children:"km"})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-2xl font-bold text-white",children:"15"}),s.jsx("p",{className:"text-xs text-white/60",children:"min"})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-2xl font-bold text-white",children:"R$ 12,50"}),s.jsx("p",{className:"text-xs text-white/60",children:"total"})]})]})})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h4",{className:"text-lg font-semibold text-white mb-4 text-center",children:"⭐ Avalie sua experiência"}),T&&s.jsxs("div",{className:"flex items-center space-x-4 mb-6 p-4 bg-white/5 rounded-xl",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center",children:s.jsx(S,{className:"w-6 h-6 text-white"})}),s.jsxs("div",{children:[s.jsx("h5",{className:"font-semibold text-white",children:T.name}),s.jsx("p",{className:"text-sm text-white/70",children:T.vehicle})]})]}),s.jsx("div",{className:"flex justify-center space-x-2 mb-6",children:[1,2,3,4,5].map((e=>s.jsx(a.button,{onClick:()=>qe(e),className:"text-3xl transition-colors "+(e<=K?"text-yellow-400":"text-white/30"),whileHover:{scale:1.1},whileTap:{scale:.9},children:s.jsx(U,{className:"w-8 h-8 "+(e<=K?"fill-current":"")})},e)))}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-sm font-medium text-white/90 mb-2",children:"💬 Comentário (opcional)"}),s.jsx("textarea",{value:Y,onChange:e=>Z(e.target.value),placeholder:"Como foi sua experiência?",className:"w-full p-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none",rows:3})]}),s.jsxs(a.button,{onClick:Fe,disabled:0===K,className:"w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2",whileHover:{scale:K>0?1.02:1},whileTap:{scale:K>0?.98:1},children:[s.jsx(U,{className:"w-5 h-5"}),s.jsx("span",{children:"Finalizar avaliação"})]})]})]})]})})]})]})};class Rs{static instance;static getInstance(){return Rs.instance||(Rs.instance=new Rs),Rs.instance}async sendTextMessage(e,t){try{const{data:{user:s}}=await Rt.auth.getUser();if(!s)throw new Error("Usuário não autenticado");const{data:a}=await Rt.from("profiles").select("user_type").eq("id",s.id).single();if(!a)throw new Error("Perfil não encontrado");const{data:i,error:r}=await Rt.from("chat_messages").insert({ride_id:e,sender_id:s.id,sender_type:a.user_type,message:t.trim(),message_type:"text"}).select().single();return r?null:i}catch(s){return null}}async sendLocationMessage(e,t){try{const{data:{user:s}}=await Rt.auth.getUser();if(!s)throw new Error("Usuário não autenticado");const{data:a}=await Rt.from("profiles").select("user_type").eq("id",s.id).single();if(!a)throw new Error("Perfil não encontrado");const{data:i,error:r}=await Rt.from("chat_messages").insert({ride_id:e,sender_id:s.id,sender_type:a.user_type,message:"Localização compartilhada",message_type:"location",metadata:{location:t}}).select().single();return r?null:i}catch(s){return null}}async sendSystemMessage(e,t,s){try{const a=s||{ride_started:"🚗 Corrida iniciada! O motorista está a caminho.",ride_completed:"✅ Corrida finalizada! Obrigado por usar o MobiDrive.",driver_arrived:"📍 Motorista chegou ao local de partida!"}[t],{data:i,error:r}=await Rt.from("chat_messages").insert({ride_id:e,sender_id:"00000000-0000-0000-0000-000000000000",sender_type:"driver",message:a,message_type:"system",metadata:{system_type:t}}).select().single();return r?null:i}catch(a){return null}}async getChatMessages(e,t=50){try{const{data:s,error:a}=await Rt.from("chat_messages").select("\n          *,\n          sender:profiles!chat_messages_sender_id_fkey(\n            id,\n            full_name\n          )\n        ").eq("ride_id",e).order("created_at",{ascending:!0}).limit(t);return a?[]:s||[]}catch(s){return[]}}async markMessagesAsRead(e){try{const{data:{user:t}}=await Rt.auth.getUser();if(!t)return!1;const{error:s}=await Rt.from("chat_messages").update({read_at:(new Date).toISOString()}).eq("ride_id",e).neq("sender_id",t.id).is("read_at",null);return!s}catch(t){return!1}}async getChatParticipants(e){try{const{data:t,error:s}=await Rt.from("ride_requests").select("\n          user_id,\n          driver_id,\n          user:profiles!ride_requests_user_id_fkey(\n            id,\n            full_name,\n            user_type\n          ),\n          driver:profiles!ride_requests_driver_id_fkey(\n            id,\n            full_name,\n            user_type\n          )\n        ").eq("id",e).single();if(s||!t)return[];const a=[];return t.user&&a.push({id:t.user.id,name:t.user.full_name||"Passageiro",user_type:"passenger",is_online:!0}),t.driver&&a.push({id:t.driver.id,name:t.driver.full_name||"Motorista",user_type:"driver",is_online:!0}),a}catch(t){return[]}}subscribeToMessages(e,t){return{unsubscribe:()=>{}}}async getUnreadCount(e){try{const{data:{user:t}}=await Rt.auth.getUser();if(!t)return 0;const{count:s,error:a}=await Rt.from("chat_messages").select("*",{count:"exact",head:!0}).eq("ride_id",e).neq("sender_id",t.id).is("read_at",null);return a?0:s||0}catch(t){return 0}}async deleteMessage(e){try{const{data:{user:t}}=await Rt.auth.getUser();if(!t)return!1;const{error:s}=await Rt.from("chat_messages").delete().eq("id",e).eq("sender_id",t.id);return!s}catch(t){return!1}}async searchMessages(e,t){try{const{data:s,error:a}=await Rt.from("chat_messages").select("*").eq("ride_id",e).ilike("message",`%${t}%`).order("created_at",{ascending:!1}).limit(20);return a?[]:s||[]}catch(s){return[]}}}const Is=Rs.getInstance(),Ts=({rideId:e,onClose:t,isOpen:n})=>{const{user:o}=Mt(),[l,c]=r.useState([]),[d,m]=r.useState([]),[h,u]=r.useState(""),[x,p]=r.useState(!1),[b,g]=r.useState(0),w=r.useRef(null),v=r.useRef(null);r.useEffect((()=>(n&&e&&(y(),j(),N()),()=>{v.current&&v.current.unsubscribe()})),[n,e]),r.useEffect((()=>{k()}),[l]);const y=async()=>{p(!0);try{const[t,s,a]=await Promise.all([Is.getChatMessages(e),Is.getChatParticipants(e),Is.getUnreadCount(e)]);c(t),m(s),g(a)}catch(t){}finally{p(!1)}},j=()=>{v.current=Is.subscribeToMessages(e,(e=>{c((t=>[...t,e])),e.sender_id!==o?.id&&setTimeout((()=>N()),1e3)}))},N=async()=>{await Is.markMessagesAsRead(e),g(0)},_=async()=>{if(!h.trim()||x)return;const t=h.trim();u(""),p(!0);try{await Is.sendTextMessage(e,t)}catch(s){u(t)}finally{p(!1)}},k=()=>{w.current?.scrollIntoView({behavior:"smooth"})},E=e=>new Date(e).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"}),R=d.find((e=>e.id!==o?.id));return n?s.jsxs(a.div,{initial:{opacity:0,y:"100%"},animate:{opacity:1,y:0},exit:{opacity:0,y:"100%"},transition:{duration:.3,ease:"easeOut"},className:"fixed inset-0 z-50 bg-black flex flex-col",children:[s.jsx("div",{className:"bg-black/20 backdrop-blur-md border-b border-white/10 p-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(a.button,{onClick:t,className:"p-2 text-white/80 hover:text-white transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(P,{className:"w-5 h-5"})}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center",children:s.jsx(S,{className:"w-5 h-5 text-white"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-white font-semibold",children:R?.name||"Chat"}),s.jsx("p",{className:"text-white/60 text-sm",children:R?.is_online?"Online":"Offline"})]})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(a.button,{className:"p-2 text-white/80 hover:text-white transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(C,{className:"w-5 h-5"})}),s.jsx(a.button,{className:"p-2 text-white/80 hover:text-white transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(V,{className:"w-5 h-5"})})]})]})}),s.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[x&&0===l.length?s.jsx("div",{className:"flex justify-center items-center h-full",children:s.jsx("div",{className:"text-white/60",children:"Carregando mensagens..."})}):s.jsx(i,{children:l.map(((e,t)=>{const i=e.sender_id===o?.id,r="system"===e.message_type;return s.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:`flex ${i?"justify-end":"justify-start"} ${r?"justify-center":""}`,children:r?s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-xl px-4 py-2 max-w-xs",children:[s.jsx("p",{className:"text-white/80 text-sm text-center",children:e.message}),s.jsx("p",{className:"text-white/50 text-xs text-center mt-1",children:E(e.created_at)})]}):s.jsxs("div",{className:"max-w-xs "+(i?"ml-12":"mr-12"),children:[s.jsx("div",{className:"rounded-2xl px-4 py-3 "+(i?"bg-gradient-to-r from-blue-500 to-purple-500 text-white":"bg-white/10 backdrop-blur-md text-white border border-white/20"),children:"location"===e.message_type?s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(f,{className:"w-4 h-4"}),s.jsx("span",{className:"text-sm",children:"Localização compartilhada"})]}):s.jsx("p",{className:"text-sm",children:e.message})}),s.jsxs("div",{className:"flex items-center space-x-1 mt-1 "+(i?"justify-end":"justify-start"),children:[s.jsx("span",{className:"text-white/50 text-xs",children:E(e.created_at)}),i&&s.jsx("div",{className:"text-white/50",children:e.read_at?s.jsx(K,{className:"w-3 h-3"}):s.jsx(G,{className:"w-3 h-3"})})]})]})},e.id)}))}),s.jsx("div",{ref:w})]}),s.jsx("div",{className:"bg-black/20 backdrop-blur-md border-t border-white/10 p-4",children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(a.button,{onClick:async()=>{navigator.geolocation&&(p(!0),navigator.geolocation.getCurrentPosition((async t=>{try{const s=[t.coords.longitude,t.coords.latitude];await Is.sendLocationMessage(e,s)}catch(s){}finally{p(!1)}}),(e=>{p(!1)})))},disabled:x,className:"p-3 bg-white/10 backdrop-blur-md rounded-xl text-white/80 hover:text-white transition-colors disabled:opacity-50",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(f,{className:"w-5 h-5"})}),s.jsx("div",{className:"flex-1 relative",children:s.jsx("input",{type:"text",value:h,onChange:e=>u(e.target.value),onKeyPress:e=>"Enter"===e.key&&_(),placeholder:"Digite sua mensagem...",className:"w-full bg-white/10 backdrop-blur-md border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:x})}),s.jsx(a.button,{onClick:_,disabled:!h.trim()||x,className:"p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl text-white disabled:opacity-50 disabled:cursor-not-allowed",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(z,{className:"w-5 h-5"})})]})})]}):null};class Ms{static instance;static getInstance(){return Ms.instance||(Ms.instance=new Ms),Ms.instance}constructor(){this.requestPermission()}async requestPermission(){if(!("Notification"in window))return!1;if("granted"===Notification.permission)return!0;if("denied"!==Notification.permission){return"granted"===await Notification.requestPermission()}return!1}async createNotification(e,t,s,a,i,r="normal"){try{const{data:n,error:o}=await Rt.from("notifications").insert({user_id:e,title:t,message:s,type:a,data:i,priority:r}).select().single();return o?null:(await this.showNativeNotification(n),n)}catch(n){return null}}async showNativeNotification(e){try{if("granted"!==Notification.permission)return;const t=new Notification(e.title,{body:e.message,icon:"/icons/icon-192x192.png",badge:"/icons/icon-72x72.png",tag:e.type,requireInteraction:"urgent"===e.priority,data:e.data});t.onclick=()=>{window.focus(),t.close(),this.handleNotificationClick(e)},"urgent"!==e.priority&&setTimeout((()=>t.close()),5e3)}catch(t){}}handleNotificationClick(e){const{type:t,data:s}=e;switch(t){case"ride_request":case"ride_accepted":case"ride_started":s?.ride_id&&(window.location.href=`/ride-tracking/${s.ride_id}`);break;case"chat_message":s?.ride_id&&(window.location.href=`/ride-tracking/${s.ride_id}?tab=chat`);break;case"payment":window.location.href="/payment-history";break;default:window.location.href="/dashboard"}}async getUserNotifications(e=20,t=0){try{const{data:{user:s}}=await Rt.auth.getUser();if(!s)return[];const{data:a,error:i}=await Rt.from("notifications").select("*").eq("user_id",s.id).order("created_at",{ascending:!1}).range(t,t+e-1);return i?[]:a||[]}catch(s){return[]}}async markAsRead(e){try{const{error:t}=await Rt.from("notifications").update({read_at:(new Date).toISOString()}).eq("id",e);return!t}catch(t){return!1}}async markAllAsRead(){try{const{data:{user:e}}=await Rt.auth.getUser();if(!e)return!1;const{error:t}=await Rt.from("notifications").update({read_at:(new Date).toISOString()}).eq("user_id",e.id).is("read_at",null);return!t}catch(e){return!1}}async getUnreadCount(){try{const{data:{user:e}}=await Rt.auth.getUser();if(!e)return 0;const{count:t,error:s}=await Rt.from("notifications").select("*",{count:"exact",head:!0}).eq("user_id",e.id).is("read_at",null);return s?0:t||0}catch(e){return 0}}subscribeToNotifications(e){return Rt.channel("user_notifications").on("postgres_changes",{event:"INSERT",schema:"public",table:"notifications"},(t=>{const s=t.new;e(s),this.showNativeNotification(s)})).subscribe()}async deleteNotification(e){try{const{error:t}=await Rt.from("notifications").delete().eq("id",e);return!t}catch(t){return!1}}async notifyRideAccepted(e,t,s){await this.createNotification(e,"🎉 Corrida Aceita!",`${s} aceitou sua corrida e está a caminho.`,"ride_accepted",{ride_id:t},"high")}async notifyRideStarted(e,t){await this.createNotification(e,"🚗 Corrida Iniciada!","Sua corrida foi iniciada. Tenha uma boa viagem!","ride_started",{ride_id:t},"high")}async notifyRideCompleted(e,t,s){await this.createNotification(e,"✅ Corrida Finalizada!",`Corrida finalizada. Total: R$ ${s.toFixed(2)}`,"ride_completed",{ride_id:t,amount:s},"normal")}async notifyNewMessage(e,t,s){await this.createNotification(e,"💬 Nova Mensagem",`${s} enviou uma mensagem`,"chat_message",{ride_id:t},"normal")}async notifyPaymentProcessed(e,t,s){const a="success"===s?"💳 Pagamento Processado":"❌ Falha no Pagamento",i="success"===s?`Pagamento de R$ ${t.toFixed(2)} processado com sucesso`:`Falha ao processar pagamento de R$ ${t.toFixed(2)}`;await this.createNotification(e,a,i,"payment",{amount:t,status:s},"failed"===s?"high":"normal")}}const Ds=Ms.getInstance();class Ps{static instance;static getInstance(){return Ps.instance||(Ps.instance=new Ps),Ps.instance}async createRideRequest(e,t,s,a,i="economy",r="cash"){try{const{data:{user:n}}=await Rt.auth.getUser();if(!n)throw new Error("Usuário não autenticado");const o=await js.calculateRideEstimate(t,a,i);if(!o)throw new Error("Não foi possível calcular a rota");const{data:l,error:c}=await Rt.rpc("create_ride_request",{p_user_id:n.id,p_origin_address:e,p_origin_lat:t[1],p_origin_lng:t[0],p_destination_address:s,p_destination_lat:a[1],p_destination_lng:a[0],p_distance:o.distance/1e3,p_duration:o.duration/60,p_estimated_price:o.price,p_vehicle_type:i,p_payment_method:r});if(c)return null;const{data:d,error:m}=await Rt.rpc("assign_driver_to_ride",{p_ride_id:l,p_max_distance_km:10}),{data:h,error:u}=await Rt.from("ride_requests").select("\n          *,\n          driver:profiles!ride_requests_driver_id_fkey(full_name, phone),\n          passenger:profiles!ride_requests_user_id_fkey(full_name, phone)\n        ").eq("id",l).single();if(u)return null;const x={id:h.id,user_id:h.user_id,driver_id:h.driver_id,origin_address:h.origin_address,origin_coords:[h.origin_coords?.x||h.origin_coords?.coordinates?.[0]||t[0],h.origin_coords?.y||h.origin_coords?.coordinates?.[1]||t[1]],destination_address:h.destination_address,destination_coords:[h.destination_coords?.x||h.destination_coords?.coordinates?.[0]||a[0],h.destination_coords?.y||h.destination_coords?.coordinates?.[1]||a[1]],distance:h.distance,duration:h.duration,estimated_price:h.estimated_price,final_price:h.final_price,status:h.status,vehicle_type:h.vehicle_type,payment_method:h.payment_method,created_at:h.created_at,accepted_at:h.accepted_at,started_at:h.started_at,completed_at:h.completed_at,cancelled_at:h.cancelled_at};return Ft.trackRideEvent("request_created",{ride_id:l,origin_address:e,destination_address:s,distance:o.distance,estimated_price:o.price,vehicle_type:i,payment_method:r,driver_assigned:!!d}),await Ds.createNotification(n.id,"🚗 Solicitação Criada",d?"Motorista encontrado! Aguarde a confirmação.":"Sua solicitação foi criada. Procurando motoristas próximos...","ride_request",{ride_id:l},"normal"),x}catch(n){return null}}async findNearbyDrivers(e,t=5){try{const{data:s,error:a}=await Rt.rpc("find_nearby_drivers",{user_lat:e[1],user_lng:e[0],radius_km:t});return a?[]:s&&0!==s.length?s.map((e=>({id:e.driver_id,name:e.driver_name||"Motorista",phone:e.driver_phone||"",rating:e.rating||4.5,vehicle:{model:"moto"===e.vehicle_type?"Honda CG 160":"Honda Civic",plate:"ABC-1234",color:"Prata",type:e.vehicle_type||"economy"},location:[e.location_lng,e.location_lat],distance:Math.round(100*e.distance_km)/100,eta:Math.max(Math.round(2*e.distance_km),2),heading:e.heading||0,speed:e.speed||0,isAvailable:e.is_available}))):[]}catch(s){return[]}}async notifyNearbyDrivers(e,t){try{const s=await this.findNearbyDrivers(e);for(const e of s)await this.sendDriverNotification(e.id,t)}catch(s){}}async sendDriverNotification(e,t){try{await Rt.from("notifications").insert({user_id:e,title:"Nova Corrida Disponível",message:"Uma nova corrida está disponível próxima a você",type:"ride_request",data:{ride_id:t}})}catch(s){}}async getRideStatus(e){try{const{data:t,error:s}=await Rt.from("ride_requests").select("\n          *,\n          driver:profiles!ride_requests_driver_id_fkey(\n            id,\n            full_name,\n            phone\n          )\n        ").eq("id",e).single();return s?null:t}catch(t){return null}}async cancelRide(e,t){try{const{error:t}=await Rt.from("ride_requests").update({status:"cancelled",cancelled_at:(new Date).toISOString()}).eq("id",e);return!t}catch(s){return!1}}async getUserRideHistory(e=10){try{const{data:{user:t}}=await Rt.auth.getUser();if(!t)return[];const{data:s,error:a}=await Rt.from("ride_requests").select("\n          *,\n          driver:profiles!ride_requests_driver_id_fkey(\n            id,\n            full_name,\n            phone\n          )\n        ").eq("user_id",t.id).order("created_at",{ascending:!1}).limit(e);return a?[]:s||[]}catch(t){return[]}}async rateRide(e,t,s){try{const{data:{user:a}}=await Rt.auth.getUser();if(!a)return!1;const i=await this.getRideStatus(e);if(!i||!i.driver_id)return!1;const{error:r}=await Rt.from("ride_ratings").insert({ride_id:e,rater_id:a.id,rated_id:i.driver_id,rating:t,comment:s});return!r}catch(a){return!1}}calculateEstimatedPrice(e,t,s="economy"){const a={economy:{base:5,perKm:2.5,perMin:.3},comfort:{base:8,perKm:3.5,perMin:.4},premium:{base:12,perKm:5,perMin:.6},moto:{base:3,perKm:1.8,perMin:.2}},i=a[s]||a.economy,r=e/1e3,n=t/60,o=i.base+r*i.perKm+n*i.perMin;return Math.max(o,5)}subscribeToRideUpdates(e,t){try{const s=Rt.channel(`ride-updates-${e}`).on("postgres_changes",{event:"*",schema:"public",table:"ride_requests",filter:`id=eq.${e}`},(e=>{if(e.new){const s=e.new,a={id:s.id,user_id:s.user_id,driver_id:s.driver_id,origin_address:s.origin_address,origin_coords:[s.origin_coords?.x||s.origin_coords?.coordinates?.[0]||0,s.origin_coords?.y||s.origin_coords?.coordinates?.[1]||0],destination_address:s.destination_address,destination_coords:[s.destination_coords?.x||s.destination_coords?.coordinates?.[0]||0,s.destination_coords?.y||s.destination_coords?.coordinates?.[1]||0],distance:s.distance,duration:s.duration,estimated_price:s.estimated_price,final_price:s.final_price,status:s.status,vehicle_type:s.vehicle_type,payment_method:s.payment_method,created_at:s.created_at,accepted_at:s.accepted_at,started_at:s.started_at,completed_at:s.completed_at,cancelled_at:s.cancelled_at};t(a)}})).subscribe();return{unsubscribe:()=>{Rt.removeChannel(s)}}}catch(s){return{unsubscribe:()=>{}}}}}const As=Ps.getInstance(),Ls=()=>{const{user:e}=Mt(),{rideId:t}=function(){let{matches:e}=r.useContext(Ze),t=e[e.length-1];return t?t.params:{}}();et();const[n,o]=r.useState(null),[l,d]=r.useState(!0),[m,h]=r.useState(null),[u,p]=r.useState(!1),[b,g]=r.useState(null);if(is(),!e)return s.jsx(ut,{to:"/login",replace:!0});r.useEffect((()=>(t&&(f(),w()),()=>{b&&b.unsubscribe()})),[t]),r.useEffect((()=>{Ft.trackPageView("ride_tracking",{ride_id:t,has_driver:!!n?.driver_id})}),[t,n]);const f=async()=>{if(t){d(!0),h(null);try{const e=await As.getRideStatus(t);e?(o(e),Ft.trackRideEvent("tracking_viewed",{ride_id:t,status:e.status,has_driver:!!e.driver_id})):h("Corrida não encontrada")}catch(e){h("Erro ao carregar dados da corrida")}finally{d(!1)}}},w=()=>{if(!t)return;const s=As.subscribeToRideUpdates(t,(s=>{o(s),"accepted"===s.status&&"pending"===n?.status?Ds.notifyRideAccepted(e.id,t,"Motorista"):"in_progress"===s.status&&"accepted"===n?.status?Ds.notifyRideStarted(e.id,t):"completed"===s.status&&"in_progress"===n?.status&&Ds.notifyRideCompleted(e.id,t,s.final_price||s.estimated_price)}));g(s)};if(l)return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden",children:[s.jsx(as,{variant:"static",opacity:.7}),s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsx("div",{className:"relative z-10 flex flex-col h-full min-h-screen items-center justify-center px-4",children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center",children:[s.jsx(a.div,{className:"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4",animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},children:s.jsx(I,{className:"w-8 h-8 text-white"})}),s.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Carregando corrida..."}),s.jsx("p",{className:"text-white/70",children:"Aguarde um momento"})]})})]});if(m||!n)return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden",children:[s.jsx(as,{variant:"static",opacity:.7}),s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsx("div",{className:"relative z-10 flex flex-col h-full min-h-screen items-center justify-center px-4",children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center",children:[s.jsx(c,{className:"w-16 h-16 text-red-400 mx-auto mb-4"}),s.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Erro"}),s.jsx("p",{className:"text-white/70 mb-4",children:m||"Corrida não encontrada"}),s.jsx(a.button,{onClick:()=>window.location.href="/dashboard",className:"bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-xl font-semibold",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Voltar ao Dashboard"})]})})]});const v=(e=>{switch(e){case"pending":return{icon:"🔍",title:"Procurando motorista...",color:"from-blue-500 to-blue-600",description:"Aguarde enquanto encontramos um motorista próximo"};case"accepted":return{icon:"✅",title:"Motorista encontrado!",color:"from-green-500 to-green-600",description:"O motorista está a caminho do local de partida"};case"in_progress":return{icon:"🛣️",title:"Viagem em andamento",color:"from-purple-500 to-purple-600",description:"Tenha uma boa viagem!"};case"completed":return{icon:"🎉",title:"Viagem concluída!",color:"from-green-500 to-green-600",description:"Obrigado por usar o MobiDrive!"};case"cancelled":return{icon:"❌",title:"Corrida cancelada",color:"from-red-500 to-red-600",description:"A corrida foi cancelada"};default:return{icon:"⏳",title:"Carregando...",color:"from-gray-500 to-gray-600",description:"Aguarde..."}}})(n.status);return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden",children:[s.jsx(as,{variant:"static",opacity:.7}),s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6 text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"flex items-center justify-between px-4 mb-4",children:[s.jsx(a.button,{onClick:()=>window.location.href="/dashboard",className:"p-2 text-white/80 hover:text-white transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(P,{className:"w-6 h-6"})}),s.jsxs("div",{className:"inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center",children:s.jsx("img",{src:"/icons/icon-48x48.png",alt:"MobiDrive",className:"w-6 h-6"})}),s.jsxs("div",{children:[s.jsxs("h1",{className:"text-2xl font-bold text-white",children:["MOBI",s.jsx("span",{className:"text-blue-400",children:"DRIVE"})]}),s.jsx("p",{className:"text-xs text-white/70",children:"Acompanhar Corrida"})]})]}),s.jsx("div",{className:"w-10 h-10"})," "]})}),s.jsxs("div",{className:"flex-1 overflow-y-auto px-4 space-y-6",children:[s.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:s.jsxs("div",{className:"text-center",children:[s.jsx(a.div,{className:`w-16 h-16 bg-gradient-to-r ${v.color} rounded-full flex items-center justify-center mx-auto mb-4`,animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0},children:s.jsx("span",{className:"text-2xl text-white",children:v.icon})}),s.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:v.title}),s.jsx("p",{className:"text-white/80 text-sm",children:v.description}),s.jsxs("p",{className:"text-white/60 text-xs mt-2",children:["Criada em ",(j=n.created_at,new Date(j).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"}))]})]})}),n.driver_id&&"pending"!==n.status&&s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"👤 Seu Motorista"}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center",children:s.jsx(S,{className:"w-8 h-8 text-white"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-semibold text-white",children:"Motorista"}),s.jsxs("div",{className:"flex items-center space-x-2 text-sm text-white/70",children:[s.jsx(U,{className:"w-4 h-4 text-yellow-500"}),s.jsx("span",{children:"4.8"}),s.jsx("span",{children:"•"}),s.jsx("span",{children:"Honda Civic"})]}),s.jsx("p",{className:"text-sm text-white/60",children:"Prata • ABC-1234"})]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx(a.button,{className:"p-3 bg-green-500 text-white rounded-xl",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(C,{className:"w-5 h-5"})}),s.jsx(a.button,{onClick:()=>{p(!0),Ft.trackUserAction("chat_opened","ride_tracking",{ride_id:t})},className:"p-3 bg-blue-500 text-white rounded-xl",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(B,{className:"w-5 h-5"})})]})]})]}),s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"📍 Detalhes da Viagem"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full mt-2"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-white",children:"Origem"}),s.jsx("p",{className:"text-sm text-white/70",children:n.origin_address})]})]}),s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full mt-2"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-white",children:"Destino"}),s.jsx("p",{className:"text-sm text-white/70",children:n.destination_address})]})]}),s.jsx("div",{className:"border-t border-white/10 pt-4",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-sm text-white/70",children:"Valor da corrida:"}),s.jsx("span",{className:"text-lg font-bold text-white",children:(y=n.final_price||n.estimated_price,new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(y))})]})})]})]}),s.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:"completed"===n.status?s.jsxs("div",{className:"space-y-3",children:[s.jsxs(a.button,{onClick:()=>window.location.href="/dashboard",className:"w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(x,{className:"w-5 h-5"}),s.jsx("span",{children:"Finalizar e Avaliar"})]}),s.jsx(a.button,{onClick:()=>window.location.href="/request-ride",className:"w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-4 rounded-xl font-semibold",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Nova Corrida"})]}):"cancelled"!==n.status?s.jsx(a.button,{onClick:async()=>{if(t&&n&&confirm("Tem certeza que deseja cancelar a corrida?")){await As.cancelRide(t,"Cancelado pelo usuário")?(Ft.trackRideEvent("cancelled",{ride_id:t,status:n.status,reason:"user_cancelled"}),window.location.href="/dashboard"):alert("Erro ao cancelar corrida. Tente novamente.")}},className:"w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-xl font-semibold",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Cancelar Corrida"}):s.jsx(a.button,{onClick:()=>window.location.href="/dashboard",className:"w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-4 rounded-xl font-semibold",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Voltar ao Dashboard"})})]}),s.jsx(a.div,{className:"pb-4 text-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:.5},children:s.jsx("p",{className:"text-white/50 text-xs",children:"© 2024 MobiDrive. Todos os direitos reservados."})})]})]}),s.jsx(i,{children:u&&t&&s.jsx(Ts,{rideId:t,isOpen:u,onClose:()=>p(!1)})})]});var y,j},$s=()=>{const{user:e,updateProfile:t}=Mt(),[i,n]=r.useState({full_name:"",phone:"",emergency_contact:"",notification_preferences:{ride_updates:!0,promotions:!1,driver_messages:!0},privacy_settings:{share_location:!0,save_addresses:!0,data_analytics:!1}}),[o,l]=r.useState("profile"),[c,d]=r.useState(!1),[m,h]=r.useState(!1);if(is(),r.useEffect((()=>{let e=document.querySelector('meta[name="viewport"]');e||(e=document.createElement("meta"),e.setAttribute("name","viewport"),document.head.appendChild(e)),e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover"),document.body.style.margin="0",document.body.style.padding="0",document.body.style.overflow="hidden",document.documentElement.style.margin="0",document.documentElement.style.padding="0",document.documentElement.style.overflow="hidden"}),[]),!e)return s.jsx(ut,{to:"/login",replace:!0});r.useEffect((()=>{e?.user_metadata&&n((t=>({...t,full_name:e.user_metadata.full_name||"",phone:e.user_metadata.phone||"",emergency_contact:e.user_metadata.emergency_contact||""})))}),[e]);const u=[{id:"profile",label:"Perfil",icon:S},{id:"notifications",label:"Notificações",icon:w},{id:"privacy",label:"Privacidade",icon:A},{id:"payment",label:"Pagamento",icon:Y}],x={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden",children:[s.jsx(as,{variant:"static",opacity:.7}),s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6 text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center",children:s.jsx("img",{src:"/icons/icon-48x48.png",alt:"MobiDrive",className:"w-6 h-6"})}),s.jsxs("div",{children:[s.jsxs("h1",{className:"text-2xl font-bold text-white",children:["MOBI",s.jsx("span",{className:"text-blue-400",children:"DRIVE"})]}),s.jsx("p",{className:"text-xs text-white/70",children:"Configurações"})]})]})}),s.jsx("div",{className:"flex-1 flex flex-col justify-center px-4 space-y-6",children:s.jsxs(a.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.6,staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"space-y-6",children:[s.jsx(a.div,{variants:x,children:s.jsx("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:s.jsx("div",{className:"grid grid-cols-2 gap-2",children:u.map((e=>{const t=e.icon;return s.jsxs(a.button,{onClick:()=>l(e.id),className:"p-3 rounded-xl text-sm font-semibold transition-all duration-200 flex flex-col items-center space-y-1 "+(o===e.id?"bg-gradient-to-r from-blue-500 to-purple-500 text-white":"bg-white/10 text-white/80 hover:bg-white/20"),whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(t,{className:"w-5 h-5"}),s.jsx("span",{children:e.label})]},e.id)}))})})}),"profile"===o&&s.jsx(a.div,{variants:x,children:s.jsx(ModernCard,{title:"Informações Pessoais",icon:"👤",children:s.jsxs("div",{className:"space-y-4",children:[s.jsx(ModernInput,{label:"Nome Completo",icon:"👤",value:i.full_name,onChange:e=>n((t=>({...t,full_name:e.target.value}))),placeholder:"Seu nome completo"}),s.jsx(ModernInput,{label:"Telefone",icon:"📱",type:"tel",value:i.phone,onChange:e=>n((t=>({...t,phone:e.target.value}))),placeholder:"(11) 99999-9999"}),s.jsx(ModernInput,{label:"Contato de Emergência",icon:"🚨",type:"tel",value:i.emergency_contact,onChange:e=>n((t=>({...t,emergency_contact:e.target.value}))),placeholder:"(11) 88888-8888"})]})})}),"notifications"===o&&s.jsx(a.div,{variants:x,children:s.jsx(ModernCard,{title:"Preferências de Notificação",icon:"🔔",children:s.jsx("div",{className:"space-y-4",children:Object.entries(i.notification_preferences).map((([e,t])=>s.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-xl",children:[s.jsxs("div",{children:[s.jsxs("h4",{className:"font-medium text-gray-900",children:["ride_updates"===e&&"Atualizações de Corrida","promotions"===e&&"Promoções e Ofertas","driver_messages"===e&&"Mensagens do Motorista"]}),s.jsxs("p",{className:"text-sm text-gray-600",children:["ride_updates"===e&&"Receber notificações sobre o status da corrida","promotions"===e&&"Receber ofertas especiais e promoções","driver_messages"===e&&"Receber mensagens do motorista"]})]}),s.jsx(a.button,{onClick:()=>n((s=>({...s,notification_preferences:{...s.notification_preferences,[e]:!t}}))),className:"w-12 h-6 rounded-full transition-colors "+(t?"bg-blue-500":"bg-gray-300"),whileTap:{scale:.95},children:s.jsx(a.div,{className:"w-5 h-5 bg-white rounded-full shadow-md",animate:{x:t?24:2},transition:{type:"spring",stiffness:500,damping:30}})})]},e)))})})}),"privacy"===o&&s.jsx(a.div,{variants:x,children:s.jsx(ModernCard,{title:"Configurações de Privacidade",icon:"🔒",children:s.jsx("div",{className:"space-y-4",children:Object.entries(i.privacy_settings).map((([e,t])=>s.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-xl",children:[s.jsxs("div",{children:[s.jsxs("h4",{className:"font-medium text-gray-900",children:["share_location"===e&&"Compartilhar Localização","save_addresses"===e&&"Salvar Endereços","data_analytics"===e&&"Análise de Dados"]}),s.jsxs("p",{className:"text-sm text-gray-600",children:["share_location"===e&&"Permitir compartilhamento de localização em tempo real","save_addresses"===e&&"Salvar endereços frequentes para acesso rápido","data_analytics"===e&&"Permitir coleta de dados para melhorar o serviço"]})]}),s.jsx(a.button,{onClick:()=>n((s=>({...s,privacy_settings:{...s.privacy_settings,[e]:!t}}))),className:"w-12 h-6 rounded-full transition-colors "+(t?"bg-green-500":"bg-gray-300"),whileTap:{scale:.95},children:s.jsx(a.div,{className:"w-5 h-5 bg-white rounded-full shadow-md",animate:{x:t?24:2},transition:{type:"spring",stiffness:500,damping:30}})})]},e)))})})}),"payment"===o&&s.jsx(a.div,{variants:x,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4 text-center",children:"💳 Métodos de Pagamento"}),s.jsxs("div",{className:"text-center py-8",children:[s.jsx(Y,{className:"w-16 h-16 text-white/60 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Métodos de Pagamento"}),s.jsx("p",{className:"text-white/70 mb-6",children:"Gerencie seus cartões e formas de pagamento"}),s.jsx(a.button,{className:"bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-xl font-semibold",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Adicionar Cartão"})]})]})}),s.jsx(a.div,{variants:x,children:s.jsx("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:m?s.jsxs(a.div,{className:"flex items-center justify-center space-x-2 text-green-400 py-3",initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},children:[s.jsx(G,{className:"w-5 h-5"}),s.jsx("span",{className:"font-semibold",children:"Configurações salvas com sucesso!"})]}):s.jsx(a.button,{onClick:async()=>{d(!0);try{await new Promise((e=>setTimeout(e,1500))),h(!0),setTimeout((()=>h(!1)),3e3)}catch(e){}finally{d(!1)}},disabled:c,className:"w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-4 rounded-xl font-semibold flex items-center justify-center space-x-2 disabled:opacity-50",whileHover:{scale:1.02},whileTap:{scale:.98},children:c?s.jsxs(s.Fragment,{children:[s.jsx(a.div,{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),s.jsx("span",{children:"Salvando..."})]}):s.jsxs(s.Fragment,{children:[s.jsx(Z,{className:"w-5 h-5"}),s.jsx("span",{children:"Salvar Configurações"})]})})})}),s.jsx(a.div,{variants:x,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4 text-center",children:"⚡ Ações Rápidas"}),s.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[s.jsxs(a.button,{onClick:()=>window.location.href="/dashboard",className:"bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(R,{className:"w-6 h-6"}),s.jsx("span",{className:"text-sm font-medium",children:"Dashboard"})]}),s.jsxs(a.button,{onClick:()=>window.location.href="/request-ride",className:"bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(f,{className:"w-6 h-6"}),s.jsx("span",{className:"text-sm font-medium",children:"Nova Corrida"})]})]})]})})]})}),s.jsx(a.div,{className:"pb-4 text-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:.5},children:s.jsx("p",{className:"text-white/50 text-xs",children:"© 2024 MobiDrive. Todos os direitos reservados."})})]})]})},Os=()=>{const[e,t]=r.useState("ads"),i={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:100,damping:10}}};return s.jsxs("div",{className:"min-h-screen bg-black relative overflow-hidden",children:[s.jsxs("div",{className:"absolute inset-0 z-0",children:[s.jsx("img",{src:"/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pb/Pb_00000.gif",alt:"Background",className:"w-full h-full object-cover opacity-30"}),s.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-black/70 via-purple-900/30 to-black/70"})]}),s.jsx("div",{className:"relative z-10 p-4",children:s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsx(a.button,{onClick:()=>window.history.back(),className:"bg-black/20 backdrop-blur-md p-3 rounded-xl border border-white/10",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(P,{className:"w-6 h-6 text-white"})}),s.jsxs("div",{className:"text-center",children:[s.jsx("h1",{className:"text-2xl font-bold text-white",children:"💰 Recompensas"}),s.jsx("p",{className:"text-white/70 text-sm",children:"Ganhe dinheiro real!"})]}),s.jsx("div",{className:"w-12"})]})}),s.jsx("div",{className:"relative z-10 px-4 pb-20",children:s.jsxs(a.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.2}}},initial:"hidden",animate:"visible",className:"space-y-6",children:[s.jsx(a.div,{variants:i,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center",children:[s.jsx(a.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0,repeatDelay:3},className:"w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(T,{className:"w-8 h-8 text-white"})}),s.jsx("h1",{className:"text-2xl font-bold mb-3 text-white",children:"💰 Ganhe Dinheiro Real!"}),s.jsx("p",{className:"text-white/70 mb-4 text-sm",children:"Assista até 20 anúncios por dia e receba 50% da receita"}),s.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:[s.jsx(X,{className:"w-6 h-6 mx-auto mb-1 text-white"}),s.jsx("div",{className:"text-xs font-medium text-white",children:"Assista"}),s.jsx("div",{className:"text-xs text-white/60",children:"Anúncios"})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:[s.jsx(T,{className:"w-6 h-6 mx-auto mb-1 text-white"}),s.jsx("div",{className:"text-xs font-medium text-white",children:"Ganhe"}),s.jsx("div",{className:"text-xs text-white/60",children:"MobiCoins"})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:[s.jsx(Q,{className:"w-6 h-6 mx-auto mb-1 text-white"}),s.jsx("div",{className:"text-xs font-medium text-white",children:"Saque"}),s.jsx("div",{className:"text-xs text-white/60",children:"Dinheiro Real"})]})]})]})}),s.jsx(a.div,{variants:i,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center gap-2",children:[s.jsx(U,{className:"w-5 h-5 text-yellow-400"}),"Como Funciona"]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-12 h-12 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-2",children:s.jsx(X,{className:"w-6 h-6 text-blue-400"})}),s.jsx("h3",{className:"font-bold text-white mb-1 text-sm",children:"1. Assista"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Escolha um anúncio e assista"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-12 h-12 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-2",children:s.jsx(T,{className:"w-6 h-6 text-purple-400"})}),s.jsx("h3",{className:"font-bold text-white mb-1 text-sm",children:"2. Ganhe Coins"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Receba MobiCoins automaticamente"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-12 h-12 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-2",children:s.jsx(ee,{className:"w-6 h-6 text-green-400"})}),s.jsx("h3",{className:"font-bold text-white mb-1 text-sm",children:"3. Acumule"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Junte coins para saque"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-12 h-12 bg-emerald-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-2",children:s.jsx(Q,{className:"w-6 h-6 text-emerald-400"})}),s.jsx("h3",{className:"font-bold text-white mb-1 text-sm",children:"4. Saque"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Converta em dinheiro via PIX"})]})]})]})}),s.jsx(a.div,{variants:i,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsxs("h2",{className:"text-xl font-bold mb-4 flex items-center gap-2 text-white",children:[s.jsx(te,{className:"w-5 h-5 text-yellow-400"}),"Por que Escolher o MobiDrive?"]}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-8 h-8 bg-yellow-500/20 backdrop-blur-sm rounded-lg flex items-center justify-center",children:s.jsx(F,{className:"w-5 h-5 text-yellow-400"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-bold text-white text-sm",children:"50% de Participação"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Você recebe metade de toda receita gerada"})]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-500/20 backdrop-blur-sm rounded-lg flex items-center justify-center",children:s.jsx(v,{className:"w-5 h-5 text-blue-400"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-bold text-white text-sm",children:"Pagamento Instantâneo"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Saque via PIX sem taxas"})]})]})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-8 h-8 bg-green-500/20 backdrop-blur-sm rounded-lg flex items-center justify-center",children:s.jsx(se,{className:"w-5 h-5 text-green-400"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-bold text-white text-sm",children:"Anúncios de Qualidade"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Conteúdo relevante e interessante"})]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-8 h-8 bg-purple-500/20 backdrop-blur-sm rounded-lg flex items-center justify-center",children:s.jsx(U,{className:"w-5 h-5 text-purple-400"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-bold text-white text-sm",children:"Sistema de Níveis"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Suba de nível e desbloqueie benefícios"})]})]})]})]})]})}),s.jsx(a.div,{variants:i,children:s.jsxs("div",{className:"bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-2xl p-6 border border-yellow-400/20 shadow-2xl text-center",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2 mb-3",children:[s.jsx(M,{className:"w-6 h-6 text-yellow-400"}),s.jsx("h2",{className:"text-xl font-bold text-white",children:"Quer Ganhar Mais?"})]}),s.jsx("p",{className:"text-white/70 mb-4 text-sm",children:"Com o Premium, assista até 1.000 anúncios por dia e ganhe 2x mais MobiCoins!"}),s.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:[s.jsx("div",{className:"text-lg font-bold text-white",children:"1.000"}),s.jsx("div",{className:"text-xs text-white/60",children:"anúncios/dia"})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:[s.jsx("div",{className:"text-lg font-bold text-white",children:"2x"}),s.jsx("div",{className:"text-xs text-white/60",children:"ganhos"})]})]}),s.jsxs(a.button,{onClick:()=>window.location.href="/premium",className:"bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-6 py-3 rounded-xl font-bold transition-all",whileHover:{scale:1.05},whileTap:{scale:.95},children:[s.jsx(M,{className:"w-4 h-4 inline mr-2"}),"Assinar Premium"]})]})}),s.jsx(a.div,{variants:i,children:s.jsx("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-2 border border-white/10 shadow-2xl",children:s.jsxs("div",{className:"flex gap-2",children:[s.jsxs(a.button,{onClick:()=>t("ads"),className:"flex-1 py-3 px-4 rounded-xl font-bold transition-all text-sm "+("ads"===e?"bg-gradient-to-r from-purple-500 to-blue-500 text-white":"text-white/70 hover:bg-white/10"),whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(X,{className:"w-4 h-4 inline mr-2"}),"Assistir Anúncios"]}),s.jsxs(a.button,{onClick:()=>t("withdraw"),className:"flex-1 py-3 px-4 rounded-xl font-bold transition-all text-sm "+("withdraw"===e?"bg-gradient-to-r from-green-500 to-emerald-500 text-white":"text-white/70 hover:bg-white/10"),whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(Q,{className:"w-4 h-4 inline mr-2"}),"Sacar Dinheiro"]})]})})}),s.jsxs(a.div,{variants:i,children:["ads"===e&&s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"🚀 Migração para Appodeal"}),s.jsx("p",{className:"text-white/70 mb-4",children:"Sistema de anúncios sendo migrado para Appodeal para melhor experiência e maiores ganhos."}),s.jsx(a.button,{onClick:()=>window.location.href="/free-ads",className:"bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-3 rounded-xl font-bold",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Acessar Novo Sistema"})]}),"withdraw"===e&&s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"💰 Sistema de Saque"}),s.jsx("p",{className:"text-white/70 mb-4",children:"Sistema de saque será integrado com Appodeal em breve."}),s.jsx("div",{className:"text-yellow-400 text-sm",children:"🔄 Em desenvolvimento..."})]})]})]})})]})},qs=()=>{const{user:e}=Mt(),[t,n]=r.useState([]),[o,l]=r.useState(null),[c,d]=r.useState(null),[m,h]=r.useState("monthly"),[u,x]=r.useState(!1),[b,g]=r.useState(""),[f,w]=r.useState(null),[y,j]=r.useState(!1);r.useEffect((()=>{e&&(N(),_())}),[e]);const N=async()=>{try{const{data:e,error:t}=await Rt.from("subscription_plans").select("*").eq("active",!0).order("price_monthly",{ascending:!0});e&&n(e)}catch(e){}},_=async()=>{try{const{data:t,error:s}=await Rt.from("user_subscriptions").select("\n          *,\n          plan:subscription_plans(*)\n        ").eq("user_id",e?.id).eq("status","active").single();t&&l(t)}catch(t){l(null)}},k=async t=>{if(c){j(!0);try{const s="yearly"===m?c.price_yearly:c.price_monthly,a=f?f.final_price:s,{data:i,error:r}=await Rt.from("user_subscriptions").insert([{user_id:e?.id,plan_id:c.id,billing_cycle:m,payment_method:t,status:"active",subscription_end_date:new Date(Date.now()+24*("yearly"===m?365:30)*60*60*1e3).toISOString()}]).select().single();if(i&&!r){const{data:s,error:r}=await Rt.rpc("process_subscription_payment",{p_subscription_id:i.id,p_amount:a,p_payment_method:t,p_external_payment_id:`mock_${Date.now()}`});r||(f&&await Rt.from("coupon_usage").insert([{coupon_id:f.coupon_id,user_id:e?.id,subscription_id:i.id,discount_applied:f.discount_amount}]),alert(`Assinatura ${c.name} ativada com sucesso!`),x(!1),d(null),w(null),g(""),_())}}catch(s){alert("Erro ao processar pagamento. Tente novamente.")}finally{j(!1)}}},S=e=>{switch(e.toLowerCase()){case"gratuito":return s.jsx(se,{className:"w-8 h-8"});case"premium":return s.jsx(M,{className:"w-8 h-8"});case"premium plus":return s.jsx(U,{className:"w-8 h-8"});default:return s.jsx(te,{className:"w-8 h-8"})}},C=e=>{switch(e.toLowerCase()){case"gratuito":return"from-gray-500 to-gray-600";case"premium":return"from-purple-500 to-blue-500";case"premium plus":return"from-yellow-500 to-orange-500";default:return"from-blue-500 to-purple-500"}},E=e=>new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e);return s.jsxs("div",{className:"premium-subscription-system p-6 space-y-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsxs(a.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"inline-flex items-center gap-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-3 rounded-2xl mb-4",children:[s.jsx(M,{className:"w-6 h-6"}),s.jsx("span",{className:"font-bold",children:"MobiDrive Premium"})]}),s.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"Maximize Seus Ganhos"}),s.jsx("p",{className:"text-gray-600",children:"Desbloqueie todo o potencial do MobiDrive com nossos planos premium"})]}),o&&s.jsx(a.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl p-6 text-white",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsxs("h3",{className:"text-xl font-bold flex items-center gap-2",children:[s.jsx(A,{className:"w-6 h-6"}),"Assinatura Ativa"]}),s.jsxs("p",{className:"text-green-100",children:["Plano ",o.plan.name," - ","yearly"===o.billing_cycle?"Anual":"Mensal"]}),s.jsxs("p",{className:"text-sm text-green-200",children:["Válido até: ",new Date(o.subscription_end_date).toLocaleDateString("pt-BR")]})]}),s.jsx("button",{onClick:async()=>{if(!o)return;if(confirm("Tem certeza que deseja cancelar sua assinatura?"))try{const{error:e}=await Rt.from("user_subscriptions").update({status:"cancelled",auto_renew:!1}).eq("id",o.id);e||(alert("Assinatura cancelada. Você ainda terá acesso aos benefícios até o final do período pago."),_())}catch(e){alert("Erro ao cancelar assinatura")}},className:"bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-sm font-medium transition-all",children:"Cancelar"})]})}),s.jsx("div",{className:"flex justify-center",children:s.jsxs("div",{className:"bg-gray-100 rounded-2xl p-2 flex",children:[s.jsx("button",{onClick:()=>h("monthly"),className:"px-6 py-2 rounded-xl font-medium transition-all "+("monthly"===m?"bg-white text-gray-800 shadow-md":"text-gray-600"),children:"Mensal"}),s.jsxs("button",{onClick:()=>h("yearly"),className:"px-6 py-2 rounded-xl font-medium transition-all "+("yearly"===m?"bg-white text-gray-800 shadow-md":"text-gray-600"),children:["Anual",s.jsx("span",{className:"ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full",children:"Economize até 50%"})]})]})}),s.jsx("div",{className:"grid md:grid-cols-3 gap-6",children:t.map(((e,t)=>{const i=o?.plan_id===e.id,r="yearly"===m?e.price_yearly:e.price_monthly,n="yearly"===m?((e,t)=>{const s=12*e,a=s-t;return{savings:a,percentage:Math.round(a/s*100)}})(e.price_monthly,e.price_yearly):null;return s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"relative bg-white rounded-2xl p-6 shadow-lg border-2 transition-all "+(i?"border-green-500 bg-green-50":"Premium"===e.name?"border-purple-500 transform scale-105":"border-gray-200 hover:border-gray-300"),children:["Premium"===e.name&&s.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:s.jsx("span",{className:"bg-gradient-to-r from-purple-500 to-blue-500 text-white px-4 py-1 rounded-full text-sm font-bold",children:"Mais Popular"})}),s.jsxs("div",{className:"text-center mb-6",children:[s.jsx("div",{className:`w-16 h-16 bg-gradient-to-r ${C(e.name)} rounded-full flex items-center justify-center mx-auto mb-4 text-white`,children:S(e.name)}),s.jsx("h3",{className:"text-2xl font-bold text-gray-800 mb-2",children:e.name}),s.jsx("p",{className:"text-gray-600 text-sm mb-4",children:e.description}),s.jsxs("div",{className:"mb-4",children:[s.jsx("div",{className:"text-4xl font-bold text-gray-800",children:E(r)}),s.jsxs("div",{className:"text-gray-500 text-sm",children:["por ","yearly"===m?"ano":"mês"]}),n&&n.percentage>0&&s.jsxs("div",{className:"text-green-600 text-sm font-medium",children:["Economize ",n.percentage,"% (",E(n.savings),")"]})]})]}),s.jsxs("div",{className:"space-y-3 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-5 h-5 bg-green-100 rounded-full flex items-center justify-center",children:20===e.daily_ad_limit?s.jsx("span",{className:"text-green-600 text-xs font-bold",children:"20"}):s.jsx(ae,{className:"w-3 h-3 text-green-600"})}),s.jsx("span",{className:"text-gray-700 text-sm",children:20===e.daily_ad_limit?"20 anúncios/dia":`${e.daily_ad_limit.toLocaleString()} anúncios/dia`})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center",children:s.jsx(ee,{className:"w-3 h-3 text-blue-600"})}),s.jsxs("span",{className:"text-gray-700 text-sm",children:[e.reward_multiplier,"x recompensas"]})]}),e.priority_ads&&s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-5 h-5 bg-purple-100 rounded-full flex items-center justify-center",children:s.jsx(v,{className:"w-3 h-3 text-purple-600"})}),s.jsx("span",{className:"text-gray-700 text-sm",children:"Anúncios prioritários"})]}),e.exclusive_offers&&s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center",children:s.jsx(se,{className:"w-3 h-3 text-yellow-600"})}),s.jsx("span",{className:"text-gray-700 text-sm",children:"Ofertas exclusivas"})]})]}),i?s.jsx("div",{className:"text-center",children:s.jsxs("div",{className:"bg-green-100 text-green-800 py-3 px-4 rounded-xl font-bold",children:[s.jsx(G,{className:"w-5 h-5 inline mr-2"}),"Plano Atual"]})}):s.jsx("button",{onClick:()=>(async e=>{d(e),x(!0)})(e),disabled:0===e.price_monthly,className:"w-full py-3 px-4 rounded-xl font-bold transition-all "+(0===e.price_monthly?"bg-gray-200 text-gray-500 cursor-not-allowed":`bg-gradient-to-r ${C(e.name)} text-white hover:shadow-lg transform hover:scale-105`),children:0===e.price_monthly?"Plano Atual":"Assinar Agora"})]},e.id)}))}),s.jsx(i,{children:u&&c&&s.jsx(a.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:s.jsxs(a.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white rounded-2xl p-6 max-w-md w-full max-h-[90vh] overflow-y-auto",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsxs("h3",{className:"text-xl font-bold text-gray-800",children:["Assinar ",c.name]}),s.jsx("button",{onClick:()=>x(!1),className:"text-gray-500 hover:text-gray-700",children:s.jsx(p,{className:"w-6 h-6"})})]}),s.jsxs("div",{className:"bg-gray-50 rounded-xl p-4 mb-6",children:[s.jsxs("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("span",{className:"font-medium",children:"Plano:"}),s.jsx("span",{children:c.name})]}),s.jsxs("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("span",{className:"font-medium",children:"Ciclo:"}),s.jsx("span",{children:"yearly"===m?"Anual":"Mensal"})]}),s.jsxs("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("span",{className:"font-medium",children:"Preço:"}),s.jsx("span",{className:"font-bold",children:E("yearly"===m?c.price_yearly:c.price_monthly)})]}),f&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"flex justify-between items-center mb-2 text-green-600",children:[s.jsx("span",{className:"font-medium",children:"Desconto:"}),s.jsxs("span",{children:["-",E(f.discount_amount)]})]}),s.jsxs("div",{className:"flex justify-between items-center font-bold text-lg border-t pt-2",children:[s.jsx("span",{children:"Total:"}),s.jsx("span",{children:E(f.final_price)})]})]})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cupom de Desconto (Opcional)"}),s.jsxs("div",{className:"flex gap-2",children:[s.jsx("input",{type:"text",value:b,onChange:e=>g(e.target.value.toUpperCase()),placeholder:"Digite o código",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),s.jsx("button",{onClick:async()=>{if(b&&c)try{const{data:t,error:s}=await Rt.rpc("apply_discount_coupon",{p_coupon_code:b,p_user_id:e?.id,p_plan_id:c.id});t?.valid?(w(t),alert(`Cupom aplicado! Desconto de R$ ${t.discount_amount.toFixed(2)}`)):(alert(t?.error||"Cupom inválido"),w(null))}catch(t){alert("Erro ao aplicar cupom")}},className:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"Aplicar"})]})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("h4",{className:"font-medium text-gray-800",children:"Método de Pagamento"}),s.jsxs("button",{onClick:()=>k("pix"),disabled:y,className:"w-full flex items-center gap-3 p-4 border border-gray-300 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all disabled:opacity-50",children:[s.jsx(ie,{className:"w-6 h-6 text-blue-600"}),s.jsxs("div",{className:"text-left",children:[s.jsx("div",{className:"font-medium",children:"PIX"}),s.jsx("div",{className:"text-sm text-gray-600",children:"Pagamento instantâneo"})]})]}),s.jsxs("button",{onClick:()=>k("credit_card"),disabled:y,className:"w-full flex items-center gap-3 p-4 border border-gray-300 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all disabled:opacity-50",children:[s.jsx(Y,{className:"w-6 h-6 text-green-600"}),s.jsxs("div",{className:"text-left",children:[s.jsx("div",{className:"font-medium",children:"Cartão de Crédito"}),s.jsx("div",{className:"text-sm text-gray-600",children:"Visa, Mastercard, Elo"})]})]})]}),y&&s.jsx("div",{className:"mt-4 text-center",children:s.jsxs("div",{className:"inline-flex items-center gap-2 text-blue-600",children:[s.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),"Processando pagamento..."]})})]})})})]})},Fs=()=>{const e={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:100,damping:10}}};return s.jsxs("div",{className:"min-h-screen bg-black relative overflow-hidden",children:[s.jsxs("div",{className:"absolute inset-0 z-0",children:[s.jsx("img",{src:"/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pb/Pb_00000.gif",alt:"Background",className:"w-full h-full object-cover opacity-30"}),s.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-black/70 via-yellow-900/30 to-black/70"})]}),s.jsx("div",{className:"relative z-10 p-4",children:s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsx(a.button,{onClick:()=>window.history.back(),className:"bg-black/20 backdrop-blur-md p-3 rounded-xl border border-white/10",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(P,{className:"w-6 h-6 text-white"})}),s.jsxs("div",{className:"text-center",children:[s.jsx("h1",{className:"text-2xl font-bold text-white",children:"👑 Premium"}),s.jsx("p",{className:"text-white/70 text-sm",children:"Desbloqueie todo potencial"})]}),s.jsx("div",{className:"w-12"})]})}),s.jsx("div",{className:"relative z-10 px-4 pb-20",children:s.jsxs(a.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.2}}},initial:"hidden",animate:"visible",className:"space-y-6",children:[s.jsx(a.div,{variants:e,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-yellow-400/20 shadow-2xl text-center",children:[s.jsx(a.div,{animate:{rotate:[0,5,-5,0]},transition:{duration:3,repeat:ae,repeatDelay:2},className:"w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(M,{className:"w-8 h-8 text-white"})}),s.jsx("h1",{className:"text-2xl font-bold mb-3 text-white",children:"👑 Seja Premium!"}),s.jsx("p",{className:"text-white/70 mb-4 text-sm",children:"Ganhe até 3x mais MobiCoins e desbloqueie benefícios exclusivos"}),s.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:[s.jsx(ae,{className:"w-6 h-6 mx-auto mb-1 text-yellow-400"}),s.jsx("div",{className:"text-xs font-medium text-white",children:"Até 1.000"}),s.jsx("div",{className:"text-xs text-white/60",children:"anúncios/dia"})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:[s.jsx(ee,{className:"w-6 h-6 mx-auto mb-1 text-green-400"}),s.jsx("div",{className:"text-xs font-medium text-white",children:"3x Ganhos"}),s.jsx("div",{className:"text-xs text-white/60",children:"multiplicador"})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:[s.jsx(v,{className:"w-6 h-6 mx-auto mb-1 text-blue-400"}),s.jsx("div",{className:"text-xs font-medium text-white",children:"Prioritário"}),s.jsx("div",{className:"text-xs text-white/60",children:"anúncios"})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:[s.jsx(se,{className:"w-6 h-6 mx-auto mb-1 text-purple-400"}),s.jsx("div",{className:"text-xs font-medium text-white",children:"Exclusivo"}),s.jsx("div",{className:"text-xs text-white/60",children:"ofertas"})]})]})]})}),s.jsx(a.div,{variants:e,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-4 text-center",children:"Gratuito vs Premium"}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-white text-sm font-medium",children:"Anúncios/dia"}),s.jsxs("div",{className:"flex gap-2 text-xs",children:[s.jsx("span",{className:"text-white/70",children:"20"}),s.jsx("span",{className:"text-yellow-400 font-bold",children:"1.000"}),s.jsx("span",{className:"text-orange-400 font-bold",children:"2.000"})]})]})}),s.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-white text-sm font-medium",children:"Multiplicador"}),s.jsxs("div",{className:"flex gap-2 text-xs",children:[s.jsx("span",{className:"text-white/70",children:"1x"}),s.jsx("span",{className:"text-yellow-400 font-bold",children:"2x"}),s.jsx("span",{className:"text-orange-400 font-bold",children:"3x"})]})]})}),s.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-white text-sm font-medium",children:"Anúncios prioritários"}),s.jsxs("div",{className:"flex gap-2 text-xs",children:[s.jsx("span",{className:"text-red-400",children:"✗"}),s.jsx("span",{className:"text-green-400",children:"✓"}),s.jsx("span",{className:"text-green-400",children:"✓"})]})]})}),s.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-3",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-white text-sm font-medium",children:"Suporte prioritário"}),s.jsxs("div",{className:"flex gap-2 text-xs",children:[s.jsx("span",{className:"text-red-400",children:"✗"}),s.jsx("span",{className:"text-green-400",children:"✓"}),s.jsx("span",{className:"text-green-400",children:"✓ VIP"})]})]})})]})]})}),s.jsx(a.div,{variants:e,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-green-400/20 shadow-2xl",children:[s.jsx("h2",{className:"text-xl font-bold mb-4 text-center text-white",children:"💰 Potencial de Ganhos Mensais"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center",children:[s.jsx("h3",{className:"text-sm font-bold mb-2 text-white",children:"Gratuito"}),s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-xs text-white/70",children:"20 anúncios/dia × 30 dias"}),s.jsx("div",{className:"text-lg font-bold text-white",children:"R$ 15,00"}),s.jsx("div",{className:"text-xs text-white/60",children:"por mês"})]})]}),s.jsxs("div",{className:"bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-xl p-4 text-center border border-yellow-400/30",children:[s.jsxs("h3",{className:"text-sm font-bold mb-2 text-white flex items-center justify-center gap-1",children:[s.jsx(M,{className:"w-4 h-4 text-yellow-400"}),"Premium"]}),s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-xs text-white/70",children:"1.000 anúncios/dia × 30 dias"}),s.jsx("div",{className:"text-xl font-bold text-yellow-400",children:"R$ 1.500,00"}),s.jsx("div",{className:"text-xs text-white/60",children:"por mês"})]})]}),s.jsxs("div",{className:"bg-gradient-to-r from-orange-500/20 to-red-500/20 backdrop-blur-sm rounded-xl p-4 text-center border border-orange-400/30",children:[s.jsxs("h3",{className:"text-sm font-bold mb-2 text-white flex items-center justify-center gap-1",children:[s.jsx(U,{className:"w-4 h-4 text-orange-400"}),"Premium Plus"]}),s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-xs text-white/70",children:"2.000 anúncios/dia × 30 dias"}),s.jsx("div",{className:"text-xl font-bold text-orange-400",children:"R$ 4.500,00"}),s.jsx("div",{className:"text-xs text-white/60",children:"por mês"})]})]})]}),s.jsx("div",{className:"text-center mt-4",children:s.jsx("p",{className:"text-white/60 text-xs",children:"* Valores estimados baseados na média de recompensas por anúncio"})})]})}),s.jsx(a.div,{variants:e,children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-4 text-center",children:"⭐ O que nossos usuários dizem"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4",children:[s.jsxs("div",{className:"flex items-center mb-3",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm",children:"M"}),s.jsxs("div",{className:"ml-3",children:[s.jsx("div",{className:"font-bold text-white text-sm",children:"Maria Silva"}),s.jsx("div",{className:"text-xs text-white/60",children:"Premium há 6 meses"})]})]}),s.jsx("p",{className:"text-white/80 text-xs italic",children:'"Com o Premium, consegui ganhar mais de R$ 800 no primeiro mês!"'}),s.jsx("div",{className:"flex text-yellow-400 mt-2",children:[...Array(5)].map(((e,t)=>s.jsx(U,{className:"w-3 h-3 fill-current"},t)))})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4",children:[s.jsxs("div",{className:"flex items-center mb-3",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold text-sm",children:"J"}),s.jsxs("div",{className:"ml-3",children:[s.jsx("div",{className:"font-bold text-white text-sm",children:"João Santos"}),s.jsx("div",{className:"text-xs text-white/60",children:"Premium Plus há 3 meses"})]})]}),s.jsx("p",{className:"text-white/80 text-xs italic",children:'"O Premium Plus vale cada centavo! Renda extra significativa."'}),s.jsx("div",{className:"flex text-yellow-400 mt-2",children:[...Array(5)].map(((e,t)=>s.jsx(U,{className:"w-3 h-3 fill-current"},t)))})]})]})]})}),s.jsx(a.div,{variants:e,children:s.jsx(qs,{})})]})})]})};class Hs{static instance;subscriptions=new Map;cleanupInterval=null;maxSubscriptions=10;subscriptionTimeout=3e5;static getInstance(){return Hs.instance||(Hs.instance=new Hs),Hs.instance}constructor(){this.startCleanupInterval(),this.setupGlobalCleanup()}createSafeSubscription(e,t,s={}){const a=`${e}_${Date.now()}_${Math.random()}`;try{this.subscriptions.size>=this.maxSubscriptions&&this.cleanupOldSubscriptions();const i=Rt.channel(e,{config:{presence:{key:a}}}),r=this.createSafeCallback(t,a),n=setTimeout((()=>{this.unsubscribe(a)}),s.timeout||this.subscriptionTimeout),o={id:a,channel:i,callback:r,createdAt:Date.now(),isActive:!0};return this.subscriptions.set(a,o),i.subscribe((e=>{"SUBSCRIBED"===e||"CLOSED"!==e&&"CHANNEL_ERROR"!==e||this.unsubscribe(a)})),setTimeout((()=>{clearTimeout(n)}),s.timeout||this.subscriptionTimeout),a}catch(i){return""}}createSafeCallback(e,t){let s=!1,a=0;return(...i)=>{if(!s)if(a++,a>100)this.unsubscribe(t);else{setTimeout((()=>{a=Math.max(0,a-1)}),6e4),s=!0;try{e(...i)}catch(r){}finally{s=!1}}}}unsubscribe(e){const t=this.subscriptions.get(e);if(!t)return!1;try{return t.isActive=!1,t.channel&&"function"==typeof t.channel.unsubscribe&&t.channel.unsubscribe(),this.subscriptions.delete(e),!0}catch(s){return this.subscriptions.delete(e),!1}}cleanupOldSubscriptions(){const e=Date.now();for(const[t,s]of this.subscriptions.entries())(e-s.createdAt>3e5||!s.isActive)&&this.unsubscribe(t)}unsubscribeAll(){const e=Array.from(this.subscriptions.keys());for(const t of e)this.unsubscribe(t)}startCleanupInterval(){this.cleanupInterval=setInterval((()=>{this.cleanupOldSubscriptions(),this.subscriptions.size}),6e4)}setupGlobalCleanup(){"undefined"!=typeof window&&(window.addEventListener("beforeunload",(()=>{this.unsubscribeAll()})),window.addEventListener("pagehide",(()=>{this.unsubscribeAll()})),document.addEventListener("visibilitychange",(()=>{document.hidden&&this.cleanupOldSubscriptions()})))}getStats(){const e=Date.now(),t=Array.from(this.subscriptions.values()),s=t.filter((e=>e.isActive)).length,a=t.length-s,i=t.map((t=>e-t.createdAt)),r=i.length>0?Math.max(...i):0,n=i.length>0?Math.min(...i):0;return{total:t.length,active:s,inactive:a,oldest:r,newest:n}}checkHealth(){const e=this.getStats(),t=[],s=[];return e.total>.8*this.maxSubscriptions&&(t.push(`Muitas subscriptions ativas: ${e.total}`),s.push("Considere reduzir o número de subscriptions simultâneas")),e.oldest>this.subscriptionTimeout&&(t.push("Subscriptions muito antigas detectadas"),s.push("Execute limpeza manual de subscriptions antigas")),e.inactive>0&&(t.push(`${e.inactive} subscriptions inativas encontradas`),s.push("Execute limpeza de subscriptions inativas")),{healthy:0===t.length,issues:t,recommendations:s}}destroy(){this.cleanupInterval&&(clearInterval(this.cleanupInterval),this.cleanupInterval=null),this.unsubscribeAll()}}class zs{static instance;static getInstance(){return zs.instance||(zs.instance=new zs),zs.instance}async runFullIntegrationTest(){const e=Date.now(),t=[];t.push(await this.testDatabaseConnection()),t.push(await this.testAuthenticationSystem()),t.push(await this.testMapboxIntegration()),t.push(await this.testRealtimeConnection()),t.push(await this.testRideRequestFlow()),t.push(await this.testChatSystem()),t.push(await this.testNotificationSystem()),t.push(await this.testAnalyticsTracking()),t.push(await this.testRideChatIntegration()),t.push(await this.testNotificationTriggers()),t.push(await this.testDataConsistency()),t.push(await this.testPWAFunctionality()),t.push(await this.testPerformanceMetrics()),t.push(await this.testCacheStrategies());Date.now();const s=t.filter((e=>"passed"===e.status)).length,a=t.filter((e=>"failed"===e.status)).length,i=t.filter((e=>"warning"===e.status)).length,r=a>0?"critical":i>0?"issues":"healthy";return{timestamp:(new Date).toISOString(),totalTests:t.length,passed:s,failed:a,warnings:i,overallStatus:r,tests:t,systemHealth:{database:!t.find((e=>e.testName.includes("Database")&&"failed"===e.status)),mapbox:!t.find((e=>e.testName.includes("Mapbox")&&"failed"===e.status)),authentication:!t.find((e=>e.testName.includes("Auth")&&"failed"===e.status)),realtime:!t.find((e=>e.testName.includes("Realtime")&&"failed"===e.status)),notifications:!t.find((e=>e.testName.includes("Notification")&&"failed"===e.status))}}}async testDatabaseConnection(){const e=Date.now();try{const{data:t,error:s}=await Rt.from("profiles").select("count").limit(1);return s?{testName:"Database Connection",status:"failed",message:`Erro de conexão: ${s.message}`,duration:Date.now()-e,details:s}:{testName:"Database Connection",status:"passed",message:"Conexão com Supabase estabelecida com sucesso",duration:Date.now()-e}}catch(t){return{testName:"Database Connection",status:"failed",message:`Erro inesperado: ${t}`,duration:Date.now()-e,details:t}}}async testAuthenticationSystem(){const e=Date.now();try{const{data:{user:t},error:s}=await Rt.auth.getUser();return s?{testName:"Authentication System",status:"warning",message:"Usuário não autenticado (esperado em alguns casos)",duration:Date.now()-e}:{testName:"Authentication System",status:"passed",message:t?"Usuário autenticado":"Sistema de auth funcionando",duration:Date.now()-e,details:{userId:t?.id}}}catch(t){return{testName:"Authentication System",status:"failed",message:`Erro no sistema de auth: ${t}`,duration:Date.now()-e,details:t}}}async testMapboxIntegration(){const e=Date.now();try{const t=await js.searchPlaces("São Paulo",{lat:-23.5505,lng:-46.6333});if(0===t.length)return{testName:"Mapbox Integration",status:"warning",message:"Mapbox conectado mas sem resultados de busca",duration:Date.now()-e};const s=await js.getRoute({lat:-23.5505,lng:-46.6333},{lat:-23.5489,lng:-46.6388});return s?{testName:"Mapbox Integration",status:"passed",message:"Mapbox totalmente funcional",duration:Date.now()-e,details:{placesFound:t.length,routeDistance:s.distance}}:{testName:"Mapbox Integration",status:"warning",message:"Busca funcionando mas cálculo de rota falhou",duration:Date.now()-e}}catch(t){return{testName:"Mapbox Integration",status:"failed",message:`Erro no Mapbox: ${t}`,duration:Date.now()-e,details:t}}}async testRealtimeConnection(){const e=Date.now();try{Hs.getInstance();return"function"!=typeof Rt.channel?{testName:"Realtime Connection",status:"failed",message:"Supabase realtime não disponível",duration:Date.now()-e}:{testName:"Realtime Connection",status:"skipped",message:"Realtime desabilitado para estabilidade",duration:Date.now()-e}}catch(t){return{testName:"Realtime Connection",status:"failed",message:`Erro na conexão realtime: ${t}`,duration:Date.now()-e,details:t}}}async testRideRequestFlow(){const e=Date.now();try{const{data:t,error:s}=await Rt.from("ride_requests").select("count").limit(1);if(s)return{testName:"Ride Request Flow",status:"failed",message:`Tabela ride_requests não encontrada: ${s.message}`,duration:Date.now()-e,details:s};const{data:a,error:i}=await Rt.from("driver_locations").select("count").limit(1);return i?{testName:"Ride Request Flow",status:"warning",message:"Tabela driver_locations não encontrada",duration:Date.now()-e}:{testName:"Ride Request Flow",status:"passed",message:"Estrutura de corridas configurada corretamente",duration:Date.now()-e}}catch(t){return{testName:"Ride Request Flow",status:"failed",message:`Erro no teste de corridas: ${t}`,duration:Date.now()-e,details:t}}}async testChatSystem(){const e=Date.now();try{const{data:t,error:s}=await Rt.from("chat_messages").select("count").limit(1);return s?{testName:"Chat System",status:"failed",message:`Tabela chat_messages não encontrada: ${s.message}`,duration:Date.now()-e,details:s}:{testName:"Chat System",status:"passed",message:"Sistema de chat configurado corretamente",duration:Date.now()-e}}catch(t){return{testName:"Chat System",status:"failed",message:`Erro no teste de chat: ${t}`,duration:Date.now()-e,details:t}}}async testNotificationSystem(){const e=Date.now();try{const t=Notification.permission;if("denied"===t)return{testName:"Notification System",status:"warning",message:"Permissão de notificações negada pelo usuário",duration:Date.now()-e};const{data:s,error:a}=await Rt.from("notifications").select("count").limit(1);return a?{testName:"Notification System",status:"failed",message:`Tabela notifications não encontrada: ${a.message}`,duration:Date.now()-e,details:a}:{testName:"Notification System",status:"passed",message:`Sistema de notificações OK (permissão: ${t})`,duration:Date.now()-e}}catch(t){return{testName:"Notification System",status:"failed",message:`Erro no teste de notificações: ${t}`,duration:Date.now()-e,details:t}}}async testAnalyticsTracking(){const e=Date.now();try{Ft.track("integration_test",{test:!0});const t=Ft.getSessionStats();return{testName:"Analytics Tracking",status:"passed",message:"Sistema de analytics funcionando",duration:Date.now()-e,details:t}}catch(t){return{testName:"Analytics Tracking",status:"failed",message:`Erro no analytics: ${t}`,duration:Date.now()-e,details:t}}}async testRideChatIntegration(){const e=Date.now();try{const{data:t,error:s}=await Rt.from("ride_requests").select("id").limit(1),{data:a,error:i}=await Rt.from("chat_messages").select("ride_id").limit(1);return s||i?{testName:"Ride-Chat Integration",status:"warning",message:"Tabelas não acessíveis para teste de integração",duration:Date.now()-e}:{testName:"Ride-Chat Integration",status:"passed",message:"Integração ride-chat configurada",duration:Date.now()-e}}catch(t){return{testName:"Ride-Chat Integration",status:"warning",message:"Teste de integração limitado",duration:Date.now()-e}}}async testNotificationTriggers(){const e=Date.now();try{const t=await Ds.requestPermission();return{testName:"Notification Triggers",status:t?"passed":"warning",message:t?"Triggers de notificação OK":"Permissão necessária",duration:Date.now()-e}}catch(t){return{testName:"Notification Triggers",status:"failed",message:`Erro nos triggers: ${t}`,duration:Date.now()-e,details:t}}}async testDataConsistency(){const e=Date.now();try{const t=["profiles","ride_requests","notifications","chat_messages"],s=[];for(const e of t)try{const{error:t}=await Rt.from(e).select("count").limit(1);s.push({table:e,exists:!t})}catch{s.push({table:e,exists:!1})}const a=s.filter((e=>!e.exists));return a.length>0?{testName:"Data Consistency",status:"failed",message:`Tabelas faltando: ${a.map((e=>e.table)).join(", ")}`,duration:Date.now()-e,details:s}:{testName:"Data Consistency",status:"passed",message:"Todas as tabelas principais existem",duration:Date.now()-e,details:s}}catch(t){return{testName:"Data Consistency",status:"failed",message:`Erro na verificação: ${t}`,duration:Date.now()-e,details:t}}}async testPWAFunctionality(){const e=Date.now();try{const t="serviceWorker"in navigator,s="Notification"in window,a="PushManager"in window;let i=!1;if(t)try{i=!!(await navigator.serviceWorker.getRegistration())}catch{i=!1}const r=[t,s,a,i].filter(Boolean).length;return 4===r?{testName:"PWA Functionality",status:"passed",message:"PWA totalmente funcional",duration:Date.now()-e,details:{hasServiceWorker:t,hasNotifications:s,hasPushManager:a,swRegistered:i}}:r>=2?{testName:"PWA Functionality",status:"warning",message:`PWA parcialmente funcional (${r}/4)`,duration:Date.now()-e,details:{hasServiceWorker:t,hasNotifications:s,hasPushManager:a,swRegistered:i}}:{testName:"PWA Functionality",status:"failed",message:"PWA não funcional",duration:Date.now()-e,details:{hasServiceWorker:t,hasNotifications:s,hasPushManager:a,swRegistered:i}}}catch(t){return{testName:"PWA Functionality",status:"failed",message:`Erro no teste PWA: ${t}`,duration:Date.now()-e,details:t}}}async testPerformanceMetrics(){const e=Date.now();try{const t=performance.getEntriesByType("navigation")[0],s=t.loadEventEnd-t.loadEventStart,a=t.domContentLoadedEventEnd-t.domContentLoadedEventStart;let i="passed",r="Performance adequada";return s>3e3&&(i="warning",r="Tempo de carregamento alto"),s>5e3&&(i="failed",r="Performance crítica"),{testName:"Performance Metrics",status:i,message:r,duration:Date.now()-e,details:{loadTime:s,domContentLoaded:a}}}catch(t){return{testName:"Performance Metrics",status:"warning",message:"Não foi possível medir performance",duration:Date.now()-e,details:t}}}async testCacheStrategies(){const e=Date.now();try{if("caches"in window){const t=await caches.keys(),s=t.some((e=>e.includes("mobidrive")||e.includes("passenger-app")));return{testName:"Cache Strategies",status:s?"passed":"warning",message:s?"Cache funcionando":"Cache não encontrado",duration:Date.now()-e,details:{cacheNames:t}}}return{testName:"Cache Strategies",status:"warning",message:"Cache API não suportada",duration:Date.now()-e}}catch(t){return{testName:"Cache Strategies",status:"failed",message:`Erro no teste de cache: ${t}`,duration:Date.now()-e,details:t}}}}const Us=zs.getInstance();class Bs{static instance;subscriptions=new Set;timers=new Set;intervals=new Set;eventListeners=new Set;static getInstance(){return Bs.instance||(Bs.instance=new Bs),Bs.instance}registerSubscription(e){this.subscriptions.add(e)}registerTimer(e){this.timers.add(e)}registerInterval(e){this.intervals.add(e)}registerEventListener(e,t,s){this.eventListeners.add({element:e,event:t,handler:s})}cleanupSubscription(e){try{e(),this.subscriptions.delete(e)}catch(t){}}cleanupTimer(e){try{clearTimeout(e),this.timers.delete(e)}catch(t){}}cleanupInterval(e){try{clearInterval(e),this.intervals.delete(e)}catch(t){}}cleanupEventListener(e,t,s){try{e.removeEventListener(t,s),this.eventListeners.delete({element:e,event:t,handler:s})}catch(a){}}cleanupAllSubscriptions(){this.subscriptions.forEach((e=>{try{e()}catch(t){}})),this.subscriptions.clear()}cleanupAllTimers(){this.timers.forEach((e=>{try{clearTimeout(e)}catch(t){}})),this.timers.clear()}cleanupAllIntervals(){this.intervals.forEach((e=>{try{clearInterval(e)}catch(t){}})),this.intervals.clear()}cleanupAllEventListeners(){this.eventListeners.forEach((({element:e,event:t,handler:s})=>{try{e.removeEventListener(t,s)}catch(a){}})),this.eventListeners.clear()}cleanupAll(){this.cleanupAllSubscriptions(),this.cleanupAllTimers(),this.cleanupAllIntervals(),this.cleanupAllEventListeners()}getStats(){return{subscriptions:this.subscriptions.size,timers:this.timers.size,intervals:this.intervals.size,eventListeners:this.eventListeners.size}}checkForLeaks(){const e=this.getStats();return e.subscriptions+e.timers+e.intervals+e.eventListeners>50}startAutoCleanup(e=6e4){const t=setInterval((()=>{this.checkForLeaks()&&this.cleanupAll()}),e);this.registerInterval(t)}}const Ws=(e,t)=>{const s=Bs.getInstance(),a=setInterval(e,t);return s.registerInterval(a),a};if("undefined"!=typeof window){const e=Bs.getInstance();window.addEventListener("beforeunload",(()=>{e.cleanupAll()})),window.addEventListener("pagehide",(()=>{e.cleanupAll()})),e.startAutoCleanup(6e4)}class Js{static instance;healthChecks=new Map;startTime=Date.now();autoFixEnabled=!0;static getInstance(){return Js.instance||(Js.instance=new Js),Js.instance}constructor(){this.startPeriodicHealthChecks()}startPeriodicHealthChecks(){Ws((()=>{this.quickHealthCheck()}),3e4),Ws((()=>{this.fullHealthCheck()}),3e5),setTimeout((()=>this.fullHealthCheck()),5e3)}async quickHealthCheck(){const e=[this.checkDatabaseHealth(),this.checkAuthHealth(),this.checkRealtimeHealth()];(await Promise.allSettled(e)).forEach(((e,t)=>{const s=["database","auth","realtime"];"fulfilled"===e.status&&this.healthChecks.set(s[t],e.value)}))}async fullHealthCheck(){const e=Date.now(),t=await Us.runFullIntegrationTest(),s=await Promise.allSettled([this.checkDatabaseHealth(),this.checkAuthHealth(),this.checkRealtimeHealth(),this.checkMapboxHealth(),this.checkNotificationHealth(),this.checkAnalyticsHealth(),this.checkPWAHealth(),this.checkCacheHealth()]),a=[],i=["database","auth","realtime","mapbox","notifications","analytics","pwa","cache"];s.forEach(((e,t)=>{"fulfilled"===e.status?(a.push(e.value),this.healthChecks.set(i[t],e.value)):a.push({component:i[t],status:"down",lastCheck:(new Date).toISOString(),responseTime:0,errorCount:1,details:{error:e.reason}})}));const r=a.filter((e=>"down"===e.status)).length,n=a.filter((e=>"degraded"===e.status)).length;let o;o=r>2?"down":r>0||n>2?"degraded":"healthy";const l=[],c=[];t.failed>0&&(l.push(`${t.failed} testes de integração falharam`),c.push("Executar correções automáticas de integração")),r>0&&(l.push(`${r} componentes offline`),c.push("Verificar conectividade e configurações")),this.autoFixEnabled&&(l.length>0||t.failed>0)&&await this.attemptAutoFix(t,a);const d={overall:o,components:a,lastFullCheck:(new Date).toISOString(),uptime:Date.now()-this.startTime,activeUsers:await this.getActiveUsersCount(),criticalIssues:l,recommendations:c};return Ft.track("system_health_check",{overall_status:o,components_down:r,components_degraded:n,critical_issues:l.length,check_duration:Date.now()-e}),d}async checkDatabaseHealth(){const e=Date.now();try{const{data:t,error:s}=await Rt.from("profiles").select("count").limit(1),a=Date.now()-e;return s?{component:"database",status:"down",lastCheck:(new Date).toISOString(),responseTime:a,errorCount:1,details:{error:s.message}}:{component:"database",status:a>1e3?"degraded":"healthy",lastCheck:(new Date).toISOString(),responseTime:a,errorCount:0}}catch(t){return{component:"database",status:"down",lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:1,details:{error:String(t)}}}}async checkAuthHealth(){const e=Date.now();try{const{data:t,error:s}=await Rt.auth.getSession(),a=Date.now()-e;return{component:"auth",status:s?"degraded":"healthy",lastCheck:(new Date).toISOString(),responseTime:a,errorCount:s?1:0,details:s?{error:s.message}:{hasSession:!!t.session}}}catch(t){return{component:"auth",status:"down",lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:1,details:{error:String(t)}}}}async checkRealtimeHealth(){const e=Date.now();return{component:"realtime",status:"disabled",lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:0,details:{message:"Realtime desabilitado para estabilidade"}}}async checkMapboxHealth(){const e=Date.now();try{const t=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/test.json?access_token=pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g&limit=1"),s=Date.now()-e;return t.ok?{component:"mapbox",status:s>2e3?"degraded":"healthy",lastCheck:(new Date).toISOString(),responseTime:s,errorCount:0}:{component:"mapbox",status:"down",lastCheck:(new Date).toISOString(),responseTime:s,errorCount:1,details:{httpStatus:t.status}}}catch(t){return{component:"mapbox",status:"down",lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:1,details:{error:String(t)}}}}async checkNotificationHealth(){const e=Date.now();try{const t=Notification.permission,s="serviceWorker"in navigator;let a;return a="granted"===t&&s?"healthy":"denied"!==t?"degraded":"down",{component:"notifications",status:a,lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:"down"===a?1:0,details:{permission:t,hasServiceWorker:s}}}catch(t){return{component:"notifications",status:"down",lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:1,details:{error:String(t)}}}}async checkAnalyticsHealth(){const e=Date.now();try{const t=Ft.getSessionStats();return{component:"analytics",status:"healthy",lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:0,details:t}}catch(t){return{component:"analytics",status:"down",lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:1,details:{error:String(t)}}}}async checkPWAHealth(){const e=Date.now();try{const t="serviceWorker"in navigator;let s=!1;if(t){const e=await navigator.serviceWorker.getRegistration();s=!!e?.active}const a=t&&s?"healthy":"degraded";return{component:"pwa",status:a,lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:"degraded"===a?1:0,details:{hasServiceWorker:t,swActive:s}}}catch(t){return{component:"pwa",status:"down",lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:1,details:{error:String(t)}}}}async checkCacheHealth(){const e=Date.now();try{if("caches"in window){const t=await caches.keys(),s=t.some((e=>e.includes("mobidrive")||e.includes("passenger-app")));return{component:"cache",status:s?"healthy":"degraded",lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:s?0:1,details:{cacheCount:t.length,hasAppCache:s}}}return{component:"cache",status:"degraded",lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:1,details:{error:"Cache API not supported"}}}catch(t){return{component:"cache",status:"down",lastCheck:(new Date).toISOString(),responseTime:Date.now()-e,errorCount:1,details:{error:String(t)}}}}async getActiveUsersCount(){try{const{data:{user:e}}=await Rt.auth.getUser();return e?1:0}catch{return 0}}async attemptAutoFix(e,t){const s=[],a=t.find((e=>"cache"===e.component));"healthy"!==a?.status&&s.push(await this.fixCacheIssues());const i=t.find((e=>"pwa"===e.component));"healthy"!==i?.status&&s.push(await this.fixServiceWorkerIssues());const r=t.find((e=>"notifications"===e.component));return"degraded"===r?.status&&s.push(await this.fixNotificationIssues()),s}async fixCacheIssues(){try{if("caches"in window){const e=(await caches.keys()).filter((e=>e.includes("old")||e.includes("v0")));return await Promise.all(e.map((e=>caches.delete(e)))),{issue:"Cache Issues",attempted:!0,success:!0,message:`Limpou ${e.length} caches antigos`,details:{deletedCaches:e}}}return{issue:"Cache Issues",attempted:!1,success:!1,message:"Cache API não suportada"}}catch(e){return{issue:"Cache Issues",attempted:!0,success:!1,message:`Erro ao corrigir cache: ${e}`,details:{error:e}}}}async fixServiceWorkerIssues(){try{return"serviceWorker"in navigator?(await navigator.serviceWorker.register("/sw.js"),{issue:"Service Worker Issues",attempted:!0,success:!0,message:"Service Worker re-registrado com sucesso"}):{issue:"Service Worker Issues",attempted:!1,success:!1,message:"Service Worker não suportado"}}catch(e){return{issue:"Service Worker Issues",attempted:!0,success:!1,message:`Erro ao corrigir Service Worker: ${e}`,details:{error:e}}}}async fixNotificationIssues(){try{const e=await Ds.requestPermission();return{issue:"Notification Issues",attempted:!0,success:e,message:e?"Permissão concedida":"Permissão negada pelo usuário"}}catch(e){return{issue:"Notification Issues",attempted:!0,success:!1,message:`Erro ao solicitar permissão: ${e}`,details:{error:e}}}}getSystemStatus(){const e=Array.from(this.healthChecks.values());if(0===e.length)return null;const t=e.filter((e=>"down"===e.status)).length,s=e.filter((e=>"degraded"===e.status)).length;let a;return a=t>2?"down":t>0||s>2?"degraded":"healthy",{overall:a,components:e,lastFullCheck:(new Date).toISOString(),uptime:Date.now()-this.startTime,activeUsers:0,criticalIssues:[],recommendations:[]}}setAutoFixEnabled(e){this.autoFixEnabled=e}}const Vs=Js.getInstance(),Ks=()=>{const[e,t]=r.useState(null),[n,o]=r.useState(null),[l,m]=r.useState(!1),[h,u]=r.useState(null);is(),r.useEffect((()=>{p(),Ft.trackPageView("system_tests",{user_type:"admin"})}),[]);const p=async()=>{const e=Vs.getSystemStatus();o(e)},f=e=>{switch(e){case"passed":return s.jsx(x,{className:"w-5 h-5 text-green-500"});case"failed":return s.jsx(oe,{className:"w-5 h-5 text-red-500"});case"warning":return s.jsx(c,{className:"w-5 h-5 text-yellow-500"})}},v=e=>{const t={database:g,auth:R,realtime:ne,mapbox:b,notifications:w,analytics:re,pwa:ie,cache:d}[e]||b;return s.jsx(t,{className:"w-5 h-5"})},y=e=>{switch(e){case"healthy":return"from-green-500 to-green-600";case"degraded":return"from-yellow-500 to-yellow-600";case"down":return"from-red-500 to-red-600"}};return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden",children:[s.jsx(as,{variant:"static",opacity:.7}),s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"flex items-center justify-between px-4 mb-4",children:[s.jsx(a.button,{onClick:()=>window.location.href="/dashboard",className:"p-2 text-white/80 hover:text-white transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(P,{className:"w-6 h-6"})}),s.jsxs("div",{className:"text-center",children:[s.jsx("h1",{className:"text-2xl font-bold text-white",children:"🧪 Testes do Sistema"}),s.jsx("p",{className:"text-xs text-white/70",children:"Diagnóstico e Integração"})]}),s.jsx("div",{className:"w-10 h-10"})]})}),s.jsxs("div",{className:"flex-1 overflow-y-auto px-4 space-y-6",children:[s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"🚀 Ações"}),s.jsxs("div",{className:"space-y-3",children:[s.jsx(a.button,{onClick:async()=>{m(!0),t(null);try{Ft.trackUserAction("integration_tests_started","system_tests");const e=await Us.runFullIntegrationTest();t(e),await p(),Ft.trackUserAction("integration_tests_completed","system_tests",{total_tests:e.totalTests,passed:e.passed,failed:e.failed,warnings:e.warnings,overall_status:e.overallStatus})}catch(e){Ft.trackError("integration_tests_error",e)}finally{m(!1)}},disabled:l,className:"w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2 disabled:opacity-50",whileHover:{scale:l?1:1.02},whileTap:{scale:l?1:.98},children:l?s.jsxs(s.Fragment,{children:[s.jsx(d,{className:"w-5 h-5 animate-spin"}),s.jsx("span",{children:"Executando Testes..."})]}):s.jsxs(s.Fragment,{children:[s.jsx(X,{className:"w-5 h-5"}),s.jsx("span",{children:"Executar Testes de Integração"})]})}),s.jsx(a.button,{onClick:async()=>{m(!0);try{const e=await Vs.fullHealthCheck();o(e),Ft.trackUserAction("health_check_completed","system_tests",{overall_status:e.overall,components_count:e.components.length,critical_issues:e.criticalIssues.length})}catch(e){}finally{m(!1)}},disabled:l,className:"w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2 disabled:opacity-50",whileHover:{scale:l?1:1.02},whileTap:{scale:l?1:.98},children:l?s.jsxs(s.Fragment,{children:[s.jsx(d,{className:"w-5 h-5 animate-spin"}),s.jsx("span",{children:"Verificando Saúde..."})]}):s.jsxs(s.Fragment,{children:[s.jsx(b,{className:"w-5 h-5"}),s.jsx("span",{children:"Verificar Saúde do Sistema"})]})})]})]}),n&&s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"💊 Saúde do Sistema"}),s.jsx("div",{className:"mb-4",children:s.jsx("div",{className:`inline-flex items-center space-x-2 px-3 py-1 rounded-full bg-gradient-to-r ${y(n.overall)}`,children:s.jsx("span",{className:"text-white font-medium text-sm",children:"healthy"===n.overall?"✅ Saudável":"degraded"===n.overall?"⚠️ Degradado":"❌ Crítico"})})}),s.jsx("div",{className:"grid grid-cols-2 gap-3",children:n.components.map((e=>s.jsxs("div",{className:`p-3 rounded-xl bg-gradient-to-r ${y(e.status)} bg-opacity-20 border border-white/10`,children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[v(e.component),s.jsx("span",{className:"text-white font-medium text-sm capitalize",children:e.component})]}),s.jsxs("div",{className:"text-xs text-white/70",children:[e.responseTime,"ms"]})]},e.component)))}),n.criticalIssues.length>0&&s.jsxs("div",{className:"mt-4 p-3 bg-red-500/20 rounded-xl border border-red-500/30",children:[s.jsx("h4",{className:"text-red-200 font-medium text-sm mb-2",children:"⚠️ Problemas Críticos:"}),n.criticalIssues.map(((e,t)=>s.jsxs("p",{className:"text-red-200 text-xs",children:["• ",e]},t)))]})]}),e&&s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"📊 Resultados dos Testes"}),s.jsxs("div",{className:"grid grid-cols-3 gap-3 mb-4",children:[s.jsxs("div",{className:"text-center p-3 bg-green-500/20 rounded-xl border border-green-500/30",children:[s.jsx("div",{className:"text-2xl font-bold text-green-400",children:e.passed}),s.jsx("div",{className:"text-xs text-green-200",children:"Passou"})]}),s.jsxs("div",{className:"text-center p-3 bg-red-500/20 rounded-xl border border-red-500/30",children:[s.jsx("div",{className:"text-2xl font-bold text-red-400",children:e.failed}),s.jsx("div",{className:"text-xs text-red-200",children:"Falhou"})]}),s.jsxs("div",{className:"text-center p-3 bg-yellow-500/20 rounded-xl border border-yellow-500/30",children:[s.jsx("div",{className:"text-2xl font-bold text-yellow-400",children:e.warnings}),s.jsx("div",{className:"text-xs text-yellow-200",children:"Avisos"})]})]}),s.jsx("div",{className:"space-y-2",children:e.tests.map(((e,t)=>s.jsxs(a.div,{onClick:()=>u(e),className:"flex items-center justify-between p-3 bg-white/5 rounded-xl border border-white/10 cursor-pointer hover:bg-white/10 transition-colors",whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[f(e.status),s.jsxs("div",{children:[s.jsx("div",{className:"text-white font-medium text-sm",children:e.testName}),s.jsx("div",{className:"text-white/60 text-xs",children:e.message})]})]}),s.jsxs("div",{className:"flex items-center space-x-2 text-white/50 text-xs",children:[s.jsx(I,{className:"w-3 h-3"}),s.jsxs("span",{children:[e.duration,"ms"]})]})]},t)))})]})]}),s.jsx(a.div,{className:"pb-4 text-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:.5},children:s.jsx("p",{className:"text-white/50 text-xs",children:"© 2024 MobiDrive. Sistema de Diagnóstico."})})]}),s.jsx(i,{children:h&&s.jsx(a.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4",onClick:()=>u(null),children:s.jsxs(a.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-black/80 backdrop-blur-md rounded-2xl p-6 border border-white/10 max-w-sm w-full",onClick:e=>e.stopPropagation(),children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-semibold text-white",children:"Detalhes do Teste"}),s.jsx("button",{onClick:()=>u(null),className:"text-white/60 hover:text-white",children:"×"})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-white/70",children:"Nome:"}),s.jsx("div",{className:"text-white font-medium",children:h.testName})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-white/70",children:"Status:"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[f(h.status),s.jsx("span",{className:"text-white capitalize",children:h.status})]})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-white/70",children:"Mensagem:"}),s.jsx("div",{className:"text-white",children:h.message})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-white/70",children:"Duração:"}),s.jsxs("div",{className:"text-white",children:[h.duration,"ms"]})]}),h.details&&s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-white/70",children:"Detalhes:"}),s.jsx("div",{className:"text-white/80 text-xs bg-white/5 p-2 rounded",children:s.jsx("pre",{children:JSON.stringify(h.details,null,2)})})]})]})]})})})]})};const Gs=new class{results=[];startTime=Date.now();async runCompleteValidation(){return await this.validateSystemHealth(),await this.validateIntegration(),await this.validateUserJourney(),await this.validateDatabase(),await this.validatePerformance(),await this.validatePWA(),this.generateComprehensiveReport()}async validateSystemHealth(){try{const e=await Vs.fullHealthCheck();for(const t of e.components)this.addResult("systemHealth",`System Health - ${t.component}`,"healthy"===t.status?"passed":"degraded"===t.status?"warning":"failed",`${t.component}: ${t.status} (${t.responseTime}ms)`,t);e.criticalIssues.length>0?this.addResult("systemHealth","Critical Issues Check","failed",`${e.criticalIssues.length} problemas críticos encontrados`,e.criticalIssues):this.addResult("systemHealth","Critical Issues Check","passed","Nenhum problema crítico encontrado")}catch(e){this.addResult("systemHealth","System Health Check","failed",`Erro na verificação de saúde: ${e}`,e)}}async validateIntegration(){try{const e=await Us.runFullIntegrationTest();for(const t of e.tests)this.addResult("integration",t.testName,t.status,t.message,t.details);await this.validateRideChatIntegration(),await this.validateNotificationIntegration(),await this.validateAnalyticsIntegration()}catch(e){this.addResult("integration","Integration Tests","failed",`Erro nos testes de integração: ${e}`,e)}}async validateUserJourney(){await this.validateAuthenticationFlow(),await this.validateDashboardAccess(),await this.validateRideRequestFlow(),await this.validateRideTrackingFlow()}async validateDatabase(){try{const t=["profiles","ride_requests","driver_locations","notifications","chat_messages","notification_settings","push_subscriptions","analytics_sessions","analytics_events"];for(const s of t)try{const{data:e,error:t}=await Rt.from(s).select("count").limit(1);t?this.addResult("database",`Table Check - ${s}`,"failed",`Tabela ${s} não acessível: ${t.message}`,t):this.addResult("database",`Table Check - ${s}`,"passed",`Tabela ${s} acessível`)}catch(e){this.addResult("database",`Table Check - ${s}`,"failed",`Erro ao acessar tabela ${s}: ${e}`,e)}await this.validateRLS(),await this.validateRelationships()}catch(t){this.addResult("database","Database Validation","failed",`Erro na validação do banco: ${t}`,t)}}async validatePerformance(){try{if("performance"in window){const e=performance.getEntriesByType("navigation")[0],t={loadTime:e.loadEventEnd-e.loadEventStart,domContentLoaded:e.domContentLoadedEventEnd-e.domContentLoadedEventStart,firstPaint:performance.getEntriesByName("first-paint")[0]?.startTime||0,firstContentfulPaint:performance.getEntriesByName("first-contentful-paint")[0]?.startTime||0};t.loadTime<3e3?this.addResult("performance","Load Time","passed",`Tempo de carregamento: ${t.loadTime}ms`,t):t.loadTime<5e3?this.addResult("performance","Load Time","warning",`Tempo de carregamento alto: ${t.loadTime}ms`,t):this.addResult("performance","Load Time","failed",`Tempo de carregamento crítico: ${t.loadTime}ms`,t),t.domContentLoaded<2e3?this.addResult("performance","DOM Content Loaded","passed",`DOM carregado em: ${t.domContentLoaded}ms`,t):this.addResult("performance","DOM Content Loaded","warning",`DOM carregamento lento: ${t.domContentLoaded}ms`,t)}await this.validateBundleSize()}catch(e){this.addResult("performance","Performance Validation","failed",`Erro na validação de performance: ${e}`,e)}}async validatePWA(){try{if("serviceWorker"in navigator){await navigator.serviceWorker.getRegistration()?this.addResult("pwa","Service Worker","passed","Service Worker registrado e ativo"):this.addResult("pwa","Service Worker","warning","Service Worker não registrado")}else this.addResult("pwa","Service Worker","failed","Service Worker não suportado");if(document.querySelector('link[rel="manifest"]')?this.addResult("pwa","Web App Manifest","passed","Manifest encontrado"):this.addResult("pwa","Web App Manifest","warning","Manifest não encontrado"),"Notification"in window){const e=Notification.permission;"granted"===e?this.addResult("pwa","Push Notifications","passed","Notificações permitidas"):"default"===e?this.addResult("pwa","Push Notifications","warning","Permissão de notificações não solicitada"):this.addResult("pwa","Push Notifications","failed","Notificações negadas")}else this.addResult("pwa","Push Notifications","failed","API de notificações não suportada");if("caches"in window){const e=await caches.keys();e.length>0?this.addResult("pwa","Cache Storage","passed",`${e.length} caches encontrados`):this.addResult("pwa","Cache Storage","warning","Nenhum cache encontrado")}else this.addResult("pwa","Cache Storage","failed","Cache API não suportada")}catch(e){this.addResult("pwa","PWA Validation","failed",`Erro na validação PWA: ${e}`,e)}}async validateRideChatIntegration(){try{const{data:e}=await Rt.from("ride_requests").select("id").limit(1),{data:t}=await Rt.from("chat_messages").select("ride_id").limit(1);this.addResult("integration","Ride-Chat Integration","passed","Integração ride-chat verificada")}catch(e){this.addResult("integration","Ride-Chat Integration","failed",`Erro na integração ride-chat: ${e}`)}}async validateNotificationIntegration(){try{await Ds.requestPermission(),this.addResult("integration","Notification Integration","passed","Sistema de notificações integrado")}catch(e){this.addResult("integration","Notification Integration","warning",`Notificações com limitações: ${e}`)}}async validateAnalyticsIntegration(){try{Ft.track("validation_test",{test:!0});const e=Ft.getSessionStats();this.addResult("integration","Analytics Integration","passed","Analytics funcionando",e)}catch(e){this.addResult("integration","Analytics Integration","failed",`Erro no analytics: ${e}`)}}async validateAuthenticationFlow(){try{const{data:{user:e}}=await Rt.auth.getUser();e?this.addResult("userJourney","Authentication Flow","passed","Usuário autenticado"):this.addResult("userJourney","Authentication Flow","warning","Usuário não autenticado")}catch(e){this.addResult("userJourney","Authentication Flow","failed",`Erro na autenticação: ${e}`)}}async validateDashboardAccess(){try{document.querySelectorAll('[data-testid="dashboard"]').length>0||window.location.pathname.includes("dashboard")?this.addResult("userJourney","Dashboard Access","passed","Dashboard acessível"):this.addResult("userJourney","Dashboard Access","warning","Dashboard não detectado na página atual")}catch(e){this.addResult("userJourney","Dashboard Access","failed",`Erro no acesso ao dashboard: ${e}`)}}async validateRideRequestFlow(){try{await js.calculateRideEstimate({lat:-23.5505,lng:-46.6333},{lat:-23.5489,lng:-46.6388})?this.addResult("userJourney","Ride Request Flow","passed","Fluxo de solicitação funcional"):this.addResult("userJourney","Ride Request Flow","warning","Estimativa de corrida não calculada")}catch(e){this.addResult("userJourney","Ride Request Flow","failed",`Erro no fluxo de corrida: ${e}`)}}async validateRideTrackingFlow(){try{const{data:e}=await Rt.from("ride_requests").select("id").limit(1);this.addResult("userJourney","Ride Tracking Flow","passed","Sistema de tracking configurado")}catch(e){this.addResult("userJourney","Ride Tracking Flow","failed",`Erro no tracking: ${e}`)}}async validateRLS(){try{const{error:e}=await Rt.from("profiles").select("*").limit(1);e&&e.message.includes("RLS")?this.addResult("database","RLS Validation","passed","RLS funcionando corretamente"):this.addResult("database","RLS Validation","warning","RLS pode não estar configurado adequadamente")}catch(e){this.addResult("database","RLS Validation","failed",`Erro na validação RLS: ${e}`)}}async validateRelationships(){try{const{data:e,error:t}=await Rt.from("ride_requests").select("\n          id,\n          user_id,\n          profiles!inner(id, email)\n        ").limit(1);t?this.addResult("database","Relationships","warning","Alguns relacionamentos podem ter problemas"):this.addResult("database","Relationships","passed","Relacionamentos funcionando")}catch(e){this.addResult("database","Relationships","failed",`Erro nos relacionamentos: ${e}`)}}async validateBundleSize(){try{const e=performance.getEntriesByType("resource"),t=e.reduce(((e,t)=>e+(t.transferSize||0)),0)/1048576;t<5?this.addResult("performance","Bundle Size","passed",`Tamanho total: ${t.toFixed(2)}MB`):t<10?this.addResult("performance","Bundle Size","warning",`Tamanho alto: ${t.toFixed(2)}MB`):this.addResult("performance","Bundle Size","failed",`Tamanho crítico: ${t.toFixed(2)}MB`)}catch(e){this.addResult("performance","Bundle Size","warning","Não foi possível medir o tamanho do bundle")}}addResult(e,t,s,a,i){const r={component:t,status:s,message:a,details:i,timestamp:(new Date).toISOString(),duration:Date.now()-this.startTime};this.results.push(r)}generateComprehensiveReport(){const e=this.results.filter((e=>"passed"===e.status)).length,t=this.results.filter((e=>"failed"===e.status)).length,s=this.results.filter((e=>"warning"===e.status)).length,a=t>0?"critical":s>3?"issues":"healthy",i=0===t&&s<5,r=this.results.filter((e=>"failed"===e.status)).map((e=>`${e.component}: ${e.message}`)),n=[];return s>0&&n.push(`Resolver ${s} avisos para melhorar a qualidade`),t>0&&n.push(`Corrigir ${t} problemas críticos antes do deploy`),i&&n.push("Sistema pronto para deploy em produção"),{timestamp:(new Date).toISOString(),overallStatus:a,totalTests:this.results.length,passed:e,failed:t,warnings:s,categories:{systemHealth:this.results.filter((e=>e.component.includes("System Health")||e.component.includes("Critical Issues"))),integration:this.results.filter((e=>e.component.includes("Integration")||e.component.includes("Test"))),userJourney:this.results.filter((e=>e.component.includes("Authentication")||e.component.includes("Dashboard")||e.component.includes("Ride"))),database:this.results.filter((e=>e.component.includes("Table")||e.component.includes("RLS")||e.component.includes("Relationships"))),performance:this.results.filter((e=>e.component.includes("Load")||e.component.includes("DOM")||e.component.includes("Bundle"))),pwa:this.results.filter((e=>e.component.includes("Service Worker")||e.component.includes("Manifest")||e.component.includes("Notifications")||e.component.includes("Cache")))},criticalIssues:r,recommendations:n,deploymentReady:i}}},Ys=()=>{const[e,t]=r.useState(null),[i,n]=r.useState(!1),[o,l]=r.useState(""),[m,h]=r.useState(0);is(),r.useEffect((()=>{Ft.trackPageView("comprehensive_validation",{user_type:"admin"})}),[]);const u=e=>{switch(e){case"passed":return s.jsx(x,{className:"w-5 h-5 text-green-500"});case"failed":return s.jsx(oe,{className:"w-5 h-5 text-red-500"});case"warning":return s.jsx(c,{className:"w-5 h-5 text-yellow-500"})}},p=e=>{const t={systemHealth:b,integration:ne,userJourney:ce,database:g,performance:v,pwa:ie}[e]||R;return s.jsx(t,{className:"w-5 h-5"})};return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden",children:[s.jsx(as,{variant:"static",opacity:.7}),s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"flex items-center justify-between px-4 mb-4",children:[s.jsx(a.button,{onClick:()=>window.location.href="/dashboard",className:"p-2 text-white/80 hover:text-white transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(P,{className:"w-6 h-6"})}),s.jsxs("div",{className:"text-center",children:[s.jsx("h1",{className:"text-2xl font-bold text-white",children:"🔍 Validação Completa"}),s.jsx("p",{className:"text-xs text-white/70",children:"Inspeção End-to-End"})]}),s.jsx("div",{className:"w-10 h-10"})]})}),s.jsxs("div",{className:"flex-1 overflow-y-auto px-4 space-y-6",children:[s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsxs("div",{className:"text-center mb-4",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"🚀 Validação Completa do Sistema"}),s.jsx("p",{className:"text-white/70 text-sm",children:"Executa todos os testes de integração, performance, PWA e jornadas do usuário"})]}),i&&s.jsxs("div",{className:"mb-4",children:[s.jsx("div",{className:"bg-white/10 rounded-full h-2 mb-2",children:s.jsx(a.div,{className:"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full",initial:{width:0},animate:{width:`${m}%`},transition:{duration:.3}})}),s.jsx("p",{className:"text-white/60 text-xs text-center",children:o||`Progresso: ${m}%`})]}),s.jsx(a.button,{onClick:async()=>{n(!0),t(null),h(0);try{Ft.trackUserAction("comprehensive_validation_started","validation");const e=setInterval((()=>{h((e=>Math.min(e+2,95)))}),200),s=await Gs.runCompleteValidation();clearInterval(e),h(100),t(s),Ft.trackUserAction("comprehensive_validation_completed","validation",{overall_status:s.overallStatus,total_tests:s.totalTests,passed:s.passed,failed:s.failed,warnings:s.warnings,deployment_ready:s.deploymentReady})}catch(e){Ft.trackError("comprehensive_validation_error",e)}finally{n(!1),l("")}},disabled:i,className:"w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-4 px-6 rounded-xl font-semibold flex items-center justify-center space-x-2 disabled:opacity-50",whileHover:{scale:i?1:1.02},whileTap:{scale:i?1:.98},children:i?s.jsxs(s.Fragment,{children:[s.jsx(d,{className:"w-5 h-5 animate-spin"}),s.jsx("span",{children:"Executando Validação..."})]}):s.jsxs(s.Fragment,{children:[s.jsx(X,{className:"w-5 h-5"}),s.jsx("span",{children:"Iniciar Validação Completa"})]})})]}),e&&s.jsxs(s.Fragment,{children:[s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsx("div",{className:"text-center mb-4",children:s.jsx("div",{className:`inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-gradient-to-r ${(e=>{switch(e){case"healthy":return"from-green-500 to-green-600";case"issues":return"from-yellow-500 to-yellow-600";case"critical":return"from-red-500 to-red-600";default:return"from-gray-500 to-gray-600"}})(e.overallStatus)}`,children:s.jsx("span",{className:"text-white font-bold",children:(e=>{switch(e){case"healthy":return"✅ Sistema Saudável";case"issues":return"⚠️ Problemas Detectados";case"critical":return"❌ Problemas Críticos";default:return"⏳ Aguardando Validação"}})(e.overallStatus)})})}),s.jsxs("div",{className:"grid grid-cols-3 gap-3 mb-4",children:[s.jsxs("div",{className:"text-center p-3 bg-green-500/20 rounded-xl border border-green-500/30",children:[s.jsx("div",{className:"text-2xl font-bold text-green-400",children:e.passed}),s.jsx("div",{className:"text-xs text-green-200",children:"Passou"})]}),s.jsxs("div",{className:"text-center p-3 bg-red-500/20 rounded-xl border border-red-500/30",children:[s.jsx("div",{className:"text-2xl font-bold text-red-400",children:e.failed}),s.jsx("div",{className:"text-xs text-red-200",children:"Falhou"})]}),s.jsxs("div",{className:"text-center p-3 bg-yellow-500/20 rounded-xl border border-yellow-500/30",children:[s.jsx("div",{className:"text-2xl font-bold text-yellow-400",children:e.warnings}),s.jsx("div",{className:"text-xs text-yellow-200",children:"Avisos"})]})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("div",{className:"px-3 py-1 rounded-full text-sm font-medium "+(e.deploymentReady?"bg-green-500/20 text-green-200 border border-green-500/30":"bg-red-500/20 text-red-200 border border-red-500/30"),children:e.deploymentReady?"🚀 Pronto para Deploy":"⚠️ Não Pronto para Deploy"}),s.jsx(a.button,{onClick:()=>{if(!e)return;const t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json"}),a=URL.createObjectURL(s),i=document.createElement("a");i.href=a,i.download=`mobidrive-validation-report-${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(a),Ft.trackUserAction("validation_report_downloaded","validation")},className:"p-2 bg-blue-500/20 text-blue-200 rounded-lg border border-blue-500/30 hover:bg-blue-500/30 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(le,{className:"w-4 h-4"})})]})]}),Object.entries(e.categories).map((([e,t])=>t.length>0&&s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[p(e),s.jsx("h3",{className:"text-lg font-semibold text-white capitalize",children:e.replace(/([A-Z])/g," $1").trim()}),s.jsxs("div",{className:"text-white/60 text-sm",children:["(",t.length," testes)"]})]}),s.jsx("div",{className:"space-y-2",children:t.map(((e,t)=>s.jsxs("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-xl border border-white/10",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[u(e.status),s.jsxs("div",{children:[s.jsx("div",{className:"text-white font-medium text-sm",children:e.component}),s.jsx("div",{className:"text-white/60 text-xs",children:e.message})]})]}),s.jsxs("div",{className:"text-white/50 text-xs",children:[e.duration,"ms"]})]},t)))})]},e))),e.criticalIssues.length>0&&s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-red-500/20 backdrop-blur-md rounded-2xl p-6 border border-red-500/30 shadow-2xl",children:[s.jsxs("h3",{className:"text-lg font-semibold text-red-200 mb-4 flex items-center space-x-2",children:[s.jsx(c,{className:"w-5 h-5"}),s.jsx("span",{children:"Problemas Críticos"})]}),s.jsx("div",{className:"space-y-2",children:e.criticalIssues.map(((e,t)=>s.jsxs("div",{className:"text-red-200 text-sm",children:["• ",e]},t)))})]}),e.recommendations.length>0&&s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-blue-500/20 backdrop-blur-md rounded-2xl p-6 border border-blue-500/30 shadow-2xl",children:[s.jsxs("h3",{className:"text-lg font-semibold text-blue-200 mb-4 flex items-center space-x-2",children:[s.jsx(A,{className:"w-5 h-5"}),s.jsx("span",{children:"Recomendações"})]}),s.jsx("div",{className:"space-y-2",children:e.recommendations.map(((e,t)=>s.jsxs("div",{className:"text-blue-200 text-sm",children:["• ",e]},t)))})]})]})]}),s.jsx(a.div,{className:"pb-4 text-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:.5},children:s.jsx("p",{className:"text-white/50 text-xs",children:"© 2024 MobiDrive. Sistema de Validação Completa."})})]})]})};var Zs={};class Xs{static instance;isInitialized=!1;config;dailyLimit=1e3;maxAdsPerDay=50;webConfig={simulateAds:!0,baseRewardAmount:25,cpmRange:{min:15,max:75}};static getInstance(){return Xs.instance||(Xs.instance=new Xs),Xs.instance}constructor(){this.config={appKey:Zs.REACT_APP_APPODEAL_APP_KEY||"demo-app-key",testMode:!1,environment:"web",productionConfig:{iosAppKey:Zs.REACT_APP_APPODEAL_IOS_KEY,androidAppKey:Zs.REACT_APP_APPODEAL_ANDROID_KEY,webFallbackEnabled:!0}}}async initialize(e){try{return this.config.userId=e,"web"===this.config.environment?await this.initializeWebSimulation():await this.initializeNativeSDK(),this.isInitialized=!0,Ft.track("appodeal_initialized",{user_id:e,environment:this.config.environment,test_mode:this.config.testMode}),!0}catch(t){return!1}}async initializeWebSimulation(){await new Promise((e=>setTimeout(e,1e3))),Ft.track("appodeal_web_simulation_started",{config:this.webConfig})}async initializeNativeSDK(){}async getAvailableAds(e,t=10){this.isInitialized||await this.initialize(e);try{return(await this.getUserStats(e)).canWatchMore?"web"===this.config.environment?this.generateWebSimulationAds(t):this.getNativeAds(t):[]}catch(s){return[]}}generateWebSimulationAds(e){const t=[{title:"Novo iPhone 15 Pro",description:"Descubra as inovações do iPhone 15 Pro",advertiser:"Apple",category:"high_revenue",duration:30,baseCPM:60},{title:"Netflix - Séries Exclusivas",description:"Assista às melhores séries originais",advertiser:"Netflix",category:"medium_revenue",duration:20,baseCPM:40},{title:"Coca-Cola Zero",description:"O sabor que você ama, zero açúcar",advertiser:"Coca-Cola",category:"medium_revenue",duration:15,baseCPM:35},{title:"Banco Inter Digital",description:"Conta digital gratuita com cartão",advertiser:"Banco Inter",category:"high_revenue",duration:45,baseCPM:70},{title:"Shopee - Ofertas",description:"Milhões de produtos com frete grátis",advertiser:"Shopee",category:"low_revenue",duration:10,baseCPM:20}],s=[];for(let a=0;a<Math.min(e,t.length);a++){const e=t[a],i=.3*(Math.random()-.5),r=Math.round(e.baseCPM*(1+i));s.push({id:`appodeal_ad_${Date.now()}_${a}`,title:e.title,description:e.description,duration:e.duration,rewardAmount:r,category:e.category,advertiser:e.advertiser,isReady:!0,estimatedCPM:r})}return s.sort(((e,t)=>t.estimatedCPM-e.estimatedCPM))}async getNativeAds(e){return[]}async startAdSession(e,t){try{const s=`appodeal_session_${Date.now()}_${Math.random()}`,{error:a}=await Rt.from("ad_watch_sessions").insert([{id:s,user_id:e,ad_id:t,start_time:(new Date).toISOString(),watched_duration:0,completed:!1,reward_earned:0,ip_address:await this.getUserIP(),user_agent:navigator.userAgent}]);if(a)throw a;return Ft.track("appodeal_ad_started",{user_id:e,ad_id:t,session_id:s,platform:"appodeal"}),s}catch(s){throw s}}async completeAdSession(e,t){try{const{data:s,error:a}=await Rt.from("ad_watch_sessions").select("\n          *,\n          ad_videos (\n            duration,\n            reward_value\n          )\n        ").eq("id",e).single();if(a)throw a;const i=.8,r=t/(s.ad_videos?.duration||30);if(r<i)return{success:!1,rewardEarned:0,message:`Assista pelo menos ${Math.round(100*i)}% do anúncio para ganhar a recompensa`};const n=await this.getUserStats(s.user_id);if(n.dailyEarnings>=this.dailyLimit)return{success:!1,rewardEarned:0,message:"Limite diário de R$ 10 atingido"};let o=s.ad_videos?.reward_value||this.webConfig.baseRewardAmount;r>=.95&&(o=Math.round(1.1*o)),n.dailyEarnings+o>this.dailyLimit&&(o=this.dailyLimit-n.dailyEarnings);const{error:l}=await Rt.from("ad_watch_sessions").update({end_time:(new Date).toISOString(),completed:!0,reward_earned:o,watched_duration:t}).eq("id",e);if(l)throw l;return await this.updateDailyProgress(s.user_id,o),Ft.track("appodeal_ad_completed",{user_id:s.user_id,session_id:e,reward_earned:o,watch_percentage:r,platform:"appodeal"}),{success:!0,rewardEarned:o,message:`Parabéns! Você ganhou R$ ${(o/100).toFixed(2)} com Appodeal!`}}catch(s){return{success:!1,rewardEarned:0,message:"Erro ao processar recompensa"}}}async getUserStats(e){try{const t=(new Date).toISOString().split("T")[0],{data:s,error:a}=await Rt.from("user_ad_progress").select("*").eq("user_id",e).eq("date",t).single();if(a&&"PGRST116"!==a.code)throw a;const i=s?.total_earned||0,r=s?.videos_watched||0,{data:n}=await Rt.from("user_ad_progress").select("total_earned, videos_watched").eq("user_id",e),o=n?.reduce(((e,t)=>e+t.total_earned),0)||0,l=n?.reduce(((e,t)=>e+t.videos_watched),0)||0,c=l>0?o/l:0,d=i<this.dailyLimit&&r<this.maxAdsPerDay,m=new Date;return m.setDate(m.getDate()+1),m.setHours(0,0,0,0),{totalEarned:o,totalAdsWatched:l,averageCPM:c,dailyEarnings:i,canWatchMore:d,nextResetTime:m.toISOString()}}catch(t){return{totalEarned:0,totalAdsWatched:0,averageCPM:0,dailyEarnings:0,canWatchMore:!0,nextResetTime:(new Date).toISOString()}}}async updateDailyProgress(e,t){const s=(new Date).toISOString().split("T")[0],{data:a}=await Rt.from("user_ad_progress").select("*").eq("user_id",e).eq("date",s).single();if(a){const i=a.total_earned+t,r=a.videos_watched+1;await Rt.from("user_ad_progress").update({total_earned:i,videos_watched:r,can_watch_more:i<this.dailyLimit&&r<this.maxAdsPerDay}).eq("user_id",e).eq("date",s)}else await Rt.from("user_ad_progress").insert([{user_id:e,date:s,total_earned:t,videos_watched:1,daily_limit:this.dailyLimit,can_watch_more:t<this.dailyLimit}])}async getUserIP(){try{const e=await fetch("https://api.ipify.org?format=json");return(await e.json()).ip}catch{return"unknown"}}isReady(){return this.isInitialized}getConfig(){return{...this.config}}formatCurrency(e){return`R$ ${(e/100).toFixed(2)}`}}const Qs=Xs.getInstance(),ea=()=>{const{user:e}=Mt(),[t,n]=r.useState(null),[o,l]=r.useState([]),[c,d]=r.useState(!0),[m,h]=r.useState(null),[u,x]=r.useState(null),[p,b]=r.useState(0),[g,f]=r.useState(!1);is(),r.useEffect((()=>{e&&(w(),Ft.track("appodeal_page_view",{user_id:e.id,platform:"appodeal"}))}),[e]);const w=async()=>{if(e){d(!0);try{if(!g){const t=await Qs.initialize(e.id);f(t)}const[t,s]=await Promise.all([Qs.getUserStats(e.id),Qs.getAvailableAds(e.id,20)]);n(t),l(s)}catch(t){}finally{d(!1)}}},v=(e,t)=>{const s=100/(1e3*e.duration)*100,a=setInterval((async()=>{b((i=>{const r=i+s;return r>=100?(clearInterval(a),y(t,e.duration),100):r}))}),100)},y=async(e,t)=>{try{const s=await Qs.completeAdSession(e,t);s.success?(alert(`🎉 ${s.message}\nPowered by Appodeal`),Ft.track("appodeal_ad_completed",{session_id:e,reward_earned:s.rewardEarned,platform:"appodeal"})):alert(`⚠️ ${s.message}`),await w()}catch(s){alert("Erro ao processar recompensa. Tente novamente.")}finally{h(null),x(null),b(0)}},j=e=>{switch(e){case"high_revenue":return"from-green-500 to-green-600";case"medium_revenue":return"from-yellow-500 to-yellow-600";case"low_revenue":return"from-blue-500 to-blue-600";default:return"from-gray-500 to-gray-600"}},N=e=>{switch(e){case"high_revenue":return"💰 Alta Receita";case"medium_revenue":return"💵 Média Receita";case"low_revenue":return"💴 Baixa Receita";default:return"💰 Padrão"}};return c?s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden",children:[s.jsx(as,{variant:"static",opacity:.7}),s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsx("div",{className:"relative z-10 flex flex-col h-full min-h-screen items-center justify-center px-4",children:s.jsxs("div",{className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center",children:[s.jsx(a.div,{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},children:s.jsx(Q,{className:"w-8 h-8 text-white"})}),s.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Inicializando Appodeal..."}),s.jsx("p",{className:"text-white/70",children:"Carregando anúncios premium para você ganhar mais"})]})})]}):s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden",children:[s.jsx(as,{variant:"static",opacity:.7}),s.jsx("div",{className:"absolute inset-0 bg-black/20"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"flex items-center justify-between px-4 mb-4",children:[s.jsx(a.button,{onClick:()=>window.location.href="/dashboard",className:"p-2 text-white/80 hover:text-white transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(P,{className:"w-6 h-6"})}),s.jsxs("div",{className:"text-center",children:[s.jsx("h1",{className:"text-2xl font-bold text-white",children:"💰 Anúncios Appodeal"}),s.jsx("p",{className:"text-xs text-white/70",children:"Ganhe até R$ 10 por dia • Powered by Appodeal"})]}),s.jsx("div",{className:"w-10 h-10"})]})}),s.jsxs("div",{className:"flex-1 overflow-y-auto px-4 space-y-6",children:[t&&s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center space-x-2",children:[s.jsx(F,{className:"w-5 h-5"}),s.jsx("span",{children:"Progresso de Hoje"})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[s.jsxs("div",{className:"text-center p-3 bg-green-500/20 rounded-xl border border-green-500/30",children:[s.jsx("div",{className:"text-2xl font-bold text-green-400",children:Qs.formatCurrency(t.dailyEarnings)}),s.jsx("div",{className:"text-xs text-green-200",children:"Ganho Hoje"})]}),s.jsxs("div",{className:"text-center p-3 bg-blue-500/20 rounded-xl border border-blue-500/30",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-400",children:t.totalAdsWatched}),s.jsx("div",{className:"text-xs text-blue-200",children:"Anúncios Assistidos"})]})]}),s.jsxs("div",{className:"mb-4",children:[s.jsxs("div",{className:"flex justify-between text-sm text-white/70 mb-2",children:[s.jsx("span",{children:"Meta Diária"}),s.jsx("span",{children:"R$ 10,00"})]}),s.jsx("div",{className:"bg-white/10 rounded-full h-2",children:s.jsx(a.div,{className:"bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full",initial:{width:0},animate:{width:t.dailyEarnings/1e3*100+"%"},transition:{duration:1}})})]}),!t.canWatchMore&&s.jsxs("div",{className:"text-center p-3 bg-yellow-500/20 rounded-xl border border-yellow-500/30",children:[s.jsx("p",{className:"text-yellow-200 text-sm",children:"🎉 Meta diária atingida! Volte amanhã para ganhar mais."}),s.jsx("p",{className:"text-yellow-300 text-xs mt-1",children:"Powered by Appodeal"})]})]}),t&&s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center space-x-2",children:[s.jsx(ee,{className:"w-5 h-5"}),s.jsx("span",{children:"Suas Estatísticas"})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[s.jsxs("div",{className:"text-center p-3 bg-purple-500/20 rounded-xl border border-purple-500/30",children:[s.jsx("div",{className:"text-lg font-bold text-purple-400",children:Qs.formatCurrency(t.totalEarned)}),s.jsx("div",{className:"text-xs text-purple-200",children:"Total Ganho"})]}),s.jsxs("div",{className:"text-center p-3 bg-orange-500/20 rounded-xl border border-orange-500/30",children:[s.jsx("div",{className:"text-lg font-bold text-orange-400",children:t.totalAdsWatched}),s.jsx("div",{className:"text-xs text-orange-200",children:"Total Anúncios"})]}),s.jsxs("div",{className:"text-center p-3 bg-pink-500/20 rounded-xl border border-pink-500/30",children:[s.jsx("div",{className:"text-lg font-bold text-pink-400",children:Qs.formatCurrency(t.averageCPM)}),s.jsx("div",{className:"text-xs text-pink-200",children:"CPM Médio"})]}),s.jsxs("div",{className:"text-center p-3 bg-cyan-500/20 rounded-xl border border-cyan-500/30",children:[s.jsx("div",{className:"text-lg font-bold text-cyan-400",children:"Appodeal"}),s.jsx("div",{className:"text-xs text-cyan-200",children:"Plataforma"})]})]})]}),o.length>0&&t?.canWatchMore&&s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl",children:[s.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center space-x-2",children:[s.jsx(X,{className:"w-5 h-5"}),s.jsx("span",{children:"Anúncios Appodeal Disponíveis"})]}),s.jsx("div",{className:"space-y-3",children:o.map((i=>s.jsx(a.div,{className:"bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-colors cursor-pointer",whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>(async s=>{if(e&&t?.canWatchMore)try{const t=await Qs.startAdSession(e.id,s.id);x(t),h(s),b(0),Ft.track("appodeal_ad_started",{ad_id:s.id,ad_title:s.title,reward_amount:s.rewardAmount,platform:"appodeal"}),v(s,t)}catch(a){alert("Erro ao iniciar anúncio. Tente novamente.")}})(i),children:s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center",children:s.jsx(X,{className:"w-6 h-6 text-white"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-semibold text-white text-sm",children:i.title}),s.jsx("p",{className:"text-white/60 text-xs mt-1",children:i.description}),s.jsxs("div",{className:"flex items-center space-x-3 mt-2",children:[s.jsx("div",{className:`px-2 py-1 rounded-full bg-gradient-to-r ${j(i.category)} text-xs text-white`,children:N(i.category)}),s.jsxs("div",{className:"flex items-center space-x-1 text-green-400 text-xs",children:[s.jsx(Q,{className:"w-3 h-3"}),s.jsx("span",{children:Qs.formatCurrency(i.rewardAmount)})]}),s.jsxs("div",{className:"flex items-center space-x-1 text-white/60 text-xs",children:[s.jsx(I,{className:"w-3 h-3"}),s.jsxs("span",{children:[Math.ceil(i.duration/60),"min"]})]}),s.jsx("div",{className:"flex items-center space-x-1 text-blue-400 text-xs",children:s.jsxs("span",{children:["📱 ",i.advertiser]})})]})]})]})},i.id)))})]}),0===o.length&&t?.canWatchMore&&s.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center",children:[s.jsx(_,{className:"w-16 h-16 text-white/50 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Carregando anúncios Appodeal..."}),s.jsx("p",{className:"text-white/70 text-sm",children:"Aguarde enquanto carregamos os melhores anúncios para você!"}),s.jsx("div",{className:"mt-4 text-blue-400 text-xs",children:"Powered by Appodeal"})]})]}),s.jsx(a.div,{className:"pb-4 text-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:.5},children:s.jsx("p",{className:"text-white/50 text-xs",children:"© 2024 MobiDrive. Ganhe dinheiro assistindo anúncios."})})]})]}),s.jsx(i,{children:m&&s.jsx(a.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4",children:s.jsxs(a.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-black/80 backdrop-blur-md rounded-2xl p-6 border border-white/10 max-w-sm w-full",children:[s.jsxs("div",{className:"text-center mb-4",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:m.title}),s.jsx("p",{className:"text-white/70 text-sm",children:m.description}),s.jsx("div",{className:"text-blue-400 text-xs mt-1",children:"Powered by Appodeal"})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg h-32 flex items-center justify-center mb-4",children:s.jsx(X,{className:"w-12 h-12 text-white"})}),s.jsxs("div",{className:"mb-2",children:[s.jsxs("div",{className:"flex justify-between text-sm text-white/70 mb-1",children:[s.jsx("span",{children:"Progresso"}),s.jsxs("span",{children:[Math.round(p),"%"]})]}),s.jsx("div",{className:"bg-white/10 rounded-full h-2",children:s.jsx(a.div,{className:"bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full",style:{width:`${p}%`}})})]}),s.jsxs("div",{className:"text-center text-green-400 text-sm",children:["Recompensa: ",Qs.formatCurrency(m.rewardAmount)]})]}),s.jsx(a.button,{onClick:()=>{u&&Ft.track("appodeal_ad_skipped",{session_id:u,progress:p,platform:"appodeal"}),h(null),x(null),b(0)},className:"w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-xl font-semibold",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Pular Anúncio"})]})})})]})},ta="pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g";de.accessToken=ta;const sa=()=>{const e=st(),{user:t}=Mt(),{searchQuery:i,searchResults:n,isSearching:o,searchPlaces:l,selectResult:c,getCurrentLocation:d}=Ns();if(is(),!t)return s.jsx(ut,{to:"/login",replace:!0});const[m,h]=r.useState(!1),[u,x]=r.useState(null),[p,b]=r.useState(null),[g,w]=r.useState(null),[v,y]=r.useState([]),[j,N]=r.useState(!1),[_,k]=r.useState(!1),S=r.useRef(null),C=r.useRef(null),[E,R]=r.useState(!1),I=r.useRef(null),T=r.useRef(null),M=r.useCallback(((e,t)=>{const s=(t[1]-e[1])*Math.PI/180,a=(t[0]-e[0])*Math.PI/180,i=Math.sin(s/2)*Math.sin(s/2)+Math.cos(e[1]*Math.PI/180)*Math.cos(t[1]*Math.PI/180)*Math.sin(a/2)*Math.sin(a/2);return 6371*(2*Math.atan2(Math.sqrt(i),Math.sqrt(1-i)))}),[]),D=r.useCallback((e=>{if(!g||!e.length)return e;return e.map((e=>{const t=M(g,e.center);return{...e,distance:t}})).sort(((e,t)=>e.distance-t.distance))}),[g,M]),A=r.useCallback((async e=>{try{const t=await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${e[0]},${e[1]}.json?access_token=${ta}&language=pt-BR&limit=1`),s=await t.json();if(s.features&&s.features.length>0)return s.features[0].place_name}catch(t){}return`${e[1].toFixed(4)}, ${e[0].toFixed(4)}`}),[]),F=r.useCallback((e=>(I.current&&I.current.remove(),I.current=new de.Marker({color:"#ef4444",scale:1.2,draggable:!0}).setLngLat(e).addTo(C.current),I.current.on("dragstart",(()=>{C.current?.getSource("route")&&(C.current.removeLayer("route"),C.current.removeSource("route"))})),I.current.on("dragend",(async()=>{const e=I.current.getLngLat(),t=[e.lng,e.lat],s=await A(t),a={id:`destination-${Date.now()}`,place_name:s,center:t,geometry:{type:"Point",coordinates:t},properties:{},context:[]};b(a),await H(g,t)})),I.current)),[A,g]),H=r.useCallback((async(e,t)=>{if(C.current&&E&&e&&t)try{const s=`https://api.mapbox.com/directions/v5/mapbox/driving/${e[0]},${e[1]};${t[0]},${t[1]}?geometries=geojson&overview=full&steps=false&access_token=${ta}`,a=await fetch(s),i=await a.json();if(i.routes&&i.routes.length>0){const e=i.routes[0],t=()=>{if(!C.current)return;if(C.current.getSource("route"))try{C.current.removeLayer("route"),C.current.removeSource("route")}catch(pa){}C.current.addSource("route",{type:"geojson",data:{type:"Feature",properties:{},geometry:e.geometry}}),C.current.addLayer({id:"route",type:"line",source:"route",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"#3b82f6","line-width":6,"line-opacity":.9}});const t=e.geometry.coordinates,s=t.reduce(((e,t)=>e.extend(t)),new de.LngLatBounds(t[0],t[0]));C.current.fitBounds(s,{padding:80,duration:1e3})};C.current.isStyleLoaded()?t():C.current.once("styledata",t)}else{const s={type:"Feature",properties:{},geometry:{type:"LineString",coordinates:[e,t]}};C.current.getSource("route")&&(C.current.removeLayer("route"),C.current.removeSource("route")),C.current.addSource("route",{type:"geojson",data:s}),C.current.addLayer({id:"route",type:"line",source:"route",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"#ef4444","line-width":4,"line-opacity":.7}})}}catch(s){try{const s={type:"Feature",properties:{},geometry:{type:"LineString",coordinates:[e,t]}};C.current?.getSource("route")&&(C.current.removeLayer("route"),C.current.removeSource("route")),C.current?.addSource("route",{type:"geojson",data:s}),C.current?.addLayer({id:"route",type:"line",source:"route",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"#ef4444","line-width":4,"line-opacity":.7}})}catch(a){}}}),[E]),z=r.useCallback((e=>{S.current&&!C.current&&(C.current=new de.Map({container:S.current,style:"mapbox://styles/mapbox/dark-v11",center:e,zoom:14,attributionControl:!1,antialias:!0,pitch:0,bearing:0}),C.current.on("load",(()=>{R(!0);const t=document.createElement("div");t.innerHTML='\n        <div style="\n          width: 20px;\n          height: 20px;\n          background: #3b82f6;\n          border: 3px solid white;\n          border-radius: 50%;\n          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);\n        "></div>\n      ',T.current=new de.Marker({element:t}).setLngLat(e).addTo(C.current),C.current.on("click",(e=>{const t=[e.lngLat.lng,e.lngLat.lat];U(t)})),C.current.on("mouseenter",(()=>{C.current.getCanvas().style.cursor="crosshair"})),C.current.on("mouseleave",(()=>{C.current.getCanvas().style.cursor=""}))})),C.current.addControl(new de.NavigationControl,"bottom-right"))}),[]),U=r.useCallback((async e=>{F(e);const t=await A(e),s={id:`destination-${Date.now()}`,place_name:t,center:e,geometry:{type:"Point",coordinates:e},properties:{},context:[]};b(s),g&&await H(g,e)}),[F,A,g,H]);r.useEffect((()=>{(async()=>{try{const e=await d();w(e),z(e)}catch(e){const t=[-46.6333,-23.5505];w(t),z(t)}})()}),[d,z]),r.useEffect((()=>{("webkitSpeechRecognition"in window||"SpeechRecognition"in window)&&k(!0)}),[]),r.useEffect((()=>{const e=D(n);y(e)}),[n,D]),r.useEffect((()=>()=>{C.current&&C.current.remove()}),[]);const B=r.useCallback((()=>{if(!_)return;N(!0);const e=new(window.webkitSpeechRecognition||window.SpeechRecognition);e.lang="pt-BR",e.continuous=!1,e.interimResults=!1,e.onresult=e=>{const t=e.results[0][0].transcript;l(t),N(!1)},e.onerror=()=>{N(!1),x("Erro no reconhecimento de voz")},e.onend=()=>{N(!1)},e.start()}),[_,l]),W=r.useCallback((async e=>{b(e),c(e),C.current&&e.center&&(F(e.center),g&&await H(g,e.center),C.current.flyTo({center:e.center,zoom:16,duration:1e3,essential:!0}))}),[c,F,g,H]),J=r.useCallback((()=>{p&&(sessionStorage.setItem("rideDestination",JSON.stringify(p)),sessionStorage.setItem("rideOrigin",JSON.stringify({center:g,place_name:"Sua localização atual"})),e("/ride-request/details"))}),[p,g,e]);return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden",children:[s.jsx("div",{className:"absolute inset-0 bg-black/40"}),s.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6 px-4",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(a.button,{onClick:()=>e("/dashboard"),className:"p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(P,{className:"w-5 h-5"})}),s.jsxs("div",{className:"flex-1 text-center",children:[s.jsxs("h1",{className:"text-2xl font-bold text-white",children:["MOBI",s.jsx("span",{className:"text-blue-400",children:"DRIVE"})]}),s.jsx("p",{className:"text-sm text-white/70",children:"Selecione o destino"})]}),s.jsx("div",{className:"w-9"})]})}),s.jsxs("div",{className:"flex-1 px-4 pb-8",children:[s.jsxs("div",{className:"relative h-[70vh] rounded-2xl overflow-hidden border border-white/20 shadow-2xl bg-gray-900",children:[s.jsx("div",{ref:S,className:"w-full h-full"}),s.jsx("div",{className:"absolute top-4 left-4 right-4 z-10",children:s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-3 border border-white/20 shadow-lg",children:[s.jsxs("div",{className:"relative",children:[s.jsx(L,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4"}),s.jsx("input",{type:"text",value:i,onChange:e=>l(e.target.value),placeholder:"Para onde vamos?",className:"w-full pl-10 pr-16 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"}),_&&s.jsx("button",{onClick:B,disabled:j,className:"absolute right-8 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors disabled:opacity-50",children:j?s.jsx($,{className:"w-4 h-4 text-red-400 animate-pulse"}):s.jsx(O,{className:"w-4 h-4"})}),o&&s.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:s.jsx(q,{className:"w-4 h-4 animate-spin text-blue-400"})})]}),v.length>0&&s.jsx("div",{className:"mt-3 bg-white/10 rounded-lg border border-white/20 overflow-hidden max-h-48 overflow-y-auto",children:v.map(((e,t)=>s.jsx("button",{onClick:()=>W(e),className:"w-full text-left px-3 py-2 text-white hover:bg-white/10 transition-colors border-b border-white/10 last:border-b-0",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2 flex-1",children:[s.jsx(f,{className:"w-3 h-3 text-white/60 flex-shrink-0"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-sm font-medium",children:e.text}),s.jsx("p",{className:"text-xs text-white/60 truncate",children:e.place_name})]})]}),e.distance&&s.jsx("div",{className:"text-xs text-white/50 ml-2",children:e.distance<1?`${Math.round(1e3*e.distance)}m`:`${e.distance.toFixed(1)}km`})]})},t)))})]})}),!p&&s.jsx("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10",children:s.jsx("div",{className:"bg-black/60 backdrop-blur-sm rounded-full px-4 py-2",children:s.jsx("span",{className:"text-white text-sm",children:"Toque no mapa"})})}),!E&&s.jsx("div",{className:"absolute inset-0 bg-gray-900/80 flex items-center justify-center z-30",children:s.jsxs("div",{className:"text-center text-white",children:[s.jsx(q,{className:"w-12 h-12 animate-spin mx-auto mb-4"}),s.jsx("p",{className:"text-lg font-medium",children:"Carregando mapa..."}),s.jsx("p",{className:"text-sm text-white/70",children:"Aguarde um momento"})]})})]}),p&&s.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mt-6",children:s.jsxs("div",{className:"space-y-4",children:[s.jsx("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20 shadow-2xl",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center",children:s.jsx(f,{className:"w-4 h-4 text-white"})}),s.jsx("div",{className:"flex-1",children:s.jsx("p",{className:"text-white text-sm font-medium line-clamp-1",children:p.place_name})})]}),s.jsx("button",{onClick:()=>{b(null),I.current&&(I.current.remove(),I.current=null),C.current?.getSource("route")&&(C.current.removeLayer("route"),C.current.removeSource("route"))},className:"text-white/60 hover:text-white transition-colors p-1",children:"✕"})]})}),s.jsx(a.button,{onClick:J,className:"w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200",whileHover:{scale:1.02},whileTap:{scale:.98},children:"Confirmar destino"})]})})]})]})]})},aa=[{id:"moto",name:"MobiMoto",icon:"🏍️",description:"Rápido e econômico",basePrice:3.5,pricePerKm:.8,eta:3,available:!0},{id:"carro",name:"MobiCar",icon:"🚗",description:"Conforto e segurança",basePrice:5,pricePerKm:1.2,eta:5,available:!0},{id:"suv",name:"MobiSUV",icon:"🚙",description:"Espaço e luxo",basePrice:8,pricePerKm:1.8,eta:7,available:!0}],ia=[{id:"card",name:"Cartão",icon:"💳",description:"Crédito ou débito",available:!0},{id:"cash",name:"Dinheiro",icon:"💵",description:"Pagamento em espécie",available:!0},{id:"pix",name:"PIX",icon:"📱",description:"Pagamento instantâneo",available:!0}],ra=()=>{const e=st(),{user:t}=Mt();if(is(),!t)return s.jsx(ut,{to:"/login",replace:!0});const[i,n]=r.useState(!1),[o,l]=r.useState(null),[c,d]=r.useState(null),[m,h]=r.useState(null),[u,p]=r.useState(null),[b,g]=r.useState("");r.useEffect((()=>{const t=sessionStorage.getItem("rideDestination"),s=sessionStorage.getItem("rideOrigin");t&&d(JSON.parse(t)),s&&l(JSON.parse(s)),t||e("/ride-request/map")}),[e]);const f=r.useCallback((e=>e.basePrice+5*e.pricePerKm),[]),w=r.useCallback((e=>{h(e)}),[]),v=r.useCallback((e=>{p(e)}),[]),y=r.useCallback((async()=>{if(!m||!u||!c)return;n(!0);const t={origin:o,destination:c,vehicle:m,payment:u,observations:b,price:f(m),requestTime:(new Date).toISOString()};sessionStorage.setItem("tripData",JSON.stringify(t)),setTimeout((()=>{n(!1),e("/ride-request/waiting")}),1e3)}),[m,u,c,o,b,f,e]),j=r.useCallback((()=>{e("/ride-request/map")}),[e]),N={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden",children:[s.jsx("div",{className:"absolute inset-0 bg-black/40"}),s.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6 px-4",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(a.button,{onClick:j,className:"p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(P,{className:"w-5 h-5"})}),s.jsxs("div",{className:"flex-1 text-center",children:[s.jsxs("h1",{className:"text-2xl font-bold text-white",children:["MOBI",s.jsx("span",{className:"text-blue-400",children:"DRIVE"})]}),s.jsx("p",{className:"text-sm text-white/70",children:"Detalhes da corrida"})]}),s.jsx("div",{className:"w-9"})]})}),s.jsx("div",{className:"flex-1 px-4 pb-8",children:s.jsxs(a.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.6,staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"space-y-6",children:[s.jsxs(a.div,{variants:N,className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Resumo da Rota"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-white font-medium",children:"Origem"}),s.jsx("p",{className:"text-white/70 text-sm",children:o?.place_name||"Sua localização atual"})]})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-white font-medium",children:"Destino"}),s.jsx("p",{className:"text-white/70 text-sm",children:c?.place_name})]})]}),s.jsxs("div",{className:"flex items-center justify-between pt-2 border-t border-white/20",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(W,{className:"w-4 h-4 text-white/60"}),s.jsx("span",{className:"text-white/70 text-sm",children:"5.0 km"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(I,{className:"w-4 h-4 text-white/60"}),s.jsx("span",{className:"text-white/70 text-sm",children:"15 min"})]})]})]})]}),s.jsxs(a.div,{variants:N,className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Escolha o Veículo"}),s.jsx("div",{className:"space-y-3",children:aa.map((e=>s.jsx("button",{onClick:()=>w(e),className:"w-full p-4 rounded-xl border transition-all "+(m?.id===e.id?"bg-blue-500/20 border-blue-500/50 text-white":"bg-white/5 border-white/20 text-white/80 hover:bg-white/10"),children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("span",{className:"text-2xl",children:e.icon}),s.jsxs("div",{className:"text-left",children:[s.jsx("p",{className:"font-medium",children:e.name}),s.jsx("p",{className:"text-sm opacity-70",children:e.description})]})]}),s.jsxs("div",{className:"text-right",children:[s.jsxs("p",{className:"font-bold",children:["R$ ",f(e).toFixed(2)]}),s.jsxs("p",{className:"text-sm opacity-70",children:[e.eta," min"]})]})]})},e.id)))})]}),s.jsxs(a.div,{variants:N,className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Forma de Pagamento"}),s.jsx("div",{className:"grid grid-cols-3 gap-3",children:ia.map((e=>s.jsx("button",{onClick:()=>v(e),className:"p-4 rounded-xl border transition-all "+(u?.id===e.id?"bg-blue-500/20 border-blue-500/50 text-white":"bg-white/5 border-white/20 text-white/80 hover:bg-white/10"),children:s.jsxs("div",{className:"text-center",children:[s.jsx("span",{className:"text-2xl block mb-2",children:e.icon}),s.jsx("p",{className:"text-sm font-medium",children:e.name})]})},e.id)))})]}),s.jsxs(a.div,{variants:N,className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Observações (opcional)"}),s.jsx("textarea",{value:b,onChange:e=>g(e.target.value),placeholder:"Instruções para o motorista...",className:"w-full p-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none",rows:3,maxLength:200}),s.jsxs("p",{className:"text-white/50 text-xs mt-2",children:[b.length,"/200 caracteres"]})]}),s.jsx(a.button,{onClick:y,disabled:!m||!u||i,className:"w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2",whileHover:{scale:m&&u&&!i?1.02:1},whileTap:{scale:m&&u&&!i?.98:1},variants:N,children:i?s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),s.jsx("span",{children:"Solicitando..."})]}):s.jsxs(s.Fragment,{children:[s.jsx(x,{className:"w-5 h-5"}),s.jsx("span",{children:"Solicitar motorista"})]})})]})})]})]})},na="pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g";de.accessToken=na;const oa=({rideId:e,userLocation:t,destination:i,onDriverUpdate:n,className:o=""})=>{const l=r.useRef(null),c=r.useRef(null),d=r.useRef(null),m=r.useRef(null),u=r.useRef(null),[x,p]=r.useState(null),[b,g]=r.useState(!1),[f,w]=r.useState(null),[v,y]=r.useState(null),[j,N]=r.useState(!1),_=r.useRef(null),k=r.useRef(null);r.useCallback(((e,t)=>{const s=(t[1]-e[1])*Math.PI/180,a=(t[0]-e[0])*Math.PI/180,i=Math.sin(s/2)*Math.sin(s/2)+Math.cos(e[1]*Math.PI/180)*Math.cos(t[1]*Math.PI/180)*Math.sin(a/2)*Math.sin(a/2);return 6371*(2*Math.atan2(Math.sqrt(i),Math.sqrt(1-i)))}),[]);const S=r.useCallback(((e,t)=>{const s=(t[0]-e[0])*Math.PI/180,a=e[1]*Math.PI/180,i=t[1]*Math.PI/180,r=Math.sin(s)*Math.cos(i),n=Math.cos(a)*Math.sin(i)-Math.sin(a)*Math.cos(i)*Math.cos(s);return(180*Math.atan2(r,n)/Math.PI+360)%360}),[]),C=r.useCallback(((e,t,s)=>{const a=e[1]+(t[1]-e[1])*s;return[e[0]+(t[0]-e[0])*s,a]}),[]),R=r.useCallback((()=>{if(l.current&&!c.current)try{c.current=new de.Map({container:l.current,style:"mapbox://styles/mapbox/dark-v11",center:t,zoom:14,attributionControl:!1,antialias:!0}),c.current.on("load",(()=>{g(!0),T()})),c.current.on("error",(e=>{w("Erro ao carregar o mapa")}))}catch(e){w("Falha ao inicializar o mapa")}}),[t]),T=r.useCallback((()=>{if(!c.current||!b)return;const e=document.createElement("div");e.className="user-marker",e.innerHTML='\n      <div class="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-lg animate-pulse"></div>\n    ',m.current=new de.Marker(e).setLngLat(t).addTo(c.current);const s=document.createElement("div");s.className="destination-marker",s.innerHTML='\n      <div class="w-6 h-6 bg-red-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">\n        <div class="w-2 h-2 bg-white rounded-full"></div>\n      </div>\n    ',u.current=new de.Marker(s).setLngLat(i).addTo(c.current),M(t,i);const a=new de.LngLatBounds;a.extend(t),a.extend(i),c.current.fitBounds(a,{padding:50,duration:1e3})}),[t,i,b]),M=r.useCallback((async(e,t)=>{if(c.current)try{const s=await fetch(`https://api.mapbox.com/directions/v5/mapbox/driving/${e[0]},${e[1]};${t[0]},${t[1]}?geometries=geojson&overview=full&access_token=${na}`),a=await s.json();if(a.routes&&a.routes.length>0){const e=a.routes[0];c.current.getSource("route")&&(c.current.removeLayer("route"),c.current.removeSource("route")),c.current.addSource("route",{type:"geojson",data:{type:"Feature",properties:{},geometry:e.geometry}}),c.current.addLayer({id:"route",type:"line",source:"route",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"#3b82f6","line-width":4,"line-opacity":.8}})}}catch(s){}}),[]),D=r.useCallback(((e,s=!0)=>{if(!c.current||!b)return;const a=[e.longitude,e.latitude];if(d.current)if(s){const t=d.current.getLngLat(),s=[t.lng,t.lat],i=a;let r=0;const n=1/(2e3/1e3*60),o=()=>{r+=n,r>=1&&(r=1);const t=C(s,i,r);d.current?.setLngLat(t);const a=d.current?.getElement();if(a){const t=a.querySelector(".driver-car-icon");t&&(t.style.transform=`rotate(${e.bearing}deg)`)}r<1&&requestAnimationFrame(o)};requestAnimationFrame(o)}else d.current.setLngLat(a);else{const t=document.createElement("div");t.className="driver-marker",t.innerHTML=`\n        <div class="relative">\n          <div class="w-10 h-10 bg-green-500 rounded-full border-3 border-white shadow-lg flex items-center justify-center">\n            <div class="driver-car-icon w-6 h-6 text-white transition-transform duration-300" style="transform: rotate(${e.bearing}deg)">\n              <svg fill="currentColor" viewBox="0 0 24 24">\n                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>\n              </svg>\n            </div>\n          </div>\n          <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>\n          <div class="absolute top-0 left-0 w-10 h-10 bg-green-400 rounded-full animate-ping opacity-20"></div>\n        </div>\n      `,d.current=new de.Marker(t).setLngLat(a).addTo(c.current)}"en_route"===e.status&&P(a,t)}),[t,b,C]),P=r.useCallback((async(e,t)=>{if(c.current&&b)try{const s=await fetch(`https://api.mapbox.com/directions/v5/mapbox/driving/${e[0]},${e[1]};${t[0]},${t[1]}?geometries=geojson&overview=full&access_token=${na}`),a=await s.json();if(a.routes&&a.routes.length>0){const t=a.routes[0];y(t),c.current.getSource("driver-route")&&(c.current.removeLayer("driver-route"),c.current.removeSource("driver-route")),c.current.addSource("driver-route",{type:"geojson",data:{type:"Feature",properties:{},geometry:t.geometry}}),c.current.addLayer({id:"driver-route",type:"line",source:"driver-route",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"#10b981","line-width":5,"line-opacity":.8}});const s=Math.ceil(t.duration/60);(t.distance/1e3).toFixed(1);if(x){const t={...x,eta:s,location:e};p(t),n?.(t)}const i=t.geometry.coordinates,r=i.reduce(((e,t)=>e.extend(t)),new de.LngLatBounds(i[0],i[0]));c.current.fitBounds(r,{padding:60,duration:1e3})}}catch(s){}}),[b,x,n]),A=r.useCallback((e=>{if(j)return;N(!0),p(e);let s=e.location,a=t,i=0,r=[];(async()=>{try{const e=await fetch(`https://api.mapbox.com/directions/v5/mapbox/driving/${s[0]},${s[1]};${a[0]},${a[1]}?geometries=geojson&overview=full&steps=true&access_token=${na}`),t=await e.json();if(t.routes&&t.routes.length>0){const e=t.routes[0].geometry.coordinates;r=e.filter(((e,t)=>t%10==0))}}catch(e){r=[s,a]}})().then((()=>{_.current=setInterval((()=>{if(i<r.length-1){i++;const a=r[i],o=S(s,a),l={...e,location:a,latitude:a[1],longitude:a[0],bearing:o,speed:25+15*Math.random(),eta:Math.max(1,Math.ceil((r.length-i)/3))};if(p(l),D(l,!0),n?.(l),s=a,i%3==0&&P(a,t),i>=r.length-1){const e={...l,status:"arrived",eta:0};p(e),n?.(e),L()}}}),3e3)}))}),[j,t,S,D,P,n]),L=r.useCallback((()=>{N(!1),_.current&&(clearInterval(_.current),_.current=null),k.current&&(clearInterval(k.current),k.current=null)}),[]);return r.useEffect((()=>{if(!e)return;const t=Rt.channel(`driver-location-${e}`).on("postgres_changes",{event:"*",schema:"public",table:"driver_locations",filter:`ride_id=eq.${e}`},(e=>{if(e.new){const t=e.new,s={id:t.driver_id,name:t.driver_name||"Motorista",latitude:t.latitude,longitude:t.longitude,bearing:t.bearing||0,speed:t.speed||0,eta:t.eta||5,vehicle:{model:t.vehicle_model||"Veículo",color:t.vehicle_color||"Branco",plate:t.vehicle_plate||"ABC-1234"},status:t.status||"en_route"};p(s),D(s),n?.(s)}})).subscribe();return()=>{t.unsubscribe()}}),[e,D,n]),r.useEffect((()=>(R(),()=>{c.current&&(c.current.remove(),c.current=null)})),[R]),r.useEffect((()=>{if(!x&&!e&&!j){const e=setTimeout((()=>{const e=.03*(Math.random()-.5),s=.03*(Math.random()-.5),a={id:"mock-driver-1",name:"João Silva",latitude:t[1]+e,longitude:t[0]+s,location:[t[0]+s,t[1]+e],bearing:S([t[0]+s,t[1]+e],t),speed:30,eta:8,vehicle:{model:"Honda Civic",color:"Branco",plate:"ABC-1234"},status:"en_route"};A(a),n?.(a)}),3e3);return()=>clearTimeout(e)}}),[x,e,t,j,S,A,n]),r.useEffect((()=>()=>{L()}),[L]),f?s.jsx("div",{className:`bg-red-500/10 border border-red-500/20 rounded-lg p-4 ${o}`,children:s.jsxs("div",{className:"flex items-center space-x-2 text-red-400",children:[s.jsx(h,{className:"w-5 h-5"}),s.jsx("span",{className:"text-sm",children:f})]})}):s.jsxs("div",{className:`relative overflow-hidden rounded-lg ${o}`,children:[s.jsx("div",{ref:l,className:"w-full h-full min-h-[300px]"}),x&&s.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"absolute bottom-4 left-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg p-3 text-white",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-10 h-10 bg-green-500 rounded-full flex items-center justify-center",children:s.jsx(E,{className:"w-5 h-5 text-white"})}),s.jsxs("div",{children:[s.jsx("p",{className:"font-semibold text-sm",children:x.name}),s.jsxs("p",{className:"text-xs text-gray-300",children:[x.vehicle.model," • ",x.vehicle.plate]})]})]}),s.jsxs("div",{className:"text-right",children:[s.jsxs("div",{className:"flex items-center space-x-1 text-blue-400",children:[s.jsx(I,{className:"w-4 h-4"}),s.jsxs("span",{className:"text-sm font-semibold",children:[x.eta," min"]})]}),s.jsx("p",{className:"text-xs text-gray-300",children:"ETA"})]})]})}),!b&&s.jsx("div",{className:"absolute inset-0 bg-gray-900/50 flex items-center justify-center",children:s.jsxs("div",{className:"text-white text-center",children:[s.jsx("div",{className:"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"}),s.jsx("p",{className:"text-sm",children:"Carregando mapa..."})]})})]})};class la{static instance;subscriptions=new Map;updateInterval=null;static getInstance(){return la.instance||(la.instance=new la),la.instance}async updateDriverLocation(e,t,s,a,i,r=!0){try{const{error:n}=await Rt.from("driver_locations").upsert({user_id:e,location:`POINT(${s} ${t})`,heading:a,speed:i,is_active:!0,is_available:r,last_ping:(new Date).toISOString(),updated_at:(new Date).toISOString()});return!n}catch(n){return!1}}async getDriverLocation(e){try{const{data:t,error:s}=await Rt.from("driver_locations").select("*").eq("user_id",e).single();return s?null:t?{id:t.id,user_id:t.user_id,latitude:t.location?.y||t.location?.coordinates?.[1]||0,longitude:t.location?.x||t.location?.coordinates?.[0]||0,heading:t.heading,speed:t.speed,is_active:t.is_active,is_available:t.is_available,last_ping:t.last_ping,updated_at:t.updated_at}:null}catch(t){return null}}subscribeToDriverLocation(e,t){const s=`driver-location-${e}`;try{this.unsubscribeFromDriverLocation(e);const a=Rt.channel(s).on("postgres_changes",{event:"*",schema:"public",table:"driver_locations",filter:`user_id=eq.${e}`},(e=>{if(e.new){const s=e.new,a={driverId:s.user_id,location:[s.location?.x||s.location?.coordinates?.[0]||0,s.location?.y||s.location?.coordinates?.[1]||0],heading:s.heading,speed:s.speed,timestamp:new Date(s.updated_at)};t(a)}})).subscribe();return this.subscriptions.set(e,a),()=>this.unsubscribeFromDriverLocation(e)}catch(a){return()=>{}}}unsubscribeFromDriverLocation(e){const t=this.subscriptions.get(e);t&&(Rt.removeChannel(t),this.subscriptions.delete(e))}async findNearbyDrivers(e,t,s=5){try{const{data:a,error:i}=await Rt.rpc("find_nearby_drivers",{user_lat:e,user_lng:t,radius_km:s});return i?[]:(a||[]).map((e=>({id:e.driver_id,user_id:e.driver_id,latitude:e.location_lat,longitude:e.location_lng,heading:e.heading,speed:e.speed,is_active:!0,is_available:e.is_available,last_ping:(new Date).toISOString(),updated_at:(new Date).toISOString()})))}catch(a){return[]}}async setDriverUnavailable(e){try{const{error:t}=await Rt.from("driver_locations").update({is_available:!1,updated_at:(new Date).toISOString()}).eq("user_id",e);return!t}catch(t){return!1}}async setDriverAvailable(e){try{const{error:t}=await Rt.from("driver_locations").update({is_available:!0,is_active:!0,updated_at:(new Date).toISOString()}).eq("user_id",e);return!t}catch(t){return!1}}cleanup(){this.subscriptions.forEach(((e,t)=>{this.unsubscribeFromDriverLocation(t)})),this.updateInterval&&(clearInterval(this.updateInterval),this.updateInterval=null)}}const ca=la.getInstance(),da=()=>{const e=st(),{user:t}=Mt();if(is(),!t)return s.jsx(ut,{to:"/login",replace:!0});const[n,o]=r.useState(null),[l,c]=r.useState(null),[d,m]=r.useState(null),[h,u]=r.useState(null),{ride:x,driver:p,isLoading:b,searchPhase:g,searchPhases:w,createRide:v}=((e={})=>{const{rideId:t,autoStart:s=!0}=e,[a,i]=r.useState(null),[n,o]=r.useState(null),[l,c]=r.useState(!1),[d,m]=r.useState(null),[h,u]=r.useState(0),x=r.useRef(null),p=r.useRef(null),b=r.useCallback((async(e,t,s,a,r,n="cash")=>{try{c(!0),m(null);const{data:{user:n},error:o}=await Rt.auth.getUser();n||Date.now();const l="ride-"+Date.now();return i({id:l,passenger_id:n?.id||"mock-user",status:"requested",origin:e,destination:t,estimated_duration:s,estimated_distance:a,estimated_price:r}),g(l,[e.longitude,e.latitude]),l}catch(o){return m("Erro inesperado ao criar corrida"),null}finally{c(!1)}}),[]),g=r.useCallback((async(e,t)=>{const s=["Procurando motoristas próximos...","Analisando disponibilidade...","Motorista encontrado!","Confirmando corrida..."];let a=0;const i=setInterval((()=>{u(a),a++,a>=s.length&&(clearInterval(i),f(e,t))}),1500);return()=>clearInterval(i)}),[]),f=r.useCallback((async(e,t)=>{try{const s=[t[0]+.02*(Math.random()-.5),t[1]+.02*(Math.random()-.5)],a={id:"mock-driver-"+Date.now(),name:"João Silva",phone:"+55 11 99999-9999",rating:4.8,vehicle:{model:"Honda Civic",color:"Branco",plate:"ABC-1234"},location:s,bearing:Math.floor(360*Math.random()),speed:30+20*Math.random(),eta:3+Math.floor(7*Math.random()),status:"en_route"};o(a);const{error:i}=await Rt.from("rides").update({status:"assigned",driver_id:a.id,assigned_at:(new Date).toISOString()}).eq("id",e);w(a,e)}catch(s){}}),[]),w=r.useCallback(((e,t)=>()=>{}),[]),v=r.useCallback((async e=>{try{c(!0),m(null);const{data:t,error:s}=await Rt.from("rides").select("\n          *,\n          drivers (\n            id,\n            name,\n            phone,\n            rating,\n            vehicle_model,\n            vehicle_color,\n            vehicle_plate\n          )\n        ").eq("id",e).single();if(s)return void m("Erro ao carregar dados da corrida");i(t),t.driver_id&&y(t.driver_id,e)}catch(t){m("Erro inesperado ao carregar corrida")}finally{c(!1)}}),[]),y=r.useCallback((async(e,t)=>{try{const{data:s,error:a}=await Rt.from("driver_locations").select("*").eq("driver_id",e).eq("ride_id",t).order("created_at",{ascending:!1}).limit(1).single();if(a)return;o((e=>e?{...e,location:[s.longitude,s.latitude],bearing:s.bearing||0,speed:s.speed||0,eta:s.eta||5,status:s.status||"en_route"}:null))}catch(s){}}),[]),j=r.useCallback((e=>(x.current=Rt.channel(`ride-${e}`).on("postgres_changes",{event:"*",schema:"public",table:"rides",filter:`id=eq.${e}`},(e=>{e.new&&i(e.new)})).subscribe(),()=>{x.current&&(x.current.unsubscribe(),x.current=null)})),[]),N=r.useCallback((e=>{const t=ca.subscribeToDriverLocation(e,(e=>{o((t=>t?{...t,location:e.location,bearing:e.heading||0,speed:e.speed||0,eta:Math.max(Math.round(2*(t.location?_(t.location,e.location):1)),2),status:"en_route"}:null))}));return p.current={unsubscribe:t},t}),[]),_=r.useCallback(((e,t)=>{const s=(t[1]-e[1])*Math.PI/180,a=(t[0]-e[0])*Math.PI/180,i=Math.sin(s/2)*Math.sin(s/2)+Math.cos(e[1]*Math.PI/180)*Math.cos(t[1]*Math.PI/180)*Math.sin(a/2)*Math.sin(a/2);return 2*Math.atan2(Math.sqrt(i),Math.sqrt(1-i))*6371}),[]),k=r.useCallback((async(e="Cancelled by passenger")=>{if(!a)return!1;try{c(!0);const{error:t}=await Rt.from("rides").update({status:"cancelled",cancelled_at:(new Date).toISOString(),cancellation_reason:e}).eq("id",a.id);return!t||(m("Erro ao cancelar corrida"),!1)}catch(t){return m("Erro inesperado ao cancelar corrida"),!1}finally{c(!1)}}),[a]);return r.useEffect((()=>{t&&s&&v(t)}),[t,s,v]),r.useEffect((()=>{if(a?.id){const e=j(a.id),t=N(a.id);return()=>{e(),t()}}}),[a?.id,j,N]),r.useEffect((()=>()=>{x.current&&x.current.unsubscribe(),p.current&&p.current.unsubscribe()}),[]),{ride:a,driver:n,isLoading:l,error:d,searchPhase:h,createRide:b,loadRide:v,cancelRide:k,searchPhases:["Procurando motoristas próximos...","Analisando disponibilidade...","Motorista encontrado!","Confirmando corrida..."]}})({rideId:l||void 0}),[y,j]=r.useState(null),[N,_]=r.useState(5);r.useEffect((()=>{const e=sessionStorage.getItem("tripData"),t=sessionStorage.getItem("rideOrigin"),s=sessionStorage.getItem("rideDestination");if(e){const t=JSON.parse(e);o(t),c(t.rideId||null)}else{o({vehicle:{name:"Econômico"},payment:{name:"Dinheiro"},price:15.5,estimatedDuration:600,estimatedDistance:5e3,estimatedPrice:15.5})}if(t){const e=JSON.parse(t);m(e.center)}else{m([-46.6333,-23.5505])}if(s){const e=JSON.parse(s);u(e.center)}else{u([-46.6234,-23.5456])}}),[e]),r.useEffect((()=>{p&&(j({id:p.id,name:p.name,rating:p.rating,vehicle:p.vehicle,photo:"👨‍💼",eta:p.eta,phone:p.phone}),_(p.eta))}),[p]),r.useEffect((()=>{if(n&&d&&h&&!l&&!x){const e={latitude:d[1],longitude:d[0],address:n.origin?.place_name||"Origem"},t={latitude:h[1],longitude:h[0],address:n.destination?.place_name||"Destino"};v(e,t,n.estimatedDuration||600,n.estimatedDistance||5e3,n.estimatedPrice||15,"cash").then((e=>{e&&c(e)}))}}),[n,d,h,l,x,v]),r.useEffect((()=>{if(y&&N>0){const t=setInterval((()=>{_((s=>s<=1?(clearInterval(t),e("/ride-request/riding"),0):s-1))}),6e4);return()=>clearInterval(t)}}),[y,N,e]);const k=r.useCallback((()=>{e("/ride-request/details")}),[e]),S=r.useCallback((()=>{y?.phone&&window.open(`tel:${y.phone}`)}),[y]),R=r.useCallback((()=>{y?.phone&&window.open(`sms:${y.phone}`)}),[y]),T=r.useCallback((()=>{alert("Emergência ativada! Contatos de emergência foram notificados.")}),[]),M=r.useCallback((()=>{const t={...n,driver:y,startTime:(new Date).toISOString()};sessionStorage.setItem("rideData",JSON.stringify(t)),e("/ride-request/riding")}),[n,y,e]),D={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden",children:[s.jsx("div",{className:"absolute inset-0 bg-black/40"}),s.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6 px-4",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(a.button,{onClick:k,className:"p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(P,{className:"w-5 h-5"})}),s.jsxs("div",{className:"flex-1 text-center",children:[s.jsxs("h1",{className:"text-2xl font-bold text-white",children:["MOBI",s.jsx("span",{className:"text-blue-400",children:"DRIVE"})]}),s.jsx("p",{className:"text-sm text-white/70",children:b?"Procurando motorista":"Motorista a caminho"})]}),s.jsx(a.button,{onClick:T,className:"p-2 rounded-xl bg-red-500/20 backdrop-blur-sm border border-red-500/30 text-red-400",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(A,{className:"w-5 h-5"})})]})}),s.jsx("div",{className:"flex-1 px-4 pb-8",children:s.jsx(a.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.6,staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"space-y-6",children:s.jsx(i,{mode:"wait",children:b?s.jsx(a.div,{variants:D,initial:"hidden",animate:"visible",exit:"hidden",className:"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 shadow-2xl text-center",children:s.jsxs("div",{className:"mb-6",children:[s.jsx(a.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-20 h-20 mx-auto mb-4",children:s.jsx(E,{className:"w-full h-full text-blue-400"})}),s.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:w[g]}),s.jsx("div",{className:"flex justify-center space-x-1 mb-4",children:[0,1,2].map((e=>s.jsx(a.div,{animate:{scale:[1,1.2,1],opacity:[.5,1,.5]},transition:{duration:1,repeat:1/0,delay:.2*e},className:"w-2 h-2 bg-blue-400 rounded-full"},e)))}),s.jsx("p",{className:"text-white/70",children:"Estamos encontrando o melhor motorista para você"})]})},"searching"):s.jsxs(a.div,{variants:D,initial:"hidden",animate:"visible",className:"space-y-6",children:[s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-2xl",children:y?.photo}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h3",{className:"text-xl font-bold text-white",children:y?.name}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(U,{className:"w-4 h-4 text-yellow-400 fill-current"}),s.jsx("span",{className:"text-white/70",children:y?.rating})]}),s.jsxs("p",{className:"text-white/60 text-sm",children:[y?.vehicle.color," ",y?.vehicle.model]}),s.jsx("p",{className:"text-white/60 text-sm font-mono",children:y?.vehicle.plate})]})]}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsxs("button",{onClick:S,className:"flex-1 bg-green-500/20 border border-green-500/30 text-green-400 py-3 px-4 rounded-xl flex items-center justify-center space-x-2 hover:bg-green-500/30 transition-colors",children:[s.jsx(C,{className:"w-4 h-4"}),s.jsx("span",{children:"Ligar"})]}),s.jsxs("button",{onClick:R,className:"flex-1 bg-blue-500/20 border border-blue-500/30 text-blue-400 py-3 px-4 rounded-xl flex items-center justify-center space-x-2 hover:bg-blue-500/30 transition-colors",children:[s.jsx(B,{className:"w-4 h-4"}),s.jsx("span",{children:"Mensagem"})]})]})]}),s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl text-center",children:[s.jsx(I,{className:"w-12 h-12 text-blue-400 mx-auto mb-4"}),s.jsxs("h3",{className:"text-2xl font-bold text-white mb-2",children:[N," minutos"]}),s.jsx("p",{className:"text-white/70",children:"Tempo estimado de chegada"})]}),s.jsx("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl overflow-hidden",children:d&&h?s.jsx(oa,{rideId:l||void 0,userLocation:d,destination:h,onDriverUpdate:e=>{e&&(j({id:e.id,name:e.name,rating:4.8,vehicle:e.vehicle,photo:"👨‍💼",eta:e.eta,phone:"+55 11 99999-9999"}),_(e.eta))},className:"h-64"}):s.jsx("div",{className:"h-64 bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center relative",children:s.jsxs("div",{className:"text-center text-white/70",children:[s.jsx(f,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),s.jsx("p",{className:"text-sm",children:"Carregando localização..."})]})})}),n&&s.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Resumo da Corrida"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-white/70",children:"Veículo:"}),s.jsx("span",{className:"text-white",children:n.vehicle?.name})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-white/70",children:"Pagamento:"}),s.jsx("span",{className:"text-white",children:n.payment?.name})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-white/70",children:"Valor estimado:"}),s.jsxs("span",{className:"text-white font-bold",children:["R$ ",n.price?.toFixed(2)]})]})]})]}),N<=1&&s.jsx(a.button,{onClick:M,className:"w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200",whileHover:{scale:1.02},whileTap:{scale:.98},initial:{opacity:0,y:20},animate:{opacity:1,y:0},children:"Motorista chegou - Iniciar corrida"})]},"driver-info")})})})]})]})},ma=()=>{const e=st(),{user:t}=Mt();if(is(),!t)return s.jsx(ut,{to:"/login",replace:!0});const[i,n]=r.useState(null),[o,l]=r.useState(0),[c,d]=r.useState(0),[m,h]=r.useState(0),[u,p]=r.useState(!1);r.useEffect((()=>{const t=sessionStorage.getItem("rideData");t?n(JSON.parse(t)):e("/ride-request/map")}),[e]),r.useEffect((()=>{const e=setInterval((()=>{l((t=>{const s=t+1;return h(s/100*5),d(Math.floor(s/100*15*60)),s>=90&&p(!0),s>=100?(clearInterval(e),100):s}))}),3e3);return()=>clearInterval(e)}),[]);const b=r.useCallback((()=>{i?.driver?.phone&&window.open(`tel:${i.driver.phone}`)}),[i]),g=r.useCallback((()=>{i?.driver?.phone&&window.open(`sms:${i.driver.phone}`)}),[i]),w=r.useCallback((()=>{alert("Emergência ativada! Contatos de emergência foram notificados.")}),[]),v=r.useCallback((()=>{if(navigator.share)navigator.share({title:"Minha corrida MobiDrive",text:`Estou em uma corrida para ${i?.destination?.place_name}`,url:window.location.href});else{const e=`Estou em uma corrida MobiDrive para ${i?.destination?.place_name}`;navigator.clipboard.writeText(e),alert("Link da corrida copiado!")}}),[i]),y=r.useCallback((()=>{const t={...i,endTime:(new Date).toISOString(),finalDistance:5,finalDuration:15,finalPrice:i?.price||0};sessionStorage.setItem("completedRide",JSON.stringify(t)),e("/ride-request/rating")}),[i,e]),j={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden",children:[s.jsx("div",{className:"absolute inset-0 bg-black/40"}),s.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6 px-4",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex-1 text-center",children:[s.jsxs("h1",{className:"text-2xl font-bold text-white",children:["MOBI",s.jsx("span",{className:"text-blue-400",children:"DRIVE"})]}),s.jsx("p",{className:"text-sm text-white/70",children:"Corrida em andamento"})]}),s.jsx(a.button,{onClick:w,className:"p-2 rounded-xl bg-red-500/20 backdrop-blur-sm border border-red-500/30 text-red-400",whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(A,{className:"w-5 h-5"})})]})}),s.jsx("div",{className:"flex-1 px-4 pb-8",children:s.jsxs(a.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.6,staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"space-y-6",children:[i?.driver&&s.jsx(a.div,{variants:j,className:"bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20 shadow-2xl",children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-lg",children:i.driver.photo}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h3",{className:"text-lg font-bold text-white",children:i.driver.name}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(U,{className:"w-3 h-3 text-yellow-400 fill-current"}),s.jsx("span",{className:"text-white/70 text-sm",children:i.driver.rating})]})]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:b,className:"p-2 bg-green-500/20 border border-green-500/30 text-green-400 rounded-lg hover:bg-green-500/30 transition-colors",children:s.jsx(C,{className:"w-4 h-4"})}),s.jsx("button",{onClick:g,className:"p-2 bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors",children:s.jsx(B,{className:"w-4 h-4"})})]})]})}),s.jsx(a.div,{variants:j,className:"bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl overflow-hidden",children:s.jsxs("div",{className:"h-64 bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center relative",children:[s.jsxs("div",{className:"text-center text-white/70",children:[s.jsx(H,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),s.jsx("p",{className:"text-sm",children:"Rota em tempo real"})]}),s.jsxs("div",{className:"absolute inset-4",children:[s.jsx("svg",{className:"w-full h-full",children:s.jsx("path",{d:"M 20 200 Q 100 50 200 100 T 300 180",stroke:"#3b82f6",strokeWidth:"3",fill:"none",strokeDasharray:"5,5"})}),s.jsx(a.div,{animate:{x:2.8*o+"%",y:20*Math.sin(.1*o)+160+"px"},transition:{duration:.5},className:"absolute w-4 h-4 bg-blue-500 rounded-full shadow-lg",style:{left:"20px",top:"0px"}}),s.jsx("div",{className:"absolute right-4 bottom-8 w-4 h-4 bg-red-500 rounded-full shadow-lg"})]})]})}),s.jsxs(a.div,{variants:j,className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Progresso da Viagem"}),s.jsx("div",{className:"w-full bg-white/20 rounded-full h-2 mb-4",children:s.jsx(a.div,{className:"bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full",initial:{width:0},animate:{width:`${o}%`},transition:{duration:.5}})}),s.jsxs("div",{className:"grid grid-cols-3 gap-4 text-center",children:[s.jsxs("div",{children:[s.jsx(W,{className:"w-5 h-5 text-blue-400 mx-auto mb-1"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Distância"}),s.jsxs("p",{className:"text-white font-bold",children:[m.toFixed(1)," km"]})]}),s.jsxs("div",{children:[s.jsx(I,{className:"w-5 h-5 text-blue-400 mx-auto mb-1"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Tempo"}),s.jsx("p",{className:"text-white font-bold",children:(N=c,`${Math.floor(N/60)}:${(N%60).toString().padStart(2,"0")}`)})]}),s.jsxs("div",{children:[s.jsx(H,{className:"w-5 h-5 text-blue-400 mx-auto mb-1"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Progresso"}),s.jsxs("p",{className:"text-white font-bold",children:[o,"%"]})]})]})]}),i?.destination&&s.jsx(a.div,{variants:j,className:"bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20 shadow-2xl",children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-10 h-10 bg-red-500 rounded-full flex items-center justify-center",children:s.jsx(f,{className:"w-5 h-5 text-white"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-white/70 text-sm",children:"Destino"}),s.jsx("p",{className:"text-white font-medium",children:i.destination.place_name})]})]})}),s.jsxs(a.div,{variants:j,className:"flex space-x-3",children:[s.jsxs("button",{onClick:v,className:"flex-1 bg-blue-500/20 border border-blue-500/30 text-blue-400 py-3 px-4 rounded-xl flex items-center justify-center space-x-2 hover:bg-blue-500/30 transition-colors",children:[s.jsx(J,{className:"w-4 h-4"}),s.jsx("span",{children:"Compartilhar"})]}),u&&s.jsxs(a.button,{onClick:y,className:"flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white py-3 px-4 rounded-xl flex items-center justify-center space-x-2 transition-all",whileHover:{scale:1.02},whileTap:{scale:.98},initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},children:[s.jsx(x,{className:"w-4 h-4"}),s.jsx("span",{children:"Finalizar"})]})]}),i&&s.jsxs(a.div,{variants:j,className:"bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-3",children:"Resumo"}),s.jsxs("div",{className:"space-y-2 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-white/70",children:"Veículo:"}),s.jsx("span",{className:"text-white",children:i.vehicle?.name})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-white/70",children:"Pagamento:"}),s.jsx("span",{className:"text-white",children:i.payment?.name})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-white/70",children:"Valor:"}),s.jsxs("span",{className:"text-white font-bold",children:["R$ ",i.price?.toFixed(2)]})]})]})]})]})})]})]});var N},ha=()=>{const e=st(),{user:t}=Mt();if(is(),!t)return s.jsx(ut,{to:"/login",replace:!0});const[i,n]=r.useState(null),[o,l]=r.useState(0),[c,d]=r.useState(""),[m,h]=r.useState(!1);r.useEffect((()=>{const t=sessionStorage.getItem("completedRide");t?n(JSON.parse(t)):e("/dashboard")}),[e]);const u=r.useCallback((e=>{l(e)}),[]),p=r.useCallback((async()=>{0!==o&&(h(!0),setTimeout((()=>{sessionStorage.removeItem("rideDestination"),sessionStorage.removeItem("rideOrigin"),sessionStorage.removeItem("tripData"),sessionStorage.removeItem("rideData"),sessionStorage.removeItem("completedRide"),h(!1),e("/dashboard")}),1500))}),[o,e]),b={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}},g={empty:{scale:1,color:"#ffffff40"},filled:{scale:1.1,color:"#fbbf24"},hover:{scale:1.2}};return s.jsxs("div",{className:"no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden",children:[s.jsx("div",{className:"absolute inset-0 bg-black/40"}),s.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"}),s.jsxs("div",{className:"relative z-10 flex flex-col h-full min-h-screen",children:[s.jsx(a.div,{className:"pt-8 pb-6 px-4",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},children:s.jsxs("div",{className:"text-center",children:[s.jsxs("h1",{className:"text-2xl font-bold text-white",children:["MOBI",s.jsx("span",{className:"text-blue-400",children:"DRIVE"})]}),s.jsx("p",{className:"text-sm text-white/70",children:"Avalie sua corrida"})]})}),s.jsx("div",{className:"flex-1 px-4 pb-8",children:s.jsxs(a.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.6,staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"space-y-6",children:[s.jsxs(a.div,{variants:b,className:"text-center",children:[s.jsx(a.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(x,{className:"w-10 h-10 text-white"})}),s.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"Corrida finalizada!"}),s.jsx("p",{className:"text-white/70",children:"Obrigado por usar o MobiDrive"})]}),i&&s.jsxs(a.div,{variants:b,className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Resumo da Viagem"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),s.jsx("span",{className:"text-white/70 text-sm",children:"Origem"})]}),s.jsx("p",{className:"text-white text-sm ml-6",children:i.origin?.place_name||"Sua localização"}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full"}),s.jsx("span",{className:"text-white/70 text-sm",children:"Destino"})]}),s.jsx("p",{className:"text-white text-sm ml-6",children:i.destination?.place_name})]}),s.jsxs("div",{className:"grid grid-cols-3 gap-4 pt-4 border-t border-white/20",children:[s.jsxs("div",{className:"text-center",children:[s.jsx(W,{className:"w-5 h-5 text-blue-400 mx-auto mb-1"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Distância"}),s.jsxs("p",{className:"text-white font-bold",children:[i.finalDistance," km"]})]}),s.jsxs("div",{className:"text-center",children:[s.jsx(I,{className:"w-5 h-5 text-blue-400 mx-auto mb-1"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Tempo"}),s.jsxs("p",{className:"text-white font-bold",children:[i.finalDuration," min"]})]}),s.jsxs("div",{className:"text-center",children:[s.jsx(Q,{className:"w-5 h-5 text-blue-400 mx-auto mb-1"}),s.jsx("p",{className:"text-white/70 text-xs",children:"Valor"}),s.jsxs("p",{className:"text-white font-bold",children:["R$ ",i.finalPrice?.toFixed(2)]})]})]})]})]}),i?.driver&&s.jsxs(a.div,{variants:b,className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-2xl",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Avalie o motorista"}),s.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-2xl",children:i.driver.photo}),s.jsxs("div",{children:[s.jsx("h4",{className:"text-xl font-bold text-white",children:i.driver.name}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(U,{className:"w-4 h-4 text-yellow-400 fill-current"}),s.jsx("span",{className:"text-white/70",children:i.driver.rating})]}),s.jsxs("p",{className:"text-white/60 text-sm",children:[i.driver.vehicle.color," ",i.driver.vehicle.model]})]})]}),s.jsxs("div",{className:"text-center mb-6",children:[s.jsx("p",{className:"text-white mb-4",children:"Como foi sua experiência?"}),s.jsx("div",{className:"flex justify-center space-x-2",children:[1,2,3,4,5].map((e=>s.jsx(a.button,{onClick:()=>u(e),className:"focus:outline-none",whileHover:"hover",whileTap:{scale:.9},children:s.jsx(a.div,{variants:g,animate:o>=e?"filled":"empty",transition:{duration:.2},children:s.jsx(U,{className:"w-8 h-8 "+(o>=e?"text-yellow-400 fill-current":"text-white/40")})})},e)))}),o>0&&s.jsxs(a.p,{initial:{opacity:0},animate:{opacity:1},className:"text-white/70 text-sm mt-2",children:[1===o&&"Muito ruim",2===o&&"Ruim",3===o&&"Regular",4===o&&"Bom",5===o&&"Excelente"]})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-white mb-2",children:"Comentário (opcional)"}),s.jsx("textarea",{value:c,onChange:e=>d(e.target.value),placeholder:"Conte-nos sobre sua experiência...",className:"w-full p-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none",rows:3,maxLength:200}),s.jsxs("p",{className:"text-white/50 text-xs mt-1",children:[c.length,"/200 caracteres"]})]})]}),s.jsx(a.button,{onClick:p,disabled:0===o||m,className:"w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2",whileHover:{scale:o>0&&!m?1.02:1},whileTap:{scale:o>0&&!m?.98:1},variants:b,children:m?s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),s.jsx("span",{children:"Enviando avaliação..."})]}):s.jsxs(s.Fragment,{children:[s.jsx(z,{className:"w-5 h-5"}),s.jsx("span",{children:"Finalizar avaliação"})]})}),s.jsx(a.button,{onClick:()=>e("/dashboard"),className:"w-full text-white/60 hover:text-white transition-colors py-2",variants:b,children:"Pular avaliação"})]})})]})]})},ua=()=>{const{notifications:e,removeNotification:t}=(()=>{const e=r.useContext(Dt);if(void 0===e)throw new Error("useNotifications must be used within a NotificationProvider");return e})();return s.jsxs(s.Fragment,{children:[s.jsx(yt,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:s.jsxs(bt,{children:[s.jsx(xt,{path:"/",element:s.jsx(ts,{children:s.jsx(rs,{})})}),s.jsx(xt,{path:"/login",element:s.jsx(ts,{children:s.jsx(rs,{})})}),s.jsx(xt,{path:"/register",element:s.jsx(ts,{children:s.jsx(ms,{})})}),s.jsx(xt,{path:"/dashboard",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(hs,{})})})}),s.jsx(xt,{path:"/request-ride",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(Es,{})})})}),s.jsx(xt,{path:"/ride-request/map",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(sa,{})})})}),s.jsx(xt,{path:"/ride-request/details",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(ra,{})})})}),s.jsx(xt,{path:"/ride-request/waiting",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(da,{})})})}),s.jsx(xt,{path:"/ride-request/riding",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(ma,{})})})}),s.jsx(xt,{path:"/ride-request/rating",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(ha,{})})})}),s.jsx(xt,{path:"/ride-tracking/:rideId",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(Ls,{})})})}),s.jsx(xt,{path:"/setup",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx($s,{})})})}),s.jsx(xt,{path:"/rewards",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(Os,{})})})}),s.jsx(xt,{path:"/premium",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(Fs,{})})})}),s.jsx(xt,{path:"/system-tests",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(Ks,{})})})}),s.jsx(xt,{path:"/comprehensive-validation",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(Ys,{})})})}),s.jsx(xt,{path:"/free-ads",element:s.jsx(Vt,{children:s.jsx(ts,{children:s.jsx(ea,{})})})}),s.jsx(xt,{path:"*",element:s.jsx(ts,{children:s.jsx(rs,{})})})]})}),s.jsx(qt,{notifications:e,onClose:t}),s.jsx(Wt,{})]})};function xa(){return s.jsx(At,{children:s.jsx(Tt,{children:s.jsx(Pt,{children:s.jsx(ua,{})})})})}"undefined"!=typeof window&&(window.MAPBOX_ACCESS_TOKEN="pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g");l.createRoot(document.getElementById("root")).render(s.jsx(o.StrictMode,{children:s.jsx(xa,{})})),(async()=>{try{const{analyticsService:e}=await t((async()=>{const{analyticsService:e}=await Promise.resolve().then((()=>Ht));return{analyticsService:e}}),void 0),{cacheService:s}=await t((async()=>{const{cacheService:e}=await Promise.resolve().then((()=>Bt));return{cacheService:e}}),void 0);setTimeout((()=>{e.trackPageView("app_start",performance.now()),s.preloadCommonData()}),100)}catch(e){}})();
