/**
 * 📱 APPODEAL NATIVE WRAPPER - MOBIDRIVE
 * Wrapper para SDK nativo que será usado quando o app for publicado nas lojas
 */

import { getAppodealConfig, validateAppodealConfig, MOBIDRIVE_AD_CONFIG } from '../config/appodeal.config';

// Tipos para SDK nativo
declare global {
  interface Window {
    Appodeal?: {
      // Constantes de tipos de anúncios
      REWARDED_VIDEO: number;
      INTERSTITIAL: number;
      BANNER: number;
      NATIVE: number;
      
      // Métodos principais
      initialize: (appKey: string, adTypes: number) => void;
      show: (adType: number, placement?: string) => boolean;
      isLoaded: (adType: number) => boolean;
      cache: (adType: number) => void;
      hide: (adType: number) => void;
      
      // Configurações
      setTesting: (enabled: boolean) => void;
      setLogLevel: (level: string) => void;
      setUserId: (userId: string) => void;
      
      // Callbacks
      setRewardedVideoCallbacks: (callbacks: AppodealRewardedVideoCallbacks) => void;
      setInterstitialCallbacks: (callbacks: AppodealInterstitialCallbacks) => void;
      setBannerCallbacks: (callbacks: AppodealBannerCallbacks) => void;
      
      // Compliance
      updateGDPRUserConsent: (consent: boolean) => void;
      updateCCPAUserConsent: (consent: boolean) => void;
      
      // Configurações avançadas
      setAutoCache: (adType: number, enabled: boolean) => void;
      setSmartBanners: (enabled: boolean) => void;
      setTabletBanners: (enabled: boolean) => void;
    };
  }
}

interface AppodealRewardedVideoCallbacks {
  onLoaded?: () => void;
  onFailedToLoad?: () => void;
  onShown?: () => void;
  onShowFailed?: () => void;
  onRewarded?: (amount: number, currency: string) => void;
  onClosed?: (finished: boolean) => void;
  onClicked?: () => void;
}

interface AppodealInterstitialCallbacks {
  onLoaded?: () => void;
  onFailedToLoad?: () => void;
  onShown?: () => void;
  onShowFailed?: () => void;
  onClosed?: () => void;
  onClicked?: () => void;
}

interface AppodealBannerCallbacks {
  onLoaded?: () => void;
  onFailedToLoad?: () => void;
  onShown?: () => void;
  onClicked?: () => void;
}

export class AppodealNativeWrapper {
  private static instance: AppodealNativeWrapper;
  private isInitialized = false;
  private currentEnvironment: 'web' | 'ios' | 'android' = 'web';
  private callbacks: Map<string, Function> = new Map();

  static getInstance(): AppodealNativeWrapper {
    if (!AppodealNativeWrapper.instance) {
      AppodealNativeWrapper.instance = new AppodealNativeWrapper();
    }
    return AppodealNativeWrapper.instance;
  }

  // Detectar ambiente atual
  detectEnvironment(): 'web' | 'ios' | 'android' {
    if (typeof window === 'undefined') return 'web';
    
    const userAgent = window.navigator.userAgent;
    
    if (/iPad|iPhone|iPod/.test(userAgent)) {
      return 'ios';
    } else if (/Android/.test(userAgent)) {
      return 'android';
    } else {
      return 'web';
    }
  }

  // Verificar se SDK nativo está disponível
  isNativeSDKAvailable(): boolean {
    return typeof window !== 'undefined' && !!window.Appodeal;
  }

  // Inicializar Appodeal (nativo ou simulação)
  async initialize(userId?: string): Promise<boolean> {
    try {
      this.currentEnvironment = this.detectEnvironment();
      const config = getAppodealConfig(this.currentEnvironment);
      
      if (!validateAppodealConfig(config)) {
        console.warn('⚠️ Configuração Appodeal inválida, usando simulação');
        return this.initializeSimulation();
      }

      if (this.isNativeSDKAvailable()) {
        return this.initializeNativeSDK(config, userId);
      } else {
        console.log('📱 SDK nativo não disponível, usando simulação');
        return this.initializeSimulation();
      }
    } catch (error) {
      console.error('❌ Erro ao inicializar Appodeal:', error);
      return false;
    }
  }

  // Inicializar SDK nativo
  private initializeNativeSDK(config: any, userId?: string): boolean {
    try {
      const { Appodeal } = window;
      if (!Appodeal) return false;

      console.log('🚀 Inicializando Appodeal SDK nativo...');

      // Configurar modo de teste
      Appodeal.setTesting(config.testMode);
      
      // Configurar logs
      Appodeal.setLogLevel(config.testMode ? 'debug' : 'none');
      
      // Configurar usuário
      if (userId) {
        Appodeal.setUserId(userId);
      }

      // Configurar compliance
      this.setupCompliance();

      // Configurar callbacks
      this.setupCallbacks();

      // Configurações avançadas
      if (config.mediationSettings.autoCache) {
        Appodeal.setAutoCache(Appodeal.REWARDED_VIDEO, true);
        Appodeal.setAutoCache(Appodeal.INTERSTITIAL, true);
      }

      if (config.mediationSettings.smartBanners) {
        Appodeal.setSmartBanners(true);
      }

      if (config.mediationSettings.tabletBanners) {
        Appodeal.setTabletBanners(true);
      }

      // Inicializar com tipos de anúncios
      const adTypes = Appodeal.REWARDED_VIDEO | Appodeal.INTERSTITIAL | Appodeal.BANNER;
      Appodeal.initialize(config.appKey, adTypes);

      this.isInitialized = true;
      console.log('✅ Appodeal SDK nativo inicializado com sucesso');
      return true;

    } catch (error) {
      console.error('❌ Erro ao inicializar SDK nativo:', error);
      return false;
    }
  }

  // Inicializar simulação (ambiente web)
  private initializeSimulation(): boolean {
    console.log('🌐 Inicializando simulação Appodeal para web...');
    this.isInitialized = true;
    return true;
  }

  // Configurar compliance
  private setupCompliance(): void {
    const { Appodeal } = window;
    if (!Appodeal) return;

    // GDPR (Europa)
    const gdprConsent = localStorage.getItem('gdpr_consent') === 'true';
    Appodeal.updateGDPRUserConsent(gdprConsent);

    // CCPA (Califórnia)
    const ccpaConsent = localStorage.getItem('ccpa_consent') !== 'false';
    Appodeal.updateCCPAUserConsent(ccpaConsent);
  }

  // Configurar callbacks
  private setupCallbacks(): void {
    const { Appodeal } = window;
    if (!Appodeal) return;

    // Callbacks para Rewarded Video
    Appodeal.setRewardedVideoCallbacks({
      onLoaded: () => {
        console.log('📺 Rewarded video carregado');
        this.triggerCallback('rewarded_loaded');
      },
      onFailedToLoad: () => {
        console.log('❌ Falha ao carregar rewarded video');
        this.triggerCallback('rewarded_failed');
      },
      onShown: () => {
        console.log('▶️ Rewarded video exibido');
        this.triggerCallback('rewarded_shown');
      },
      onRewarded: (amount: number, currency: string) => {
        console.log(`💰 Recompensa recebida: ${amount} ${currency}`);
        this.triggerCallback('rewarded_earned', { amount, currency });
      },
      onClosed: (finished: boolean) => {
        console.log(`🔚 Rewarded video fechado (completo: ${finished})`);
        this.triggerCallback('rewarded_closed', { finished });
      }
    });

    // Callbacks para Interstitial
    Appodeal.setInterstitialCallbacks({
      onLoaded: () => {
        console.log('📱 Interstitial carregado');
        this.triggerCallback('interstitial_loaded');
      },
      onShown: () => {
        console.log('▶️ Interstitial exibido');
        this.triggerCallback('interstitial_shown');
      },
      onClosed: () => {
        console.log('🔚 Interstitial fechado');
        this.triggerCallback('interstitial_closed');
      }
    });
  }

  // Exibir anúncio recompensado
  showRewardedVideo(placement?: string): Promise<{ success: boolean; reward?: any }> {
    return new Promise((resolve) => {
      if (!this.isInitialized) {
        resolve({ success: false });
        return;
      }

      if (this.isNativeSDKAvailable()) {
        const { Appodeal } = window;
        
        // Configurar callback temporário para esta exibição
        const originalCallback = this.callbacks.get('rewarded_earned');
        
        this.setCallback('rewarded_earned', (reward: any) => {
          resolve({ success: true, reward });
          // Restaurar callback original
          if (originalCallback) {
            this.setCallback('rewarded_earned', originalCallback);
          }
        });

        const shown = Appodeal!.show(Appodeal!.REWARDED_VIDEO, placement);
        
        if (!shown) {
          resolve({ success: false });
        }
      } else {
        // Simulação para web
        setTimeout(() => {
          resolve({ 
            success: true, 
            reward: { 
              amount: Math.floor(Math.random() * 50) + 25, // 25-75 centavos
              currency: 'BRL' 
            }
          });
        }, 1000);
      }
    });
  }

  // Verificar se anúncio está carregado
  isRewardedVideoLoaded(): boolean {
    if (this.isNativeSDKAvailable()) {
      const { Appodeal } = window;
      return Appodeal!.isLoaded(Appodeal!.REWARDED_VIDEO);
    } else {
      // Simulação sempre retorna true
      return true;
    }
  }

  // Cache de anúncios
  cacheRewardedVideo(): void {
    if (this.isNativeSDKAvailable()) {
      const { Appodeal } = window;
      Appodeal!.cache(Appodeal!.REWARDED_VIDEO);
    }
  }

  // Configurar callback
  setCallback(event: string, callback: Function): void {
    this.callbacks.set(event, callback);
  }

  // Disparar callback
  private triggerCallback(event: string, data?: any): void {
    const callback = this.callbacks.get(event);
    if (callback) {
      callback(data);
    }
  }

  // Obter estatísticas
  getStats(): any {
    return {
      isInitialized: this.isInitialized,
      environment: this.currentEnvironment,
      nativeSDKAvailable: this.isNativeSDKAvailable(),
      rewardedVideoLoaded: this.isRewardedVideoLoaded()
    };
  }

  // Limpar recursos
  destroy(): void {
    this.callbacks.clear();
    this.isInitialized = false;
  }
}

export const appodealNativeWrapper = AppodealNativeWrapper.getInstance();
