import React, { useEffect, useState } from 'react'
import { mapboxService } from '../services/MapboxService'

export const MapboxTestComponent: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const runTests = async () => {
    setIsLoading(true)
    const results = []

    try {
      // Test 1: Search Places
      console.log('🧪 Testing search places...')
      const searchResults = await mapboxService.searchPlaces('Shopping Ibirapuera São Paulo')
      results.push({
        test: 'Search Places',
        status: searchResults.length > 0 ? 'SUCCESS' : 'FAILED',
        data: searchResults.slice(0, 2)
      })

      // Test 2: Get Directions
      console.log('🧪 Testing directions...')
      const directions = await mapboxService.getDirections(
        [-46.6333, -23.5505], // São Paulo center
        [-46.6875, -23.5505]  // Nearby location
      )
      results.push({
        test: 'Get Directions',
        status: directions.length > 0 ? 'SUCCESS' : 'FAILED',
        data: directions[0] ? {
          distance: directions[0].distance,
          duration: directions[0].duration
        } : null
      })

      // Test 3: Calculate Ride Estimate
      console.log('🧪 Testing ride estimate...')
      const estimate = await mapboxService.calculateRideEstimate(
        [-46.6333, -23.5505],
        [-46.6875, -23.5505]
      )
      results.push({
        test: 'Ride Estimate',
        status: estimate ? 'SUCCESS' : 'FAILED',
        data: estimate
      })

      // Test 4: Find Nearby Drivers
      console.log('🧪 Testing nearby drivers...')
      const drivers = await mapboxService.findNearbyDrivers([-46.6333, -23.5505])
      results.push({
        test: 'Nearby Drivers',
        status: drivers.length > 0 ? 'SUCCESS' : 'FAILED',
        data: drivers.slice(0, 2)
      })

      // Test 5: Reverse Geocoding
      console.log('🧪 Testing reverse geocoding...')
      const address = await mapboxService.reverseGeocode([-46.6333, -23.5505])
      results.push({
        test: 'Reverse Geocoding',
        status: address && !address.includes('Erro') ? 'SUCCESS' : 'FAILED',
        data: address
      })

    } catch (error) {
      console.error('❌ Test error:', error)
      results.push({
        test: 'Error',
        status: 'FAILED',
        data: error
      })
    }

    setTestResults(results)
    setIsLoading(false)
  }

  // Disabled auto-run to prevent rate limiting
  // useEffect(() => {
  //   runTests()
  // }, [])

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <h3 className="text-lg font-bold mb-4">🧪 Mapbox Service Tests</h3>

      {isLoading && (
        <div className="flex items-center space-x-2 mb-4">
          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span>Running tests...</span>
        </div>
      )}

      <div className="space-y-3">
        {testResults.map((result, index) => (
          <div key={index} className="border rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold">{result.test}</h4>
              <span className={`px-2 py-1 rounded text-sm ${
                result.status === 'SUCCESS'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {result.status}
              </span>
            </div>
            {result.data && (
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(result.data, null, 2)}
              </pre>
            )}
          </div>
        ))}
      </div>

      <button
        onClick={runTests}
        disabled={isLoading}
        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        Run Tests Again
      </button>
    </div>
  )
}

export default MapboxTestComponent
