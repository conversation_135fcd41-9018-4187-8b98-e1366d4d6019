// 📦 BACKUP - DETECTOR DE DISPOSITIVO
// Este arquivo foi movido para backup pois o projeto agora é mobile-only
// Para restaurar: copie de volta para src/utils/

// 📱🖥️ DETECTOR DE DISPOSITIVO (BACKUP)
// Detecta automaticamente se é mobile ou desktop para carregar a versão otimizada

export interface DeviceInfo {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  screenWidth: number
  screenHeight: number
  devicePixelRatio: number
  touchSupport: boolean
  platform: string
  userAgent: string
}

export const detectDevice = (): DeviceInfo => {
  // Verificar se estamos no browser
  if (typeof window === 'undefined') {
    return {
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      screenWidth: 1920,
      screenHeight: 1080,
      devicePixelRatio: 1,
      touchSupport: false,
      platform: 'server',
      userAgent: 'server'
    }
  }

  const userAgent = navigator.userAgent.toLowerCase()
  const screenWidth = window.screen.width
  const screenHeight = window.screen.height
  const devicePixelRatio = window.devicePixelRatio || 1
  const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0

  // Detectar mobile por user agent
  const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i
  const tabletRegex = /ipad|android(?!.*mobile)|tablet/i
  
  const isMobileUA = mobileRegex.test(userAgent)
  const isTabletUA = tabletRegex.test(userAgent)
  
  // Detectar mobile por tamanho de tela
  const isMobileScreen = screenWidth <= 768
  const isTabletScreen = screenWidth > 768 && screenWidth <= 1024
  
  // Combinação final
  const isMobile = isMobileUA || (isMobileScreen && touchSupport)
  const isTablet = isTabletUA || (isTabletScreen && touchSupport && !isMobile)
  const isDesktop = !isMobile && !isTablet

  return {
    isMobile,
    isTablet,
    isDesktop,
    screenWidth,
    screenHeight,
    devicePixelRatio,
    touchSupport,
    platform: navigator.platform,
    userAgent
  }
}

// Hook React para usar o detector
import { useState, useEffect } from 'react'

export const useDeviceDetection = () => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(() => detectDevice())

  useEffect(() => {
    const handleResize = () => {
      setDeviceInfo(detectDevice())
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return deviceInfo
}

// Função para determinar qual componente 3D usar
export const getOptimalCarComponent = (deviceInfo: DeviceInfo) => {
  if (deviceInfo.isMobile) {
    return 'mobile' // Car3DMobile
  } else if (deviceInfo.isTablet) {
    return 'tablet' // Pode usar mobile ou desktop dependendo da performance
  } else {
    return 'desktop' // Car3DDesktop
  }
}

// Configurações otimizadas por dispositivo
export const getDeviceOptimizedSettings = (deviceInfo: DeviceInfo) => {
  if (deviceInfo.isMobile) {
    return {
      shadowMapSize: 1024,
      antialias: false,
      dpr: [1, 1.5],
      powerPreference: 'low-power' as const,
      enableRealisticMaterials: false,
      enablePBRTextures: false,
      maxLights: 2,
      enablePostProcessing: false
    }
  } else if (deviceInfo.isTablet) {
    return {
      shadowMapSize: 2048,
      antialias: true,
      dpr: [1, 2],
      powerPreference: 'default' as const,
      enableRealisticMaterials: true,
      enablePBRTextures: false,
      maxLights: 4,
      enablePostProcessing: false
    }
  } else {
    return {
      shadowMapSize: 8192,
      antialias: true,
      dpr: [1, 2],
      powerPreference: 'high-performance' as const,
      enableRealisticMaterials: true,
      enablePBRTextures: true,
      maxLights: 8,
      enablePostProcessing: true
    }
  }
}

// Log de informações do dispositivo
export const logDeviceInfo = (deviceInfo: DeviceInfo) => {
  console.log('📱🖥️ DEVICE DETECTION:', {
    type: deviceInfo.isMobile ? 'Mobile' : deviceInfo.isTablet ? 'Tablet' : 'Desktop',
    screen: `${deviceInfo.screenWidth}x${deviceInfo.screenHeight}`,
    dpr: deviceInfo.devicePixelRatio,
    touch: deviceInfo.touchSupport,
    platform: deviceInfo.platform,
    userAgent: deviceInfo.userAgent.substring(0, 50) + '...'
  })
}
