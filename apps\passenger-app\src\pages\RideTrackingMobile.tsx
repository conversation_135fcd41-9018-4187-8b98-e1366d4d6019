import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Navigate, useLocation } from 'react-router-dom'
import { MapPin, Clock, User, Star, Phone, MessageCircle, Navigation, CheckCircle } from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { GradientBackground } from '../components/GradientBackground'
import { useNoZoom } from '../hooks/useNoZoom'
import '../styles/no-zoom.css'

// 📱 RIDE TRACKING MOBILE ANDROID NATIVA
// MANTÉM DESIGN ORIGINAL + CONVERSÃO ANDROID NATIVA

interface RideData {
  id: string
  status: 'searching' | 'accepted' | 'driver_arriving' | 'in_progress' | 'completed'
  driver?: {
    id: string
    name: string
    rating: number
    vehicle: {
      model: string
      plate: string
      color: string
    }
    location?: [number, number]
    estimatedArrival?: string
  }
  pickup: {
    address: string
    coords: [number, number]
  }
  destination: {
    address: string
    coords: [number, number]
  }
  price: number
  estimatedDuration: string
}

export const RideTrackingMobile: React.FC = () => {
  const { user } = useAuth()
  const location = useLocation()
  const [rideData, setRideData] = useState<RideData | null>(null)
  const [currentStatus, setCurrentStatus] = useState<string>('searching')

  // 🚫 DESABILITA ZOOM COMPLETAMENTE + CONFIGURAÇÕES ANDROID NATIVAS
  useNoZoom()

  // Configurações Android nativas (mantendo design original)
  useEffect(() => {
    // Meta viewport para Android nativo
    let metaViewport = document.querySelector('meta[name="viewport"]')
    if (!metaViewport) {
      metaViewport = document.createElement('meta')
      metaViewport.setAttribute('name', 'viewport')
      document.head.appendChild(metaViewport)
    }
    metaViewport.setAttribute('content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )

    // Configurações de overflow para Android
    document.body.style.margin = '0'
    document.body.style.padding = '0'
    document.body.style.overflow = 'hidden'
    document.documentElement.style.margin = '0'
    document.documentElement.style.padding = '0'
    document.documentElement.style.overflow = 'hidden'
  }, [])

  // Redirect se não logado
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // Dados de exemplo para demonstração
  const mockRideData: RideData = {
    id: '1',
    status: 'driver_arriving',
    driver: {
      id: '1',
      name: 'João Silva',
      rating: 4.8,
      vehicle: {
        model: 'Honda Civic',
        plate: 'ABC-1234',
        color: 'Prata'
      },
      estimatedArrival: '3 min'
    },
    pickup: {
      address: 'Rua das Flores, 123 - Centro',
      coords: [-46.6333, -23.5505]
    },
    destination: {
      address: 'Shopping Center Norte - Zona Norte',
      coords: [-46.6565, -23.5618]
    },
    price: 18.50,
    estimatedDuration: '25 min'
  }

  // Inicializar dados da corrida
  useEffect(() => {
    const stateData = location.state as { rideId?: string } | null
    if (stateData?.rideId) {
      // Em uma implementação real, buscar dados da corrida pela API
      setRideData(mockRideData)
    } else {
      // Usar dados mock para demonstração
      setRideData(mockRideData)
    }
  }, [location.state])

  // Simular atualizações de status
  useEffect(() => {
    if (!rideData) return

    const statusSequence = ['searching', 'accepted', 'driver_arriving', 'in_progress', 'completed']
    let currentIndex = statusSequence.indexOf(rideData.status)

    const interval = setInterval(() => {
      if (currentIndex < statusSequence.length - 1) {
        currentIndex++
        setCurrentStatus(statusSequence[currentIndex])
        setRideData(prev => prev ? { ...prev, status: statusSequence[currentIndex] as any } : null)
      } else {
        clearInterval(interval)
      }
    }, 10000) // Atualiza a cada 10 segundos

    return () => clearInterval(interval)
  }, [rideData])

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'searching':
        return { icon: '🔍', title: 'Procurando motorista...', color: 'bg-blue-500' }
      case 'accepted':
        return { icon: '✅', title: 'Motorista encontrado!', color: 'bg-green-500' }
      case 'driver_arriving':
        return { icon: '🚗', title: 'Motorista a caminho', color: 'bg-yellow-500' }
      case 'in_progress':
        return { icon: '🛣️', title: 'Viagem em andamento', color: 'bg-purple-500' }
      case 'completed':
        return { icon: '🎉', title: 'Viagem concluída!', color: 'bg-green-600' }
      default:
        return { icon: '⏳', title: 'Carregando...', color: 'bg-gray-500' }
    }
  }

  const statusInfo = getStatusInfo(currentStatus)

  // Animações simples e limpas (MANTENDO ORIGINAIS)
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  if (!rideData) {
    return (
      <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">
        <GradientBackground variant="static" opacity={0.7} />
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 flex flex-col h-full min-h-screen items-center justify-center px-4">
          <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl text-center">
            <div className="text-6xl mb-4">⏳</div>
            <h2 className="text-xl font-bold text-white mb-2">Carregando corrida...</h2>
            <p className="text-white/70">Aguarde um momento</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">

      {/* Background Gradient Sutil (FIEL AO LOGIN) */}
      <GradientBackground
        variant="static"
        opacity={0.7}
      />

      {/* Overlay muito sutil para legibilidade (FIEL AO LOGIN) */}
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Conteúdo Principal (FIEL AO LOGIN) */}
      <div className="relative z-10 flex flex-col h-full min-h-screen">

        {/* Header com Logo (FIEL AO LOGIN) */}
        <motion.div
          className="pt-8 pb-6 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <img
                src="/icons/icon-48x48.png"
                alt="MobiDrive"
                className="w-6 h-6"
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-xs text-white/70">Acompanhar Corrida</p>
            </div>
          </div>
        </motion.div>

        {/* Conteúdo Central (FIEL AO LOGIN) */}
        <div className="flex-1 flex flex-col justify-center px-4 space-y-6">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {/* Status da Corrida (FIEL AO LOGIN) */}
            <motion.div variants={itemVariants}>
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                <div className="text-center">
                  <motion.div
                    className={`w-16 h-16 ${statusInfo.color} rounded-full flex items-center justify-center mx-auto mb-4`}
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <span className="text-2xl text-white">{statusInfo.icon}</span>
                  </motion.div>
                  <h2 className="text-xl font-bold text-white mb-2">{statusInfo.title}</h2>
                  <p className="text-white/80 text-sm">
                    {currentStatus === 'driver_arriving' && rideData.driver?.estimatedArrival &&
                      `Chegada em ${rideData.driver.estimatedArrival}`}
                    {currentStatus === 'in_progress' &&
                      `Tempo estimado: ${rideData.estimatedDuration}`}
                    {currentStatus === 'completed' &&
                      'Obrigado por usar o MobiDrive!'}
                  </p>
                </div>
              </div>
            </motion.div>

        {/* Informações do Motorista (MANTENDO DESIGN ORIGINAL) */}
        {rideData.driver && currentStatus !== 'searching' && (
          <motion.div variants={itemVariants}>
            <ModernCard title="Seu Motorista" icon="👤">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                  <User className="w-8 h-8 text-gray-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900">{rideData.driver.name}</h3>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Star className="w-4 h-4 text-yellow-500" />
                    <span>{rideData.driver.rating}</span>
                    <span>•</span>
                    <span>{rideData.driver.vehicle.model}</span>
                  </div>
                  <p className="text-sm text-gray-500">
                    {rideData.driver.vehicle.color} • {rideData.driver.vehicle.plate}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <motion.button
                    className="p-3 bg-green-500 text-white rounded-xl"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Phone className="w-5 h-5" />
                  </motion.button>
                  <motion.button
                    className="p-3 bg-blue-500 text-white rounded-xl"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <MessageCircle className="w-5 h-5" />
                  </motion.button>
                </div>
              </div>
            </ModernCard>
          </motion.div>
        )}

        {/* Detalhes da Viagem (MANTENDO DESIGN ORIGINAL) */}
        <motion.div variants={itemVariants}>
          <ModernCard title="Detalhes da Viagem" icon="📍">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Origem</p>
                  <p className="text-sm text-gray-600">{rideData.pickup.address}</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-3 h-3 bg-red-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Destino</p>
                  <p className="text-sm text-gray-600">{rideData.destination.address}</p>
                </div>
              </div>
              <div className="border-t pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Valor da corrida:</span>
                  <span className="text-lg font-bold text-gray-900">R$ {rideData.price.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </ModernCard>
        </motion.div>

        {/* Mapa Placeholder (MANTENDO DESIGN ORIGINAL) */}
        <motion.div variants={itemVariants}>
          <ModernCard title="Localização em Tempo Real" icon="🗺️">
            <div className="bg-gray-100 rounded-xl h-48 flex items-center justify-center">
              <div className="text-center">
                <Navigation className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500 text-sm">Mapa em tempo real</p>
                <p className="text-gray-400 text-xs">Integração com Mapbox</p>
              </div>
            </div>
          </ModernCard>
        </motion.div>

        {/* Ações (MANTENDO DESIGN ORIGINAL) */}
        {currentStatus === 'completed' ? (
          <motion.div variants={itemVariants}>
            <ModernCard glass>
              <div className="space-y-3">
                <motion.button
                  onClick={() => window.location.href = '/dashboard'}
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <CheckCircle className="w-5 h-5" />
                  <span>Finalizar e Avaliar</span>
                </motion.button>
                <motion.button
                  onClick={() => window.location.href = '/request-ride'}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-4 rounded-xl font-semibold"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Nova Corrida
                </motion.button>
              </div>
            </ModernCard>
          </motion.div>
        ) : (
          <motion.div variants={itemVariants}>
            <ModernCard glass>
              <motion.button
                onClick={() => {
                  if (confirm('Tem certeza que deseja cancelar a corrida?')) {
                    window.location.href = '/dashboard'
                  }
                }}
                className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-xl font-semibold"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Cancelar Corrida
              </motion.button>
            </ModernCard>
          </motion.div>
            )}
          </motion.div>
        </div>

        {/* Footer Simples (FIEL AO LOGIN) */}
        <motion.div
          className="pb-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <p className="text-white/50 text-xs">
            © 2024 MobiDrive. Todos os direitos reservados.
          </p>
        </motion.div>
      </div>
    </div>
  )
}

export default RideTrackingMobile
