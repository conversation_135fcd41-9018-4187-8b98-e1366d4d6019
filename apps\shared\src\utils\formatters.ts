/**
 * Formata um valor monetário para o formato de moeda brasileira (R$)
 * @param value Valor a ser formatado
 * @returns String formatada
 */
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
};

/**
 * Formata uma data para o formato brasileiro (DD/MM/YYYY)
 * @param dateString String de data no formato ISO
 * @returns String formatada
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('pt-BR').format(date);
};

/**
 * Formata uma data e hora para o formato brasileiro (DD/MM/YYYY HH:MM)
 * @param dateString String de data no formato ISO
 * @returns String formatada
 */
export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

/**
 * Formata uma distância em quilômetros
 * @param distance Distância em quilômetros
 * @returns String formatada
 */
export const formatDistance = (distance: number): string => {
  return `${distance.toFixed(1)} km`;
};

/**
 * Formata uma duração em segundos para o formato HH:MM ou MM:SS
 * @param seconds Duração em segundos
 * @returns String formatada
 */
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}h ${minutes}min`;
  } else if (minutes > 0) {
    return `${minutes}min ${remainingSeconds}s`;
  } else {
    return `${remainingSeconds}s`;
  }
};

/**
 * Formata um número de telefone para o formato brasileiro
 * @param phone Número de telefone
 * @returns String formatada
 */
export const formatPhone = (phone: string): string => {
  // Remove caracteres não numéricos
  const cleaned = phone.replace(/\D/g, '');
  
  // Verifica se é um número de telefone válido
  if (cleaned.length < 10 || cleaned.length > 11) {
    return phone;
  }
  
  // Formata como (XX) XXXXX-XXXX ou (XX) XXXX-XXXX
  if (cleaned.length === 11) {
    return `(${cleaned.substring(0, 2)}) ${cleaned.substring(2, 7)}-${cleaned.substring(7)}`;
  } else {
    return `(${cleaned.substring(0, 2)}) ${cleaned.substring(2, 6)}-${cleaned.substring(6)}`;
  }
};

/**
 * Formata um CPF para o formato brasileiro
 * @param cpf Número de CPF
 * @returns String formatada
 */
export const formatCPF = (cpf: string): string => {
  // Remove caracteres não numéricos
  const cleaned = cpf.replace(/\D/g, '');
  
  // Verifica se é um CPF válido
  if (cleaned.length !== 11) {
    return cpf;
  }
  
  // Formata como XXX.XXX.XXX-XX
  return `${cleaned.substring(0, 3)}.${cleaned.substring(3, 6)}.${cleaned.substring(6, 9)}-${cleaned.substring(9)}`;
};

/**
 * Formata um tempo relativo (ex: "há 5 minutos", "há 2 horas")
 * @param dateString String de data no formato ISO
 * @returns String formatada
 */
export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffMonth / 12);

  if (diffSec < 60) {
    return 'agora mesmo';
  } else if (diffMin < 60) {
    return `há ${diffMin} ${diffMin === 1 ? 'minuto' : 'minutos'}`;
  } else if (diffHour < 24) {
    return `há ${diffHour} ${diffHour === 1 ? 'hora' : 'horas'}`;
  } else if (diffDay < 30) {
    return `há ${diffDay} ${diffDay === 1 ? 'dia' : 'dias'}`;
  } else if (diffMonth < 12) {
    return `há ${diffMonth} ${diffMonth === 1 ? 'mês' : 'meses'}`;
  } else {
    return `há ${diffYear} ${diffYear === 1 ? 'ano' : 'anos'}`;
  }
};

/**
 * Formata um número de placa de veículo para o formato brasileiro
 * @param plate Número da placa
 * @returns String formatada
 */
export const formatLicensePlate = (plate: string): string => {
  // Remove caracteres não alfanuméricos
  const cleaned = plate.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
  
  // Verifica se é uma placa válida
  if (cleaned.length !== 7) {
    return plate.toUpperCase();
  }
  
  // Formata como XXX-XXXX (padrão antigo) ou XXX-XXXX (Mercosul)
  return `${cleaned.substring(0, 3)}-${cleaned.substring(3)}`;
};

/**
 * Formata um número para exibir com casas decimais
 * @param value Valor numérico
 * @param decimals Número de casas decimais
 * @returns String formatada
 */
export const formatNumber = (value: number, decimals: number = 2): string => {
  return value.toFixed(decimals).replace('.', ',');
};

/**
 * Formata um percentual
 * @param value Valor percentual (ex: 0.25 para 25%)
 * @returns String formatada
 */
export const formatPercent = (value: number): string => {
  return `${(value * 100).toFixed(1).replace('.', ',')}%`;
};
