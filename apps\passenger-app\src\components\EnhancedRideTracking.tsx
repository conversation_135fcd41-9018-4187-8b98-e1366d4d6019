import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MapPin, 
  Clock, 
  Navigation, 
  Car, 
  Phone, 
  MessageCircle,
  Star,
  AlertCircle,
  CheckCircle,
  TrendingUp,
  TrendingDown
} from 'lucide-react'
import { rideService, RideRequest } from '../services/RideService'
import { driverLocationService, DriverLocationUpdate } from '../services/DriverLocationService'
import { mapboxService } from '../services/MapboxService'

interface EnhancedRideTrackingProps {
  rideId: string
  onRideComplete?: () => void
  onRideCancel?: () => void
}

interface DriverInfo {
  id: string
  name: string
  phone: string
  rating: number
  vehicle: {
    model: string
    plate: string
    color: string
    type: string
  }
  location: [number, number]
  heading: number
  speed: number
  eta: number
}

interface RideStatus {
  status: 'pending' | 'accepted' | 'driver_arriving' | 'in_progress' | 'completed' | 'cancelled'
  message: string
  icon: React.ReactNode
  color: string
}

export const EnhancedRideTracking: React.FC<EnhancedRideTrackingProps> = ({
  rideId,
  onRideComplete,
  onRideCancel
}) => {
  const [rideData, setRideData] = useState<RideRequest | null>(null)
  const [driverInfo, setDriverInfo] = useState<DriverInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [realTimeETA, setRealTimeETA] = useState<number | null>(null)
  const [etaTrend, setETATrend] = useState<'improving' | 'worsening' | 'stable' | null>(null)
  const [lastETAUpdate, setLastETAUpdate] = useState<Date | null>(null)

  // Carregar dados da corrida
  const loadRideData = useCallback(async () => {
    try {
      setLoading(true)
      
      const ride = await rideService.getRideById(rideId)
      if (!ride) {
        throw new Error('Corrida não encontrada')
      }

      setRideData(ride)

      // Se há motorista atribuído, carregar informações do motorista
      if (ride.driver_id) {
        const driverLocation = await driverLocationService.getDriverLocation(ride.driver_id)
        
        if (driverLocation) {
          setDriverInfo({
            id: ride.driver_id,
            name: 'Motorista', // TODO: Buscar nome real do banco
            phone: '(11) 99999-9999', // TODO: Buscar telefone real
            rating: 4.8, // TODO: Buscar rating real
            vehicle: {
              model: 'Honda Civic',
              plate: 'ABC-1234',
              color: 'Prata',
              type: 'economy'
            },
            location: [driverLocation.longitude, driverLocation.latitude],
            heading: driverLocation.heading || 0,
            speed: driverLocation.speed || 0,
            eta: 5 // Será calculado em tempo real
          })
        }
      }

    } catch (err) {
      console.error('Erro ao carregar dados da corrida:', err)
      setError(err instanceof Error ? err.message : 'Erro desconhecido')
    } finally {
      setLoading(false)
    }
  }, [rideId])

  // Calcular ETA em tempo real
  const calculateRealTimeETA = useCallback(async (driverLocation: [number, number], destination: [number, number]) => {
    try {
      const eta = await mapboxService.calculateRealTimeETA(driverLocation, destination)
      
      if (eta !== null) {
        // Determinar tendência do ETA
        if (realTimeETA !== null) {
          if (eta < realTimeETA) {
            setETATrend('improving')
          } else if (eta > realTimeETA) {
            setETATrend('worsening')
          } else {
            setETATrend('stable')
          }
        }

        setRealTimeETA(eta)
        setLastETAUpdate(new Date())
      }
    } catch (error) {
      console.error('Erro ao calcular ETA:', error)
    }
  }, [realTimeETA])

  // Subscrever a atualizações da corrida
  useEffect(() => {
    if (!rideId) return

    loadRideData()

    // Subscrever a atualizações da corrida
    const rideSubscription = rideService.subscribeToRideUpdates(rideId, (updatedRide) => {
      console.log('📡 Ride update received:', updatedRide)
      setRideData(updatedRide)

      // Se o status mudou para completed, chamar callback
      if (updatedRide.status === 'completed' && onRideComplete) {
        onRideComplete()
      }
    })

    return () => {
      rideSubscription.unsubscribe()
    }
  }, [rideId, loadRideData, onRideComplete])

  // Subscrever a atualizações de localização do motorista
  useEffect(() => {
    if (!driverInfo?.id || !rideData) return

    const driverSubscription = driverLocationService.subscribeToDriverLocation(
      driverInfo.id,
      (locationUpdate: DriverLocationUpdate) => {
        console.log('📍 Driver location update:', locationUpdate)
        
        setDriverInfo(prev => prev ? {
          ...prev,
          location: locationUpdate.location,
          heading: locationUpdate.heading || 0,
          speed: locationUpdate.speed || 0
        } : null)

        // Calcular ETA baseado no status da corrida
        if (rideData.status === 'accepted' || rideData.status === 'driver_arriving') {
          // Motorista indo buscar passageiro
          calculateRealTimeETA(locationUpdate.location, rideData.origin_coords)
        } else if (rideData.status === 'in_progress') {
          // Motorista levando passageiro ao destino
          calculateRealTimeETA(locationUpdate.location, rideData.destination_coords)
        }
      }
    )

    return driverSubscription
  }, [driverInfo?.id, rideData, calculateRealTimeETA])

  // Determinar status da corrida
  const getRideStatus = (): RideStatus => {
    if (!rideData) {
      return {
        status: 'pending',
        message: 'Carregando...',
        icon: <Clock className="w-5 h-5" />,
        color: 'text-gray-500'
      }
    }

    switch (rideData.status) {
      case 'pending':
        return {
          status: 'pending',
          message: 'Procurando motorista...',
          icon: <Clock className="w-5 h-5 animate-spin" />,
          color: 'text-yellow-500'
        }
      case 'accepted':
        return {
          status: 'accepted',
          message: driverInfo ? `${driverInfo.name} está a caminho` : 'Motorista confirmado',
          icon: <CheckCircle className="w-5 h-5" />,
          color: 'text-green-500'
        }
      case 'driver_arriving':
        return {
          status: 'driver_arriving',
          message: `Motorista chegando em ${realTimeETA || 'alguns'} minutos`,
          icon: <Car className="w-5 h-5" />,
          color: 'text-blue-500'
        }
      case 'in_progress':
        return {
          status: 'in_progress',
          message: 'Em viagem para o destino',
          icon: <Navigation className="w-5 h-5" />,
          color: 'text-purple-500'
        }
      case 'completed':
        return {
          status: 'completed',
          message: 'Corrida finalizada',
          icon: <CheckCircle className="w-5 h-5" />,
          color: 'text-green-500'
        }
      case 'cancelled':
        return {
          status: 'cancelled',
          message: 'Corrida cancelada',
          icon: <AlertCircle className="w-5 h-5" />,
          color: 'text-red-500'
        }
      default:
        return {
          status: 'pending',
          message: 'Status desconhecido',
          icon: <AlertCircle className="w-5 h-5" />,
          color: 'text-gray-500'
        }
    }
  }

  const rideStatus = getRideStatus()

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Carregando informações da corrida...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
          <span className="text-red-700">{error}</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Status da Corrida */}
      <motion.div
        className="bg-white rounded-xl shadow-lg p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={rideStatus.color}>
              {rideStatus.icon}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{rideStatus.message}</h3>
              {realTimeETA && (
                <div className="flex items-center space-x-2 mt-1">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    ETA: {realTimeETA} min
                  </span>
                  {etaTrend && (
                    <div className="flex items-center">
                      {etaTrend === 'improving' && <TrendingDown className="w-4 h-4 text-green-500" />}
                      {etaTrend === 'worsening' && <TrendingUp className="w-4 h-4 text-red-500" />}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          
          {lastETAUpdate && (
            <span className="text-xs text-gray-500">
              Atualizado {lastETAUpdate.toLocaleTimeString()}
            </span>
          )}
        </div>
      </motion.div>

      {/* Informações do Motorista */}
      <AnimatePresence>
        {driverInfo && (
          <motion.div
            className="bg-white rounded-xl shadow-lg p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <h4 className="font-semibold text-gray-900 mb-4">Seu Motorista</h4>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                  <Car className="w-6 h-6 text-gray-600" />
                </div>
                
                <div>
                  <h5 className="font-medium text-gray-900">{driverInfo.name}</h5>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span>{driverInfo.rating}</span>
                    <span>•</span>
                    <span>{driverInfo.vehicle.model} {driverInfo.vehicle.color}</span>
                  </div>
                  <p className="text-sm text-gray-500">{driverInfo.vehicle.plate}</p>
                </div>
              </div>

              <div className="flex space-x-2">
                <button className="p-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors">
                  <Phone className="w-5 h-5" />
                </button>
                <button className="p-2 bg-green-100 text-green-600 rounded-lg hover:bg-green-200 transition-colors">
                  <MessageCircle className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Informações de Velocidade e Direção */}
            {driverInfo.speed > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <span>Velocidade: {Math.round(driverInfo.speed)} km/h</span>
                  <span>Direção: {Math.round(driverInfo.heading)}°</span>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Informações da Viagem */}
      {rideData && (
        <motion.div
          className="bg-white rounded-xl shadow-lg p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <h4 className="font-semibold text-gray-900 mb-4">Detalhes da Viagem</h4>
          
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-3 h-3 bg-blue-500 rounded-full mt-2"></div>
              <div>
                <p className="font-medium text-gray-900">Origem</p>
                <p className="text-sm text-gray-600">{rideData.origin_address}</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-3 h-3 bg-red-500 rounded-full mt-2"></div>
              <div>
                <p className="font-medium text-gray-900">Destino</p>
                <p className="text-sm text-gray-600">{rideData.destination_address}</p>
              </div>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t border-gray-200 flex justify-between text-sm">
            <span className="text-gray-600">Distância: {(rideData.distance / 1000).toFixed(1)} km</span>
            <span className="text-gray-600">Preço: R$ {rideData.estimated_price.toFixed(2)}</span>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default EnhancedRideTracking
