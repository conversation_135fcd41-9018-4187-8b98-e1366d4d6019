import React from 'react'
import { motion } from 'framer-motion'
import { Loader, MapPin, CreditCard, Wifi } from 'lucide-react'
import { usePreloadResources } from '../hooks/usePreloadResources'

interface LoadingOptimizerProps {
  children: React.ReactNode
  showProgress?: boolean
}

/**
 * Componente que otimiza o carregamento da aplicação
 * Mostra progresso de preload e só renderiza filhos quando pronto
 */
export const LoadingOptimizer: React.FC<LoadingOptimizerProps> = ({ 
  children, 
  showProgress = false 
}) => {
  const { status, isComplete, progress } = usePreloadResources()

  // Se não quiser mostrar progresso, renderizar direto
  if (!showProgress) {
    return <>{children}</>
  }

  // Se ainda não está completo, mostrar loading
  if (!isComplete) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-black/20 backdrop-blur-md rounded-2xl p-8 border border-white/10 shadow-2xl max-w-sm w-full mx-4"
        >
          {/* Logo */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <img src="/icons/icon-48x48.png" alt="MobiDrive" className="w-8 h-8" />
            </div>
            <h2 className="text-2xl font-bold text-white">
              MOBI<span className="text-blue-400">DRIVE</span>
            </h2>
            <p className="text-white/70 text-sm">Preparando sua experiência</p>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex items-center justify-between text-sm text-white/80 mb-2">
              <span>Carregando recursos</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-white/10 rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </div>

          {/* Status Items */}
          <div className="space-y-3">
            <StatusItem
              icon={<MapPin className="w-4 h-4" />}
              label="Mapbox"
              isComplete={status.mapbox}
            />
            <StatusItem
              icon={<Wifi className="w-4 h-4" />}
              label="Localização"
              isComplete={status.userLocation}
            />
            <StatusItem
              icon={<CreditCard className="w-4 h-4" />}
              label="Pagamentos"
              isComplete={status.paymentMethods}
            />
          </div>

          {/* Loading Animation */}
          <div className="flex justify-center mt-6">
            <Loader className="w-6 h-6 text-blue-400 animate-spin" />
          </div>
        </motion.div>
      </div>
    )
  }

  // Quando completo, renderizar com animação
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {children}
    </motion.div>
  )
}

interface StatusItemProps {
  icon: React.ReactNode
  label: string
  isComplete: boolean
}

const StatusItem: React.FC<StatusItemProps> = ({ icon, label, isComplete }) => (
  <div className="flex items-center space-x-3">
    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
      isComplete ? 'bg-green-500' : 'bg-white/10'
    }`}>
      {isComplete ? (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="text-white"
        >
          ✓
        </motion.div>
      ) : (
        <div className="text-white/60">
          {icon}
        </div>
      )}
    </div>
    <span className={`text-sm ${isComplete ? 'text-white' : 'text-white/60'}`}>
      {label}
    </span>
    {!isComplete && (
      <Loader className="w-4 h-4 text-white/40 animate-spin ml-auto" />
    )}
  </div>
)

export default LoadingOptimizer
