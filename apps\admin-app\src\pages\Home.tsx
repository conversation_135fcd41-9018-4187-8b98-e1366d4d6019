import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Shield, BarChart3, Users, Settings } from 'lucide-react'
import { MobiDriveLogo } from '../components/MobiDriveLogo'

export const Home: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 to-violet-700 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl p-8 border border-white/20">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-6">
            <MobiDriveLogo size="lg" variant="admin" showText={false} animated={true} />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">MobiDrive</h1>
          <p className="text-gray-600">Painel Administrativo</p>
        </div>

        <div className="space-y-4 mb-8">
          <div className="flex items-center space-x-3">
            <BarChart3 className="w-5 h-5 text-purple-600" />
            <span className="text-gray-700">Analytics em tempo real</span>
          </div>
          <div className="flex items-center space-x-3">
            <Users className="w-5 h-5 text-purple-600" />
            <span className="text-gray-700">Gerenciamento de usuários</span>
          </div>
          <div className="flex items-center space-x-3">
            <Settings className="w-5 h-5 text-purple-600" />
            <span className="text-gray-700">Configurações avançadas</span>
          </div>
        </div>

        <div className="space-y-3">
          <Link
            to="/login"
            className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-purple-700 transition-colors block text-center"
          >
            Entrar
          </Link>
          <Link
            to="/dashboard"
            className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-semibold hover:bg-gray-200 transition-colors block text-center"
          >
            Demo Dashboard
          </Link>
        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            Versão 1.0.0 - Administrador
          </p>
        </div>
      </div>
    </div>
  )
}
