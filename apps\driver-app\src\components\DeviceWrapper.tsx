import React, { ReactNode, useEffect } from 'react'
import { useMobileForcer } from '../utils/mobileForcer'
import { useBrazilTime } from '../hooks/useBrazilTime'
import { useForceZoom100 } from '../hooks/useForceZoom100'
import { TouchScrollContainer } from './TouchScrollContainer'
import '../styles/device-wrapper.css'

// 📱 DEVICE WRAPPER GLOBAL - MOCKUP ÚNICO PARA TODAS AS PÁGINAS
// Separa completamente o mockup do iPhone do conteúdo das páginas

interface DeviceWrapperProps {
  children: ReactNode
}

export const DeviceWrapper: React.FC<DeviceWrapperProps> = ({ children }) => {
  const shouldUseMockup = useMobileForcer()
  const brazilTime = useBrazilTime()

  // 🔒 FORÇA ZOOM 100% GLOBAL - IMPEDE ZOOM DO BROWSER
  useForceZoom100()

  // Configurações específicas do mockup (não interfere com páginas)
  useEffect(() => {
    if (shouldUseMockup) {
      // Apenas configurações visuais do mockup, não de zoom
      // O sistema anti-zoom das páginas é independente
    }
  }, [shouldUseMockup])

  // Se é mobile real, renderiza o conteúdo diretamente
  if (!shouldUseMockup) {
    return (
      <div className="device-content-mobile">
        {children}
      </div>
    )
  }

  // Se é desktop, renderiza dentro do mockup iPhone
  return (
    <div className="device-wrapper-container">
      {/* Background do desktop */}
      <div className="device-wrapper-background">

        {/* Container do iPhone */}
        <div className="device-iphone-container">

          {/* Sombra do dispositivo */}
          <div className="device-iphone-shadow"></div>

          {/* Corpo do iPhone */}
          <div className="device-iphone-body">

            {/* Moldura interna */}
            <div className="device-iphone-frame">

              {/* Tela do iPhone */}
              <div className="device-iphone-screen">

                {/* Notch (entalhe superior) */}
                <div className="device-notch">
                  <div className="device-notch-content">
                    <div className="device-notch-inner">
                      <div className="device-camera-left"></div>
                      <div className="device-speaker"></div>
                      <div className="device-camera-right"></div>
                    </div>
                  </div>
                </div>

                {/* Status bar */}
                <div className="device-status-bar">
                  <div className="device-status-content">
                    <div className="device-status-left">
                      <span className="device-time">{brazilTime || '9:41'}</span>
                    </div>
                    <div className="device-status-right">
                      {/* Sinal */}
                      <div className="device-signal">
                        <div className="device-signal-bar device-signal-1"></div>
                        <div className="device-signal-bar device-signal-2"></div>
                        <div className="device-signal-bar device-signal-3"></div>
                        <div className="device-signal-bar device-signal-4"></div>
                      </div>
                      {/* WiFi */}
                      <svg className="device-wifi" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.24 0 1 1 0 01-1.415-1.414 5 5 0 017.07 0 1 1 0 01-1.415 1.414zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                      {/* Bateria */}
                      <div className="device-battery">
                        <div className="device-battery-body">
                          <div className="device-battery-level"></div>
                        </div>
                        <div className="device-battery-tip"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* CONTEÚDO DAS PÁGINAS - ÁREA LIVRE COM TOUCH GESTURES */}
                <div className="device-content-area">
                  <TouchScrollContainer
                    enableVerticalScroll={true}
                    enableHorizontalScroll={false}
                    bounceStiffness={400}
                    bounceDamping={40}
                    className="device-touch-container"
                  >
                    {children}
                  </TouchScrollContainer>
                </div>

                {/* Home indicator */}
                <div className="device-home-indicator">
                  <div className="device-home-bar"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Botões físicos */}
          <div className="device-button-volume-1"></div>
          <div className="device-button-volume-2"></div>
          <div className="device-button-power"></div>
        </div>

        {/* Badges informativos */}
        <div className="device-info-badge-left">
          <div className="device-badge-content">
            <div className="device-badge-row">
              <div className="device-badge-indicator device-badge-green"></div>
              <span>🚗 Driver App</span>
            </div>
            <div className="device-badge-subtitle">
              Zoom fixo • Fundo estável
            </div>
          </div>
        </div>

        <div className="device-info-badge-right">
          <div className="device-badge-content">
            <div className="device-badge-title">DISPOSITIVO SIMULADO:</div>
            <div className="device-badge-row">
              <div className="device-badge-indicator device-badge-blue"></div>
              <span className="device-badge-text">iPhone 14 Pro</span>
            </div>
            <div className="device-badge-row">
              <div className="device-badge-indicator device-badge-purple"></div>
              <span className="device-badge-text">375 × 812px</span>
            </div>
            <div className="device-badge-row">
              <div className="device-badge-indicator device-badge-green"></div>
              <span className="device-badge-text">Zoom Fixo</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DeviceWrapper
