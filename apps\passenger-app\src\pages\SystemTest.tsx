import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ArrowLeft, CheckCircle, XCircle, AlertCircle, MapPin, Clock, Zap } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { mapboxService } from '../services/MapboxService'
import { useMapboxSearch } from '../hooks/useMapboxSearch'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error' | 'warning'
  message: string
  duration?: number
}

export default function SystemTest() {
  const navigate = useNavigate()
  const [tests, setTests] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null)
  
  const { searchPlaces } = useMapboxSearch({ userLocation: userLocation || undefined })

  const updateTest = (name: string, status: TestResult['status'], message: string, duration?: number) => {
    setTests(prev => prev.map(test => 
      test.name === name ? { ...test, status, message, duration } : test
    ))
  }

  const runTests = async () => {
    setIsRunning(true)
    
    // Initialize tests
    const initialTests: TestResult[] = [
      { name: 'Localização do Usuário', status: 'pending', message: 'Obtendo localização...' },
      { name: 'Token do Mapbox', status: 'pending', message: 'Verificando token...' },
      { name: 'Busca de Endereços', status: 'pending', message: 'Testando busca...' },
      { name: 'Proximidade', status: 'pending', message: 'Testando ordenação por proximidade...' },
      { name: 'Cache do Sistema', status: 'pending', message: 'Verificando cache...' },
      { name: 'Performance', status: 'pending', message: 'Medindo performance...' }
    ]
    
    setTests(initialTests)

    try {
      // Test 1: User Location
      const startTime = Date.now()
      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000
          })
        })
        
        const coords: [number, number] = [position.coords.longitude, position.coords.latitude]
        setUserLocation(coords)
        
        updateTest('Localização do Usuário', 'success', 
          `Localização obtida: ${coords[1].toFixed(4)}, ${coords[0].toFixed(4)} (±${position.coords.accuracy.toFixed(0)}m)`,
          Date.now() - startTime
        )
      } catch (error) {
        const defaultCoords: [number, number] = [-46.6333, -23.5505]
        setUserLocation(defaultCoords)
        updateTest('Localização do Usuário', 'warning', 
          'GPS não disponível, usando São Paulo como padrão',
          Date.now() - startTime
        )
      }

      // Test 2: Mapbox Token
      const tokenStart = Date.now()
      try {
        const healthStatus = await mapboxService.getHealthStatus()
        updateTest('Token do Mapbox', 'success', 
          `Token válido - Status: ${healthStatus.status}`,
          Date.now() - tokenStart
        )
      } catch (error) {
        updateTest('Token do Mapbox', 'error', 
          'Token inválido ou expirado',
          Date.now() - tokenStart
        )
      }

      // Test 3: Address Search
      const searchStart = Date.now()
      try {
        const results = await mapboxService.searchPlaces('rua boa vista', {
          proximity: userLocation || [-46.6333, -23.5505],
          types: ['address', 'poi', 'place'],
          limit: 5,
          biasStrength: 'strong'
        })
        
        updateTest('Busca de Endereços', 'success', 
          `${results.length} resultados encontrados`,
          Date.now() - searchStart
        )
        
        // Test 4: Proximity
        const proximityStart = Date.now()
        if (results.length > 0) {
          const hasDistance = results.some(r => (r as any).distance !== undefined)
          const isOrdered = results.every((r, i) => 
            i === 0 || ((r as any).distance || 0) >= ((results[i-1] as any).distance || 0)
          )
          
          if (hasDistance && isOrdered) {
            updateTest('Proximidade', 'success', 
              `Resultados ordenados por distância (${((results[0] as any).distance || 0).toFixed(2)}km - ${((results[results.length-1] as any).distance || 0).toFixed(2)}km)`,
              Date.now() - proximityStart
            )
          } else {
            updateTest('Proximidade', 'warning', 
              'Ordenação por proximidade não detectada',
              Date.now() - proximityStart
            )
          }
        } else {
          updateTest('Proximidade', 'error', 
            'Nenhum resultado para testar proximidade',
            Date.now() - proximityStart
          )
        }
      } catch (error) {
        updateTest('Busca de Endereços', 'error', 
          `Erro na busca: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
          Date.now() - searchStart
        )
        updateTest('Proximidade', 'error', 
          'Não foi possível testar proximidade',
          Date.now() - searchStart
        )
      }

      // Test 5: Cache
      const cacheStart = Date.now()
      try {
        // First search
        await mapboxService.searchPlaces('teste cache', {
          proximity: userLocation || [-46.6333, -23.5505],
          limit: 3
        })
        
        // Second search (should be cached)
        const cachedStart = Date.now()
        await mapboxService.searchPlaces('teste cache', {
          proximity: userLocation || [-46.6333, -23.5505],
          limit: 3
        })
        const cachedDuration = Date.now() - cachedStart
        
        updateTest('Cache do Sistema', 'success', 
          `Cache funcionando (${cachedDuration}ms para busca repetida)`,
          Date.now() - cacheStart
        )
      } catch (error) {
        updateTest('Cache do Sistema', 'warning', 
          'Cache não testado devido a erro na busca',
          Date.now() - cacheStart
        )
      }

      // Test 6: Performance
      const perfStart = Date.now()
      const searches = ['rua augusta', 'avenida paulista', 'praça da sé']
      const searchTimes: number[] = []
      
      for (const query of searches) {
        const start = Date.now()
        try {
          await mapboxService.searchPlaces(query, {
            proximity: userLocation || [-46.6333, -23.5505],
            limit: 3
          })
          searchTimes.push(Date.now() - start)
        } catch (error) {
          searchTimes.push(5000) // Penalty for errors
        }
      }
      
      const avgTime = searchTimes.reduce((a, b) => a + b, 0) / searchTimes.length
      const status = avgTime < 2000 ? 'success' : avgTime < 5000 ? 'warning' : 'error'
      
      updateTest('Performance', status, 
        `Tempo médio de busca: ${avgTime.toFixed(0)}ms`,
        Date.now() - perfStart
      )

    } catch (error) {
      console.error('Error running tests:', error)
    } finally {
      setIsRunning(false)
    }
  }

  useEffect(() => {
    runTests()
  }, [])

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-5 h-5 text-green-400" />
      case 'error': return <XCircle className="w-5 h-5 text-red-400" />
      case 'warning': return <AlertCircle className="w-5 h-5 text-yellow-400" />
      default: return <Clock className="w-5 h-5 text-blue-400 animate-spin" />
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return 'border-green-500/50 bg-green-500/10'
      case 'error': return 'border-red-500/50 bg-red-500/10'
      case 'warning': return 'border-yellow-500/50 bg-yellow-500/10'
      default: return 'border-blue-500/50 bg-blue-500/10'
    }
  }

  const successCount = tests.filter(t => t.status === 'success').length
  const totalTests = tests.length

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[url('/api/placeholder/1920/1080')] bg-cover bg-center opacity-5" />
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10" />

      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative z-10 p-6 pb-0"
      >
        <div className="flex items-center justify-between mb-6">
          <motion.button
            onClick={() => navigate('/dashboard')}
            className="p-3 rounded-2xl bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <ArrowLeft className="w-6 h-6" />
          </motion.button>

          <div className="text-center">
            <h1 className="text-2xl font-bold text-white mb-1">Teste do Sistema</h1>
            <p className="text-white/70 text-sm">Verificação de funcionalidades</p>
          </div>

          <motion.button
            onClick={runTests}
            disabled={isRunning}
            className="p-3 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:shadow-lg transition-all duration-300 disabled:opacity-50"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Zap className="w-6 h-6" />
          </motion.button>
        </div>

        {/* Progress */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white/10 backdrop-blur-md rounded-3xl p-4 border border-white/20 mb-6"
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-white font-semibold">Progresso dos Testes</span>
            <span className="text-white/70 text-sm">{successCount}/{totalTests}</span>
          </div>
          <div className="w-full bg-white/20 rounded-full h-2">
            <motion.div
              className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${(successCount / totalTests) * 100}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </motion.div>
      </motion.div>

      {/* Test Results */}
      <div className="relative z-10 px-6 pb-6">
        <div className="space-y-3">
          {tests.map((test, index) => (
            <motion.div
              key={test.name}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`p-4 rounded-3xl border backdrop-blur-md transition-all duration-300 ${getStatusColor(test.status)}`}
            >
              <div className="flex items-center space-x-3">
                {getStatusIcon(test.status)}
                <div className="flex-1">
                  <h3 className="text-white font-semibold">{test.name}</h3>
                  <p className="text-white/70 text-sm">{test.message}</p>
                  {test.duration && (
                    <p className="text-white/50 text-xs mt-1">{test.duration}ms</p>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Summary */}
        {!isRunning && tests.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-6 p-4 rounded-3xl bg-white/10 backdrop-blur-md border border-white/20"
          >
            <div className="text-center">
              <h3 className="text-white font-semibold mb-2">Resumo dos Testes</h3>
              <div className="flex items-center justify-center space-x-6">
                <div className="text-center">
                  <div className="text-green-400 text-2xl font-bold">{tests.filter(t => t.status === 'success').length}</div>
                  <div className="text-white/70 text-xs">Sucessos</div>
                </div>
                <div className="text-center">
                  <div className="text-yellow-400 text-2xl font-bold">{tests.filter(t => t.status === 'warning').length}</div>
                  <div className="text-white/70 text-xs">Avisos</div>
                </div>
                <div className="text-center">
                  <div className="text-red-400 text-2xl font-bold">{tests.filter(t => t.status === 'error').length}</div>
                  <div className="text-white/70 text-xs">Erros</div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}
