import React, { useEffect } from 'react'
import { motion } from 'framer-motion'
import { Navigate } from 'react-router-dom'
import { Car, MapPin, Clock, User, Settings, LogOut, Coins, Crown } from 'lucide-react'
import { useAuth } from '../contexts/AuthContextSimple'
import { GradientBackground } from '../components/GradientBackground'

import { useNoZoom } from '../hooks/useNoZoom'
import '../styles/no-zoom.css'

// 📱 DASHBOARD MOBILE ANDROID NATIVA
// DESIGN FIEL AO LOGIN + CONVERSÃO ANDROID NATIVA

export const DashboardMobile: React.FC = () => {
  const { user, signOut, loading } = useAuth()

  // 🚫 DESABILITA ZOOM COMPLETAMENTE + CONFIGURAÇÕES ANDROID NATIVAS
  useNoZoom()

  // Configurações Android nativas (mantendo design original)
  useEffect(() => {
    // Meta viewport para Android nativo
    let metaViewport = document.querySelector('meta[name="viewport"]')
    if (!metaViewport) {
      metaViewport = document.createElement('meta')
      metaViewport.setAttribute('name', 'viewport')
      document.head.appendChild(metaViewport)
    }
    metaViewport.setAttribute('content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )

    // Configurações de overflow para Android
    document.body.style.margin = '0'
    document.body.style.padding = '0'
    document.body.style.overflow = 'hidden'
    document.documentElement.style.margin = '0'
    document.documentElement.style.padding = '0'
    document.documentElement.style.overflow = 'hidden'
  }, [])

  // Redirect se não logado
  if (!user && !loading) {
    return <Navigate to="/login" replace />
  }

  const handleLogout = async () => {
    await signOut()
  }

  // Animações simples e limpas (MANTENDO ORIGINAIS)
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-black relative overflow-hidden">

      {/* Background Gradient Sutil (FIEL AO LOGIN) */}
      <GradientBackground
        variant="static"
        opacity={0.7}
      />

      {/* Overlay muito sutil para legibilidade (FIEL AO LOGIN) */}
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Conteúdo Principal (FIEL AO LOGIN) */}
      <div className="relative z-10 flex flex-col h-full min-h-screen">

        {/* Header com Logo (FIEL AO LOGIN) */}
        <motion.div
          className="pt-8 pb-6 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center space-x-3 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/10">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <img
                src="/icons/icon-48x48.png"
                alt="MobiDrive"
                className="w-6 h-6"
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-xs text-white/70">
                Bem-vindo, {user?.user_metadata?.full_name || user?.email || 'Usuário'}!
              </p>
            </div>
          </div>
        </motion.div>

        {/* Conteúdo Central (FIEL AO LOGIN) */}
        <div className="flex-1 flex flex-col justify-center px-4 space-y-6">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {/* Card de Boas-vindas (FIEL AO LOGIN) */}
            <motion.div variants={itemVariants}>
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                <div className="text-center">
                  <motion.div
                    className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <User className="w-10 h-10 text-white" />
                  </motion.div>
                  <h2 className="text-xl font-bold text-white mb-2">
                    Olá, {user?.user_metadata?.full_name || 'Usuário'}!
                  </h2>
                  <p className="text-white/80 text-sm">
                    Pronto para sua próxima viagem?
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Ações Principais (FIEL AO LOGIN) */}
            <motion.div variants={itemVariants}>
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                <h3 className="text-lg font-semibold text-white mb-4 text-center">⚡ Ações Rápidas</h3>
                <div className="grid grid-cols-2 gap-3">
                  <motion.button
                    onClick={() => window.location.href = '/ride-request/map'}
                    className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Car className="w-6 h-6" />
                    <span className="text-sm font-medium">Solicitar Corrida</span>
                  </motion.button>

                  <motion.button
                    onClick={() => window.location.href = '/ride-tracking'}
                    className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <MapPin className="w-6 h-6" />
                    <span className="text-sm font-medium">Acompanhar</span>
                  </motion.button>

                  <motion.button
                    onClick={() => window.location.href = '/setup'}
                    className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Settings className="w-6 h-6" />
                    <span className="text-sm font-medium">Configurações</span>
                  </motion.button>

                  <motion.button
                    onClick={() => window.location.href = '/ride-tracking'}
                    className="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Clock className="w-6 h-6" />
                    <span className="text-sm font-medium">Histórico</span>
                  </motion.button>

                  <motion.button
                    onClick={() => window.location.href = '/free-ads'}
                    className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200 relative overflow-hidden"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="absolute top-0 right-0 bg-green-500 text-white text-xs px-2 py-1 rounded-bl-lg font-bold">
                      R$ 10
                    </div>
                    <Coins className="w-6 h-6" />
                    <span className="text-sm font-medium">Ganhar Dinheiro</span>
                  </motion.button>

                  <motion.button
                    onClick={() => window.location.href = '/premium'}
                    className="bg-gradient-to-r from-amber-500 to-orange-600 text-white p-4 rounded-xl flex flex-col items-center space-y-2 transition-all duration-200 relative overflow-hidden"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="absolute top-0 right-0 bg-red-500 text-white text-xs px-2 py-1 rounded-bl-lg font-bold">
                      HOT
                    </div>
                    <Crown className="w-6 h-6" />
                    <span className="text-sm font-medium">Premium</span>
                  </motion.button>
                </div>
              </div>
            </motion.div>

            {/* Estatísticas (FIEL AO LOGIN) */}
            <motion.div variants={itemVariants}>
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                <h3 className="text-lg font-semibold text-white mb-4 text-center">📈 Suas Estatísticas</h3>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-400 mb-1">12</div>
                    <div className="text-xs text-white/70">Corridas</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-400 mb-1">4.8</div>
                    <div className="text-xs text-white/70">Avaliação</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-400 mb-1">R$ 240</div>
                    <div className="text-xs text-white/70">Economizado</div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Testes do Sistema (apenas desenvolvimento) */}
            {process.env.NODE_ENV === 'development' && (
              <motion.div variants={itemVariants}>
                <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                  <div className="space-y-3">
                    <motion.button
                      onClick={() => window.location.href = '/system-tests'}
                      className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl flex items-center justify-center space-x-2 hover:from-green-600 hover:to-green-700 transition-all"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="text-lg">🧪</span>
                      <span className="text-sm font-medium">Testes do Sistema</span>
                    </motion.button>

                    <motion.button
                      onClick={() => window.location.href = '/comprehensive-validation'}
                      className="w-full bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-xl flex items-center justify-center space-x-2 hover:from-purple-600 hover:to-purple-700 transition-all"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="text-lg">🔍</span>
                      <span className="text-sm font-medium">Validação Completa</span>
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Informações do Usuário (FIEL AO LOGIN) */}
            <motion.div variants={itemVariants}>
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                <h3 className="text-lg font-semibold text-white mb-4 text-center">👤 Minha Conta</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-white/70">Email:</span>
                    <span className="text-sm font-medium text-white">{user?.email}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-white/70">Nome:</span>
                    <span className="text-sm font-medium text-white">
                      {user?.user_metadata?.full_name || 'Não informado'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-white/70">Telefone:</span>
                    <span className="text-sm font-medium text-white">
                      {user?.user_metadata?.phone || 'Não informado'}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Botão de Logout (FIEL AO LOGIN) */}
            <motion.div variants={itemVariants}>
              <div className="bg-black/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-2xl">
                <motion.button
                  onClick={handleLogout}
                  className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center space-x-2 transition-all duration-200"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <LogOut className="w-4 h-4" />
                  <span>Sair da Conta</span>
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Footer Simples (FIEL AO LOGIN) */}
        <motion.div
          className="pb-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <p className="text-white/50 text-xs">
            © 2024 MobiDrive. Todos os direitos reservados.
          </p>
        </motion.div>
      </div>
    </div>
  )
}

export default DashboardMobile
