// =====================================================
// VERIFICAR CAMPOS REAIS DA TABELA RIDE_REQUESTS
// =====================================================

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

const supabase = createClient(supabaseUrl, supabaseKey)

async function checkRideFields() {
  console.log('🔍 VERIFICANDO CAMPOS REAIS DA TABELA')
  console.log('=' .repeat(50))

  try {
    // Testar campos um por um
    const possibleFields = [
      'id', 'user_id', 'driver_id',
      'pickup_address', 'destination_address', 'origin_address',
      'pickup_lat', 'pickup_lng', 'pickup_latitude', 'pickup_longitude',
      'destination_lat', 'destination_lng', 'destination_latitude', 'destination_longitude',
      'origin_lat', 'origin_lng', 'origin_latitude', 'origin_longitude',
      'distance', 'duration', 'estimated_price', 'final_price',
      'status', 'vehicle_type', 'payment_method',
      'created_at', 'updated_at', 'accepted_at', 'started_at', 'completed_at', 'cancelled_at'
    ]

    const existingFields = []
    
    for (const field of possibleFields) {
      try {
        const { error } = await supabase
          .from('ride_requests')
          .select(field)
          .limit(1)
        
        if (!error) {
          existingFields.push(field)
          console.log(`✅ ${field}`)
        }
      } catch (e) {
        console.log(`❌ ${field}`)
      }
    }

    console.log('\n📊 CAMPOS EXISTENTES:')
    console.log(existingFields.join(', '))

    // Tentar inserção com campos mínimos
    console.log('\n🧪 TESTANDO INSERÇÃO COM CAMPOS MÍNIMOS...')
    
    const minimalData = {
      user_id: '9ad5afad-8d2d-423e-b6e9-15e3e5a6ddc3'
    }
    
    // Adicionar campos que existem
    if (existingFields.includes('pickup_address')) {
      minimalData.pickup_address = 'Teste Origin'
    }
    if (existingFields.includes('status')) {
      minimalData.status = 'pending'
    }
    
    console.log('📝 Dados para inserção:', minimalData)
    
    const { data: insertData, error: insertError } = await supabase
      .from('ride_requests')
      .insert(minimalData)
      .select('id')
      .single()
    
    if (insertError) {
      console.error('❌ Erro na inserção mínima:', insertError)
    } else {
      console.log('✅ Inserção mínima bem-sucedida:', insertData)
      
      // Limpar
      await supabase
        .from('ride_requests')
        .delete()
        .eq('id', insertData.id)
      
      console.log('🧹 Registro removido')
    }

  } catch (error) {
    console.error('💥 Erro:', error)
  }
}

checkRideFields()
  .then(() => {
    console.log('\n🏁 VERIFICAÇÃO CONCLUÍDA!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
