-- 🚗 FUNÇÕES SQL PARA REALTIME DE CORRIDAS
-- Execute estas funções no Supabase SQL Editor

-- 1. Função para buscar motoristas ativos próximos
CREATE OR REPLACE FUNCTION get_nearby_active_drivers(
  passenger_lat FLOAT,
  passenger_lng FLOAT,
  radius_km FLOAT DEFAULT 10
)
RETURNS TABLE (
  user_id UUID,
  driver_name TEXT,
  driver_phone TEXT,
  latitude FLOAT,
  longitude FLOAT,
  distance FLOAT,
  rating FLOAT,
  is_available BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    dl.user_id,
    p.full_name as driver_name,
    p.phone as driver_phone,
    (dl.location->>'lat')::FLOAT as latitude,
    (dl.location->>'lng')::FLOAT as longitude,
    ST_Distance(
      ST_Point(passenger_lng, passenger_lat)::geography,
      ST_Point((dl.location->>'lng')::FLOAT, (dl.location->>'lat')::FLOAT)::geography
    ) / 1000 as distance,
    COALESCE(dr.rating, 4.5) as rating,
    dl.is_available
  FROM driver_locations dl
  JOIN profiles p ON p.id = dl.user_id
  LEFT JOIN driver_ratings dr ON dr.driver_id = dl.user_id
  WHERE 
    dl.is_active = true 
    AND dl.is_available = true
    AND p.user_type = 'driver'
    AND ST_DWithin(
      ST_Point(passenger_lng, passenger_lat)::geography,
      ST_Point((dl.location->>'lng')::FLOAT, (dl.location->>'lat')::FLOAT)::geography,
      radius_km * 1000
    )
  ORDER BY distance ASC
  LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- 2. Função para calcular distância entre dois pontos
CREATE OR REPLACE FUNCTION calculate_distance(
  lat1 FLOAT,
  lng1 FLOAT,
  lat2 FLOAT,
  lng2 FLOAT
)
RETURNS FLOAT AS $$
BEGIN
  RETURN ST_Distance(
    ST_Point(lng1, lat1)::geography,
    ST_Point(lng2, lat2)::geography
  ) / 1000; -- Retorna em quilômetros
END;
$$ LANGUAGE plpgsql;

-- 3. Função para atualizar localização do motorista
CREATE OR REPLACE FUNCTION update_driver_location(
  driver_id UUID,
  new_lat FLOAT,
  new_lng FLOAT,
  is_available BOOLEAN DEFAULT true
)
RETURNS BOOLEAN AS $$
BEGIN
  INSERT INTO driver_locations (user_id, location, is_active, is_available, updated_at)
  VALUES (
    driver_id,
    json_build_object('lat', new_lat, 'lng', new_lng),
    true,
    is_available,
    NOW()
  )
  ON CONFLICT (user_id) 
  DO UPDATE SET
    location = json_build_object('lat', new_lat, 'lng', new_lng),
    is_active = true,
    is_available = is_available,
    updated_at = NOW();
  
  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- 4. Função para notificar motoristas sobre nova corrida
CREATE OR REPLACE FUNCTION notify_drivers_new_ride(
  ride_id UUID,
  pickup_lat FLOAT,
  pickup_lng FLOAT,
  radius_km FLOAT DEFAULT 10
)
RETURNS INTEGER AS $$
DECLARE
  driver_count INTEGER := 0;
  driver_record RECORD;
BEGIN
  -- Buscar motoristas próximos
  FOR driver_record IN
    SELECT * FROM get_nearby_active_drivers(pickup_lat, pickup_lng, radius_km)
  LOOP
    -- Inserir notificação para cada motorista
    INSERT INTO driver_notifications (
      driver_id,
      ride_id,
      notification_type,
      message,
      data,
      created_at
    ) VALUES (
      driver_record.user_id,
      ride_id,
      'new_ride_request',
      'Nova solicitação de corrida próxima a você',
      json_build_object(
        'ride_id', ride_id,
        'pickup_location', json_build_object('lat', pickup_lat, 'lng', pickup_lng),
        'distance', driver_record.distance
      ),
      NOW()
    );
    
    driver_count := driver_count + 1;
  END LOOP;
  
  RETURN driver_count;
END;
$$ LANGUAGE plpgsql;

-- 5. Trigger para atualizar timestamp de ride_requests
CREATE OR REPLACE FUNCTION update_ride_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar trigger
DROP TRIGGER IF EXISTS update_ride_requests_timestamp ON ride_requests;
CREATE TRIGGER update_ride_requests_timestamp
  BEFORE UPDATE ON ride_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_ride_timestamp();

-- 6. Função para aceitar corrida (motorista)
CREATE OR REPLACE FUNCTION accept_ride_request(
  ride_id UUID,
  driver_id UUID,
  estimated_arrival INTEGER DEFAULT 5
)
RETURNS BOOLEAN AS $$
DECLARE
  ride_status TEXT;
  driver_info RECORD;
BEGIN
  -- Verificar se a corrida ainda está disponível
  SELECT status INTO ride_status FROM ride_requests WHERE id = ride_id;
  
  IF ride_status != 'pending' THEN
    RETURN false; -- Corrida já foi aceita ou cancelada
  END IF;
  
  -- Buscar informações do motorista
  SELECT 
    p.full_name,
    p.phone,
    dl.location
  INTO driver_info
  FROM profiles p
  LEFT JOIN driver_locations dl ON dl.user_id = p.id
  WHERE p.id = driver_id;
  
  -- Atualizar corrida com informações do motorista
  UPDATE ride_requests SET
    status = 'accepted',
    driver_id = driver_id,
    driver_name = driver_info.full_name,
    driver_phone = driver_info.phone,
    driver_location = driver_info.location,
    estimated_arrival = estimated_arrival,
    accepted_at = NOW(),
    updated_at = NOW()
  WHERE id = ride_id;
  
  -- Marcar motorista como indisponível
  UPDATE driver_locations SET
    is_available = false,
    updated_at = NOW()
  WHERE user_id = driver_id;
  
  -- Inserir resposta do motorista
  INSERT INTO driver_responses (
    ride_id,
    driver_id,
    driver_name,
    driver_phone,
    driver_location,
    estimated_arrival,
    accepted,
    created_at
  ) VALUES (
    ride_id,
    driver_id,
    driver_info.full_name,
    driver_info.phone,
    driver_info.location,
    estimated_arrival,
    true,
    NOW()
  );
  
  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- 7. Habilitar Realtime para as tabelas necessárias
ALTER PUBLICATION supabase_realtime ADD TABLE ride_requests;
ALTER PUBLICATION supabase_realtime ADD TABLE driver_responses;
ALTER PUBLICATION supabase_realtime ADD TABLE driver_notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE driver_locations;

-- 8. Políticas RLS (Row Level Security)
-- Permitir que passageiros vejam suas próprias corridas
CREATE POLICY "Users can view their own rides" ON ride_requests
  FOR SELECT USING (passenger_id = auth.uid());

-- Permitir que motoristas vejam corridas próximas
CREATE POLICY "Drivers can view nearby rides" ON ride_requests
  FOR SELECT USING (
    status = 'pending' OR driver_id = auth.uid()
  );

-- Permitir que motoristas respondam a corridas
CREATE POLICY "Drivers can respond to rides" ON driver_responses
  FOR INSERT WITH CHECK (driver_id = auth.uid());

-- Permitir que motoristas vejam suas notificações
CREATE POLICY "Drivers can view their notifications" ON driver_notifications
  FOR SELECT USING (driver_id = auth.uid());

-- Comentários para documentação
COMMENT ON FUNCTION get_nearby_active_drivers IS 'Busca motoristas ativos próximos a uma localização';
COMMENT ON FUNCTION notify_drivers_new_ride IS 'Notifica motoristas sobre nova solicitação de corrida';
COMMENT ON FUNCTION accept_ride_request IS 'Permite que motorista aceite uma corrida';
COMMENT ON FUNCTION update_driver_location IS 'Atualiza localização do motorista em tempo real';
