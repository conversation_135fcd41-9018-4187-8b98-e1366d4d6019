import React, { useRef, useState, useEffect, Suspense } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Environment } from '@react-three/drei'
import * as THREE from 'three'
import Car3DFallback from './Car3DFallback'

// Componente do carro 3D procedural moderno
const ModernCar: React.FC<{ rotation: [number, number, number] }> = ({ rotation }) => {
  const carRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (carRef.current) {
      // Animação sutil de flutuação
      carRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
      // Aplicar rotação do drag
      carRef.current.rotation.x = rotation[0]
      carRef.current.rotation.y = rotation[1] + state.clock.elapsedTime * 0.1 // Rotação automática lenta
      carRef.current.rotation.z = rotation[2]
    }
  })

  return (
    <group ref={carRef} position={[0, 0, 0]}>
      {/* Corpo principal do carro */}
      <mesh position={[0, 0.5, 0]} castShadow receiveShadow>
        <boxGeometry args={[4, 1, 2]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.1}
          envMapIntensity={1}
        />
      </mesh>

      {/* Teto do carro */}
      <mesh position={[0, 1.2, 0]} castShadow>
        <boxGeometry args={[3, 0.8, 1.8]} />
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>

      {/* Para-brisa frontal */}
      <mesh position={[1.3, 1.2, 0]} rotation={[0, 0, -0.2]} castShadow>
        <boxGeometry args={[0.8, 0.7, 1.6]} />
        <meshStandardMaterial
          color="#87CEEB"
          transparent
          opacity={0.3}
          metalness={0.1}
          roughness={0.1}
        />
      </mesh>

      {/* Rodas dianteiras */}
      <group position={[1.3, -0.2, 1.2]}>
        <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
          <cylinderGeometry args={[0.4, 0.4, 0.3]} />
          <meshStandardMaterial color="#2a2a2a" metalness={0.7} roughness={0.3} />
        </mesh>
      </group>

      <group position={[1.3, -0.2, -1.2]}>
        <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
          <cylinderGeometry args={[0.4, 0.4, 0.3]} />
          <meshStandardMaterial color="#2a2a2a" metalness={0.7} roughness={0.3} />
        </mesh>
      </group>

      {/* Rodas traseiras */}
      <group position={[-1.3, -0.2, 1.2]}>
        <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
          <cylinderGeometry args={[0.4, 0.4, 0.3]} />
          <meshStandardMaterial color="#2a2a2a" metalness={0.7} roughness={0.3} />
        </mesh>
      </group>

      <group position={[-1.3, -0.2, -1.2]}>
        <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
          <cylinderGeometry args={[0.4, 0.4, 0.3]} />
          <meshStandardMaterial color="#2a2a2a" metalness={0.7} roughness={0.3} />
        </mesh>
      </group>

      {/* Faróis dianteiros */}
      <mesh position={[2.1, 0.3, 0.7]} castShadow>
        <sphereGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.3}
        />
      </mesh>
      <mesh position={[2.1, 0.3, -0.7]} castShadow>
        <sphereGeometry args={[0.15]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.3}
        />
      </mesh>
    </group>
  )
}

// ErrorBoundary para capturar erros do Three.js
class Car3DErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.warn('Car3D Error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback
    }

    return this.props.children
  }
}

// Componente principal do Canvas 3D
interface Car3DProps {
  className?: string
}

export const Car3D: React.FC<Car3DProps> = ({ className = "" }) => {
  const [rotation, setRotation] = useState<[number, number, number]>([0, 0, 0])
  const [isDragging, setIsDragging] = useState(false)
  const [lastMousePosition, setLastMousePosition] = useState({ x: 0, y: 0 })

  // Handlers para mouse
  const handleMouseDown = (event: React.MouseEvent) => {
    event.preventDefault()
    console.log('Mouse down detected!', event.clientX, event.clientY)
    setIsDragging(true)
    setLastMousePosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isDragging) return
    event.preventDefault()

    console.log('Mouse move detected!', event.clientX, event.clientY)
    const deltaX = event.clientX - lastMousePosition.x
    const deltaY = event.clientY - lastMousePosition.y

    setRotation(prev => [
      prev[0] + deltaY * 0.005,
      prev[1] + deltaX * 0.005,
      prev[2]
    ])

    setLastMousePosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Handlers para touch (mobile)
  const handleTouchStart = (event: React.TouchEvent) => {
    event.preventDefault()
    const touch = event.touches[0]
    console.log('Touch start detected!', touch.clientX, touch.clientY)
    setIsDragging(true)
    setLastMousePosition({ x: touch.clientX, y: touch.clientY })
  }

  const handleTouchMove = (event: React.TouchEvent) => {
    if (!isDragging) return
    event.preventDefault()

    const touch = event.touches[0]
    console.log('Touch move detected!', touch.clientX, touch.clientY)
    const deltaX = touch.clientX - lastMousePosition.x
    const deltaY = touch.clientY - lastMousePosition.y

    setRotation(prev => [
      prev[0] + deltaY * 0.005,
      prev[1] + deltaX * 0.005,
      prev[2]
    ])

    setLastMousePosition({ x: touch.clientX, y: touch.clientY })
  }

  const handleTouchEnd = () => {
    setIsDragging(false)
  }

  useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false)
    const handleGlobalTouchEnd = () => setIsDragging(false)

    const handleGlobalMouseMove = (event: MouseEvent) => {
      if (!isDragging) return

      const deltaX = event.clientX - lastMousePosition.x
      const deltaY = event.clientY - lastMousePosition.y

      setRotation(prev => [
        prev[0] + deltaY * 0.005,
        prev[1] + deltaX * 0.005,
        prev[2]
      ])

      setLastMousePosition({ x: event.clientX, y: event.clientY })
    }

    const handleGlobalTouchMove = (event: TouchEvent) => {
      if (!isDragging) return
      event.preventDefault()

      const touch = event.touches[0]
      const deltaX = touch.clientX - lastMousePosition.x
      const deltaY = touch.clientY - lastMousePosition.y

      setRotation(prev => [
        prev[0] + deltaY * 0.005,
        prev[1] + deltaX * 0.005,
        prev[2]
      ])

      setLastMousePosition({ x: touch.clientX, y: touch.clientY })
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)
      document.addEventListener('touchmove', handleGlobalTouchMove, { passive: false })
      document.addEventListener('touchend', handleGlobalTouchEnd)
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
      document.removeEventListener('touchmove', handleGlobalTouchMove)
      document.removeEventListener('touchend', handleGlobalTouchEnd)
    }
  }, [isDragging, lastMousePosition])

  return (
    <Car3DErrorBoundary fallback={<Car3DFallback className={className} />}>
      <div
        className={`w-full h-full ${className} select-none`}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        style={{
          cursor: isDragging ? 'grabbing' : 'grab',
          touchAction: 'none'
        }}
      >
        <Suspense fallback={<Car3DFallback className={className} />}>
          <Canvas
            shadows
            camera={{ position: [3, 2, 4], fov: 60 }}
            style={{ background: 'transparent' }}
            gl={{ antialias: true, alpha: true }}
          >
            {/* Iluminação ambiente */}
            <ambientLight intensity={0.6} />

            {/* Luz direcional principal */}
            <directionalLight
              position={[8, 8, 5]}
              intensity={1.2}
              castShadow
              shadow-mapSize-width={1024}
              shadow-mapSize-height={1024}
              shadow-camera-far={50}
              shadow-camera-left={-10}
              shadow-camera-right={10}
              shadow-camera-top={10}
              shadow-camera-bottom={-10}
            />

            {/* Luz de preenchimento */}
            <pointLight position={[-5, 3, -5]} intensity={0.4} />
            <pointLight position={[5, 3, 5]} intensity={0.4} />

            {/* Ambiente HDR para reflexões */}
            <Environment preset="city" />

            {/* Carro 3D */}
            <ModernCar rotation={rotation} />

            {/* Plano do chão para sombras */}
            <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -0.8, 0]} receiveShadow>
              <planeGeometry args={[15, 15]} />
              <meshStandardMaterial
                color="#1a1a1a"
                transparent
                opacity={0.2}
              />
            </mesh>
          </Canvas>
        </Suspense>
      </div>
    </Car3DErrorBoundary>
  )
}

export default Car3D
