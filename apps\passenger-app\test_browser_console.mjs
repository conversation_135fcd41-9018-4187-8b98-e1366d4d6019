// =====================================================
// TESTE BÁSICO DO CONSOLE DO NAVEGADOR
// Versão mínima e funcional
// =====================================================

import puppeteer from 'puppeteer'

async function testConsole() {
  console.log('🎯 INICIANDO TESTE DO CONSOLE')
  console.log('=' .repeat(40))

  let browser
  try {
    console.log('🚀 Iniciando navegador...')
    browser = await puppeteer.launch({
      headless: false,
      devtools: true
    })

    const page = await browser.newPage()
    console.log('✅ Página criada')

    // Capturar logs
    const logs = []
    const errors = []

    page.on('console', (msg) => {
      const logText = `[${msg.type().toUpperCase()}] ${msg.text()}`
      logs.push(logText)
      
      if (msg.type() === 'error') {
        errors.push(logText)
        console.log(`❌ ${logText}`)
      } else if (msg.type() === 'warning') {
        console.log(`⚠️ ${logText}`)
      } else {
        console.log(`📝 ${logText}`)
      }
    })

    page.on('pageerror', (error) => {
      const errorText = `PAGE ERROR: ${error.message}`
      errors.push(errorText)
      console.log(`💥 ${errorText}`)
    })

    console.log('🌐 Navegando para localhost:3000...')
    await page.goto('http://localhost:3000/ride-request/map', {
      waitUntil: 'networkidle2',
      timeout: 30000
    })

    console.log('✅ Página carregada, aguardando 10 segundos...')
    await new Promise(resolve => setTimeout(resolve, 10000))

    console.log('\n📊 RESUMO:')
    console.log(`📝 Total de logs: ${logs.length}`)
    console.log(`❌ Total de erros: ${errors.length}`)

    if (errors.length > 0) {
      console.log('\n❌ ERROS ENCONTRADOS:')
      errors.forEach((error, i) => {
        console.log(`  ${i + 1}. ${error}`)
      })
    } else {
      console.log('\n🎉 Nenhum erro encontrado!')
    }

  } catch (error) {
    console.error('💥 Erro:', error.message)
  } finally {
    if (browser) {
      await browser.close()
      console.log('🔒 Navegador fechado')
    }
  }
}

testConsole()
  .then(() => {
    console.log('🏁 Teste concluído!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
