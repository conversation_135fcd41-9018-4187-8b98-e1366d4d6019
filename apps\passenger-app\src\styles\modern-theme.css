/* 🎨 TEMA MODERNO 2025 - PADRÃO PARA TODAS AS PÁGINAS */
/* Glassmorphism + Gradientes + Layout Compacto */

/* ANIMAÇÕES GLOBAIS */
@keyframes pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes glow {
  0%, 100% { 
    box-shadow: 0 2px 8px rgba(0, 255, 136, 0.5);
  }
  50% { 
    box-shadow: 0 4px 16px rgba(0, 255, 136, 0.8);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* CLASSES GLOBAIS DO TEMA MODERNO */
.modern-page {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Inter", sans-serif;
}

.modern-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modern-header-content {
  text-align: center;
}

.modern-brand-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 8px;
}

.modern-brand-icon-container {
  position: relative;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.modern-brand-icon {
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.modern-animated-symbol {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  background: linear-gradient(45deg, #00ff88, #00cc6a);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #fff;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 255, 136, 0.5);
  animation: pulse 2s infinite, glow 2s infinite;
  border: 2px solid rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.modern-header-title {
  color: #fff;
  font-size: 26px;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  letter-spacing: 0.5px;
}

.modern-header-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 400;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.modern-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.modern-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  padding: 24px 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: fadeInUp 0.6s ease-out;
}

.modern-glass-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: slideInRight 0.6s ease-out;
}

.modern-input-group {
  margin-bottom: 18px;
}

.modern-input-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  letter-spacing: 0.025em;
}

.modern-input-container {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 16px;
  border: 2px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.modern-input-container:focus-within {
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modern-input-icon {
  font-size: 20px;
  margin-right: 12px;
  opacity: 0.7;
}

.modern-input {
  flex: 1;
  font-size: 16px;
  color: #1f2937;
  border: none;
  outline: none;
  background-color: transparent;
  font-weight: 500;
}

.modern-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 16px 24px;
  margin-top: 16px;
  border: none;
  font-size: 18px;
  font-weight: 700;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
  cursor: pointer;
}

.modern-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.6);
}

.modern-button:active {
  transform: translateY(0);
}

.modern-button-icon {
  margin-right: 10px;
  font-size: 20px;
}

.modern-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: auto;
  padding-bottom: 16px;
}

.modern-footer-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* RESPONSIVIDADE */
@media (max-width: 480px) {
  .modern-content {
    padding: 16px;
    gap: 8px;
  }
  
  .modern-card {
    padding: 20px 16px;
  }
  
  .modern-glass-card {
    padding: 16px;
  }
}

/* DARK MODE SUPPORT */
@media (prefers-color-scheme: dark) {
  .modern-card {
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
  }
  
  .modern-input-label {
    color: rgba(255, 255, 255, 0.9);
  }
  
  .modern-input {
    color: #fff;
  }
}
