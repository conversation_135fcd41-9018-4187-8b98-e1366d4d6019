import React, { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase, TABLES } from '../lib/supabase'

interface Profile {
  id: string
  full_name?: string
  email?: string
  phone?: string
  user_type?: string
  created_at?: string
  updated_at?: string
}

interface DriverProfile {
  id: string
  user_id: string
  license_number?: string
  vehicle_type?: string
  vehicle_make?: string
  vehicle_model?: string
  vehicle_year?: number
  vehicle_color?: string
  license_plate?: string
  status?: 'online' | 'offline' | 'busy'
  rating?: number
  total_rides?: number
  is_verified?: boolean
  created_at?: string
  updated_at?: string
}

interface AuthState {
  user: User | null
  profile: Profile | null
  driverProfile: DriverProfile | null
  loading: boolean
  error: string | null
  isOnline: boolean
}

interface AuthContextType extends AuthState {
  signIn: (email: string, password: string) => Promise<{ error?: any }>
  signUp: (email: string, password: string, userData: any) => Promise<{ error?: any }>
  signOut: () => Promise<void>
  updateProfile: (data: Partial<Profile>) => Promise<{ error?: any }>
  updateDriverProfile: (data: Partial<DriverProfile>) => Promise<{ error?: any }>
  setOnlineStatus: (isOnline: boolean) => Promise<boolean>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<AuthState>({
    user: null,
    profile: null,
    driverProfile: null,
    loading: true,
    error: null,
    isOnline: false,
  })

  useEffect(() => {
    // Verificar sessão atual
    const getSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (session?.user) {
          await fetchUserProfiles(session.user)
        } else {
          setState(prev => ({ ...prev, loading: false }))
        }
      } catch (error: any) {
        setState(prev => ({ ...prev, loading: false, error: error.message }))
      }
    }

    getSession()

    // Listener para mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          await fetchUserProfiles(session.user)
        } else if (event === 'SIGNED_OUT') {
          setState({
            user: null,
            profile: null,
            driverProfile: null,
            loading: false,
            error: null,
            isOnline: false,
          })
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const fetchUserProfiles = async (user: User) => {
    try {
      // Buscar perfil do usuário
      const { data: profile, error: profileError } = await supabase
        .from(TABLES.profiles)
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError && profileError.code !== 'PGRST116') {
        setState({
          user,
          profile: null,
          driverProfile: null,
          loading: false,
          error: profileError.message,
          isOnline: false,
        })
        return
      }

      // Buscar perfil do motorista
      const { data: driverProfile, error: driverError } = await supabase
        .from(TABLES.drivers)
        .select('*')
        .eq('user_id', user.id)
        .single()

      setState({
        user,
        profile: profile || null,
        driverProfile: driverProfile || null,
        loading: false,
        error: driverError?.message || null,
        isOnline: driverProfile?.status === 'online' || false,
      })
    } catch (error: any) {
      setState({
        user,
        profile: null,
        driverProfile: null,
        loading: false,
        error: error.message,
        isOnline: false,
      })
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        return { error }
      }

      return {}
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false, error: error.message }))
      return { error }
    }
  }

  const signUp = async (email: string, password: string, userData: any) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))

      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.full_name,
            user_type: 'driver',
          },
        },
      })

      if (error) {
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        return { error }
      }

      return {}
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false, error: error.message }))
      return { error }
    }
  }

  const signOut = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }))
      await supabase.auth.signOut()
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false, error: error.message }))
    }
  }

  const updateProfile = async (data: Partial<Profile>) => {
    try {
      if (!state.user) return { error: { message: 'Usuário não autenticado' } }

      setState(prev => ({ ...prev, loading: true, error: null }))

      const { data: updatedProfile, error } = await supabase
        .from(TABLES.profiles)
        .update(data)
        .eq('id', state.user.id)
        .select()
        .single()

      if (error) {
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        return { error }
      }

      setState(prev => ({
        ...prev,
        profile: updatedProfile,
        loading: false,
      }))

      return {}
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false, error: error.message }))
      return { error }
    }
  }

  const updateDriverProfile = async (data: Partial<DriverProfile>) => {
    try {
      if (!state.user) return { error: { message: 'Usuário não autenticado' } }

      setState(prev => ({ ...prev, loading: true, error: null }))

      const { data: updatedDriverProfile, error } = await supabase
        .from(TABLES.drivers)
        .update(data)
        .eq('user_id', state.user.id)
        .select()
        .single()

      if (error) {
        setState(prev => ({ ...prev, loading: false, error: error.message }))
        return { error }
      }

      setState(prev => ({
        ...prev,
        driverProfile: updatedDriverProfile,
        loading: false,
      }))

      return {}
    } catch (error: any) {
      setState(prev => ({ ...prev, loading: false, error: error.message }))
      return { error }
    }
  }

  const setOnlineStatus = async (isOnline: boolean): Promise<boolean> => {
    try {
      if (!state.user) return false

      const status = isOnline ? 'online' : 'offline'
      
      const { error } = await supabase
        .from(TABLES.drivers)
        .update({ status })
        .eq('user_id', state.user.id)

      if (error) {
        console.error('Erro ao alterar status:', error)
        return false
      }

      setState(prev => ({ ...prev, isOnline }))
      return true
    } catch (error) {
      console.error('Erro ao alterar status online:', error)
      return false
    }
  }

  return (
    <AuthContext.Provider
      value={{
        ...state,
        signIn,
        signUp,
        signOut,
        updateProfile,
        updateDriverProfile,
        setOnlineStatus,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider')
  }
  return context
}
