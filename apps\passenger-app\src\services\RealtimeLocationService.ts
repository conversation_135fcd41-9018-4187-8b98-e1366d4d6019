import { supabase } from '../lib/supabase'
import { RealtimeChannel } from '@supabase/supabase-js'

export interface DriverLocation {
  id: string
  user_id: string
  latitude: number
  longitude: number
  heading?: number
  speed?: number
  accuracy?: number
  is_available: boolean
  is_active: boolean
  vehicle_type: string
  driver_name: string
  driver_rating: number
  vehicle_info: {
    make: string
    model: string
    color: string
    plate: string
  }
  updated_at: string
}

export interface LocationUpdate {
  driver_id: string
  latitude: number
  longitude: number
  heading?: number
  speed?: number
  accuracy?: number
  timestamp: string
}

class RealtimeLocationService {
  private channel: RealtimeChannel | null = null
  private subscribers: Map<string, (drivers: DriverLocation[]) => void> = new Map()
  private locationSubscribers: Map<string, (update: LocationUpdate) => void> = new Map()
  private activeDrivers: Map<string, DriverLocation> = new Map()
  private isConnected = false

  /**
   * Initialize realtime connection for driver locations
   */
  async initialize(): Promise<void> {
    if (this.channel) {
      await this.disconnect()
    }

    this.channel = supabase.channel('driver-locations', {
      config: {
        broadcast: { self: true },
        presence: { key: 'driver-tracking' }
      }
    })

    // Listen for driver location updates
    this.channel
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'driver_locations'
      }, (payload) => {
        this.handleDriverLocationChange(payload)
      })
      .on('broadcast', {
        event: 'location-update'
      }, (payload) => {
        this.handleLocationBroadcast(payload.payload as LocationUpdate)
      })
      .subscribe((status) => {
        console.log('🔄 Realtime connection status:', status)
        this.isConnected = status === 'SUBSCRIBED'
        
        if (this.isConnected) {
          console.log('✅ Connected to realtime driver locations')
          this.loadInitialDrivers()
        }
      })
  }

  /**
   * Load initial active drivers
   */
  private async loadInitialDrivers(): Promise<void> {
    try {
      const { data: drivers, error } = await supabase
        .from('driver_locations')
        .select(`
          *,
          profiles!inner(
            full_name,
            avatar_url
          ),
          driver_profiles!inner(
            vehicle_type,
            vehicle_make,
            vehicle_model,
            vehicle_color,
            vehicle_plate,
            rating,
            is_verified
          )
        `)
        .eq('is_active', true)
        .eq('is_available', true)
        .gte('updated_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Last 5 minutes

      if (error) {
        console.error('❌ Error loading initial drivers:', error)
        return
      }

      console.log(`📍 Loaded ${drivers?.length || 0} active drivers`)

      // Transform and store drivers
      drivers?.forEach(driver => {
        const driverLocation: DriverLocation = {
          id: driver.id,
          user_id: driver.user_id,
          latitude: driver.latitude,
          longitude: driver.longitude,
          heading: driver.heading,
          speed: driver.speed,
          accuracy: driver.accuracy,
          is_available: driver.is_available,
          is_active: driver.is_active,
          vehicle_type: driver.driver_profiles?.vehicle_type || 'economy',
          driver_name: driver.profiles?.full_name || 'Motorista',
          driver_rating: driver.driver_profiles?.rating || 4.5,
          vehicle_info: {
            make: driver.driver_profiles?.vehicle_make || '',
            model: driver.driver_profiles?.vehicle_model || '',
            color: driver.driver_profiles?.vehicle_color || '',
            plate: driver.driver_profiles?.vehicle_plate || ''
          },
          updated_at: driver.updated_at
        }

        this.activeDrivers.set(driver.user_id, driverLocation)
      })

      // Notify all subscribers
      this.notifySubscribers()
    } catch (error) {
      console.error('❌ Error in loadInitialDrivers:', error)
    }
  }

  /**
   * Handle database changes for driver locations
   */
  private handleDriverLocationChange(payload: any): void {
    console.log('📍 Driver location change:', payload.eventType, payload.new?.user_id)

    switch (payload.eventType) {
      case 'INSERT':
      case 'UPDATE':
        this.updateDriverLocation(payload.new)
        break
      case 'DELETE':
        this.removeDriver(payload.old?.user_id)
        break
    }
  }

  /**
   * Handle realtime location broadcasts
   */
  private handleLocationBroadcast(update: LocationUpdate): void {
    console.log('📡 Received location broadcast:', update.driver_id)

    const existingDriver = this.activeDrivers.get(update.driver_id)
    if (existingDriver) {
      const updatedDriver: DriverLocation = {
        ...existingDriver,
        latitude: update.latitude,
        longitude: update.longitude,
        heading: update.heading,
        speed: update.speed,
        accuracy: update.accuracy,
        updated_at: update.timestamp
      }

      this.activeDrivers.set(update.driver_id, updatedDriver)
      
      // Notify location-specific subscribers
      this.locationSubscribers.forEach(callback => {
        callback(update)
      })

      // Notify general subscribers
      this.notifySubscribers()
    }
  }

  /**
   * Update driver location in memory
   */
  private async updateDriverLocation(driverData: any): Promise<void> {
    try {
      // Fetch complete driver info if not available
      if (!this.activeDrivers.has(driverData.user_id)) {
        const { data: driverInfo, error } = await supabase
          .from('driver_locations')
          .select(`
            *,
            profiles!inner(full_name, avatar_url),
            driver_profiles!inner(
              vehicle_type, vehicle_make, vehicle_model, 
              vehicle_color, vehicle_plate, rating
            )
          `)
          .eq('user_id', driverData.user_id)
          .single()

        if (error || !driverInfo) {
          console.error('❌ Error fetching driver info:', error)
          return
        }

        const driverLocation: DriverLocation = {
          id: driverInfo.id,
          user_id: driverInfo.user_id,
          latitude: driverInfo.latitude,
          longitude: driverInfo.longitude,
          heading: driverInfo.heading,
          speed: driverInfo.speed,
          accuracy: driverInfo.accuracy,
          is_available: driverInfo.is_available,
          is_active: driverInfo.is_active,
          vehicle_type: driverInfo.driver_profiles?.vehicle_type || 'economy',
          driver_name: driverInfo.profiles?.full_name || 'Motorista',
          driver_rating: driverInfo.driver_profiles?.rating || 4.5,
          vehicle_info: {
            make: driverInfo.driver_profiles?.vehicle_make || '',
            model: driverInfo.driver_profiles?.vehicle_model || '',
            color: driverInfo.driver_profiles?.vehicle_color || '',
            plate: driverInfo.driver_profiles?.vehicle_plate || ''
          },
          updated_at: driverInfo.updated_at
        }

        this.activeDrivers.set(driverData.user_id, driverLocation)
      } else {
        // Update existing driver
        const existingDriver = this.activeDrivers.get(driverData.user_id)!
        const updatedDriver: DriverLocation = {
          ...existingDriver,
          latitude: driverData.latitude,
          longitude: driverData.longitude,
          heading: driverData.heading,
          speed: driverData.speed,
          accuracy: driverData.accuracy,
          is_available: driverData.is_available,
          is_active: driverData.is_active,
          updated_at: driverData.updated_at
        }

        this.activeDrivers.set(driverData.user_id, updatedDriver)
      }

      this.notifySubscribers()
    } catch (error) {
      console.error('❌ Error updating driver location:', error)
    }
  }

  /**
   * Remove driver from active list
   */
  private removeDriver(userId: string): void {
    if (this.activeDrivers.has(userId)) {
      this.activeDrivers.delete(userId)
      this.notifySubscribers()
      console.log('🚫 Removed driver:', userId)
    }
  }

  /**
   * Notify all subscribers of driver updates
   */
  private notifySubscribers(): void {
    const drivers = Array.from(this.activeDrivers.values())
    this.subscribers.forEach(callback => {
      callback(drivers)
    })
  }

  /**
   * Subscribe to driver location updates
   */
  subscribeToDrivers(id: string, callback: (drivers: DriverLocation[]) => void): void {
    this.subscribers.set(id, callback)
    
    // Immediately call with current data
    const drivers = Array.from(this.activeDrivers.values())
    callback(drivers)

    console.log(`📱 Subscribed to driver updates: ${id}`)
  }

  /**
   * Subscribe to specific location updates
   */
  subscribeToLocationUpdates(id: string, callback: (update: LocationUpdate) => void): void {
    this.locationSubscribers.set(id, callback)
    console.log(`📍 Subscribed to location updates: ${id}`)
  }

  /**
   * Unsubscribe from driver updates
   */
  unsubscribe(id: string): void {
    this.subscribers.delete(id)
    this.locationSubscribers.delete(id)
    console.log(`📱 Unsubscribed: ${id}`)
  }

  /**
   * Get drivers within radius of a location
   */
  getDriversNearLocation(
    latitude: number, 
    longitude: number, 
    radiusKm: number = 5
  ): DriverLocation[] {
    const drivers = Array.from(this.activeDrivers.values())
    
    return drivers.filter(driver => {
      const distance = this.calculateDistance(
        latitude, longitude,
        driver.latitude, driver.longitude
      )
      return distance <= radiusKm && driver.is_available && driver.is_active
    })
  }

  /**
   * Calculate distance between two points using Haversine formula
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371 // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1)
    const dLon = this.toRadians(lon2 - lon1)
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  /**
   * Get connection status
   */
  isConnectedToRealtime(): boolean {
    return this.isConnected
  }

  /**
   * Get current active drivers count
   */
  getActiveDriversCount(): number {
    return this.activeDrivers.size
  }

  /**
   * Disconnect from realtime
   */
  async disconnect(): Promise<void> {
    if (this.channel) {
      await this.channel.unsubscribe()
      this.channel = null
    }
    
    this.subscribers.clear()
    this.locationSubscribers.clear()
    this.activeDrivers.clear()
    this.isConnected = false
    
    console.log('🔌 Disconnected from realtime location service')
  }
}

export const realtimeLocationService = new RealtimeLocationService()
export default realtimeLocationService
