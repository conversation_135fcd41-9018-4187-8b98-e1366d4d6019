/**
 * 💰 SISTEMA DE ANÚNCIOS GRATUITOS - MOBIDRIVE
 * Permite ganhar até R$ 10/dia assistindo vídeos
 */

import { supabase } from '../lib/supabase';
import { analyticsService } from './AnalyticsService';

export interface AdVideo {
  id: string;
  title: string;
  description: string;
  videoUrl: string;
  thumbnailUrl: string;
  duration: number; // em segundos
  rewardValue: number; // valor em centavos
  category: 'high_revenue' | 'medium_revenue' | 'low_revenue';
  advertiser: string;
  targetAudience: string[];
  minWatchTime: number; // tempo mínimo para ganhar recompensa
  isActive: boolean;
}

export interface UserAdProgress {
  userId: string;
  date: string;
  totalEarned: number; // em centavos
  videosWatched: number;
  dailyLimit: number; // limite diário em centavos (1000 = R$ 10)
  canWatchMore: boolean;
  nextResetTime: string;
}

export interface WatchSession {
  id: string;
  userId: string;
  adId: string;
  startTime: string;
  endTime?: string;
  watchedDuration: number;
  completed: boolean;
  rewardEarned: number;
  ipAddress?: string;
  userAgent?: string;
}

export class FreeAdsService {
  private static instance: FreeAdsService;
  private dailyLimit = 1000; // R$ 10 em centavos
  private maxVideosPerDay = 50; // Limite de vídeos por dia
  private minWatchPercentage = 0.8; // 80% do vídeo deve ser assistido

  static getInstance(): FreeAdsService {
    if (!FreeAdsService.instance) {
      FreeAdsService.instance = new FreeAdsService();
    }
    return FreeAdsService.instance;
  }

  // Obter progresso diário do usuário
  async getUserDailyProgress(userId: string): Promise<UserAdProgress> {
    const today = new Date().toISOString().split('T')[0];
    
    try {
      const { data, error } = await supabase
        .from('user_ad_progress')
        .select('*')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (!data) {
        // Criar registro para hoje
        const newProgress: UserAdProgress = {
          userId,
          date: today,
          totalEarned: 0,
          videosWatched: 0,
          dailyLimit: this.dailyLimit,
          canWatchMore: true,
          nextResetTime: this.getNextResetTime()
        };

        const { data: created, error: createError } = await supabase
          .from('user_ad_progress')
          .insert([{
            user_id: userId,
            date: today,
            total_earned: 0,
            videos_watched: 0,
            daily_limit: this.dailyLimit,
            can_watch_more: true,
            next_reset_time: newProgress.nextResetTime
          }])
          .select()
          .single();

        if (createError) throw createError;
        return newProgress;
      }

      return {
        userId: data.user_id,
        date: data.date,
        totalEarned: data.total_earned,
        videosWatched: data.videos_watched,
        dailyLimit: data.daily_limit,
        canWatchMore: data.can_watch_more,
        nextResetTime: data.next_reset_time
      };

    } catch (error) {
      console.error('Erro ao obter progresso diário:', error);
      throw error;
    }
  }

  // Obter vídeos disponíveis (priorizando alta receita)
  async getAvailableVideos(userId: string, limit: number = 10): Promise<AdVideo[]> {
    try {
      const progress = await this.getUserDailyProgress(userId);
      
      if (!progress.canWatchMore) {
        return [];
      }

      // Priorizar vídeos de alta receita
      const { data, error } = await supabase
        .from('ad_videos')
        .select('*')
        .eq('is_active', true)
        .order('category', { ascending: true }) // high_revenue primeiro
        .order('reward_value', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data.map(video => ({
        id: video.id,
        title: video.title,
        description: video.description,
        videoUrl: video.video_url,
        thumbnailUrl: video.thumbnail_url,
        duration: video.duration,
        rewardValue: video.reward_value,
        category: video.category,
        advertiser: video.advertiser,
        targetAudience: video.target_audience || [],
        minWatchTime: video.min_watch_time,
        isActive: video.is_active
      }));

    } catch (error) {
      console.error('Erro ao obter vídeos:', error);
      return [];
    }
  }

  // Iniciar sessão de visualização
  async startWatchSession(userId: string, adId: string): Promise<string> {
    try {
      const progress = await this.getUserDailyProgress(userId);
      
      if (!progress.canWatchMore) {
        throw new Error('Limite diário atingido');
      }

      const sessionId = `session_${Date.now()}_${Math.random()}`;
      
      const { error } = await supabase
        .from('ad_watch_sessions')
        .insert([{
          id: sessionId,
          user_id: userId,
          ad_id: adId,
          start_time: new Date().toISOString(),
          watched_duration: 0,
          completed: false,
          reward_earned: 0,
          ip_address: await this.getUserIP(),
          user_agent: navigator.userAgent
        }]);

      if (error) throw error;

      // Registrar no analytics
      analyticsService.track('ad_watch_started', {
        user_id: userId,
        ad_id: adId,
        session_id: sessionId
      });

      return sessionId;

    } catch (error) {
      console.error('Erro ao iniciar sessão:', error);
      throw error;
    }
  }

  // Atualizar progresso da visualização
  async updateWatchProgress(sessionId: string, watchedDuration: number): Promise<void> {
    try {
      const { error } = await supabase
        .from('ad_watch_sessions')
        .update({
          watched_duration: watchedDuration,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId);

      if (error) throw error;

    } catch (error) {
      console.error('Erro ao atualizar progresso:', error);
    }
  }

  // Finalizar sessão e processar recompensa
  async completeWatchSession(sessionId: string): Promise<{
    success: boolean;
    rewardEarned: number;
    totalEarned: number;
    message: string;
  }> {
    try {
      // Obter dados da sessão
      const { data: session, error: sessionError } = await supabase
        .from('ad_watch_sessions')
        .select(`
          *,
          ad_videos (
            duration,
            reward_value,
            min_watch_time
          )
        `)
        .eq('id', sessionId)
        .single();

      if (sessionError) throw sessionError;

      const video = session.ad_videos;
      const watchPercentage = session.watched_duration / video.duration;
      const minWatchTime = video.min_watch_time || (video.duration * this.minWatchPercentage);

      // Verificar se assistiu tempo suficiente
      if (session.watched_duration < minWatchTime) {
        return {
          success: false,
          rewardEarned: 0,
          totalEarned: 0,
          message: `Assista pelo menos ${Math.ceil(minWatchTime)}s para ganhar a recompensa`
        };
      }

      // Verificar limite diário
      const progress = await this.getUserDailyProgress(session.user_id);
      
      if (progress.totalEarned >= this.dailyLimit) {
        return {
          success: false,
          rewardEarned: 0,
          totalEarned: progress.totalEarned,
          message: 'Limite diário de R$ 10 atingido'
        };
      }

      // Calcular recompensa
      let rewardEarned = video.reward_value;
      
      // Ajustar recompensa baseada na porcentagem assistida
      if (watchPercentage < 1.0) {
        rewardEarned = Math.floor(rewardEarned * watchPercentage);
      }

      // Verificar se não excede o limite diário
      if (progress.totalEarned + rewardEarned > this.dailyLimit) {
        rewardEarned = this.dailyLimit - progress.totalEarned;
      }

      // Atualizar sessão
      const { error: updateSessionError } = await supabase
        .from('ad_watch_sessions')
        .update({
          end_time: new Date().toISOString(),
          completed: true,
          reward_earned: rewardEarned
        })
        .eq('id', sessionId);

      if (updateSessionError) throw updateSessionError;

      // Atualizar progresso diário
      const newTotalEarned = progress.totalEarned + rewardEarned;
      const newVideosWatched = progress.videosWatched + 1;
      const canWatchMore = newTotalEarned < this.dailyLimit && newVideosWatched < this.maxVideosPerDay;

      const { error: updateProgressError } = await supabase
        .from('user_ad_progress')
        .update({
          total_earned: newTotalEarned,
          videos_watched: newVideosWatched,
          can_watch_more: canWatchMore
        })
        .eq('user_id', session.user_id)
        .eq('date', new Date().toISOString().split('T')[0]);

      if (updateProgressError) throw updateProgressError;

      // Registrar no analytics
      analyticsService.track('ad_watch_completed', {
        user_id: session.user_id,
        ad_id: session.ad_id,
        session_id: sessionId,
        reward_earned: rewardEarned,
        watch_percentage: watchPercentage,
        total_earned: newTotalEarned
      });

      return {
        success: true,
        rewardEarned,
        totalEarned: newTotalEarned,
        message: `Parabéns! Você ganhou R$ ${(rewardEarned / 100).toFixed(2)}`
      };

    } catch (error) {
      console.error('Erro ao completar sessão:', error);
      return {
        success: false,
        rewardEarned: 0,
        totalEarned: 0,
        message: 'Erro ao processar recompensa'
      };
    }
  }

  // Obter histórico de ganhos
  async getEarningsHistory(userId: string, days: number = 30): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('user_ad_progress')
        .select('*')
        .eq('user_id', userId)
        .order('date', { ascending: false })
        .limit(days);

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Erro ao obter histórico:', error);
      return [];
    }
  }

  // Obter estatísticas do usuário
  async getUserStats(userId: string): Promise<{
    totalEarned: number;
    totalVideosWatched: number;
    averageDailyEarnings: number;
    daysActive: number;
    currentStreak: number;
  }> {
    try {
      const history = await this.getEarningsHistory(userId, 365);
      
      const totalEarned = history.reduce((sum, day) => sum + day.total_earned, 0);
      const totalVideosWatched = history.reduce((sum, day) => sum + day.videos_watched, 0);
      const daysActive = history.filter(day => day.videos_watched > 0).length;
      const averageDailyEarnings = daysActive > 0 ? totalEarned / daysActive : 0;

      // Calcular streak atual
      let currentStreak = 0;
      const today = new Date().toISOString().split('T')[0];
      
      for (let i = 0; i < history.length; i++) {
        const dayDate = new Date(history[i].date);
        const expectedDate = new Date();
        expectedDate.setDate(expectedDate.getDate() - i);
        
        if (dayDate.toISOString().split('T')[0] === expectedDate.toISOString().split('T')[0] && 
            history[i].videos_watched > 0) {
          currentStreak++;
        } else {
          break;
        }
      }

      return {
        totalEarned,
        totalVideosWatched,
        averageDailyEarnings,
        daysActive,
        currentStreak
      };

    } catch (error) {
      console.error('Erro ao obter estatísticas:', error);
      return {
        totalEarned: 0,
        totalVideosWatched: 0,
        averageDailyEarnings: 0,
        daysActive: 0,
        currentStreak: 0
      };
    }
  }

  // Utilitários privados
  private getNextResetTime(): string {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow.toISOString();
  }

  private async getUserIP(): Promise<string> {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return 'unknown';
    }
  }

  // Formatar valor em reais
  formatCurrency(centavos: number): string {
    return `R$ ${(centavos / 100).toFixed(2)}`;
  }

  // Verificar se pode assistir mais vídeos
  async canWatchMoreVideos(userId: string): Promise<boolean> {
    const progress = await this.getUserDailyProgress(userId);
    return progress.canWatchMore;
  }

  // Obter tempo restante para reset
  getTimeUntilReset(): string {
    const now = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const diff = tomorrow.getTime() - now.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  }
}

export const freeAdsService = FreeAdsService.getInstance();
