import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [react()],

  // Base URL para produção
  base: mode === 'production' ? '/' : '/',

  // Otimizações de build
  build: {
    // Output directory
    outDir: 'dist',

    // Chunk size warnings
    chunkSizeWarningLimit: 1000,

    // Rollup options para otimização
    rollupOptions: {
      output: {
        // Separar chunks por vendor
        manualChunks: {
          vendor: ['react', 'react-dom'],
          supabase: ['@supabase/supabase-js'],
          mapbox: ['mapbox-gl'],
          motion: ['framer-motion'],
          icons: ['lucide-react'],
          three: ['three', '@react-three/fiber', '@react-three/drei']
        },
        // Nomes de arquivos para cache busting
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },

    // Minificação
    minify: 'terser',

    // Terser options para produção
    terserOptions: {
      compress: {
        drop_console: mode === 'production',
        drop_debugger: mode === 'production'
      }
    },

    // Source maps apenas em desenvolvimento
    sourcemap: mode !== 'production',

    // Target para browsers modernos
    target: 'esnext',

    // Reportar tamanho dos chunks
    reportCompressedSize: true
  },

  // Otimizações de desenvolvimento
  server: {
    port: 5173,
    host: true
  },

  // Preview configuration
  preview: {
    port: 4173,
    host: true
  },

  // Pre-bundling de dependências
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'framer-motion',
      'lucide-react',
      '@supabase/supabase-js',
      '@supabase/postgrest-js',
      '@supabase/realtime-js',
      '@supabase/storage-js',
      '@supabase/gotrue-js',
      'mapbox-gl'
    ]
  },

  // Resolve aliases
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@shared': resolve(__dirname, '../shared')
    }
  },

  // Define global constants
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    // FORÇAR TOKEN MAPBOX HARDCODED PARA RESOLVER PROBLEMA DE VERCEL
    'import.meta.env.VITE_MAPBOX_ACCESS_TOKEN': JSON.stringify('pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'),
    'process.env.VITE_MAPBOX_ACCESS_TOKEN': JSON.stringify('pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g')
  }
}))