import { useEffect, useRef } from 'react'

// 🔒 FORÇA ZOOM 100% GLOBAL - COMPENSAÇÃO INVERSA TOTAL
// Detecta zoom do browser e aplica transform scale inverso para compensar completamente
// APENAS PARA DESKTOP - MOBILE É IGNORADO

// Utility function to detect if device is mobile
const isMobileDevice = (): boolean => {
  // Check user agent
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone']
  const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword))

  // Check screen size
  const isMobileScreen = window.screen.width <= 768 || window.screen.height <= 768

  // Check touch capability
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0

  // Check orientation API
  const hasOrientationAPI = 'orientation' in window

  // Device is mobile if it matches multiple criteria
  return (isMobileUA || (isMobileScreen && isTouchDevice) || hasOrientationAPI)
}

// Utility function to detect actual browser zoom (desktop only)
const detectDesktopZoom = (): number => {
  // Only works reliably on desktop browsers
  if (isMobileDevice()) {
    return 1 // Always return 1 for mobile
  }

  try {
    // Method 1: Compare outer and inner width (most reliable for desktop)
    const windowRatio = window.outerWidth / window.innerWidth

    // Validate the ratio is reasonable (between 0.5 and 3.0)
    if (windowRatio >= 0.5 && windowRatio <= 3.0 && !isNaN(windowRatio)) {
      return Math.round(windowRatio * 100) / 100
    }

    // Method 2: Fallback to device pixel ratio if available
    const deviceRatio = window.devicePixelRatio || 1
    if (deviceRatio >= 0.5 && deviceRatio <= 3.0) {
      return Math.round(deviceRatio * 100) / 100
    }

    // Method 3: Last resort - assume no zoom
    return 1
  } catch (error) {
    console.warn('Zoom detection failed:', error)
    return 1
  }
}

export const useForceZoom100 = () => {
  const currentZoomRef = useRef(1)
  const isInitializedRef = useRef(false)
  const lastCheckTimeRef = useRef(0)

  useEffect(() => {
    // Skip entirely for mobile devices
    if (isMobileDevice()) {
      console.log('📱 Mobile device detected - skipping zoom compensation')
      return
    }

    console.log('🖥️ Desktop device detected - enabling zoom compensation')

    // Debounced zoom compensation function
    const applyZoomCompensation = () => {
      const now = Date.now()

      // Debounce: only check zoom every 500ms to prevent excessive calls
      if (now - lastCheckTimeRef.current < 500) {
        return
      }
      lastCheckTimeRef.current = now

      const browserZoom = detectDesktopZoom()
      const currentZoom = currentZoomRef.current

      // Only apply compensation if zoom actually changed significantly
      if (Math.abs(browserZoom - currentZoom) > 0.1) {
        currentZoomRef.current = browserZoom
        const compensationFactor = 1 / browserZoom

        console.log(`🔍 ZOOM CHANGED: ${(browserZoom * 100).toFixed(1)}% → APPLYING COMPENSATION: ${(compensationFactor * 100).toFixed(1)}%`)

        // Apply compensation to the main container
        const container = document.querySelector('.device-wrapper-container') as HTMLElement
        if (container) {
          // Apply inverse transform scale
          container.style.transform = `scale(${compensationFactor})`
          container.style.transformOrigin = 'center center'

          // Adjust dimensions to compensate for scale
          container.style.width = `${100 * browserZoom}vw`
          container.style.height = `${100 * browserZoom}vh`

          // Ensure fixed positioning
          container.style.position = 'fixed'
          container.style.top = '0'
          container.style.left = '0'
          container.style.zIndex = '9999'

          // Force anti-zoom properties
          container.style.zoom = '1'
          container.style.webkitTransform = `scale(${compensationFactor})`
          container.style.mozTransform = `scale(${compensationFactor})`
          container.style.msTransform = `scale(${compensationFactor})`

          console.log(`✅ COMPENSATION APPLIED: scale(${compensationFactor.toFixed(3)}) to container`)
        } else if (!isInitializedRef.current) {
          console.log(`❌ CONTAINER .device-wrapper-container NOT FOUND - Waiting for DOM...`)
          // Retry once after a delay if container not found during initialization
          setTimeout(applyZoomCompensation, 200)
        }

        // 📱 Maintain iPhone container with scale 0.75 independent of browser zoom
        const iphoneContainer = document.querySelector('.device-iphone-container') as HTMLElement
        if (iphoneContainer) {
          // Preserve 0.75 scale of mockup independent of browser zoom
          iphoneContainer.style.transform = 'scale(0.75)'
          iphoneContainer.style.webkitTransform = 'scale(0.75)'
          iphoneContainer.style.mozTransform = 'scale(0.75)'
          iphoneContainer.style.msTransform = 'scale(0.75)'
          iphoneContainer.style.oTransform = 'scale(0.75)'
          iphoneContainer.style.transformOrigin = 'center center'
        }

        // Force zoom 1 on HTML and Body with maximum priority
        document.documentElement.style.setProperty('zoom', '1', 'important')
        document.body.style.setProperty('zoom', '1', 'important')
        document.documentElement.style.overflow = 'hidden'
        document.body.style.overflow = 'hidden'
      }
    }

    // Setup restrictive viewport for desktop
    const setupRestrictiveViewport = () => {
      let metaViewport = document.querySelector('meta[name="viewport"]')

      if (!metaViewport) {
        metaViewport = document.createElement('meta')
        metaViewport.setAttribute('name', 'viewport')
        document.head.appendChild(metaViewport)
      }

      // Configuration that forces 100% always
      metaViewport.setAttribute('content',
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no'
      )
    }

    // Initial setup and monitoring
    setupRestrictiveViewport()

    // Initial execution with staggered delays
    applyZoomCompensation()
    setTimeout(() => {
      applyZoomCompensation()
      isInitializedRef.current = true
    }, 100)

    // Monitor zoom changes with moderate frequency (prevents infinite loops)
    const zoomInterval = setInterval(applyZoomCompensation, 5000) // Every 5 seconds (less frequent)

    // Optimized event handlers for zoom detection
    const handleZoomChange = () => {
      // Debounced zoom check - only triggers if enough time has passed
      applyZoomCompensation()
    }

    const handleKeyboardZoom = (e: KeyboardEvent) => {
      // Detect Ctrl+Plus, Ctrl+Minus, Ctrl+0 (zoom shortcuts)
      if (e.ctrlKey && (e.key === '+' || e.key === '-' || e.key === '0' || e.key === '=' || e.key === 'Equal')) {
        console.log(`🎯 ZOOM SHORTCUT DETECTED: Ctrl+${e.key}`)
        // Force immediate check for keyboard zoom
        lastCheckTimeRef.current = 0
        setTimeout(applyZoomCompensation, 100) // Small delay to let browser apply zoom
      }
    }

    // Add event listeners with optimized responsiveness
    window.addEventListener('resize', handleZoomChange, { passive: true })
    window.addEventListener('keydown', handleKeyboardZoom, { passive: true })

    // Cleanup function
    return () => {
      clearInterval(zoomInterval)
      window.removeEventListener('resize', handleZoomChange)
      window.removeEventListener('keydown', handleKeyboardZoom)
    }

  }, [])
}

// Hook for debug - shows zoom information (desktop only)
export const useZoomDebug = () => {
  useEffect(() => {
    // Skip debug for mobile devices
    if (isMobileDevice()) {
      console.log('📱 Mobile device - zoom debug disabled')
      return
    }

    const logZoomInfo = () => {
      const zoom = detectDesktopZoom()
      const container = document.querySelector('.device-wrapper-container') as HTMLElement
      const containerTransform = container ? container.style.transform : 'CONTAINER NOT FOUND'

      console.log('🔍 ZOOM DEBUG:', {
        detectedZoom: (zoom * 100).toFixed(1) + '%',
        devicePixelRatio: (window.devicePixelRatio || 1).toFixed(2),
        compensationTransform: containerTransform,
        viewport: `${window.innerWidth}×${window.innerHeight}`,
        screen: `${window.screen.width}×${window.screen.height}`,
        containerFound: !!container,
        isMobile: isMobileDevice()
      })
    }

    // Initial log
    logZoomInfo()

    // Periodic log less frequent
    const interval = setInterval(logZoomInfo, 15000) // Every 15 seconds

    return () => clearInterval(interval)
  }, [])
}

export default useForceZoom100
